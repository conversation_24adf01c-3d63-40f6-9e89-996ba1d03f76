<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.purchase.infrastructure.mapper.InquiryMapper">

    <select id="pageList" resultType="com.hightop.benyin.purchase.infrastructure.entity.Inquiry">
        select a.* from tb_inquiry a
            left join b_storage_article b on a.article_id = b.id
            left join tb_part_product_tree c on a.part_id= c.part_id
        where a.deleted = 0
        <if test="null!=query.productTreeId and !query.productTreeId.isEmpty()">
                AND
                <foreach collection="query.productTreeId" item="id" separator=" OR " open="(" close=")">
                    JSON_CONTAINS(c.product_tree_ids,#{id})
                </foreach>
            </if>
           <!-- 小类 -->
        <if test="null != query.type and '' != query.type">
            and b.type = #{query.type}
        </if>

            <!-- 供应商 -->
        <if test="null != query.manufacturerId and '' != query.manufacturerId">
            and a.manufacturer_id = #{query.manufacturerId}
        </if>

            <!-- 物品名称 -->
        <if test="null != query.articleName and '' != query.articleName">
            and b.name like concat ('%',#{query.articleName},'%')
        </if>

        <!-- 物品编号 -->
        <if test="null != query.articleCode and '' != query.articleCode">
            and b.code like concat ('%',#{query.articleCode},'%')
        </if>

        <!-- 报价时间 -->
        <if test="null != query.offerPriceStart and query.offerPriceEnd">
            and a.created_at between #{query.offerPriceStart} and #{query.offerPriceEnd}
        </if>
        <!-- oem编号 -->
        <if test="null != query.numberOem and '' != query.numberOem">
            and b.number_oem like concat ('%',#{query.numberOem},'%')
        </if>
        <!-- 渠道 -->
        <if test="null != query.manufacturerChannel and '' != query.manufacturerChannel">
            and b.manufacturer_channel = #{query.manufacturerChannel}
        </if>
        order by updated_at desc
    </select>


</mapper>
