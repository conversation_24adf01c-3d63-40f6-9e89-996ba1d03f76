<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.customer.infrastructure.mapper.CustomerMapper">
    <sql id="queryWithTag">

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.seqId and '' != qo.seqId ">
            and t.seq_id like CONCAT('%', #{qo.seqId},'%')
        </if>

        <if test="null != qo.name and '' != qo.name ">
            and t.name like CONCAT('%', #{qo.name},'%')
        </if>

        <if test="null != qo.address and '' != qo.address ">
            and t.address like CONCAT('%', #{qo.address},'%')
        </if>

        <if test="null != qo.groupName and '' != qo.groupName ">
            and t1.name like CONCAT('%', #{qo.groupName},'%')
        </if>

        <if test="null != qo.deviceSeqId and '' != qo.deviceSeqId ">
            and exists(select 1 from b_customer_device_group
            bcdg where bcdg.customer_id = t.id and bcdg.deleted = false and bcdg.device_seq_id like CONCAT('%', #{qo.deviceSeqId},'%') )
        </if>

        <if test="null != qo.regCliState and '' != qo.regCliState ">
            and exists(select 1 from b_customer_device_group
            bcdg where bcdg.customer_id = t.id and bcdg.deleted = false and bcdg.reg_cli_state =#{qo.regCliState})
        </if>

        <if test="null != qo.seqId and '' != qo.seqId ">
            and t.seq_id like CONCAT('%', #{qo.seqId},'%')
        </if>
        <if test="null != qo.licence and '' != qo.licence ">
            and tb.license like CONCAT('%', #{qo.licence},'%')
        </if>

        <if test="null != qo.updatedByName and '' != qo.updatedByName ">
            and (tu.name like CONCAT('%', #{qo.updatedByName},'%') or ts.name like CONCAT('%', #{qo.updatedByName},'%'))
        </if>

        <if test="null != qo.phone and '' != qo.phone ">
            AND EXISTS (select 1 from b_customer_staff bcs where bcs.customer_id=t.id and bcs.deleted=false and bcs.tel
            like concat ('%',#{qo.phone},'%')
            )
        </if>
        <if test="null != qo.serTypes and '' != qo.serTypes ">
            AND EXISTS (select 1 from b_customer_device_group d where d.customer_id=t.id and d.deleted=false and d.ser_type
            in
            <foreach collection="qo.serTypes" item="serType" separator="," open="(" close=")">
                #{serType}
            </foreach>
            )
        </if>
        <if test="null != qo.vxNikeName and '' != qo.vxNikeName ">
            AND EXISTS (select 1 from b_customer_staff bcs where bcs.customer_id=t.id and bcs.deleted=false and bcs.vx_nike_name
            like concat ('%',#{qo.vxNikeName},'%')
            )
        </if>

        <if test="null != qo.vxGroupName and '' != qo.vxGroupName ">
            AND EXISTS (select 1 from b_customer_staff bcs where bcs.customer_id=t.id and bcs.deleted=false and bcs.vx_group_name
            =#{qo.vxGroupName}
            )
        </if>
        <if test="null != qo.settleMethod ">
            AND EXISTS (select 1 from b_customer_business bcs where bcs.customer_id=t.id and bcs.settle_method
            =#{qo.settleMethod}
            )
        </if>

        <if test="null!=qo.lastIds and !qo.lastIds.isEmpty()">
            AND EXISTS (SELECT 1 FROM b_customer_device_group bcdg WHERE bcdg.customer_id = t.id AND bcdg.deleted =
            false AND bcdg.product_id IN
            <foreach collection="qo.lastIds" item="pid" separator=" OR " open="(" close=")">
                #{pid}
            </foreach>
            )
        </if>

        <if test="null != qo.businessStatus and '' != qo.businessStatus ">
            and t.business_status = #{qo.businessStatus}
        </if>


        <if test="null != qo.status and '' != qo.status ">
            and t.status = #{qo.status}
        </if>

        <if test="null != qo.industryAttr and '' != qo.industryAttr ">
            and t.industry_attr = #{qo.industryAttr}
        </if>


        <if test="null != qo.source and '' != qo.source ">
            and t.source = #{qo.source}
        </if>

        <if test="null != qo.membershipLevel ">
            and t.membership_level = #{qo.membershipLevel}
        </if>
        <if test="null != qo.longitude ">
            and t.longitude like CONCAT( #{qo.longitude},'%')
        </if>
        <if test="null != qo.latitude ">
            and t.latitude like CONCAT( #{qo.latitude},'%')
        </if>

        <if test="null != qo.salesmanId ">
            and tb.salesman_id =#{qo.salesmanId}
        </if>

        <if test="null != qo.shareActivity ">
            and t.share_activity =#{qo.shareActivity}
        </if>

        <if test="null != qo.shareCustomer ">
            and t.share_customer =#{qo.shareCustomer}
        </if>

        <if test="null != qo.businessmanId ">
            and tb.businessman_id =#{qo.businessmanId}
        </if>

        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.updated_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.updated_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>


        <if test="null != qo.beginMachineNum ">
            and ifnull(tm.machineNum,0) &gt;= #{qo.beginMachineNum}
        </if>
        <if test="null != qo.endMachineNum ">
            and ifnull(tm.machineNum,0) &lt;= #{qo.endMachineNum}
        </if>

        <if test="null != qo.beginCliMachineNum ">
            and ifnull(tm1.cliMechineNum,0) &gt;= #{qo.beginCliMachineNum}
        </if>

        <if test="null != qo.endCliMachineNum ">
            and ifnull(tm1.cliMechineNum,0) &lt;= #{qo.endCliMachineNum}
        </if>
    </sql>

    <select id="getCustomerList" resultType="com.hightop.benyin.customer.application.vo.CustomerListVo">
        SELECT t.id,
        t.seq_id,
        t.name,
        t.subbranch,
        t.source,
        t.type,
        t.status,
        t.membership_level,
        t.shop_recruitment,
        t.business_status,
        t.industry_attr,
        t.captcha,
        t.legal_person,
        t.legal_person_tel,
        t.taxpayer,
        t.group_id,
        t.longitude,
        t.latitude,
        t.location,
        t.region_code,
        t.address,
        t.points_balance,
        t.precision_address,
        t.created_at,
        t.updated_at,
        t.deleted,
        ifnull(tm.machineNum,0) machineNum,
        ifnull( tm1.cliMechineNum,0) cliMechineNum,
        t.`business_scope`,
        t1.name AS groupName,t2.name AS area ,t3.name AS city,t4.name AS province,
        tb.salesman,
        tb.businessman, ifnull(tu.name,ts.name) updatedByName
        FROM b_customer t
        LEFT JOIN b_customer_group t1 ON (t1.id = t.group_id)
        LEFT JOIN b_customer_business tb ON (t.id = tb.customer_id)
        LEFT JOIN st_user_basic tu ON (tu.id = t.updated_by)
        LEFT JOIN b_customer_staff ts ON (ts.tel = t.updated_by and ts.customer_id=t.id and ts.deleted=false)
        left join (select count(1) machineNum ,customer_id  from b_customer_device_group  where deleted=false group by customer_id) tm on tm.customer_id = t.id
        left join (select count(1) cliMechineNum ,customer_id  from b_customer_device_group  where deleted=false and reg_cli_state = '1'  group by customer_id) tm1 on tm1.customer_id = t.id
        left join b_region t2 on t2.code = t.region_code
        left join b_region t3 on t3.code = t2.parent_code
        left join b_region t4 on t4.code = t3.parent_code

        WHERE t.deleted = false
        <include refid="queryWithTag" />
        ORDER BY t.id DESC
    </select>

    <select id="totalCount" resultType="com.hightop.benyin.customer.application.vo.CustomerDataCountVo">
        SELECT
            count( distinct  t.id) totalCustomerNum,
            sum(ifnull(tm.machineNum,0)) totalDeviceNum
        FROM b_customer t
                 LEFT JOIN b_customer_group t1 ON (t1.id = t.group_id)
                 LEFT JOIN b_customer_business tb ON (t.id = tb.customer_id)
                 LEFT JOIN st_user_basic tu ON (tu.id = t.updated_by)
                 LEFT JOIN b_customer_staff ts ON (ts.tel = t.updated_by and ts.customer_id=t.id)
                 left join (select count(1) machineNum ,customer_id  from b_customer_device_group  where deleted=false group by customer_id) tm on tm.customer_id = t.id
                 left join (select count(1) cliMechineNum ,customer_id  from b_customer_device_group  where deleted=false and reg_cli_state = '1'  group by customer_id) tm1 on tm1.customer_id = t.id
                 left join b_region t2 on t2.code = t.region_code
                 left join b_region t3 on t3.code = t2.parent_code
                 left join b_region t4 on t4.code = t3.parent_code
        WHERE t.deleted = false
        <include refid="queryWithTag" />
    </select>

    <select id="getCustomerValue" resultType="com.hightop.benyin.customer.application.vo.CustomerValueVo">

        select t.*,t.selfRepair+t.workCount totalRepair,round(interviewCount/enterMonths,0)  monthAvgInterview,round(t.searchCount/enterMonths,0) monthAvgSearch,
               round(orderCount/enterMonths,2) monthAvgOrder, round(t.workCount/t.enterMonths,2) monthAvgWork,round(t.totalFault/enterMonths,0) monthAvgFault,
               round((t.selfRepair+t.workCount)/enterMonths,0) monthAvgRepair,t.workAmount+t.orderAmount consumeAmount, round((t.workAmount+t.orderAmount)/enterMonths,0) monthAvgConsume
        from (
                 select
                     TIMESTAMPDIFF(DAY , min(t.created_at), curdate()) enterDays,
                     ceil(TIMESTAMPDIFF(DAY , min(t.created_at), curdate()) /30) enterMonths,
                     (select count(1)  from b_customer_device_group  where deleted=false and customer_id = t.id ) real_machine_num,
                     (select count(1)  from b_customer_device_group  where deleted=false and reg_cli_state = '1' and customer_id = t.id ) cli_mechine_num,
                     t1.site_area,
                     t1.personnel_num,
                     ifnull(t2.totalPrintNum,0) totalPrintNum,
                     ifnull(t2.totalColorPrint,0) totalColorPrint,
                     ifnull(t2.totalBlackPrint,0) totalBlackPrint,
                     ifnull(t2.monthAvgPrint,0) monthAvgPrint,
                     (select count(1)
                      from b_customer_page_view
                      where customer_id = t.id) interviewCount,
                     (select count(1)
                      from b_customer_search_out_log
                      where customer_id = t.id) searchCount,
                     ifnull( t3.orderCount,0) orderCount,ifnull(t3.orderAmount,0) orderAmount,ifnull(round(t3.avgOrderAmount,0),0) avgOrderAmount,
                     ifnull( t4.workCount,0) workCount,ifnull(t4.workAmount,0) workAmount,ifnull(round(t4.avgWorkAmount,0),0) avgWorkAmount,
                     (select count(1)
                      from b_iot_state where customer_id=t.id) totalFault,
                     (select count(1)
                      from tb_self_repair_report where customer_id=t.id) selfRepair
                 from b_customer t
                          left join b_customer_tag_properties t1 on t.id = t1.customer_id
                          left join (select customer_id,
                                            sum(total_count)           totalPrintNum,
                                            sum(black_white_count)     totalBlackPrint,
                                            sum(color_count)           totalColorPrint,
                                            ROUND(avg(total_count), 0) monthAvgPrint
                                     from r_month_count
                                     group by customer_id) t2 on t2.customer_id = t.id
                          left join (select count(1) orderCount,sum(actual_amount) orderAmount,avg(actual_amount) avgOrderAmount,customer_id
                                     from tb_trade_order where order_status not in('CLOSED','WAIT_AUDIT','WAIT_PAY')  group by customer_id) t3 on t3.customer_id=t.id
                          left join (select count(1) workCount,sum(total_amount) workAmount,avg(total_amount) avgWorkAmount,customer_id
                                     from tb_work_order where status = 'completed' group by customer_id) t4 on t4.customer_id=t.id
                 where t.id = #{id}) t

    </select>

    <select id="distributeSalePage" resultType="com.hightop.benyin.customer.application.vo.CustomerDistributeSaleVo">
        select t.*,
               (ifnull(t1.orderAmount,0)+ifnull(t2.workAmount,0))/t.saleTotalAmount totalScale,
               (ifnull(t1.orderAmount,0)+ifnull(t2.workAmount,0)) saleAmount,
               ifnull(t1.orderAmount,0) orderAmount,
               ifnull(t2.workAmount,0) workAmount,
               ifnull(t3.receiptAmount,0) receiptAmount,
               ifnull(t2.workAmount,0)/t.workATotalAmount workScale,
               ifnull(t1.orderAmount,0)/t.orderTotalAmount orderScale,
               ifnull(t3.receiptAmount,0)/t.receiptTotalAmount receiptScale
        from (
                 select t1.name area,t2.name city,t3.name province,t.region_code,count(1) customerNum,count(1)/t4.num numSacle,
                        t5.orderTotalAmount+t6.workATotalAmount saleTotalAmount,t5.orderTotalAmount ,t6.workATotalAmount,t7.receiptTotalAmount
                 from b_customer t
                          left join b_region t1 on t1.code= t.region_code
                          left join b_region t2 on t2.code= t1.parent_code
                          left join b_region t3 on t3.code= t2.parent_code
                          left join (select count(1) num
                                     from b_customer where deleted=0) t4 on 1=1
                          left join (select sum(actual_amount) orderTotalAmount  from tb_trade_order t
                                     where order_status not in('CLOSED','WAIT_AUDIT','WAIT_PAY')) t5 on 1=1
                          left join (select sum(total_amount) workATotalAmount from tb_work_order where status = 'completed') t6 on 1=1
                          left join (select sum(total_amount) receiptTotalAmount from b_iot_print_receipt ) t7 on 1=1

                 where t.deleted=0
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.region_code like CONCAT( #{qo.regionPath},'%')
        </if>

                 group by t1.name, t2.name, t3.name,t.region_code
             ) t
                 left join(select sum(actual_amount) orderAmount,consignee_region_code from tb_trade_order t
                           where order_status not in('CLOSED','WAIT_AUDIT','WAIT_PAY') group by consignee_region_code) t1 on t1.consignee_region_code=t.region_code
                 left join (select sum(total_amount) workAmount,t1.region_code from tb_work_order t
                                                                                     left join b_customer t1 on t1.id=t.customer_id
                            where t.status = 'completed' group by t1.region_code) t2 on t2.region_code=t.region_code
                 left join (select sum(t.total_amount) receiptAmount,t1.region_code  from b_iot_print_receipt t
                                                                                              left join b_customer t1 on t1.id=t.customer_id   group by t1.region_code ) t3 on t3.region_code=t.region_code

        order by t.customerNum desc
    </select>

    <sql id="customerCondition">
        AND customer_id IN (
        <foreach collection="qo" item="item" separator=",">
            #{item}
        </foreach>
        )
        AND ser_type != 'SCATTERED'
        AND DATE_FORMAT( completed_at, '%Y-%m' ) = #{month}
        GROUP BY
        customer_id
    </sql>
    <sql id="devicesCondition">
        AND device_group_id IN (
        <foreach collection="qo" item="item" separator=",">
            #{item}
        </foreach>
        )
        AND DATE_FORMAT( completed_at, '%Y-%m' ) = #{month}
        GROUP BY
        device_group_id
    </sql>

    <select id="queryRepairTimesByIds" resultType="com.hightop.benyin.statistics.infrastructure.TO.CommonData">
        SELECT
        <if test="type == 1">
            customer_id as "key",
        </if>
        <if test="type == 2">
            device_group_id as "key",
        </if>
        COUNT( id ) as "value"
        FROM
        tb_work_order
        WHERE
        status != 'close'
        and ser_type NOT IN ('SCATTERED','NO_WARRANTY', 'WARRANTY','QA','QA_COMPONENT','MAINTENANCE','OTHER')
        and deleted = 0
        <if test="type == 1">
            <include refid="customerCondition"></include>
        </if>
        <if test="type == 2">
            <include refid="devicesCondition"></include>
        </if>
    </select>
    <select id="queryStatisticsPrintCountDraft"
            resultType="com.hightop.benyin.statistics.infrastructure.TO.TStatisticsMonthGrossProfitTo">
        SELECT
            t.id AS customerId,
            t.NAME AS NAME,
            t.seq_id AS seqId,
            t1.device_group_id AS deviceId,
            t1.color_inception AS colorInception,
            t1.color_cutoff AS colorCutoff,
            t1.black_white_inception AS blackWhiteInception,
            t1.black_white_cutoff AS blackWhiteCutoff,
            t1.black_white_amount AS blackWhiteAmount,
            t1.black_white_actual_amount AS blackWhiteActualAmount,
            t1.color_amount AS colorAmount,
            t1.color_actual_amount AS colorActualAmount,
            t1.settment_status AS settlementStatus
        FROM
            b_customer t
                LEFT JOIN b_iot_print_count_draft t1 ON ( t1.customer_id = t.id )
        WHERE
            t.deleted = 0
          and audit_status = 3
        <![CDATA[ AND DATE_FORMAT( t.created_at, '%Y-%m' ) <= #{month} ]]>
        <![CDATA[ AND DATE_FORMAT( t1.created_at, '%Y-%m' ) = #{month} ]]>
    </select>
</mapper>
