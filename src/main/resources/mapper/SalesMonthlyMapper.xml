<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.SalesMonthlyMapper">


    <select id="getMonthlyOrder" resultType="com.hightop.benyin.statistics.application.vo.MonthlyOrderVO">
        select t.customer_id customerId,sum(t.actual_amount) totalAmount,count(1)
        orderCount,t.order_type,date_format(t.pay_time,'%Y-%m') currMonth
        from tb_trade_order t
        where t.order_status not in('CLOSED','WAIT_AUDIT','WAIT_PAY')
        <if test="null!=qo.customerId ">
            and t.customer_id =#{qo.customerId}
        </if>
        <if test="null!=qo.cycle and qo.cycle!=''">
            and date_format(t.pay_time,'%Y-%m') = #{qo.cycle}
        </if>
        group by t.customer_id,t.order_type,date_format(t.pay_time,'%Y-%m');
    </select>

    <select id="getMonthlyRefund" resultType="com.hightop.benyin.statistics.application.vo.MonthlyOrderVO">
        select tto.customer_id customerId,sum(t.actual_refund_amount) totalAmount,count(1)
        orderCount,date_format(t.updated_at,'%Y-%m') currMonth
        from b_reverse_order t
        left join tb_trade_order tto on t.trade_order_id = tto.id
        where  t.process_status='SUCCESS' and t.actual_refund_amount>0
        <if test="null!=qo.customerId ">
            and tto.customer_id =#{qo.customerId}
        </if>
        <if test="null!=qo.cycle and qo.cycle!=''">
            and date_format(t.updated_at,'%Y-%m') = #{qo.cycle}
        </if>
        group by tto.customer_id,date_format(t.updated_at,'%Y-%m')
    </select>

    <select id="getMonthlyWorkOrder" resultType="com.hightop.benyin.statistics.application.vo.MonthlyWorkOrderVO">
        select customer_id customerId, sum(total_amount) totalAmount,count(1) orderCount,sum(ifnull(item_pay,0)) itemAmount
             ,date_format(send_report_time,'%Y-%m') currMonth,ser_type
        from tb_work_order where  status!='close'
        <if test="null!=qo.customerId ">
            and customer_id =#{qo.customerId}
        </if>
        <if test="null!=qo.cycle and qo.cycle!=''">
            and date_format(send_report_time,'%Y-%m') = #{qo.cycle}
        </if>
        group by customer_id,ser_type,date_format(send_report_time,'%Y-%m')
    </select>

    <select id="getMonthlyDeviceWorkOrder" resultType="com.hightop.benyin.statistics.application.vo.MonthlyWorkOrderVO">
        select t.device_group_id deviceGroupId,
        sum(t.total_amount) totalAmount,
        sum(t.item_pay) currItemAmount,
        count(1) orderCount,
        sum(ifnull(trd.partNum, 0)+ifnull(trds.partNum, 0)) partNum,
        sum(ifnull(trd.itemAmount, 0)) itemAmount,
        sum(ifnull(trds.selfAmount,0)) selfAmount,
        date_format(t.send_report_time, '%Y-%m') currMonth
        from tb_work_order t
        left join tb_replace_order trr on t.id = trr.work_order_id
        left join (select trd.replace_order_id,sum(trd.num) partNum,
        sum(trd.amount) itemAmount
        from tb_replace_detail trd
        join tb_item_store ts on ts.id = trd.item_store_id and ts.sku_source != 'CUSTOMER_REGISTER'
         where trd.deleted=0 group by trd.replace_order_id) trd on trd.replace_order_id = trr.id
        left join (select trd.replace_order_id,sum(trd.num) partNum,
        sum(trd.amount) selfAmount
        from tb_replace_detail trd
        join tb_item_store ts on ts.id = trd.item_store_id and ts.sku_source = 'CUSTOMER_REGISTER'
        where trd.deleted=0
        group by trd.replace_order_id) trds on trds.replace_order_id = trr.id
        where t.status = 'completed' and  trr.deleted=0
        <if test="null!=qo.deviceGroupId ">
            and t.device_group_id =#{qo.deviceGroupId}
        </if>
        <if test="null!=qo.cycle and qo.cycle!=''">
            and date_format(t.send_report_time,'%Y-%m') = #{qo.cycle}
        </if>
        group by t.device_group_id, date_format(t.send_report_time, '%Y-%m')
    </select>
    <select id="getMonthlyDeviceSelfRepair" resultType="com.hightop.benyin.statistics.application.vo.MonthlyWorkOrderVO">
        select t.device_group_id,
        date_format(t.created_at, '%Y-%m') currMonth,
        count(1) orderCount,
        sum(ifnull(trd.partNum, 0)+ifnull(tsrd.partNum, 0))    partNum,
        sum(ifnull(trd.selfAmount, 0)) selfAmount,
        sum(ifnull(trd.itemAmount, 0)) itemAmount
        from tb_self_repair_report t
        left join (select trd.self_repair_report_id,
        sum(trd.num) partNum,
        sum(trd.amount) selfAmount
        from tb_self_repair_report_replace_detail trd
        join tb_item_store ts on ts.id = trd.item_store_id and ts.sku_source = 'CUSTOMER_REGISTER'
        group by trd.self_repair_report_id) trd on trd.self_repair_report_id = t.id
        left join (select trd.self_repair_report_id,
        sum(trd.num) partNum,
        sum(trd.amount) itemAmount
        from tb_self_repair_report_replace_detail trd
        join tb_item_store ts on ts.id = trd.item_store_id and ts.sku_source != 'CUSTOMER_REGISTER'
        group by trd.self_repair_report_id) tsrd on tsrd.self_repair_report_id = t.id
        where 1=1
        <if test="null!=qo.deviceGroupId ">
            and t.device_group_id =#{qo.deviceGroupId}
        </if>
        <if test="null!=qo.cycle and qo.cycle!=''">
            and date_format(t.created_at,'%Y-%m') = #{qo.cycle}
        </if>
        group by t.device_group_id, date_format(t.created_at, '%Y-%m')
    </select>

    <select id="getMonthlySalesList" resultType="com.hightop.benyin.statistics.application.vo.SalesMonthlyVO">
        select curr_month,
        sum(order_num) as order_num,
        sum(order_amount) as order_amount,
        sum(apply_num) as apply_num,
        sum(apply_amount) as apply_amount,
        sum(work_num) as work_num,
        sum(work_part_amount) as work_part_amount,
        sum(work_labor_amount) as work_labor_amount,
        sum(work_part_amount)+sum(work_labor_amount) workTotalAmount,
        sum(pact_work_num) as pact_work_num,
        sum(pact_part_amount) as pact_part_amount,
        sum(pact_labor_amount) as pact_labor_amount,
        sum(pact_part_amount)+sum(pact_labor_amount) pactTotalAmount,
        sum(acture_amount) acture_amount,
        sum(refund_amount) refundAmount,
        sum(receipt_amount) receiptAmount,
        sum(total_amount) total_amount
        from r_sales_monthly where 1=1
        <if test="null!=qo.customerId ">
            and customer_id =#{qo.customerId}
        </if>
        <if test="null != qo.startMonth">
            and curr_month &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and curr_month &lt;= #{qo.endMonth}
        </if>
        <if test="null!=qo.cycle and qo.cycle!=''">
            and curr_month = #{qo.cycle}
        </if>

        <if test="null!=qo.cycles and !qo.cycles.isEmpty()">
            AND curr_month in
            <foreach collection="qo.cycles" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by curr_month order by curr_month desc
    </select>

    <select id="custSaleMonthlyStatistics" resultType="com.hightop.benyin.statistics.application.vo.MonthlySaleStatisticsVO">
select t.* from (
        select t1.seq_id customerSeqId,t1.name customerName,t.curr_month,
               t.order_num orderNum,
               t.order_amount orderAmount,
               t.work_num workNum,
               t.work_part_amount workPartAmount,
               t.work_labor_amount workLaborAmount,
               ifnull(t2.mechineNum,0) mechineNum ,ifnull(t2.blackWhiteCount,0) blackWhiteCount,ifnull(t2.colorCount,0) colorCount,ifnull(t2.totalCount,0) totalCount,
              t.receipt_amount receiptAmount,
        t.receipt_amount+t.order_amount+t.work_part_amount+t.work_labor_amount totalAmount
        from r_sales_monthly t
                 left join b_customer t1 on t1.id = t.customer_id
                 left join (select curr_month,customer_id,count(1) mechineNum,sum(black_white_count) blackWhiteCount,sum(color_count) colorCount,sum(total_count) totalCount
                            from r_month_count group by curr_month,customer_id)  t2 on t2.customer_id=t.customer_id and t2.curr_month=t.curr_month
         where 1=1
        <if test="null != qo.startMonth">
            and t.curr_month &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and t.curr_month &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.customerSeq and '' != qo.customerSeq">
            and  t1.seq_id like concat ('%',#{qo.customerSeq},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName">
            and  t1.name like concat ('%',#{qo.customerName},'%')
        </if>
              ) t
           where 1=1

        <if test="null != qo.beginTotalAmount and '' != qo.beginTotalAmount ">
            and t.totalAmount &gt;= #{qo.beginTotalAmount}
        </if>
        <if test="null != qo.endTotalAmount and '' != qo.endTotalAmount ">
            and t.totalAmount &lt;= #{qo.endTotalAmount}
        </if>

        <if test="null != qo.beginOrderAmount and '' != qo.beginOrderAmount ">
            and t.orderAmount &gt;= #{qo.beginOrderAmount}
        </if>
        <if test="null != qo.endOrderAmount and '' != qo.endOrderAmount ">
            and t.orderAmount &lt;= #{qo.endOrderAmount}
        </if>

        <if test="null != qo.beginRepairAmount and '' != qo.beginRepairAmount ">
            and t.workPartAmount &gt;= #{qo.beginRepairAmount}
        </if>
        <if test="null != qo.endRepairAmount and '' != qo.endRepairAmount ">
            and t.workPartAmount &lt;= #{qo.endRepairAmount}
        </if>

        <if test="null != qo.beginReceiptAmount and '' != qo.beginReceiptAmount ">
            and t.receiptAmount &gt;= #{qo.beginReceiptAmount}
        </if>
        <if test="null != qo.endReceiptAmount and '' != qo.endReceiptAmount ">
            and t.receiptAmount &lt;= #{qo.endReceiptAmount}
        </if>

        <if test="null != qo.beginOrderNum and '' != qo.beginOrderNum ">
            and t.orderNum &gt;= #{qo.beginOrderNum}
        </if>
        <if test="null != qo.endOrderNum and '' != qo.endOrderNum ">
            and t.orderNum &lt;= #{qo.endOrderNum}
        </if>

        <if test="null != qo.beginWorkNum and '' != qo.beginWorkNum ">
            and t.workNum &gt;= #{qo.beginWorkNum}
        </if>
        <if test="null != qo.endWorkNum and '' != qo.endWorkNum ">
            and t.workNum &lt;= #{qo.endWorkNum}
        </if>


        <if test="null != qo.beginBlackWhiteCount and '' != qo.beginBlackWhiteCount ">
            and t.blackWhiteCount &gt;= #{qo.beginBlackWhiteCount}
        </if>
        <if test="null != qo.endBlackWhiteCount and '' != qo.endBlackWhiteCount ">
            and t.blackWhiteCount &lt;= #{qo.endBlackWhiteCount}
        </if>
        <if test="null != qo.beginColorCount and '' != qo.beginColorCount ">
            and t.colorCount &gt;= #{qo.beginColorCount}
        </if>
        <if test="null != qo.endColorCount and '' != qo.endColorCount ">
            and t.colorCount &lt;= #{qo.endColorCount}
        </if>
        <if test="null != qo.beginTotalCount and '' != qo.beginTotalCount ">
            and t.totalCount &gt;= #{qo.beginTotalCount}
        </if>
        <if test="null != qo.endTotalCount and '' != qo.endTotalCount ">
            and t.totalCount &lt;= #{qo.endTotalCount}
        </if>

        <if test="null != qo.beginMechineNum and '' != qo.beginMechineNum ">
            and t.mechineNum &gt;= #{qo.beginMechineNum}
        </if>
        <if test="null != qo.endMechineNum and '' != qo.endMechineNum ">
            and t.mechineNum &lt;= #{qo.endMechineNum}
        </if>


order by t.totalAmount desc

    </select>

    <select id="customerSaleChangeList" resultType="com.alibaba.fastjson.JSONObject">
        select t.seq_id customerSeqId,
            <foreach collection="qo.yearMonths" item="mo" separator=" , ">
                      (select sum(acture_amount)/100
                      from r_sales_monthly where customer_id=t.id and curr_month=#{mo.title}) as '${mo.dataIndex}'
              </foreach>
        ,t.name customerName
        from  b_customer  t
        where t.deleted=0
        <if test="null != qo.customerSeqId and '' != qo.customerSeqId">
            and  t.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName">
            and  t.name like concat ('%',#{qo.customerName},'%')
        </if>

    </select>

    <select id="monthlySalesStatistics" resultType="com.hightop.benyin.statistics.application.vo.MonthlyStatisVO">
        select *
        from (
        select t.currMonth, ifnull(t1.mechineAmount,0) mechineAmount, ifnull(t2.orderAmount,0) orderAmount, ifnull(t3.itemAmount,0) workPartAmount,
        ifnull(t3.laborAmount,0) workLaborAmount,0 monthlyAmount
        ,ifnull(t5.inclusiveAmount,0) receiptAmount,ifnull(t6.refundAmount,0) refundAmount,
        ifnull(t1.mechineAmount,0)+ifnull(t2.orderAmount,0)+ifnull(t3.itemAmount,0)+ifnull(t3.laborAmount,0)+ifnull(t5.inclusiveAmount,0) totalAmount,
        ifnull(t1.mechineAmount,0)+ifnull(t2.orderAmount,0)+ifnull(t3.itemAmount,0)+ifnull(t3.laborAmount,0)+ifnull(t5.inclusiveAmount,0)-ifnull(t6.refundAmount,0) incomeAmount
        from (select date_format(created_at, '%Y-%m') currMonth
        from b_customer
        group by date_format(created_at, '%Y-%m')) t
        left join (select date_format(t1.created_at, '%Y-%m') currMonth, sum(t.actual_pay_amount) mechineAmount
        from tb_trade_order_detail t
        left join tb_trade_order t1 on t.trade_order_id = t1.id
        left join b_storage_article bsa on t.article_code = bsa.code
        where t1.order_status != 'closed' and order_type = 'SALE'
        and bsa.product_id is not null
        group by date_format(t1.created_at, '%Y-%m')) t1 on t1.currMonth = t.currMonth
        left join (select date_format(t1.created_at, '%Y-%m') currMonth, sum(t.actual_pay_amount) orderAmount
        from tb_trade_order_detail t
        left join tb_trade_order t1 on t.trade_order_id = t1.id
        left join b_storage_article bsa on t.article_code = bsa.code
        where t1.order_status != 'closed' and t1.order_type = 'SALE'
        and bsa.product_id is null
        group by date_format(t1.created_at, '%Y-%m')) t2 on t2.currMonth = t.currMonth
        left join (select date_format(t.send_report_time, '%Y-%m') currMonth
        , sum(t.item_pay)                    itemAmount
        , sum(t.total_amount - t.item_pay)      laborAmount
        from tb_work_order t
        where t.status != 'close'
        and t.ser_type not in ('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF')
        group by date_format(t.send_report_time, '%Y-%m')) t3 on t3.currMonth = t.currMonth
        left join (select cycle currMonth,sum(amount) inclusiveAmount
        from b_iot_print_count  group by cycle) t5 on t5.currMonth = t.currMonth
        left join (     select sum(t.actual_refund_amount) refundAmount,count(1)
        orderCount,date_format(t.updated_at,'%Y-%m') currMonth
        from b_reverse_order t where  t.process_status='SUCCESS' and t.actual_refund_amount>0
        group by date_format(t.updated_at,'%Y-%m')) t6 on t6.currMonth =t.currMonth
        ) t
        where 1=1
        <if test="null != qo.startMonth">
            and t.currMonth &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and t.currMonth &lt;= #{qo.endMonth}
        </if>

        <if test="null != qo.beginTotalAmount">
            and t.totalAmount &gt;= #{qo.beginTotalAmount}
        </if>
        <if test="null != qo.endTotalAmount">
            and t.totalAmount &lt;= #{qo.endTotalAmount}
        </if>

        <if test="null != qo.beginMechineAmount">
            and t.mechineAmount &gt;= #{qo.beginMechineAmount}
        </if>
        <if test="null != qo.endMechineAmount">
            and t.mechineAmount &lt;= #{qo.endMechineAmount}
        </if>

        <if test="null != qo.beginOrderAmount">
            and t.orderAmount &gt;= #{qo.beginOrderAmount}
        </if>
        <if test="null != qo.endOrderAmount">
            and t.orderAmount &lt;= #{qo.endOrderAmount}
        </if>

        <if test="null != qo.beginPartAmount">
            and t.workPartAmount &gt;= #{qo.beginPartAmount}
        </if>
        <if test="null != qo.endPartAmount">
            and t.workPartAmount &lt;= #{qo.endPartAmount}
        </if>

        <if test="null != qo.beginLaborAmount">
            and t.workLaborAmount &gt;= #{qo.beginLaborAmount}
        </if>
        <if test="null != qo.endLaborAmount">
            and t.workLaborAmount &lt;= #{qo.endLaborAmount}
        </if>

        <if test="null != qo.beginMonthAmount">
            and t.monthlyAmount &gt;= #{qo.beginMonthAmount}
        </if>
        <if test="null != qo.endMonthAmount">
            and t.monthlyAmount &lt;= #{qo.endMonthAmount}
        </if>

        <if test="null != qo.beginReceiptAmount">
            and t.receiptAmount &gt;= #{qo.beginReceiptAmount}
        </if>
        <if test="null != qo.endReceiptAmount">
            and t.receiptAmount &lt;= #{qo.endReceiptAmount}
        </if>

        order by t.currMonth desc
    </select>
    <select id="monthlySalesStatisticsSummary" resultType="com.hightop.benyin.statistics.application.vo.MonthlyStatisVO">
        select sum(mechineAmount) mechineAmount,sum(orderAmount) orderAmount,sum(workPartAmount) workPartAmount,sum(workLaborAmount) workLaborAmount,sum(monthlyAmount) monthlyAmount,sum(receiptAmount) receiptAmount,sum(totalAmount) totalAmount
        from (
        select t.currMonth, ifnull(t1.mechineAmount,0) mechineAmount, ifnull(t2.orderAmount,0) orderAmount, ifnull(t3.itemAmount,0) workPartAmount,
        ifnull(t3.laborAmount,0) workLaborAmount,0 monthlyAmount
        ,ifnull(t5.inclusiveAmount,0) receiptAmount,
        ifnull(t1.mechineAmount,0)+ifnull(t2.orderAmount,0)+ifnull(t3.itemAmount,0)+ifnull(t3.laborAmount,0)+ifnull(t5.inclusiveAmount,0) totalAmount
        from (select date_format(created_at, '%Y-%m') currMonth
        from b_customer
        group by date_format(created_at, '%Y-%m')) t
        left join (select date_format(t1.created_at, '%Y-%m') currMonth, sum(t.actual_pay_amount) mechineAmount
        from tb_trade_order_detail t
        left join tb_trade_order t1 on t.trade_order_id = t1.id
        left join b_storage_article bsa on t.article_code = bsa.code
        where t1.order_status != 'closed'
        and bsa.product_id is not null
        group by date_format(t1.created_at, '%Y-%m')) t1 on t1.currMonth = t.currMonth
        left join (select date_format(t1.created_at, '%Y-%m') currMonth, sum(t.actual_pay_amount) orderAmount
        from tb_trade_order_detail t
        left join tb_trade_order t1 on t.trade_order_id = t1.id
        left join b_storage_article bsa on t.article_code = bsa.code
        where t1.order_status != 'closed'
        and bsa.product_id is null
        group by date_format(t1.created_at, '%Y-%m')) t2 on t2.currMonth = t.currMonth
        left join (select date_format(t.send_report_time, '%Y-%m') currMonth
        , sum(t.item_pay)                    itemAmount
        , sum(t.total_amount - t.item_pay)      laborAmount
        from tb_work_order t
        where t.status != 'close'
        and t.ser_type not in ('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF')
        group by date_format(t.send_report_time, '%Y-%m')) t3 on t3.currMonth = t.currMonth
        left join (select date_format(created_at, '%Y-%m') currMonth,sum(total_amount) inclusiveAmount,type
        from b_iot_print_receipt where type='ALL_INCLUSIVE' group by date_format(created_at, '%Y-%m'),type) t5 on t5.currMonth = t.currMonth
        ) t
        where 1=1
        <if test="null != qo.startMonth">
            and t.currMonth &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and t.currMonth &lt;= #{qo.endMonth}
        </if>

        <if test="null != qo.beginTotalAmount">
            and t.totalAmount &gt;= #{qo.beginTotalAmount}
        </if>
        <if test="null != qo.endTotalAmount">
            and t.totalAmount &lt;= #{qo.endTotalAmount}
        </if>

        <if test="null != qo.beginMechineAmount">
            and t.mechineAmount &gt;= #{qo.beginMechineAmount}
        </if>
        <if test="null != qo.endMechineAmount">
            and t.mechineAmount &lt;= #{qo.endMechineAmount}
        </if>

        <if test="null != qo.beginOrderAmount">
            and t.orderAmount &gt;= #{qo.beginOrderAmount}
        </if>
        <if test="null != qo.endOrderAmount">
            and t.orderAmount &lt;= #{qo.endOrderAmount}
        </if>

        <if test="null != qo.beginPartAmount">
            and t.workPartAmount &gt;= #{qo.beginPartAmount}
        </if>
        <if test="null != qo.endPartAmount">
            and t.workPartAmount &lt;= #{qo.endPartAmount}
        </if>

        <if test="null != qo.beginLaborAmount">
            and t.workLaborAmount &gt;= #{qo.beginLaborAmount}
        </if>
        <if test="null != qo.endLaborAmount">
            and t.workLaborAmount &lt;= #{qo.endLaborAmount}
        </if>

        <if test="null != qo.beginMonthAmount">
            and t.monthlyAmount &gt;= #{qo.beginMonthAmount}
        </if>
        <if test="null != qo.endMonthAmount">
            and t.monthlyAmount &lt;= #{qo.endMonthAmount}
        </if>

        <if test="null != qo.beginReceiptAmount">
            and t.receiptAmount &gt;= #{qo.beginReceiptAmount}
        </if>
        <if test="null != qo.endReceiptAmount">
            and t.receiptAmount &lt;= #{qo.endReceiptAmount}
        </if>

        order by t.currMonth desc
    </select>
</mapper>
