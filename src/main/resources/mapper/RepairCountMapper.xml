<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.RepairCountMapper">



    <select id="getAllRepairInfo" resultType="com.hightop.benyin.statistics.infrastructure.entity.RepairCount">
        select t.*,t1.ser_type ,t1.device_group deviceGroup,t3.name brand,t2.name machine,t1.product_id productId,t1.reg_cli_state
        from (
        select code workCode, device_group_id deviceGroupId, customer_id customerId, black_white_count
        blackWhite,color_count color, created_at,'SELF' dataSource
        from tb_self_repair_report where (black_white_count > 0 or color_count > 0)
        <if test="null != qo.deviceGroupId ">
            and device_group_id = #{qo.deviceGroupId}
        </if>
        <if test="null != qo.reportTimeStart">
            and created_at &gt;= #{qo.reportTimeStart}
        </if>
        <if test="null != qo.reportTimeEnd">
            and created_at &lt;= #{qo.reportTimeEnd}
        </if>
        <if test="null != qo.workCode and qo.workCode!= ''">
            and code = #{qo.workCode}
        </if>
        <if test="null != qo.workId ">
            and id = #{qo.workId}
        </if>
        <if test="null != qo.customerId ">
            and customer_id = #{qo.customerId}
        </if>
        union all
        select work_order_code workCode, device_group_id deviceGroupId, customer_id customerId, black_white_count
        blackWhite,color_count color, created_at,'WORK' dataSource
        from tb_repair_report where (black_white_count > 0 or color_count > 0)
        <if test="null != qo.deviceGroupId ">
            and device_group_id = #{qo.deviceGroupId}
        </if>
        <if test="null != qo.reportTimeStart">
            and created_at &gt;= #{qo.reportTimeStart}
        </if>
        <if test="null != qo.reportTimeEnd">
            and created_at &lt;= #{qo.reportTimeEnd}
        </if>
        <if test="null != qo.workId ">
            and work_order_id = #{qo.workId}
        </if>
        <if test="null != qo.workCode and qo.workCode!= ''">
            and work_order_code = #{qo.workCode}
        </if>
        <if test="null != qo.customerId ">
            and customer_id = #{qo.customerId}
        </if>
        ) t
        left join b_customer_device_group t1 on t1.id = t.deviceGroupId
        left join b_product_tree t2 on t2.id = t1.product_id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        order by deviceGroupId,created_at asc
    </select>

</mapper>
