<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.storage.infrastructure.mapper.InvoiceMapper">

    <select id="pageList" resultType="com.hightop.benyin.storage.infrastructure.entity.Invoice">
        select t1.* from b_storage_invoice t1
        where t1.deleted=false
        <if test="null != qo.invoiceCode and '' != qo.invoiceCode ">
            and t1.invoice_Code like concat ('%',#{qo.invoiceCode},'%')
        </if>
        <if test="null != qo.outboundOrderNumber and '' != qo.outboundOrderNumber ">
            and t1.outbound_order_codes like concat ('%',#{qo.outboundOrderNumber},'%')
        </if>
        <if test="null != qo.logisticsWaybillNumber and '' != qo.logisticsWaybillNumber ">
            and t1.logistics_waybill_number like concat ('%',#{qo.logisticsWaybillNumber},'%')
        </if>
        <if test="null != qo.logisticsType and '' != qo.logisticsType ">
            and t1.logistics_provider = #{qo.logisticsType}
        </if>
        <if test="null != qo.status and '' != qo.status ">
            and t1.status = #{qo.status}
        </if>
        <if test="null != qo.consigneeName and '' != qo.consigneeName ">
            and json_extract(t1.receiver ,'$.name') like concat ('%',#{qo.consigneeName},'%')
        </if>
        <if test="null != qo.consigneePhone and '' != qo.consigneePhone ">
            and json_extract(t1.receiver ,'$.mobile') like concat ('%',#{qo.consigneePhone},'%')
        </if>
        <if test="null != qo.consigneeCompany and '' != qo.consigneeCompany ">
            and json_extract(t1.receiver ,'$.company') like concat ('%',#{qo.consigneeCompany},'%')
        </if>
        order by t1.created_at desc
    </select>

</mapper>