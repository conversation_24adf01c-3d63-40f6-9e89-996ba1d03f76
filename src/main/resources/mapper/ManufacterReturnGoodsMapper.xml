<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.purchase.infrastructure.mapper.ManufacterReturnGoodsMapper">


    <select id="mechinePage" resultType="com.hightop.benyin.purchase.api.dto.PurchaseMechineVo">
        select ifnull(t.series,'无') series, sum(t.number) number,sum(t.amount) amount, sum(t.refundNum) refundNum, sum(t.refundAmount) refundAmount
        from (
        select distinct t.id,t4.name series, tt.number,tt.amount, t.number refundNum, t.amount refundAmount
        from b_manufacturer_return_goods t
        left join b_manufacturer_order_goods tt on tt.id = t.manufacturer_order_goods_id
        left join b_manufacturer_return tr on tr.code = t.manufacturer_return_code
        left join b_storage_article t1 on t1.code= t.article_code
        left join b_product_part_bom t2 on t2.part_id=t1.part_id
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = t3.parent_id
        where tr.refund_status !='CLOSED'

        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and  t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and  t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND  t2.product_id in
            <foreach collection="qo.productIds" item="id" separator=" , " open="(" close=")">
                #{id}
            </foreach>
        </if>
        ) t
        group by t.series
    </select>

    <select id="supplierPage" resultType="com.hightop.benyin.purchase.api.dto.ManufacterReturnGoodsVo">
        SELECT
              bm.name AS manufacturerName,
              bm.code AS manufacturerCode,
               sum(t2.number) number,
               sum(t.number) refundNum,
               sum(t2.amount) amount,
               sum(t.amount) refundAmount
        FROM b_manufacturer_return_goods t
             join b_manufacturer_return t1 on t.manufacturer_return_code = t1.code
         join b_manufacturer bm on t1.manufacturer_id = bm.id
        join b_manufacturer_order_goods t2 on t2.id = t.manufacturer_order_goods_id
        WHERE t1.refund_status !='CLOSED'
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and  bm.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and  bm.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        group by   bm.name,bm.code
        ORDER BY bm.code DESC
</select>

    <select id="articlePage" resultType="com.hightop.benyin.purchase.api.dto.ManufacterReturnGoodsVo">
        SELECT
        sum(t4.number) number,
        sum(t4.sum_price) amount,
        sum( t.number) refundNum,
        sum( t.amount) refundAmount,
        t3.name AS manufacturerName,
        t.article_code,
        t1.part_id,
        t1.number_oem AS oemNumber,
        t1.name AS articleName,
        t1.unit ,
        t1.type ,
        t1.manufacturer_channel AS manufacturerChannel
        FROM b_manufacturer_return_goods t
        LEFT JOIN b_storage_article t1 ON (t1.code = t.article_code)
        LEFT JOIN b_manufacturer_return t2 ON (t2.code = t.manufacturer_return_code)
        LEFT JOIN b_manufacturer t3 ON (t3.id = t2.manufacturer_id)
        LEFT JOIN b_purchase_order_goods t4 ON (t4.id = t.purchase_order_goods_id)
          where  t2.refund_status !='CLOSED'
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and  t1.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and  t3.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and  t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        group by
        t3.name,
            t.article_code,
        t1.part_id,
        t1.number_oem ,
        t1.name,
        t1.unit ,
        t1.type ,
        t1.manufacturer_channel
        ORDER BY t.article_code DESC
</select>

</mapper>
