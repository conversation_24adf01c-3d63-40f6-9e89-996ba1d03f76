<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.activity.infrastructure.mapper.ActivitySkuMapper">

    <select id="getSkuPromotion" resultType="com.hightop.benyin.activity.infrastructure.entity.ActivitySku">
        SELECT t.*,t1.activity_type
        FROM tb_activity_sku t
        left join tb_activity t1 on t.activity_id = t1.id
        WHERE t1.status='IN_PROGRESS'

        <if test="null != regionCode ">
            and (JSON_LENGTH(t1.activity_range) = 0 or JSON_CONTAINS(t1.activity_range,'"${regionCode}"'))
        </if>

        <if test="null != skuIds and !skuIds.isEmpty()">
            and t.sku_id in
            <foreach collection="skuIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    and now() BETWEEN t1.start_time AND t1.end_time
        order by t.created_at desc
    </select>

    <select id="getSkuActivity" resultType="com.hightop.benyin.activity.infrastructure.entity.ActivitySku">
        SELECT t.*,t1.activity_type
        FROM tb_activity_sku t
        left join tb_activity t1 on t.activity_id = t1.id
        WHERE t1.status='IN_PROGRESS'
        <if test="null != skuIds and !skuIds.isEmpty()">
            and t.sku_id in
            <foreach collection="skuIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    and now() BETWEEN t1.start_time AND t1.end_time
        order by t.created_at desc
    </select>

</mapper>