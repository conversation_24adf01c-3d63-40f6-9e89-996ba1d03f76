<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.iot.infrastructure.mapper.IotPrintReceiptMapper">



    <select id="getMonthReceiptList" resultType="com.hightop.benyin.iot.application.vo.MonthReceiptVo">
        select cycle,sum(amount) totalPay,sum(total_point) totalCount,
               sum(black_white_point) blackPrintCount,round(sum(black_white_amount),2) blackPrintAmount,count(1) mechineCount,
               round(avg(black_white_point),0) avgBlackPrintCount,round(avg(black_white_amount),2) avgBlackPrintAmount,
               sum(color_point) colorPrintCount,round(sum(color_amount),2) colorPrintAmount,round(avg(color_point),0) avgColorPrintCount,round(avg(color_amount),2) avgColorPrintAmount
        from b_iot_print_count
        where 1=1
        <if test="null!=qo.serTypes and !qo.serTypes.isEmpty()">
            AND ser_type in
            <foreach collection="qo.serTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.startMonth">
            and cycle  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and cycle  &lt;= #{qo.endMonth}
        </if>
        group by cycle
    </select>

    <select id="getMonthReceiptSummary" resultType="com.hightop.benyin.iot.application.vo.MonthReceiptVo">
        select sum(amount) totalPay,sum(total_point) totalCount,
               sum(black_white_point) blackPrintCount,round(sum(black_white_amount),2) blackPrintAmount,count(1) mechineCount,
               sum(color_point) colorPrintCount,round(sum(color_amount),2) colorPrintAmount
        from b_iot_print_count
        where 1=1
        <if test="null!=qo.serTypes and !qo.serTypes.isEmpty()">
            AND ser_type in
            <foreach collection="qo.serTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.startMonth">
            and cycle  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and cycle  &lt;= #{qo.endMonth}
        </if>
    </select>

</mapper>
