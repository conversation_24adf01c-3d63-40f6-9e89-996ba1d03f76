<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.MonthCountMapper">

    <update id="insertMonthCount">
        insert into r_month_count (customer_id, device_group_id,product_id, device_group, brand, machine, treaty_type, reg_cli_state,curr_month,
                                   black_white_count, color_count, total_count, created_at)
        select customer_id, device_group_id,product_id, device_group, brand, machine, treaty_type, reg_cli_state,curr_month
             ,sum(black_white_count) ,sum(color_count) ,sum(total_count) ,now()
        from r_day_count where 1=1
        <if test="null != qo.deviceGroupId ">
            and device_group_id = #{qo.deviceGroupId}
        </if>
        <if test="null != qo.customerId ">
            and customer_id = #{qo.customerId}
        </if>
        <if test="null != qo.beginDate and qo.beginDate!= ''">
            and curr_month &gt;= #{qo.beginDate}
        </if>
        <if test="null != qo.endDate and qo.endDate!= ''">
            and curr_month &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.cycle and qo.cycle!= ''">
            and curr_month = #{qo.cycle}
        </if>
        group by customer_id,
        device_group_id,
        product_id,
        device_group,
        brand,
        machine,
        treaty_type,
        reg_cli_state,
        curr_month
    </update>
    <select id="customerServeDistribution" resultType="com.hightop.benyin.share.application.vo.SearchDataVo">
        select t2.name city,t2.code cityCode,count(1) quantity
        from b_customer t
                 left join b_region t1 on t1.code=t.region_code
                 left join b_region t2 on t2.code=t1.parent_code
        where region_code is not null and t.deleted=0
          and exists(select id from b_customer_device_group where ser_type in('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF') and customer_id=t.id)
        AND t2.code in
        <foreach collection="cityCodes" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        group by t2.name,t2.code order by count(1) desc limit 8
    </select>
</mapper>
