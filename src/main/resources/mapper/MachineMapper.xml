<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.machine.infrastructure.mapper.MachineMapper">

    <select id="getFinanceMachineOut" resultType="com.hightop.benyin.statistics.application.vo.FinanceMachineOutVO">

        select *
        from (
        select t.*,
        round(t.amount / 1.13, 2) noTaxAmount,
        t.amount - round((t.amount / 1.13), 2) taxAmount
        from (select distinct date_format(t.time, '%Y-%m-%d') createDate,
        t.source_code code,
        2 type,
        t2.seq_id customerCode,
        t2.name customerName,
        t3.name brand,
        t5.product_name productName,
        t4.host_type,
        t4.device_on,
        t5.product_id,
        t4.machine_num,
        1 num,
        t7.license,
        round(ifnull(t5.purchase_price,0) / 100, 2) price,
        round(ifnull(t5.purchase_price,0) / 100, 2) amount,
        case when t6.tax is null then 1.13 else 1 + t6.tax / 100 end taxRate,
        ifnull(t6.tax, 13) tax
        from b_machine_inout_flow t
        left join b_customer_contract t1 on t1.code = t.source_code
        left join b_customer t2 on t2.id = t1.customer_id
        left join b_customer_contract_serve t4 on t4.contract_code = t.source_code
        left join b_machine t5 on t5.machine_num = t4.machine_num
        left join b_manufacturer t6 on t6.id = t5.manufacturer_id
        left join b_product_tree tp on tp.id = t5.product_id and t5.host_type='2008'
        left join b_product_tree t3 on t3.id = substr(tp.full_id_path, 2, 20)
        left join b_customer_business t7 on t7.customer_id = t2.id
        where t.source_type ='rent_mall' and t1.contract_type='1265' AND t1.`status` != 'CANCEL') t
        union all
        select t.*,
        round(t.amount / 1.13, 2) noTaxAmount,
        t.amount - round((t.amount / 1.13), 2) taxAmount
        from (select distinct date_format(t.time, '%Y-%m-%d') createDate,
        t.source_code code,
        1 type,
        t2.seq_id customerCode,
        t2.name customerName,
        t3.name brand,
        t5.product_name productName,
        t4.host_type,
        t4.device_on,
        t5.product_id,
        t4.machine_num,
        1 num,
        t7.license,
        round(ifnull(t5.purchase_price,0) / 100, 2) price,
        round(ifnull(t5.purchase_price,0) / 100, 2) amount,
        case when t6.tax is null then 1.13 else 1 + t6.tax / 100 end taxRate,
        ifnull(t6.tax, 13) tax
        from b_machine_inout_flow t
        left join b_customer_contract t1 on t1.code = t.source_code
        left join b_customer t2 on t2.id = t1.customer_id
        left join b_customer_contract_buy t4 on t4.contract_code = t.source_code
        left join b_machine t5 on t5.machine_num = t4.machine_num
        left join b_manufacturer t6 on t6.id = t5.manufacturer_id
        left join b_product_tree tp on tp.id = t5.product_id and t5.host_type='2008'
        left join b_product_tree t3 on t3.id = substr(tp.full_id_path, 2, 20)
        left join b_customer_business t7 on t7.customer_id = t2.id
        where t.source_type ='shopping_mall' and t1.contract_type='1201' AND t1.`status` != 'CANCEL') t
        ) t
        where 1=1
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createDate &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createDate &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type = #{qo.type}
        </if>
        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null != qo.hostType and '' != qo.hostType ">
            and t.host_type = #{qo.hostType}
        </if>

        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and t.customerName like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t.customerCode like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t.license like concat ('%',#{qo.license},'%')
        </if>
        order by createDate desc
    </select>

    <select id="getFinanceMachineInstore" resultType="com.hightop.benyin.statistics.application.vo.FinanceMachineInstoreVO">
        select distinct t.*
        from (
        select t.*,
        round(t.amount / 1.13, 2)              noTaxAmount,
        t.amount - round((t.amount / 1.13), 2) taxAmount
        from (select distinct t.id,date_format(t.time, '%Y-%m-%d')                        createDate,
        t.source_code code,
        3 type,
        t2.seq_id                                                      manufacturerCode,
        t2.name                                                      manufacturerName,
        t3.name brand,
        t5.product_name                                                      productName,
        t5.host_type,
        t5.device_on,
        t5.product_id,
        t.machine_num,
        1 num,
        t7.license,
        round(ifnull(t.price,0) / 100, 2)                                       price,
        round(ifnull(t.price,0) / 100, 2)                                    amount,
        case when t6.tax is null then 1.13 else 1 + t6.tax / 100 end taxRate,
        ifnull(t6.tax, 13)                                           tax
        from b_machine_inout_flow t
        left join b_customer_contract t1 on t1.code = t.source_code
        left join b_customer t2 on t2.id = t1.customer_id
        left join b_machine t5 on t5.machine_num = t.machine_num
        left join b_manufacturer t6 on t6.id = t5.manufacturer_id
        left join b_product_tree tp on tp.id = t5.product_id and t5.host_type='2008'
        left join b_product_tree t3 on t3.id = substr(tp.full_id_path, 2, 20)
        left join b_customer_business t7 on t7.customer_id = t2.id
        where t.source_type ='rent_return' and t1.contract_type='1265' and t5.status!='WAIT_IN') t
        union all
        select distinct t.*,
        round(t.amount / 1.13, 2)              noTaxAmount,
        t.amount - round((t.amount / 1.13), 2) taxAmount
        from (select distinct t.id,date_format(t.created_at, '%Y-%m-%d')                        createDate,
        t.source_code code,
        2  type,
        t2.seq_id                                                      manufacturerCode,
        t2.name                                                      manufacturerName,
        t3.name brand,
        t5.product_name                                                      productName,
        t5.host_type,
        t5.device_on,
        t5.product_id,
        t.machine_num,
        1 num,
        t7.license,
        round(ifnull(t.price,0) / 100, 2)                                    price,
        round(ifnull(t.price,0) / 100, 2)                                    amount,
        case when t6.tax is null then 1.13 else 1 + t6.tax / 100 end taxRate,
        ifnull(t6.tax, 13)                                           tax
        from b_machine_inout_flow t
        left join b_customer_contract t1 on t1.code = t.source_code
        left join b_customer t2 on t2.id = t1.customer_id
        left join b_machine t5 on t5.machine_num = t.machine_num
        left join b_manufacturer t6 on t6.id = t5.manufacturer_id
        left join b_product_tree tp on tp.id = t5.product_id and t5.host_type='2008'
        left join b_product_tree t3 on t3.id = substr(tp.full_id_path, 2, 20)
        left join b_customer_business t7 on t7.customer_id = t2.id
        where t.source_type ='buy_return' and t1.contract_type='1201' and t5.status!='WAIT_IN') t
        union all

        select distinct t.*, round(t.amount / t.taxRate, 2) noTaxAmount, t.amount - round((t.amount / t.taxRate), 2) taxAmount
        from (select distinct t.id,date_format(t.created_at, '%Y-%m-%d')                        createDate,
        t.source_code code,
        1  type,
        t6.code                                                      manufacturerCode,
        t6.name                                                      manufacturerName,
        t3.name brand,
        t5.product_name                                                      productName,
        t5.host_type,
        t5.device_on,
        t5.product_id,
        t5.machine_num,
        1 num,
        '' license,
        round(ifnull(t.price,0) / 100, 2)                                    price,
        round(ifnull(t.price,0) / 100, 2)                                    amount,
        case when t6.tax is null then 1.13 else 1 + t6.tax / 100 end taxRate,
        ifnull(t6.tax, 13)                                           tax
        from b_machine_inout_flow t
        left join b_machine_purchase t1 on t1.purchase_code=t.source_code
        left join b_machine t5 on t5.machine_num = t.machine_num
        left join b_manufacturer t6 on t6.id = t1.manufacturer_id
        left join b_product_tree tp on tp.id = t5.product_id and t5.host_type='2008'
        left join b_product_tree t3 on t3.id = substr(tp.full_id_path, 2, 20)
        where t.source_type ='purchase' ) t) t

        where 1=1

        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createDate &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createDate &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type = #{qo.type}
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null != qo.hostType and '' != qo.hostType ">
            and t.hostType = #{qo.hostType}
        </if>

        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t.manufacturerCode like concat ('%',#{qo.manufacturerCode},'%')
        </if>

        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>

        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t.manufacturerName like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t.license like concat ('%',#{qo.license},'%')
        </if>
        order by t.createDate desc
    </select>

</mapper>