<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.reverse.infrastructure.mapper.ReverseOrderDetailMapper">

    <select id="getReverseArticleStatisList" resultType="com.hightop.benyin.reverse.application.vo.ReverseStatisVo">
        select date_format(t1.created_at, '%Y-%m')                                   currMonth,
        t.article_code,
        t2.name articleName,
        t2.number_oem oemNumber,
        t2.type,
        t2.manufacturer_channel,
        sum(t.item_num) itemNum,
        sum(t.pay_amount) payAmount,
        sum(t.actual_pay_amount) actualPayAmount,
        sum(t.reverse_item_num) reverseItemNum,
        sum(t.refund_amount) refundAmount
        from tb_reverse_order_detail  t
        left join b_reverse_order t1 on t1.id = t.reverse_order_id
        left join b_storage_article  t2 on t2.code = t.article_code
        where t1.process_status not in('REJECT','CLOSED')
        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and DATE_FORMAT(t1.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth and '' != qo.endMonth ">
            and DATE_FORMAT(t1.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and t2.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and t2.number_oem like CONCAT( #{qo.numberOem},'%')
        </if>
        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and t2.manufacturer_channel  = #{qo.manufacturerChannel}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t2.type  = #{qo.type}
        </if>
        group by
        date_format(t1.created_at, '%Y-%m'),
        t.article_code,
        t2.name ,
        t2.number_oem,
        t2.type,
        t2.manufacturer_channel
        order by date_format(t1.created_at, '%Y-%m') desc
    </select>
 <select id="getReverseArticleStatisSummary" resultType="com.hightop.benyin.reverse.application.vo.ReverseStatisVo">
        select
        sum(t.item_num) itemNum,
        sum(t.pay_amount) payAmount,
        sum(t.actual_pay_amount) actualPayAmount,
        sum(t.reverse_item_num) reverseItemNum,
        sum(t.refund_amount) refundAmount
        from tb_reverse_order_detail  t
        left join b_reverse_order t1 on t1.id = t.reverse_order_id
        left join b_storage_article  t2 on t2.code = t.article_code
        where t1.process_status not in('REJECT','CLOSED')
        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and DATE_FORMAT(t1.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth and '' != qo.endMonth ">
            and DATE_FORMAT(t1.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and t2.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and t2.number_oem like CONCAT( #{qo.numberOem},'%')
        </if>
        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and t2.manufacturer_channel  = #{qo.manufacturerChannel}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t2.type  = #{qo.type}
        </if>
    </select>
    <select id="getReverseManufacturerStatisList" resultType="com.hightop.benyin.reverse.application.vo.ReverseStatisVo">
        select date_format(t1.created_at, '%Y-%m')                                   currMonth,
        t3.code manufacturerCode,
        t3.name manufacturerName,
        sum(t.item_num) itemNum,
        sum(t.pay_amount) payAmount,
        sum(t.actual_pay_amount) actualPayAmount,
        sum(t.reverse_item_num) reverseItemNum,
        sum(t.refund_amount) refundAmount
        from tb_reverse_order_detail  t
        left join b_reverse_order t1 on t1.id = t.reverse_order_id
        left join b_storage_article  t2 on t2.code = t.article_code
        left join b_manufacturer t3 on t3.id = t2.manufacturer_id
        where t1.process_status not in('REJECT','CLOSED')

        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and DATE_FORMAT(t1.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth and '' != qo.endMonth ">
            and DATE_FORMAT(t1.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t3.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t3.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        group by
        date_format(t1.created_at, '%Y-%m'),
        t3.code,
        t3.name
        order by date_format(t1.created_at, '%Y-%m') desc
    </select>
 <select id="getReverseManufacturerSummary" resultType="com.hightop.benyin.reverse.application.vo.ReverseStatisVo">
     select
     sum(t.item_num) itemNum,
     sum(t.pay_amount) payAmount,
     sum(t.actual_pay_amount) actualPayAmount,
     sum(t.reverse_item_num) reverseItemNum,
     sum(t.refund_amount) refundAmount
     from tb_reverse_order_detail t
     left join b_reverse_order t1 on t1.id = t.reverse_order_id
     left join b_storage_article t2 on t2.code = t.article_code
     left join b_manufacturer t3 on t3.id = t2.manufacturer_id
     where t1.process_status not in('REJECT','CLOSED')
     <if test="null != qo.startMonth and '' != qo.startMonth ">
         and DATE_FORMAT(t1.created_at,'%Y-%m') &gt;= #{qo.startMonth}
     </if>
     <if test="null != qo.endMonth and '' != qo.endMonth ">
         and DATE_FORMAT(t1.created_at,'%Y-%m') &lt;= #{qo.endMonth}
     </if>
     <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
         and t3.code like concat ('%',#{qo.manufacturerCode},'%')
     </if>
     <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
         and t3.name like concat ('%',#{qo.manufacturerName},'%')
     </if>
 </select>


    <select id="getReverseCustomerStatisList" resultType="com.hightop.benyin.reverse.application.vo.ReverseStatisVo">
        select date_format(t1.created_at, '%Y-%m')                                   currMonth,
        t4.seq_id customerSeq,
        t4.name customerName,
        sum(t.item_num) itemNum,
        sum(t.pay_amount) payAmount,
        sum(t.actual_pay_amount) actualPayAmount,
        sum(t.reverse_item_num) reverseItemNum,
        sum(t.refund_amount) refundAmount
        from tb_reverse_order_detail  t
        left join b_reverse_order t1 on t1.id = t.reverse_order_id
        left join b_storage_article  t2 on t2.code = t.article_code
        left join tb_trade_order  t3 on t3.id = t1.trade_order_id
        left join b_customer t4 on t4.id = t3.customer_id
        where t1.process_status not in('REJECT','CLOSED')

        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and DATE_FORMAT(t1.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth and '' != qo.endMonth ">
            and DATE_FORMAT(t1.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.customerSeq and '' != qo.customerSeq ">
            and t4.seq_id like concat ('%',#{qo.customerSeq},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t4.name like concat ('%',#{qo.customerName},'%')
        </if>
        group by
        date_format(t1.created_at, '%Y-%m'),
        t4.seq_id ,
        t4.name
        order by date_format(t1.created_at, '%Y-%m') desc
    </select>
 <select id="getReverseCustomerSummary" resultType="com.hightop.benyin.reverse.application.vo.ReverseStatisVo">
     select
     sum(t.item_num) itemNum,
     sum(t.pay_amount) payAmount,
     sum(t.actual_pay_amount) actualPayAmount,
     sum(t.reverse_item_num) reverseItemNum,
     sum(t.refund_amount) refundAmount
     from tb_reverse_order_detail t
     left join b_reverse_order t1 on t1.id = t.reverse_order_id
     left join b_storage_article t2 on t2.code = t.article_code
     left join tb_trade_order  t3 on t3.id = t1.trade_order_id
     left join b_customer t4 on t4.id = t3.customer_id
     where t1.process_status not in('REJECT','CLOSED')
     <if test="null != qo.startMonth and '' != qo.startMonth ">
         and DATE_FORMAT(t1.created_at,'%Y-%m') &gt;= #{qo.startMonth}
     </if>
     <if test="null != qo.endMonth and '' != qo.endMonth ">
         and DATE_FORMAT(t1.created_at,'%Y-%m') &lt;= #{qo.endMonth}
     </if>
     <if test="null != qo.customerSeq and '' != qo.customerSeq ">
         and t4.seq_id like concat ('%',#{qo.customerSeq},'%')
     </if>
     <if test="null != qo.customerName and '' != qo.customerName ">
         and t4.name like concat ('%',#{qo.customerName},'%')
     </if>
 </select>


    <select id="getReverseMechineStatisList" resultType="com.hightop.benyin.reverse.application.vo.ReverseStatisVo">
        select  t.currMonth, ifnull(t.name,'无')  series, sum(t.item_num) itemNum,sum(t.pay_amount) payAmount,
                sum(t.actual_pay_amount) actualPayAmount,
                sum(t.reverse_item_num) reverseItemNum, sum(t.refund_amount) refundAmount
        from (select  distinct  date_format(tt.created_at, '%Y-%m')                                   currMonth,t.id,t4.name,t.item_num,t.pay_amount,t.actual_pay_amount,t.reverse_item_num,t.refund_amount
              from tb_reverse_order_detail t
                       left join b_reverse_order tt on tt.id = t.reverse_order_id
                       left join b_storage_article t1 on t1.code = t.article_code
                       left join b_product_part_bom t2 on t2.part_id = t1.part_id
                       left join b_product_tree t3 on t3.id = t2.product_id
                       left join b_product_tree t4 on t4.id = t3.parent_id
              where tt.process_status not in ('REJECT', 'CLOSED')
        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and DATE_FORMAT(tt.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth and '' != qo.endMonth ">
            and DATE_FORMAT(tt.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND  t2.product_id in
            <foreach collection="qo.productIds" item="id" separator=" , " open="(" close=")">
                #{id}
            </foreach>
        </if>
             )t
        group by t.currMonth,t.name
        order by t.currMonth asc
    </select>




</mapper>
