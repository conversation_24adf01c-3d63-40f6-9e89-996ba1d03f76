<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.TStatisticsMonthGrossProfitMapper">

    <sql id="queryInfo">
        sum(actual_receipt + cost_income + toner_income + TRUNCATE(COALESCE(repair_pay,0)/100, 2) + TRUNCATE(COALESCE(mechanical_place_order_income_amount,0)/100, 2) + TRUNCATE(COALESCE(other_income,0)/100, 2))  total_income,
        <!--sum(cost_consumables + cost_part) total_expenditure,-->
        SUM( cost_consumables + cost_part + TRUNCATE(COALESCE(mechanical_place_order_amount,0)/100, 2) + TRUNCATE(COALESCE(other_cost,0)/100, 2)) total_expenditure,
        <!--sum(actual_profit) actual_profit,-->
        sum( actual_profit + (TRUNCATE ( COALESCE ( mechanical_place_order_income_amount, 0 )/ 100, 2 ) - TRUNCATE ( COALESCE ( mechanical_place_order_amount, 0 )/ 100, 2 )) + (TRUNCATE ( COALESCE ( other_income, 0 )/ 100, 2 ) - TRUNCATE ( COALESCE ( other_cost, 0 )/ 100, 2 )) ) actual_profit,
        sum(actual_receipt) actual_receipt,
        sum(repair_times) repair_times,
        sum(mechanical_nums) mechanical_nums,
        sum(color_nums) color_nums,
        sum(black_nums)  black_nums
        FROM
        t_statistics_month_gross_profit t
        WHERE
        deleted = false
        <if test="null != query.startMonth and '' != query.startMonth">
            <![CDATA[ and monthly >= #{query.startMonth} ]]>
        </if>
        <if test="null != query.endMonth and '' != query.endMonth">
            <![CDATA[ and monthly <= #{query.endMonth} ]]>
        </if>
    </sql>
    <select id="queryMonthGrossProfitList"
            resultType="com.hightop.benyin.statistics.application.vo.StatisticsMonthGrossProfitVO">
        SELECT
        monthly,
        <include refid="queryInfo">
        </include>
        GROUP BY monthly
        HAVING 1=1
        <if test="null != query.startTotalIncome">
            <![CDATA[ and sum(actual_receipt + cost_income + toner_income) >= #{query.startTotalIncome} ]]>
        </if>
        <if test="null != query.endTotalIncome">
            <![CDATA[ and sum(actual_receipt + cost_income + toner_income) <= #{query.endTotalIncome} ]]>
        </if>
        <if test="null != query.startTotalExpenditure">
            <![CDATA[ and sum(cost_consumables + cost_part) >= #{query.startTotalExpenditure} ]]>
        </if>
        <if test="null != query.endTotalExpenditure">
            <![CDATA[ and sum(cost_consumables + cost_part) <= #{query.endTotalExpenditure} ]]>
        </if>
        ORDER BY monthly DESC,total_income DESC,total_expenditure DESC

    </select>
    <select id="queryMonthGrossProfit"
            resultType="com.hightop.benyin.statistics.application.vo.StatisticsMonthGrossProfitVO">
        SELECT
        SUM( total_income ) total_income,
        SUM( total_expenditure ) total_expenditure,
        SUM( actual_profit ) actual_profit,
        SUM( actual_receipt ) actual_receipt,
        SUM( repair_times ) repair_times,
        SUM( mechanical_nums ) mechanical_nums,
        SUM( color_nums ) color_nums,
        SUM( black_nums )  black_nums
        FROM
        (
        SELECT
        <include refid="queryInfo"></include>
        GROUP BY
        monthly
        ) t
        <where>
            <if test="null != query.startTotalIncome">
                <![CDATA[ and t.total_income >= #{query.startTotalIncome} ]]>
            </if>
            <if test="null != query.endTotalIncome">
                <![CDATA[ and t.total_income <= #{query.endTotalIncome} ]]>
            </if>
            <if test="null != query.startTotalExpenditure">
                <![CDATA[ and t.total_expenditure >= #{query.startTotalExpenditure} ]]>
            </if>
            <if test="null != query.endTotalExpenditure">
                <![CDATA[ and t.total_expenditure <= #{query.endTotalExpenditure} ]]>
            </if>
        </where>
    </select>


    <select id="queryEngineerMonthStatistics"
            resultType="com.hightop.benyin.statistics.infrastructure.entity.TStatisticsEngineerMonthGrossProfit">
        SELECT
        monthly,
        SUM( actual_receipt ) actual_receipt,
        SUM( black_nums ) black_nums,
        SUM( color_nums ) color_nums,
        SUM( repair_nums ) repair_nums,
        SUM( receive_nums ) receive_nums,
        SUM( scattered_nums ) scattered_nums,
        SUM( total_pay ) total_pay,
        SUM( repair_pay ) repair_pay,
        SUM( item_pay ) item_pay,
        SUM( submit_report_num ) submit_report_num,
        SUM( confirmed_num ) confirmed_num
        FROM
        (
        SELECT
        p.monthly,
        SUM( actual_receipt ) actual_receipt,
        SUM( black_nums ) black_nums,
        SUM( color_nums ) color_nums,
        0 repair_nums,
        0 receive_nums,
        0 scattered_nums,
        0 total_pay,
        0 repair_pay,
        0 item_pay,
        0 submit_report_num,
        0 confirmed_num
        FROM
        t_statistics_machine_gross_profit p
        INNER JOIN st_user_basic b ON p.engineer_id = b.id
        WHERE
        p.engineer_id IS NOT NULL
        and p.customer_id IS NOT NULL
        GROUP BY
        p.engineer_id
        UNION ALL
        SELECT
        monthly,
        0 actual_receipt,
        0 black_nums,
        0 color_nums,
        repair_nums,
        receive_nums,
        scattered_nums,
        total_pay,
        repair_pay,
        item_pay,
        submit_report_num,
        confirmed_num
        FROM
        t_statistics_engineer_month_gross_profit
        ) t
        <where>
            <if test="query.startMonthly != null and query.startMonthly != '' ">
                <![CDATA[ AND monthly >= #{query.startMonthly} ]]>
            </if>
            <if test="query.endMonthly != null and query.endMonthly != '' ">
                <![CDATA[ AND monthly <= #{query.endMonthly} ]]>
            </if>
        </where>
        GROUP BY
        monthly
        ORDER BY
        monthly DESC,
        actual_receipt DESC
    </select>
    <select id="queryEngineerMonthGross"
            resultType="com.hightop.benyin.statistics.application.vo.EngineerMonthStatisticsGrossProfitVO">
        SELECT
        monthly,
        engineer_id,
        engineer_code,
        engineer_name,
        SUM( actual_receipt ) actual_receipt,
        SUM( black_nums ) black_nums,
        SUM( color_nums ) color_nums,
        SUM( repair_nums ) repair_nums,
        SUM(total_pay) saleIncome,
        SUM(repair_pay) repair_pay,
        SUM(item_pay) item_pay,
        SUM(totalExpenditure) totalExpenditure,
        SUM(customer_num) customer_num,
        SUM(machine_num) machine_num,
        (SUM(actual_receipt + total_pay - totalExpenditure)) actualProfit
        FROM
        (
        SELECT
        p.monthly,
        p.engineer_id,
        b.CODE engineer_code,
        b.NAME engineer_name,
        SUM( actual_receipt ) actual_receipt,
        SUM( black_nums ) black_nums,
        SUM( color_nums ) color_nums,
        0 repair_nums,
        0 total_pay,
        0 repair_pay,
        0 item_pay,
        0 totalExpenditure,
        0 customer_num,
        0 machine_num
        FROM
        t_statistics_machine_gross_profit p
        INNER JOIN st_user_basic b ON p.engineer_id = b.id
        WHERE
        p.engineer_id IS NOT NULL
        and p.customer_id IS NOT NULL
        GROUP BY
        p.engineer_id,
        p.monthly
        UNION ALL
        SELECT
        monthly,
        engineer_id,
        engineer_code,
        engineer_name,
        0 actual_receipt,
        0 black_nums,
        0 color_nums,
        repair_nums,
        total_pay,
        repair_pay,
        item_pay,
        total_cost totalExpenditure,
        customer_num,
        machine_num
        FROM
        t_statistics_engineer_month_gross_profit
        ) t
        <where>
            <if test="query.startMonthly != null and query.startMonthly != '' ">
                <![CDATA[ AND monthly >= #{query.startMonthly} ]]>
            </if>
            <if test="query.endMonthly != null and query.endMonthly != '' ">
                <![CDATA[ AND monthly <= #{query.endMonthly} ]]>
            </if>
            <if test="query.engineerCode != null and query.engineerCode != '' ">
                <![CDATA[  and engineer_code like concat ('%',#{query.engineerCode},'%')]]>
            </if>
            <if test="query.engineerName != null and query.engineerName != '' ">
                <![CDATA[  and engineer_name like concat ('%',#{query.engineerName},'%') ]]>
            </if>
        </where>
        GROUP BY
        t.engineer_id,
        t.monthly
        ORDER BY
        t.monthly DESC,
        t.engineer_name DESC
    </select>
    <select id="queryMonthExpenditureList"
            resultType="com.hightop.benyin.statistics.application.vo.StatisticsMonthExpenditureVO">
        SELECT
            monthly,
            SUM( toner_sale_num + toner_use_num ) toner_num,
            SUM( cost_consumables ) toner_amount,
            SUM( cost_sale_num + cost_use_num ) cost_use_num,
            SUM( cost_part ) cost_use_amount,
            SUM( mechanical_place_order_num ) mechanical_place_order_num,
            SUM( mechanical_place_order_amount ) mechanical_place_order_amount,
            SUM( cost_consumables + cost_part + TRUNCATE(COALESCE(mechanical_place_order_amount,0)/100, 2) + TRUNCATE(COALESCE(other_cost,0)/100, 2)) total_amount,
            SUM( other_cost) other_amount
        FROM
            t_statistics_month_gross_profit
            <where>
                <if test="query.startMonthly != null and query.startMonthly != '' ">
                    <![CDATA[ AND monthly >= #{query.startMonthly} ]]>
                </if>
                <if test="query.endMonthly != null and query.endMonthly != '' ">
                    <![CDATA[ AND monthly <= #{query.endMonthly} ]]>
                </if>
            </where>
        GROUP BY monthly
        ORDER BY monthly DESC
    </select>
    <select id="queryMonthIncomeList"
            resultType="com.hightop.benyin.statistics.application.vo.StatisticsMonthIncomeVO">
        select * from (
        SELECT
        monthly,
        SUM( actual_receipt ) actual_receipt,
        SUM( repair_pay ) repair_pay,
        SUM( mechanical_place_order_income_amount ) mechanical_place_order_income_amount,
        SUM( toner_income ) toner_income,
        SUM( cost_income ) cost_income,
        SUM( COALESCE(actual_receipt, 0) +
             TRUNCATE(COALESCE(repair_pay,0)/100, 2) +
             TRUNCATE(COALESCE(mechanical_place_order_income_amount,0)/100, 2) +
             COALESCE(toner_income, 0) +
             COALESCE(cost_income, 0) +
             TRUNCATE(COALESCE(other_income,0)/100, 2)) sale_total_amount,
        SUM( other_income ) other_amount
        FROM
        t_statistics_month_gross_profit
        GROUP BY monthly
        <if test="query.startTotalSaleAmount != null and query.endTotalSaleAmount != null">
            HAVING sale_total_amount BETWEEN #{query.startTotalSaleAmount} AND #{query.endTotalSaleAmount}
        </if>
        <if test="query.startTotalSaleAmount != null and query.endTotalSaleAmount == null">
            <![CDATA[  HAVING sale_total_amount >= #{query.startTotalSaleAmount} ]]>
        </if>
        <if test="query.startTotalSaleAmount == null and query.endTotalSaleAmount != null">
            <![CDATA[ HAVING sale_total_amount <= #{query.endTotalSaleAmount} ]]>
        </if>
        ) t
        <where>
            <if test="query.startMonthly != null and query.startMonthly != '' ">
                <![CDATA[ AND t.monthly >= #{query.startMonthly} ]]>
            </if>
            <if test="query.endMonthly != null and query.endMonthly != '' ">
                <![CDATA[ AND t.monthly <= #{query.endMonthly} ]]>
            </if>
            <if test="query.startActualReceipt != null">
                <![CDATA[ AND t.actual_receipt >= #{query.startActualReceipt} ]]>
            </if>
            <if test="query.endActualReceipt != null">
                <![CDATA[ AND t.actual_receipt <= #{query.endActualReceipt} ]]>
            </if>
            <if test="query.startRepairPay != null">
                <![CDATA[ AND t.repair_pay >= #{query.startRepairPay} ]]>
            </if>
            <if test="query.endRepairPay != null">
                <![CDATA[ AND repair_pay <= #{query.endRepairPay} ]]>
            </if>
            <if test="query.startMechanicalSaleAmount != null">
                <![CDATA[ AND t.mechanical_place_order_income_amount >= #{query.startMechanicalSaleAmount} ]]>
            </if>
            <if test="query.endMechanicalSaleAmount != null">
                <![CDATA[ AND t.mechanical_place_order_income_amount <= #{query.endMechanicalSaleAmount} ]]>
            </if>
            <if test="query.startTonerSaleAmount != null">
                <![CDATA[ AND t.toner_income >= #{query.startTonerSaleAmount} ]]>
            </if>
            <if test="query.endTonerSaleAmount != null">
                <![CDATA[ AND t.toner_income <= #{query.endTonerSaleAmount} ]]>
            </if>
            <if test="query.startCostSaleAmount != null">
                <![CDATA[ AND t.cost_income >= #{query.startCostSaleAmount} ]]>
            </if>
            <if test="query.endCostSaleAmount != null">
                <![CDATA[ AND t.cost_income <= #{query.endCostSaleAmount} ]]>
            </if>
            <if test="query.startOtherAmount != null">
                <![CDATA[ AND t.other_amount >= #{query.startOtherAmount} ]]>
            </if>
            <if test="query.endOtherAmount != null">
                <![CDATA[ AND t.other_amount <= #{query.endOtherAmount} ]]>
            </if>
        </where>
        ORDER BY t.monthly DESC
    </select>
    <select id="getSaleOrdersOrApplyOrders" resultType="com.hightop.benyin.statistics.infrastructure.TO.TStatisticsMonthGrossProfitTo">
            SELECT
            DATE_FORMAT(tod.created_at,'%Y-%m') as monthly,
            tor.customer_id as customerId,
            sib.price as costPrice,
            sa.type as type,
            tod.item_num as num,
            tod.actual_pay_amount as amount
            FROM tb_trade_order_detail tod
            INNER JOIN tb_trade_order tor ON tor.id = tod.trade_order_id
            LEFT JOIN b_storage_article sa ON sa.code = tod.article_code
            INNER JOIN b_storage_out_warehouse sow ON sow.shop_waybill = tor.order_num
            INNER JOIN b_storage_warehouse_flow swf ON swf.flow_id = sow.out_warehouse_id
            AND swf.code = tod.article_code
            AND swf.deleted = 0
            AND swf.in_out_type = 2
            INNER JOIN b_storage_inventory_batch sib ON sib.code = swf.code and sib.batch_code = swf.batch_code
            AND sib.deleted = 0
            WHERE 1=1
            AND DATE_FORMAT(tod.created_at,'%Y-%m') BETWEEN #{startMonth} AND #{month}
            AND tor.order_status = 'SUCCESS'
            AND tor.order_type = #{orderType}
            AND sa.product_id IS NULL
            AND sow.out_status = 'YCK'
            AND sow.deleted = 0
            <if test="customerIdSet != null and !customerIdSet.isEmpty()">
                AND tor.customer_id IN
                <foreach collection="customerIdSet" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
            </if>
        </select>
</mapper>