<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.purchase.infrastructure.mapper.SupplySourceMapper">

    <select id="pageList" resultType="com.hightop.benyin.purchase.infrastructure.entity.SupplySource">
        select
            t1.*,tu.name updatedByName
        from  b_supply_source t1
        left join b_storage_article t2 on t1.article_id = t2.id
        left join tb_part_product_tree t3 on t2.part_id = t3.part_id
        left join b_manufacturer t4 on t4.id = t1.manufacturer_id
        left join st_user_basic tu on tu.id = t1.updated_by
        where t1.deleted = 0
        <!-- 机型 -->
        <if test="null!=qo.productTreeId and !qo.productTreeId.isEmpty()">
            AND
            <foreach collection="qo.productTreeId" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,#{id})
            </foreach>
        </if>
        <!-- 小类 -->
        <if test="null != qo.type and '' != qo.type">
            and t2.type = #{qo.type}
        </if>

        <!-- 启用状态 -->
        <if test="null != qo.status">
            and t1.status = #{qo.status}
        </if>

        <!-- 供应商 -->
        <if test="null != qo.manufacturerId">
            and t1.manufacturer_id = #{qo.manufacturerId}
        </if>

        <!-- 供应商编号 -->
        <if test="null != qo.manufacturerCode">
            and t4.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>

        <!-- 物品名称 -->
        <if test="null != qo.articleName and '' != qo.articleName">
            and t2.name like concat ('%',#{qo.articleName},'%')
        </if>
        <!-- 物品编号 -->
        <if test="null != qo.articleCode and '' != qo.articleCode">
            and t2.code like concat ('%',#{qo.articleCode},'%')
        </if>
        <!-- oem编号 -->
        <if test="null != qo.numberOem and '' != qo.numberOem">
            and t2.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>
        <!-- 所属单元 -->
        <if test="null != qo.partBomUnit and '' != qo.partBomUnit">
            and exists(select 1 from b_product_part_bom bppb where bppb.part_id = t2.part_id and bppb.unit = #{qo.partBomUnit})
        </if>
        <!-- 制造商渠道(字典项码) -->
        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel">
            and t2.manufacturer_channel = #{qo.manufacturerChannel}
        </if>

        <!-- 修改人 -->
        <if test="null != qo.updatedByName and '' != qo.updatedByName">
            and tu.name like concat ('%',#{qo.updatedByName},'%')
        </if>
        <!-- 申请人 -->
        <if test="null != qo.applicantName and '' != qo.applicantName">
            and t1.applicant_name like concat ('%',#{qo.applicantName},'%')
        </if>

        <if test="null != qo.startDate and '' != qo.startDate ">
            and t1.updated_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t1.updated_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>

        order by t1.id desc
    </select>


</mapper>