<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.item.infrastructure.mapper.ItemMapper">

    <resultMap id="itemSummaryResult" type="com.hightop.benyin.item.application.vo.ItemSummaryVo">
        <result column="item_id" property="itemId"/>
        <result column="item_code" property="itemCode"/>
        <result column="pics_url" property="picsUrl" />
        <result column="oem" property="oemNumber"/>
        <result column="item_name" property="itemName"/>
        <result column="article_code" property="articleCode"/>
        <result column="sale_unit_price" property="saleUnitPrice"/>
        <result column="categ_name" property="categoryName"/>
        <result column="sale_status" property="saleStatus"/>
        <result column="sold_out_num" property="soldOutNum"/>
        <result column="inventoryNum" property="inventoryNum"/>
        <result column="sale_attr_vals" property="saleAttrVals" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="itemSaleAttrVals" property="itemSaleAttrVals" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="part_id" property="partId"/>
        <result column="discount_price" property="discountPrice"/>
        <result column="vip_discount_price" property="vipDiscountPrice"/>
        <result column="sale_sku_id" property="saleSkuId"/>
        <result column="remark" property="remark"/>
        <result column="expectLife" property="expectLife"/>
        <result column="type" property="type"/>
    </resultMap>

    <!--只查询非删除状态、已上架的商品-->
    <sql id="must_sql">
        t.category_id = #{query.categoryId} AND t.sale_status = 'ON_SALE' AND t.deleted = 0
    </sql>
    <!--根据商品分类属性查询-->
    <sql id="queryWithTag">
        <if test="null!=query.itemName and query.itemName!=''">
            and t.`name` like #{query.itemName}
        </if>
        <if test="null!=tags and !tags.isEmpty()">
            AND
            <foreach collection="tags" item="tag" separator=" AND " open="(" close=")">
                <foreach collection="tag" item="g" separator=" OR " open="(" close=")">
                    JSON_CONTAINS( t.sale_attr_vals, ${g} ) = 1
                </foreach>
            </foreach>
        </if>
    </sql>

    <!-- 查询商品下sku的最低和最高价 -->
    <sql id="hlSku">
        ,IFNULL(( SELECT sale_unit_price FROM tb_sale_sku WHERE item_id = t.id ORDER BY sale_unit_price ASC LIMIT 1 ), 0 ) AS lowestSku,
        IFNULL(( SELECT sale_unit_price FROM tb_sale_sku WHERE item_id = t.id ORDER BY sale_unit_price DESC LIMIT 1 ), 0 ) AS highestSku
    </sql>

    <!--    带机型筛选的查询(连表查询)-->
    <sql id="selectWithModel">
        SELECT DISTINCT t.*
        <include refid="hlSku"/>
        FROM tb_item t
        LEFT JOIN tb_sale_sku sku ON t.id = sku.item_id
        LEFT JOIN tb_part_product_tree ppt ON ppt.part_id = sku.part_id
        <where>
            <include refid="must_sql"/>
            <include refid="queryWithTag"/>
            AND
            <foreach collection="query.productTreeIdList" item="pti" separator=" or " open="(" close=")">
                JSON_CONTAINS(ppt.product_tree_ids,#{pti})
            </foreach>
        </where>
    </sql>


    <select id="shopSearch" resultType="com.hightop.benyin.item.infrastructure.entity.Item">
        <choose>
            <when test="null!=query.productTreeIdList and !query.productTreeIdList.isEmpty()">
                <!--带机型检索-->
                <include refid="selectWithModel"/>
            </when>
            <otherwise>
                <!--不带机型检索-->
                SELECT
                *
                <include refid="hlSku"/>
                FROM (SELECT * FROM tb_item t WHERE<include refid="must_sql"/>) t
                <where>
                    <include refid="queryWithTag"/>
                </where>
            </otherwise>
        </choose>
        ORDER BY t.id DESC
    </select>


    <select id="itemSummaryList" resultMap="itemSummaryResult">
        select distinct  b.code item_code,a.sku_pic_url pics_url,a.oem ,b.name item_name ,d.code article_code,e.name categ_name,d.sum_warehouse_number inventoryNum,
        b.sale_status,b.sold_out_num,a.sale_attr_vals ,a.id sale_sku_id,a.part_id,b.id item_id,a.discount_price,a.vip_discount_price,a.sale_unit_price
        ,a.remark,s.expect_life expectLife, s.type,b.sale_attr_vals as itemSaleAttrVals
        from tb_sale_sku a
        left join tb_item b on a.item_id = b.id
        left join b_storage_article s on a.article_code = s.code
        left join tb_part_product_tree c on a.part_id = c.part_id
        left join b_storage_inventory d on d.id = a.inv_sku_id
        left join tb_item_classify e on e.id = b.category_id
        left join  b_product_part_bom f on a.part_id = f.part_id and f.deleted = false
        left join b_product_part g on g.id = a.part_id
        where a.deleted = false
          <!-- oem编号 -->
        <if test="null!=query.oemNumber and query.oemNumber!=''">
            and a.oem like concat ('%',#{query.oemNumber},'%')
        </if>
        <!-- 商品编号 -->
        <if test="null!=query.itemCode and query.itemCode!=''">
            and b.code like concat ('%',#{query.itemCode},'%')
        </if>
        <!-- 商品名称 -->
        <if test="null!=query.itemName and query.itemName!=''">
            and b.name like concat ('%',#{query.itemName},'%')
        </if>
        <!-- 物品编号 -->
        <if test="null!=query.articleCode and query.articleCode!=''">
            and d.code like concat ('%',#{query.articleCode},'%')
        </if>
        <!-- 商品分类 -->
        <if test="null!=query.categoryId and query.categoryId!=''">
            and b.category_id = #{query.categoryId}
        </if>
        <!-- 商品状态 -->
        <if test="null!=query.saleStatus and query.saleStatus!=''">
            and b.sale_status = #{query.saleStatus}
        </if>

        <!-- 备件等级 -->
        <if test="null != query.spareLevel and !query.spareLevel.isEmpty()">
            and f.spare_level in
            <foreach collection="query.spareLevel" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--  更换频次 -->
        <if test="null!=query.repFrequency and !query.repFrequency.isEmpty()">
            and f.rep_frequency in
            <foreach collection="query.repFrequency" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--  所属单元 -->
        <if test="null!=query.unitList and !query.unitList.isEmpty()">
            and f.unit in
            <foreach collection="query.unitList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--  零件小类 -->
        <if test="null!=query.productPartTypeList and !query.productPartTypeList.isEmpty()">
            and s.type in
            <foreach collection="query.productPartTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="null!=query.lastIds and !query.lastIds.isEmpty()">
            AND
            <foreach collection="query.lastIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(c.product_tree_ids,#{id})
            </foreach>
        </if>
        order by a.update_at desc
    </select>

    <select id="shopSearchForConsumables" resultType="com.hightop.benyin.item.infrastructure.entity.Item">
        SELECT DISTINCT t.*
        FROM tb_item t
        LEFT JOIN tb_sale_sku sku ON t.id = sku.item_id and sku.deleted = 0
        LEFT JOIN tb_part_product_tree ppt ON ppt.part_id = sku.part_id
        WHERE t.sale_status = 'ON_SALE' AND t.deleted = 0
        <if test="null!=query.categoryId">
           AND t.category_id = #{query.categoryId}
        </if>
        <if test="null!=query.oem and query.oem!=''">
            AND sku.`oem` like #{query.oem}
        </if>
        <if test="null!=query.productTreeIdList and !query.productTreeIdList.isEmpty()">
            AND
            <foreach collection="query.productTreeIdList" item="pti" separator=" or " open="(" close=")">
                JSON_CONTAINS(ppt.product_tree_ids,#{pti})
            </foreach>
        </if>
        <if test="null!=tags and !tags.isEmpty()">
            AND
            <foreach collection="tags" item="tag" separator=" AND " open="(" close=")">
                <foreach collection="tag" item="g" separator=" OR " open="(" close=")">
                    JSON_CONTAINS( t.sale_attr_vals, ${g} ) = 1
                </foreach>
            </foreach>
        </if>

        <if test="null!=query.productIds and !query.productIds.isEmpty()">
            AND t.id in
            <foreach collection="query.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getItemMatchPartProductIds" resultType="com.hightop.benyin.item.application.vo.ItemMatchPartProductVo">
         SELECT
                    it.id as itemId,
                    it.code as itemCode,
                   	it.`name` as itemName,
                   	tree.part_id as partId,
                   	tree.product_tree_ids as productIds
        FROM
                    tb_item it
        LEFT JOIN tb_sale_sku sku ON it.id = sku.item_id
                    LEFT JOIN b_storage_article art ON sku.article_code = art.`code`
                    LEFT JOIN tb_part_product_tree tree ON art.part_id = tree.part_id
                   WHERE
                    it.deleted = 0
                    AND sku.deleted = 0
                    AND it.id = ${ itemId };
    </select>
    <select id="getItemMinSalePriceAndPrice" resultType="com.hightop.benyin.item.application.vo.ItemMatchPartProductVo">
        SELECT
            IFNULL(( SELECT sale_unit_price FROM tb_sale_sku WHERE deleted=0 and item_id = t.id ORDER BY sale_unit_price ASC LIMIT 1 ), 0 ) AS lowestSku,
            IFNULL(( SELECT sale_unit_price FROM tb_sale_sku WHERE deleted=0 and item_id = t.id ORDER BY sale_unit_price DESC LIMIT 1 ), 0 ) AS highestSku
        FROM
            tb_item t
        WHERE
            t.deleted = 0
          AND t.id = ${ itemId };
    </select>
</mapper>
