<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.items.store.infrastructure.mapper.ItemStoreMapper">

    <resultMap id="itemStoreResultMap" type="com.hightop.benyin.items.store.infrastructure.entity.ItemStore">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_type" property="userType"/>
        <result column="oem_number" property="oemNumber"/>
        <result column="part_id" property="partId"/>
        <result column="item_id" property="itemId"/>
        <result column="item_name" property="itemName"/>
        <result column="userName" property="userName"/>
        <result column="articleName" property="articleName"/>
        <result column="customerName" property="customerName"/>
        <result column="customerSeq" property="customerSeq"/>
        <result column="sale_sku_id" property="saleSkuId"/>
        <result column="item_id" property="itemId"/>
        <result column="sale_unit_price" property="saleUnitPrice"/>
        <result column="is_pm" property="isPm"/>
        <result column="sku_source" property="skuSource"/>
        <result column="sku_info" property="skuInfo" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="batch_code" property="batchCode" />
        <!-- 当前可用=锁定后的数量 -->
        <result column="after_lock_num" property="num"/>
        <result column="type" property="type"/>
        <result column="amount" property="amount"/>
        <result column="updated_at" property="createdAt"/>
        <result column="updatedAt" property="updatedAt"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <select id="queryList" resultMap="itemStoreResultMap">
        select * from tb_item_store a
        <!-- 没有机型不连表查询 -->
        <if test="null!=query.productIdList and !query.productIdList.isEmpty()">
            left join tb_part_product_tree b on a.part_id = b.part_id
        </if>
        <!-- 没有分类属性不连表查询 -->
        left join tb_item c on a.item_id = c.id
        where a.deleted = false and a.after_lock_num >0
        <if test="null != query.userId and '' != query.userId">
            and a.user_id = #{query.userId}
        </if>
        <if test="null != query.userType">
            and a.user_type = #{query.userType}
        </if>
        <if test="null != query.categoryId">
            and c.category_id = #{query.categoryId}
        </if>
        <if test="null != query.oem and '' != query.oem">
            and a.oem_number like concat ('%',#{query.oem},'%')
        </if>
        <if test="null != query.itemName and '' != query.itemName">
            and a.item_name like  concat ('%',#{query.itemName},'%')
        </if>
        <!-- 机型 -->
        <if test="null!=query.productIdList and !query.productIdList.isEmpty()">
            AND (
            <foreach collection="query.productIdList" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(b.product_tree_ids,cast(#{id} as char ))
            </foreach>
            or c.category_id =26 )
        </if>

          <!-- 分类属性 -->
        <if test="null!=jsonTagList and !jsonTagList.isEmpty()">
            AND
            <foreach collection="query.jsonTagList" item="tag" separator=" AND " open="(" close=")">
                <foreach collection="tag" item="g" separator=" OR " open="(" close=")">
                    JSON_CONTAINS( c.sale_attr_vals, ${g} ) = 1
                </foreach>
            </foreach>
        </if>
    </select>
    <select id="pageList" resultMap="itemStoreResultMap">
        select t.*, sa.name articleName, sa.unit,t.num*t.sale_unit_price as amount,sa.type,
               case t.user_type when 'CUSTOMER' then c.name else sub.name end userName,
               c.seq_id customerSeq,c.name customerName
        from tb_item_store t
                 left join b_storage_article sa on sa.code = t.article_code
                 left join st_user_basic sub on sub.id = t.user_id and t.user_type = 'ENGINEER'
                 left join b_customer c on c.id = t.user_id and t.user_type = 'CUSTOMER'
                    left join tb_item it on t.item_id = it.id

        <if test="null!=query.productIdList and !query.productIdList.isEmpty()">
            left join tb_part_product_tree b on b.part_id = sa.part_id
        </if>
        where t.deleted = 0
        <if test="null != query.categoryId">
            and it.category_id = #{query.categoryId}
        </if>
        <if test="null != query.hasInventory and true==query.hasInventory">
            and t.after_lock_num>0
        </if>
        <if test="null != query.oem and '' != query.oem">
            and t.oem_number like concat ('%',#{query.oem},'%')
        </if>
        <if test="null!=query.productIdList and !query.productIdList.isEmpty()">
            AND
            <foreach collection="query.productIdList" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(b.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>
        <if test="null!=query.minNum">
            and t.after_lock_num &gt;= #{query.minNum}
        </if>
        <if test="null!=query.maxNum">
            and t.after_lock_num &lt;= #{query.maxNum}
        </if>
        <!-- 分类属性 -->
        <if test="null!=jsonTagList and !jsonTagList.isEmpty()">
            AND
            <foreach collection="query.jsonTagList" item="tag" separator=" AND " open="(" close=")">
                <foreach collection="tag" item="g" separator=" OR " open="(" close=")">
                    JSON_CONTAINS( c.sale_attr_vals, ${g} ) = 1
                </foreach>
            </foreach>
        </if>
        <if test="null != query.userType">
            and t.user_type = #{query.userType}
        </if>

        <if test="null != query.type">
            and sa.type = #{query.type}
        </if>

        <if test="null != query.customerId">
            and t.user_id = #{query.customerId}
        </if>

        <if test="null != query.skuSource">
            and t.sku_source = #{query.skuSource}
        </if>

        <if test="null != query.articleCode and '' != query.articleCode">
            and sa.code like concat ('%',#{query.articleCode},'%')
        </if>

        <if test="null != query.articleName and '' != query.articleName">
            and sa.name like concat ('%',#{query.articleName},'%')
        </if>

        <if test="null != query.userName and '' != query.userName">
            and( sub.name like concat ('%',#{query.userName},'%')  or
            c.name like concat ('%',#{query.userName},'%')     )
        </if>

        <if test="null != query.customerSeq and '' != query.customerSeq">
            and  c.seq_id like concat ('%',#{query.customerSeq},'%')
        </if>

        <if test="null != query.customerName and '' != query.customerName">
            and  c.name like concat ('%',#{query.customerName},'%')
        </if>

        order by t.user_id,t.created_at desc
    </select>

    <select id="total" resultType="java.math.BigDecimal">
        select COALESCE(sum(t.num*t.sale_unit_price)/100, 0)
        from tb_item_store t
                 left join b_storage_article sa on sa.code = t.article_code
                 left join st_user_basic sub on sub.id = t.user_id and t.user_type = 'ENGINEER'
                 left join b_customer c on c.id = t.user_id and t.user_type = 'CUSTOMER'
        <if test="null!=query.productIdList and !query.productIdList.isEmpty()">
            left join tb_part_product_tree b on b.part_id = sa.part_id
        </if>
        where t.deleted = 0 and t.after_lock_num>0

        <if test="null != query.oem and '' != query.oem">
            and t.oem_number like concat ('%',#{query.oem},'%')
        </if>
        <if test="null!=query.productIdList and !query.productIdList.isEmpty()">
            AND
            <foreach collection="query.productIdList" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(b.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>
        <if test="null != query.userType">
            and t.user_type = #{query.userType}
        </if>

        <if test="null != query.type">
            and sa.type = #{query.type}
        </if>

        <if test="null != query.customerId">
            and t.user_id = #{query.customerId}
        </if>

        <if test="null != query.skuSource">
            and t.sku_source = #{query.skuSource}
        </if>

        <if test="null != query.articleCode and '' != query.articleCode">
            and sa.code like concat ('%',#{query.articleCode},'%')
        </if>

        <if test="null != query.articleName and '' != query.articleName">
            and sa.name like concat ('%',#{query.articleName},'%')
        </if>

        <if test="null != query.userName and '' != query.userName">
            and( sub.name like concat ('%',#{query.userName},'%')  or
            c.name like concat ('%',#{query.userName},'%')     )
        </if>

        <if test="null != query.customerSeq and '' != query.customerSeq">
            and  c.seq_id like concat ('%',#{query.customerSeq},'%')
        </if>

        <if test="null != query.customerName and '' != query.customerName">
            and  c.name like concat ('%',#{query.customerName},'%')
        </if>

        order by t.user_id,t.created_at desc
    </select>


</mapper>
