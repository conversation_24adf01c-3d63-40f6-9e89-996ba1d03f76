<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.customer.infrastructure.mapper.CustomerSearchOutLogMapper">

    <select id="staticTotal" resultType="com.hightop.benyin.customer.application.vo.CustomerPageViewStatisticsVo">
        select count(1) custCount,sum(viewNum) viewCount
        from (
        select t.customer_id,count(1) viewNum
        from b_customer_search_out_log t
        left join b_customer t1 on t1.id = t.customer_id
        left join b_region tr2 on tr2.code = t1.region_code
        left join b_region tr3 on tr3.code = tr2.parent_code
        left join b_region tr4 on tr4.code = tr3.parent_code
        <where>
            <if test="qo.customerId!= null">
                and t.customer_id = #{qo.customerId}
            </if>
            <if test="qo.customerSeqId!= null and qo.customerSeqId!= ''">
                and t1.seq_id like concat ('%',#{qo.customerSeqId},'%')
            </if>
            <if test="qo.customerName!= null and qo.customerName!= ''">
                and t1.name like concat ('%',#{qo.customerName},'%')
            </if>
            <if test="null != qo.regionPath and '' != qo.regionPath ">
                and t1.region_code like CONCAT( #{qo.regionPath},'%')
            </if>
            <if test="qo.createStartTime!= null and qo.createStartTime!= ''">
                and t.create_time &gt;=concat(#{qo.createStartTime},' 00:00:00')
            </if>
            <if test="qo.createEndTime!= null and qo.createEndTime!= ''">
                and t.create_time &lt;= concat(#{qo.createEndTime},' 23:59:59')
            </if>
        </where>
        group by t.customer_id) t

    </select>
    <select id="staticList" resultType="com.hightop.benyin.customer.application.vo.CustomerSearchOutLogListVo">
        SELECT count(1) searchCount,
        t.`event_source`,
        date_format(t.create_time,'%Y-%m-%d')  currDate,
        t1.name AS customerName,
        t1.seq_id AS customerSeqId ,tr2.name AS area ,tr3.name AS city,tr4.name AS province
        FROM b_customer_search_out_log t
        LEFT JOIN b_customer t1 ON (t1.id = t.`customer_id`)
        left join b_region tr2 on tr2.code = t1.region_code
        left join b_region tr3 on tr3.code = tr2.parent_code
        left join b_region tr4 on tr4.code = tr3.parent_code
        <where>
            <if test="qo.customerId!= null">
                and t.customer_id = #{qo.customerId}
            </if>
            <if test="qo.customerSeqId!= null and qo.customerSeqId!= ''">
                and t1.seq_id like concat ('%',#{qo.customerSeqId},'%')
            </if>
            <if test="qo.customerName!= null and qo.customerName!= ''">
                and t1.name like concat ('%',#{qo.customerName},'%')
            </if>
            <if test="null != qo.regionPath and '' != qo.regionPath ">
                and t1.region_code like CONCAT( #{qo.regionPath},'%')
            </if>
            <if test="qo.createStartTime!= null and qo.createStartTime!= ''">
                and t.create_time &gt;=concat(#{qo.createStartTime},' 00:00:00')
            </if>
            <if test="qo.createEndTime!= null and qo.createEndTime!= ''">
                and t.create_time &lt;= concat(#{qo.createEndTime},' 23:59:59')
            </if>
            <if test="qo.eventSource!= null and qo.eventSource!= ''">
                and t.event_source = #{qo.eventSource}
            </if>
        </where>
        group by t.`event_source`,
        date_format(t.create_time,'%Y-%m-%d') ,
        t1.name ,
        t1.seq_id ,tr2.name ,tr3.name ,tr4.name
        ORDER BY date_format(t.create_time,'%Y-%m-%d') DESC
    </select>

    <select id="getPageViewCountEchartsData"
            resultType="com.hightop.benyin.customer.infrastructure.entity.CustomerPageViewEchartsView">
        <![CDATA[

        SELECT
                    DATE_FORMAT(started_at, '%H') as time ,count(1) as count
                FROM
                    b_customer_page_view
                where
                    customer_id = ${query.customerId}
                    and path = ${ query.pageViewPathEnums.code }
                    and started_at >= concat(${ query.startTime },' 00:00:00')
                    and started_at <= concat(${ query.endTime  },' 23:59:59')
                group by
                    DATE_FORMAT(started_at, '%H')

        ]]>

    </select>

    <select id="getLastMonthPageViewCountDataCountByCustomerId" resultType="java.lang.Long">
        <![CDATA[
                SELECT
                     count(1)
                FROM
                b_customer_page_view
                where
                customer_id = ${ customerId }
                and path in ('pages/home/<USER>','/pages/goods/detail','/pages/learn/index')
                and started_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
                and started_at <=  NOW()
        ]]>
    </select>

    <select id="getLastPageViewByCustomerId"
            resultType="com.hightop.benyin.customer.infrastructure.entity.CustomerPageView">
        <![CDATA[
            SELECT
                *
            FROM
                b_customer_page_view
            where
                customer_id = ${ customerId }
              and path in ('pages/home/<USER>','/pages/goods/detail','/pages/learn/index')
            order by started_at desc
            limit 1
        ]]>
    </select>

    <select id="getPageViewCountByCustomerId" resultType="java.lang.Long">
        <![CDATA[
            SELECT
                count(1)
            FROM
                b_customer_page_view
            where
                customer_id = ${ customerId }   and path in ('pages/home/<USER>','/pages/goods/detail','/pages/learn/index')
        ]]>
    </select>
</mapper>
