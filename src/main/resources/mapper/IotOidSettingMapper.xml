<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.customer.infrastructure.mapper.CustomerContractMapper">

    <select id="selectEffectiveContract" resultType="com.hightop.benyin.customer.infrastructure.entity.CustomerContractServe">
        select t.*
        from b_customer_contract_serve t  left join b_customer_contract t1 on t1.code = t.contract_code
        where t.device_group_id=#{deviceGroupId}
        <if test="null!=contractTypes and !contractTypes.isEmpty()">
            AND t1.contract_type in
            <foreach collection="contractTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        and #{reportTime} between t.start_time and t.end_time order by t.created_at desc limit 1
    </select>

    <select id="selectEffectiveContractList" resultType="com.hightop.benyin.customer.infrastructure.entity.CustomerContractServe">
        select t.*,t1.customer_id
        from b_customer_contract_serve t
        left join b_customer_contract t1 on t1.code = t.contract_code
        where
        <if test="null!=setTypes and !setTypes.isEmpty()">
             t.ser_type in
            <foreach collection="setTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != customerId ">
            and t1.customer_id = #{customerId}
        </if>
        <if test="null != deviceGroupId ">
            and t.device_group_id = #{deviceGroupId}
        </if>
        and t1.status != 'CANCEL'
        and #{reportTime} between t.start_time and t.end_time
        order by t1.created_at desc
    </select>

</mapper>
