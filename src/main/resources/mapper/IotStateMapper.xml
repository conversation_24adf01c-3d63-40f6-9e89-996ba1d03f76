<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.iot.infrastructure.mapper.IotStateMapper">

    <select id="getFaultOverview" resultType="com.hightop.benyin.report.application.vo.FaultTotalData">

        select *
        from (select t.device_group_id deviceGroupId,
                     t.product_id      productId,
                     t.label           name,
                     t.name            mode,
                     count(1)          countNumber
              from (select date_format(w1.created_at, '%Y-%m-%d'),
                           w1.num_code,
                           w1.device_group_id,
                           dg.product_id,
                           t2.label,
                           t3.name
                    from b_iot_state w1
                             left join b_customer_device_group dg on dg.id = w1.device_group_id
                             left join b_product_tree t3 on t3.id = dg.product_id
                             left join st_dict_item t2 on dg.device_group = t2.value and t2.dict_id = 20700
                             left join b_iot_numeric_code w2 on w2.num_code = w1.num_code
                    where w1.customer_id = #{qo.customerId}
                      and w2.is_statistics = 1
                      and w1.created_at &gt;= #{qo.startTime}
                      and w1.created_at &lt;= #{qo.endTime}
                    group by date_format(w1.created_at, '%Y-%m-%d'), w1.num_code, w1.device_group_id, dg.product_id,
                             t2.label, t3.name) t
              group by t.device_group_id, t.product_id, t.label, t.name) tt
        order by countNumber desc

    </select>

    <select id="getTopThree" resultType="com.hightop.benyin.report.application.vo.FaultTotalData">
        select *
        from (select t.device_group_id deviceGroupId,
                     t.product_id      productId,
                     t.label           name,
                     t.name            mode,
                     count(1)          countNumber
              from (select date_format(w1.created_at, '%Y-%m-%d'),
                           w1.num_code,
                           w1.device_group_id,
                           dg.product_id,
                           t2.label,
                           t3.name
                    from b_iot_state w1
                             left join b_customer_device_group dg on dg.id = w1.device_group_id
                             left join b_product_tree t3 on t3.id = dg.product_id
                             left join st_dict_item t2 on dg.device_group = t2.value and t2.dict_id = 20700
                             left join b_iot_numeric_code w2 on w2.num_code = w1.num_code
                    where w1.customer_id = #{qo.customerId}
                      and w2.is_statistics = 1
                      and w1.created_at &gt;= #{qo.startTime}
                      and w1.created_at &lt;= #{qo.endTime}
                    group by date_format(w1.created_at, '%Y-%m-%d'), w1.num_code, w1.device_group_id, dg.product_id,
                             t2.label, t3.name) t
              group by t.device_group_id, t.product_id, t.label, t.name) tt
        order by countNumber desc
        limit 3
    </select>
    <select id="getDayFirstFault" resultType="com.hightop.benyin.iot.infrastructure.entity.IotState">
        select t.*,t1.brand,t1.machine,t1.deviceGroup
        from b_iot_state t  join (
       select date_format(w1.created_at, '%Y-%m-%d'),
                           w1.num_code,
                           w1.device_group_id,
                           dg.device_group deviceGroup,
                           dg.product_id,
                           t2.label brand,
                           t3.name machine,
                    min(w1.id) id
                    from b_iot_state w1
                             left join b_customer_device_group dg on dg.id = w1.device_group_id
                             left join b_product_tree t3 on t3.id = dg.product_id
                             left join st_dict_item t2 on dg.device_group = t2.value and t2.dict_id = 20700
                             left join b_iot_numeric_code w2 on w2.num_code = w1.num_code
                    where w1.customer_id = #{qo.customerId}
                      and w2.is_statistics = 1
                      and w1.created_at &gt;= #{qo.startTime}
                      and w1.created_at &lt;= #{qo.endTime}
                    group by date_format(w1.created_at, '%Y-%m-%d'), w1.num_code, w1.device_group_id, dg.device_group,dg.product_id,
                             t2.label, t3.name) t1 on t1.id = t.id
    </select>
    <select id="getStatisticsList" resultType="com.hightop.benyin.report.application.vo.FaultStatisiesData">
        select state_describe,t.num_code,t4.name brand ,t3.name series,count(1)
        from b_iot_state t
                 left join b_customer_device_group t1 on t1.id = t.device_group_id
                 left join b_product_tree t2 on t2.id = t1.product_id
                 left join b_product_tree t3 on t3.id = t2.parent_id
                 left join b_product_tree t4 on t4.id = t3.parent_id
        where 1=1
        <if test="null != qo.brand and '' != qo.brand ">
            and t4.name like concat ('%',#{qo.brand},'%')
        </if>
        <if test="null != qo.series and '' != qo.series ">
            and t3.name like concat ('%',#{qo.series},'%')
        </if>
        <if test="null != qo.numCode and '' != qo.numCode ">
            and t.num_code like concat ('%',#{qo.numCode},'%')
        </if>
        <if test="null != qo.describe and '' != qo.describe ">
            and t.state_describe like concat ('%',#{qo.describe},'%')
        </if>
        group by state_describe,num_code,t4.name,t3.name
        order by count(1) desc
    </select>

</mapper>