<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.order.infrastructure.mapper.TradeOrderDetailMapper">

    <select id="monthlyArticleSaleList" resultType="com.hightop.benyin.report.application.vo.MonthlyArticleSaleVO">
        select date_format(t.created_at, '%Y-%m') currMonth
             ,t.article_code,t1.name articleName,t1.number_oem,t1.type,t1.manufacturer_channel
             ,sum(t.item_num) number,
              sum(ifnull(pay_amount,0)) amount,
             sum( t.actual_pay_amount  ) actureAmount,
              sum(ifnull(t.refund_amount,0)) returnAmount,sum(reverse_num) returnNum
        from tb_trade_order_detail t
        left join tb_trade_order tt on tt.id = t.trade_order_id
                 left join b_storage_article t1 on t1.code=t.article_code
        where t.order_status!='CLOSED'
        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth  and '' != qo.endMonth ">
            and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>

        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and  t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and  t1.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>

        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and  t1.manufacturer_channel = #{qo.manufacturerChannel}
        </if>

        <if test="null != qo.type and '' != qo.type ">
            and  t1.type = #{qo.type}
        </if>

        <if test="null != qo.unit and '' != qo.unit ">
            and  t2.unit = #{qo.unit}
        </if>

        group by  date_format(t.created_at, '%Y-%m'),t.article_code,t1.name,t1.number_oem,t1.type,t1.manufacturer_channel
        order by date_format(t.created_at, '%Y-%m') desc
    </select>

    <select id="monthlyDistributeSaleList" resultType="com.hightop.benyin.report.application.vo.MonthlyDistributeSaleVO">
        select date_format(t.created_at, '%Y-%m') currMonth,
               t2.name area,t3.name city,t4.name province,sum(itemNum) number,sum(t.actual_amount) amount,
               sum(t.refund_amount) returnAmount,sum( t.paid_amount ) actureAmount
        from tb_trade_order t
                 left join (select trade_order_id, sum(item_num) itemNum,sum(reverse_num) returnNum from tb_trade_order_detail group by trade_order_id) t1 on t1.trade_order_id=t.id
                 left join b_region t2 on t2.code = t.consignee_region_code
                 left join b_region t3 on t3.code = t2.parent_code
                 left join b_region t4 on t4.code = t3.parent_code
        where t.order_status!='CLOSED'

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth  and '' != qo.endMonth ">
            and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>
        group by  date_format(t.created_at, '%Y-%m'),t2.name ,t3.name ,t4.name
        order by date_format(t.created_at, '%Y-%m') desc
    </select>

    <select id="monthlyMechineSaleList" resultType="com.hightop.benyin.report.application.vo.SalesStatisMechineVo">
        select ifnull(t.name,'无')  series, sum(t.item_num) number,sum(t.pay_amount) amount,
        sum(t.receive_num) receiveNum,
        sum( t.actual_pay_amount  ) actureAmount,
        sum(t.reverse_num) refundNum, sum(t.refund_amount) refundAmount from (
        select distinct t.id,t.pay_amount,t4.name,t.item_num,t.reverse_num,t.actual_pay_amount,t.refund_amount,t.receive_num
        from tb_trade_order_detail t
        left join tb_trade_order tt on tt.id = t.trade_order_id
        left join b_storage_article t1 on t1.code= t.article_code
        left join b_product_part_bom t2 on t2.part_id=t1.part_id
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = t3.parent_id
        where t.order_status !='CLOSED' and t1.product_id is null
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t1.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t1.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND  t2.product_id in
            <foreach collection="qo.productIds" item="id" separator=" , " open="(" close=")">
                #{id}
            </foreach>
        </if>

        ) t  group by t.name
    </select>
    <select id="getMonthlyStatisData" resultType="com.hightop.benyin.report.application.vo.MonthlyStatisDataVo">
        select sum(t.item_num) number,sum(t.pay_amount) totalAmount,
        sum( t.actual_pay_amount ) actureAmount,
        sum(t.delivery_num) deliveryNum,
        sum(t.reverse_num) returnNum, sum(t.refund_amount) returnAmount
        from tb_trade_order_detail t
        left join tb_trade_order tt on tt.id = t.trade_order_id
        left join b_storage_article t1 on t1.code=t.article_code
        where t.order_status !='CLOSED'
        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth  and '' != qo.endMonth ">
            and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>

        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and  t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and  t1.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>

        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and  t1.manufacturer_channel = #{qo.manufacturerChannel}
        </if>

        <if test="null != qo.type and '' != qo.type ">
            and  t1.type = #{qo.type}
        </if>

        <if test="null != qo.unit and '' != qo.unit ">
            and  t1.part_id in (select part_id from b_product_part_bom where unit=#{qo.unit})
        </if>
    </select>
    <select id="getMonthlyMechineSaleList" resultType="com.hightop.benyin.order.application.vo.MonthMechineSalesVo">
        select t.*
             ,ifnull(t1.blackAmount,0) blackAmount,ifnull(t1.blackNum,0) blackNum,ifnull(t1.avgBlackAmount,0) avgBlackAmount
             ,ifnull(t2.colorAmount,0) colorAmount,ifnull(t2.colorNum,0) colorNum,ifnull(t2.avgColorAmount,0) avgColorAmount
        from (
                 select  date_format(t.created_at,'%Y-%m') currMonth,sum(t.paid_amount) totalAmount
                 from tb_trade_order t
                          join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                 where t.is_mechine = 1
                   and t.order_status!='CLOSED' group by date_format(t.created_at,'%Y-%m')
             ) t
                 left join (
            select  date_format(t.created_at,'%Y-%m') currMonth,sum(t.paid_amount) blackAmount,count(1) blackNum,round(avg(t.paid_amount/100),2) avgBlackAmount
            from tb_trade_order t
                     join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                     join b_storage_article  t2 on t2.code = t1.article_code
                     join b_product_device  t3 on t3.product_id = t2.product_id
            where t2.product_id is not null and t3.color='1702'
              and t.order_status!='CLOSED' group by date_format(t.created_at,'%Y-%m')
        ) t1 on t1.currMonth=t.currMonth
                 left join (
            select  date_format(t.created_at,'%Y-%m') currMonth,sum(t.paid_amount) colorAmount,count(1) colorNum,round(avg(t.paid_amount/100),2) avgColorAmount
            from tb_trade_order t
                     join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                     join b_storage_article  t2 on t2.code = t1.article_code
                     join b_product_device  t3 on t3.product_id = t2.product_id
            where t2.product_id is not null and t3.color='1701'
              and t.order_status!='CLOSED' group by date_format(t.created_at,'%Y-%m')
        ) t2 on t2.currMonth=t.currMonth
        where 1=1
        <if test="null != qo.startMonth">
            and t.currMonth  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and t.currMonth  &lt;= #{qo.endMonth}
        </if>
    </select>
    <select id="getMonthlyMechineSaleSummary" resultType="com.hightop.benyin.order.application.vo.MonthMechineSalesVo">
        select sum(t.totalAmount) totalAmount
             ,sum(ifnull(t1.blackAmount,0)) blackAmount,sum(ifnull(t1.blackNum,0)) blackNum
             ,sum(ifnull(t2.colorAmount,0)) colorAmount,sum(ifnull(t2.colorNum,0)) colorNum
        from (
                 select  date_format(t.created_at,'%Y-%m') currMonth,sum(t.paid_amount) totalAmount
                 from tb_trade_order t
                          join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                 where t.is_mechine = 1
                   and t.order_status!='CLOSED' group by date_format(t.created_at,'%Y-%m')
             ) t
                 left join (
            select  date_format(t.created_at,'%Y-%m') currMonth,sum(t.paid_amount) blackAmount,count(1) blackNum
            from tb_trade_order t
                     join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                     join b_storage_article  t2 on t2.code = t1.article_code
                     join b_product_device  t3 on t3.product_id = t2.product_id
            where t2.product_id is not null and t3.color='1702'
              and t.order_status!='CLOSED' group by date_format(t.created_at,'%Y-%m')
        ) t1 on t1.currMonth=t.currMonth
                 left join (
            select  date_format(t.created_at,'%Y-%m') currMonth,sum(t.paid_amount) colorAmount,count(1) colorNum
            from tb_trade_order t
                     join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                     join b_storage_article  t2 on t2.code = t1.article_code
                     join b_product_device  t3 on t3.product_id = t2.product_id
            where t2.product_id is not null and t3.color='1701'
              and t.order_status!='CLOSED' group by date_format(t.created_at,'%Y-%m')
        ) t2 on t2.currMonth=t.currMonth
        where 1=1
        <if test="null != qo.startMonth">
            and t.currMonth  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and t.currMonth  &lt;= #{qo.endMonth}
        </if>
    </select>

    <sql id="detailCondition">

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and tao.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and tao.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and bc.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and bc.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.orderNum and '' != qo.orderNum ">
            and tao.order_num like concat ('%',#{qo.orderNum},'%')
        </if>

        <if test="null != qo.itemCode and '' != qo.itemCode ">
            and ti.code like concat ('%',#{qo.itemCode},'%')
        </if>
        <if test="null != qo.itemName and '' != qo.itemName ">
            and ti.name like concat ('%',#{qo.itemName},'%')
        </if>
        <if test="null != qo.name and '' != qo.name ">
            and sa.name like concat ('%',#{qo.name},'%')
        </if>
        <if test="null != qo.code and '' != qo.code ">
            and sa.code like concat ('%',#{qo.code},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and sa.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>
        <if test="null != qo.orderType and '' != qo.orderType ">
            and tao.order_type like concat ('%',#{qo.orderType},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and tod.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and tod.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null != qo.gePayAmount ">
            and  tod.pay_amount &gt;= #{qo.gePayAmount}
        </if>
        <if test="null != qo.lePayAmount ">
            and  tod.pay_amount &lt;= #{qo.lePayAmount}
        </if>

        <if test="null != qo.isMechine ">
            and  tao.is_mechine = #{qo.isMechine}
        </if>

        <if test="null != qo.geActualPayAmount ">
            and  tod.actual_pay_amount &gt;= #{qo.geActualPayAmount}
        </if>
        <if test="null != qo.leActualPayAmount ">
            and  tod.actual_pay_amount &lt;= #{qo.leActualPayAmount}
        </if>
    </sql>
    <select id="getTradeOrderDetailList" resultType="com.hightop.benyin.order.application.vo.TradeOrderDetailExtendsVo">
        select tod.id,
               bc.seq_id                customerSeqId,
               bc.name                  customerName,
               tao.order_num            orderNum,
               tao.created_at           createdAt,
               tao.order_status           orderStatus,
               cs.name                  buyerName,
               ti.code                  itemCode,
               tod.article_code,
             ti.name                  itemName,
               sa.name                 name,
               sa.code,
               sa.number_oem            numberOem,
               sa.manufacturer_channel  manufacturerChannel,
               tod.item_num             itemNum,
               tod.sale_unit_price      saleUnitPrice,
               tod.actual_unit_price    actualUnitPrice,
               tod.discount_amount      discountAmount,
               tod.pay_amount           payAmount,
               sa.unit,
               tr2.name                 province,
               tr1.name                 city,tao.pay_time,
               tr.name                  area,tao.order_type,ifnull(tod.actual_pay_amount,0) actualPayAmount
        from tb_trade_order_detail tod
                 left join tb_trade_order tao on tao.id = tod.trade_order_id
                 left join b_region tr on tr.code = tao.consignee_region_code
                 left join b_region tr1 on tr1.code = tr.parent_code
                 left join b_region tr2 on tr2.code = tr1.parent_code
                 left join b_customer bc on tao.customer_id = bc.id
                 left join b_customer_staff cs on cs.id = tao.buyer_id
                 left join tb_item ti on ti.id = tod.item_id
                 left join b_storage_article sa on sa.code = tod.article_code
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            LEFT JOIN tb_part_product_tree t3 ON (t3.part_id = sa.part_id)
        </if>
        where 1=1
        <if test="null!=qo.orderStatusList and !qo.orderStatusList.isEmpty()">
            AND tod.order_status in
            <foreach collection="qo.orderStatusList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>

        <include refid="detailCondition"></include>

        order by tod.created_at desc
    </select>

    <select id="getTradeOrderDetailData" resultType="com.hightop.benyin.order.application.vo.TradeOrderDetailData">
        select sum(t.itemNum) itemNum,sum(payAmount) payAmount,sum(discountAmount) discountAmount,sum(actualPayAmount) actualPayAmount,sum(t.shipping_fee) shippingFee
        from (
        select
        sum(tod.item_num)             itemNum,
        sum(tod.discount_amount)             discountAmount,
        sum(tod.pay_amount)           payAmount,
        tao.shipping_fee,
        tao.order_num,
        sum( ifnull(tod.actual_pay_amount,0)) actualPayAmount
        from tb_trade_order_detail tod
        left join tb_trade_order tao on tao.id = tod.trade_order_id
        left join b_region tr on tr.code = tao.consignee_region_code
        left join b_region tr1 on tr1.code = tr.parent_code
        left join b_region tr2 on tr2.code = tr1.parent_code
        left join b_customer bc on tao.customer_id = bc.id
        left join b_customer_staff cs on cs.id = tao.buyer_id
        left join tb_item ti on ti.id = tod.item_id
        left join b_storage_article sa on sa.code = tod.article_code
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            LEFT JOIN tb_part_product_tree t3 ON (t3.part_id = sa.part_id)
        </if>
        where 1=1
        <if test="null!=qo.orderStatusList and !qo.orderStatusList.isEmpty()">
            AND tod.order_status in
            <foreach collection="qo.orderStatusList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>
        <include refid="detailCondition"></include>

        group by tao.order_num
        ) t
    </select>

    <select id="getFinanceSalesList" resultType="com.hightop.benyin.statistics.application.vo.FinanceSalesVO">
        select *
        from (select distinct t1.id,
        date_format(t4.created_at, '%Y-%m-%d') createDate,
        t1.order_num code,
        1 type,
        t4.batch_code,
        t2.name customerName,
        t2.seq_id customerCode,
        t3.code article_code,
        t3.name articleName,
        t3.manufacturer_channel,
        t3.unit,
        t3.number_oem,
        t5.license,
        <!-- 如果相同销售单同物品的出库数量 大于里面的销售单数量销售的数据数量 说明出库数量出了相同的物品，但是销售单价格不一样，所以要取销售单里面的数量-->
        case when (t4.number is null OR t4.number > t.itemNum) THEN t.itemNum else t4.number end num,
        round(t.actual_unit_price / 100, 2) price,
        case when (t4.number is null OR t4.number > t.itemNum) then (t.itemNum*round(t.actual_unit_price / 100, 2))  else  (t4.number*round(t.actual_unit_price / 100, 2)) end amount,
        case when (t4.number is null OR t4.number > t.itemNum) then round((t.itemNum*round(t.actual_unit_price , 2) ) / 113, 2) else  round((t4.number*round(t.actual_unit_price , 2) ) / 113, 2) end noTaxAmount,
        case when (t4.number is null OR t4.number > t.itemNum) then round((t.itemNum*round(t.actual_unit_price , 2) ) / 100, 2) - round((t.itemNum*round(t.actual_unit_price , 2) ) / 113, 2)
        else  round((t4.number*round(t.actual_unit_price , 2) ) / 100, 2) - round((t4.number*round(t.actual_unit_price , 2) ) / 113, 2) end taxAmount,
        t4.price purchasePrice,
        t4.tax,
        <!--t4.purchaseAmount,
        round(t4.purchaseAmount / 1.13, 2) purchaseNoTaxAmount,
        round(t4.purchaseAmount - round(t4.purchaseAmount / 1.13, 2), 2) purchaseTaxAmount,-->
        case when (t4.number is null OR t4.number > t.itemNum) then (t.itemNum * t4.price)  else  (t4.number * t4.price) end purchaseAmount,
        round(case when (t4.number is null OR t4.number > t.itemNum) then (t.itemNum * t4.price)  else  (t4.number * t4.price) end / 1.13, 2) purchaseNoTaxAmount,
        round(case when (t4.number is null OR t4.number > t.itemNum) then (t.itemNum * t4.price)  else  (t4.number * t4.price) end - round(case when (t4.number is null OR t4.number > t.itemNum) then (t.itemNum * t4.price)  else  (t4.number * t4.price) end / 1.13, 2), 2) purchaseTaxAmount,
        t1.order_status status
        from (select trade_order_id,article_code,actual_unit_price,sum(item_num) itemNum from tb_trade_order_detail group by trade_order_id,article_code,actual_unit_price)  t
        left join tb_trade_order t1 on t1.id = t.trade_order_id
        left join b_customer t2 on t2.id = t1.customer_id
        left join b_storage_article t3 on t3.code = t.article_code
        left join b_customer_business t5 on t5.customer_id = t2.id
        left join (select t.flow_id,MAX(t.created_at) as created_at,
        t.shop_waybill,
        t.code,
        t.batch_code,
        sum(t.number) number,
        sum(round(t.price * t.number)) purchaseAmount,
        t.price,
        t.tax,
        t.taxRate
        from (select distinct t.id,date_format(t.created_at, '%Y-%m-%d') created_at,
        t.flow_id,
        t1.shop_waybill,
        t.code,
        t.batch_code,
        t.number,
        ifnull(t2.tax, 13) tax,
        ifnull(t2.tax, 1.13) taxRate,
        round(t2.price / 100, 2) price
        from b_storage_warehouse_flow t
        join b_storage_out_warehouse t1 on t1.out_warehouse_id = t.flow_id
        join b_storage_inventory_batch t2
        on t2.code = t.code and t2.batch_code = t.batch_code
        where t.in_out_type = 2
        and t1.out_type = 'shopping_mall') t
        group by t.flow_id, t.shop_waybill, t.code, t.batch_code ) t4
        on t4.shop_waybill = t1.order_num and t4.code = t.article_code
        where t1.order_type = 'SALE'
        and t1.order_status != 'CLOSED'
        union all
        select distinct t.id, date_format(t1.audit_date, '%Y-%m-%d') createDate,
        tto.order_num code,0 type,
        t4.batch_code,
        t2.name customerName,
        t2.seq_id customerCode,
        t3.code article_code,
        t3.name articleName,
        t3.manufacturer_channel,
        t3.unit,
        t3.number_oem,
        t5.license,
        t.reverse_item_num num,
        round(t.refund_unit_price / 100, 2) price,
        round(t.refund_amount / 100, 2) amount,
        round(t.refund_amount / 113, 2) noTaxAmount,
        round(t.refund_amount / 100, 2)-round(t.refund_amount / 113, 2) taxAmount,
        t4.price purchasePrice,t4.tax,
        t4.purchaseAmount,
        round(t4.purchaseAmount/1.13,2) purchaseNoTaxAmount,
        round(t4.purchaseAmount-round(t4.purchaseAmount/1.13,2)) purchaseTaxAmount,
        tto.order_status status
        from tb_reverse_order_detail t
        join tb_trade_order_detail tt on tt.id = t.trade_order_detail_id
        left join b_reverse_order t1 on t1.id = t.reverse_order_id
        left join tb_trade_order tto on tto.id = t.trade_order_id
        left join b_customer t2 on t2.id = tto.customer_id
        left join b_storage_article t3 on t3.code = t.article_code
        left join b_customer_business t5 on t5.customer_id = t2.id
        left join (select t.flow_id, t.shop_waybill,t.code,t.batch_code,sum(t.number) number, round(t.price*t.number)
        purchaseAmount, t.price,t.tax,t.taxRate
        from (select distinct t.id,t.flow_id, t1.shop_waybill,t.code,t.batch_code,t.number ,ifnull(t2.tax, 13)
        tax,ifnull(t2.tax, 1.13) taxRate, round(t2.price / 100, 2) price
        from b_storage_warehouse_flow t
        join b_storage_out_warehouse t1 on t1.out_warehouse_id = t.flow_id
        join b_storage_inventory_batch t2 on t2.code = t.code and t2.batch_code = t.batch_code
        where t.in_out_type=2 and t1.out_type='shopping_mall'
        ) t
        group by t.flow_id, t.shop_waybill,t.code,t.batch_code ) t4 on t4.shop_waybill =
        tto.order_num and t4.code = t.article_code
        where tto.order_type='SALE' and t1.process_status !='REJECT'

        union all
        select distinct t.id, date_format(t.created_at, '%Y-%m-%d') createDate,
        t1.work_order_code code,
        2 type,
        t4.batch_code,
        t5.name customerName,
        t5.seq_id customerCode,
        t6.code article_code,
        t6.name articleName,
        t6.manufacturer_channel,
        t6.unit,
        t6.number_oem,
        t8.license,
        t4.num ,
        round(t.sale_unit_price / 100, 2) price,
        (t4.num*round(t.sale_unit_price / 100, 2) ) amount,
        round((t4.num*round(t.sale_unit_price , 2) ) / 113, 2) noTaxAmount,
        round((t4.num*round(t.sale_unit_price , 2) ) / 100, 2) - round((t4.num*round(t.sale_unit_price , 2) )
        / 113, 2) taxAmount,
        round(t7.price / 100, 2) purchasePrice,
        ifnull(t7.tax, 13) tax,
        (t4.num*round(t7.price / 100, 2)) purchaseAmount,
        round((t4.num*round(t7.price / 100, 2)) / 1.13, 2) purchaseNoTaxAmount,
        round((t4.num*round(t7.price / 100, 2)) - round((t4.num*round(t7.price / 100, 2)) / 1.13, 2)) purchaseTaxAmount,
        t2.status status
        from tb_replace_detail t
        left join tb_replace_order t1 on t1.id =t.replace_order_id
        left join tb_work_order t2 on t2.code=t1.work_order_code
        left join tb_item_store t3 on t3.id=t.item_store_id
        left join tb_item_store_log t4 on t4.item_store_id=t.item_store_id and t4.operate_code=t2.code
        left join b_customer t5 on t5.id = t1.customer_id
        left join b_storage_article t6 on t6.code=t3.article_code
        left join b_storage_inventory_batch t7 on t7.code=t3.article_code and t7.batch_code=t4.batch_code
        left join b_customer_business t8 on t8.customer_id = t5.id
        where t.deleted=0  and t4.deleted=0 and t2.ser_type in  ('WARRANTY', 'SCATTERED', 'NO_WARRANTY', 'QA', 'QA_COMPONENT', 'MAINTENANCE', 'OTHER') AND t3.sku_source = 'ENGINEER_APPLY'
        ) t
        where 1=1
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createDate &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createDate &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type = #{qo.type}
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t.articleName like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null!=qo.status and !qo.status.isEmpty()">
            AND t.status in
            <foreach collection="qo.status" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and t.customerName like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t.customerCode like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and t.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t.license like concat ('%',#{qo.license},'%')
        </if>
        order by t.code desc
    </select>

    <select id="getFinanceApply" resultType="com.hightop.benyin.statistics.application.vo.FinanceApplyVO">
        select t.*,t.type applyType
        from (
        select t.*,
        round(t.amount / t.taxRate, 2) noTaxAmount,
        t.amount - round((t.amount / t.taxRate), 2) taxAmount
        from (select  distinct t.id,date_format(tt.created_at, '%Y-%m-%d') createDate,
        t1.order_num code,
        case when t1.order_type='GIFT' then 4 else '1' end type ,
        t2.name customerName,
        t2.seq_id customerCode,
        t3.code article_code,
        t3.name articleName,
        t3.unit,
        t3.manufacturer_channel,
        t4.license,
        tt.number num,
        tt.name warehouseName,
        round(tt.price / 100, 2) price,
        round( round(tt.price / 100, 2) * tt.number, 2) amount,
        1.13 taxRate,
        13 tax
        from tb_trade_order_detail t
        left join tb_trade_order t1 on t1.id= t.trade_order_id
        join (select distinct t.id,tw.name,t.code,t.number,t1.shop_waybill,t2.price,t.created_at
        from b_storage_warehouse_flow t
        join b_storage_out_warehouse t1 on t1.out_warehouse_id = t.flow_id
        join b_storage_inventory_batch t2 on t2.code = t.code and t2.batch_code = t.batch_code
        left join b_storage_warehouse tw on tw.id = t2.warehouse_id
        where t.type='shopping_mall') tt on tt.shop_waybill = t1.order_num and tt.code =t.article_code
        left join b_customer t2 on t2.id = t1.customer_id
        left join b_storage_article t3 on t3.code = t.article_code
        left join b_customer_business t4 on t4.customer_id = t2.id
        where t1.order_type in ('APPLY','GIFT') and t1.order_status!='CLOSED'
        ) t
        union all
        select t.*,
        round(t.amount / t.taxRate, 2) noTaxAmount,
        t.amount - round((t.amount / t.taxRate), 2) taxAmount
        from (select  distinct t.id,date_format(t1.completed_at, '%Y-%m-%d') createDate,
        t1.code,
        3 type ,
        t7.product_name customerName,
        t1.machine_num customerCode,
        t3.code article_code,
        t3.name articleName,
        t3.unit,
        t3.manufacturer_channel,
        '' license,
        t.num,
        tw.name warehouseName,
        round(t6.price / 100, 2) price,
        round( round(t6.price / 100, 2) * t.num, 2) amount,
        1.13 taxRate,
        13 tax
        from tb_machine_repair_replace t
        left join tb_machine_repair t1 on t1.id= t.repair_id
        join tb_item_store_log tt on tt.operate_code = t1.code and tt.item_store_id =t.item_store_id AND tt.deleted = 0
        left join tb_item_store t4 on t4.id = t.item_store_id
        left join b_storage_article t3 on t3.code = t4.article_code
        left join b_storage_inventory_batch t6 on t6.code = t4.article_code and t6.batch_code = tt.batch_code
        left join b_storage_warehouse tw on tw.id = t6.warehouse_id
        left join b_machine t7 on t7.machine_num = t1.machine_num
        where t.deleted=0
        ) t
        union all
        select t.*,
        round(t.amount / t.taxRate, 2) noTaxAmount,
        t.amount - round((t.amount / t.taxRate), 2) taxAmount
        from (select  distinct t.id,date_format(t1.created_at, '%Y-%m-%d') createDate,
        t1.work_order_code code,
        2 type ,
        t8.name customerName,
        t8.seq_id customerCode ,
        t3.code article_code,
        t3.name articleName,
        t3.unit,
        t3.manufacturer_channel,
        t9.license,
        t.num,
        tw.name warehouseName,
        round(t6.price / 100, 2) price,
        round( round(t6.price / 100, 2) * t.num, 2) amount,
        1.13 taxRate,
        13 tax
        from tb_replace_detail t
        left join tb_replace_order t1 on t1.id= t.replace_order_id
        join tb_item_store_log tt on tt.operate_code = t1.work_order_code and tt.item_store_id =t.item_store_id AND tt.deleted = 0
        left join tb_item_store t4 on t4.id = t.item_store_id
        left join b_storage_article t3 on t3.code = t4.article_code
        left join b_storage_inventory_batch t6 on t6.code = t4.article_code and t6.batch_code = tt.batch_code
        left join b_storage_warehouse tw on tw.id = t6.warehouse_id
        left join tb_work_order t7 on t7.code = t1.work_order_code
        left join b_customer t8 on t8.id = t7.customer_id
        left join b_customer_business t9 on t9.customer_id = t8.id
        where t.deleted=0 and t1.deleted=0 and t7.ser_type!='SCATTERED') t

        ) t
        where 1=1
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createDate &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createDate &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type = #{qo.type}
        </if>

        <if test="null != qo.applyType and '' != qo.applyType ">
            and t.type = #{qo.applyType}
        </if>

        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t.articleName like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and t.customerName like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t.customerCode like concat ('%',#{qo.customerCode},'%')
        </if>
        <if test="null != qo.license and '' != qo.license ">
            and t.license like concat ('%',#{qo.license},'%')
        </if>
        order by code desc
    </select>
    <select id="getFinanceMachineSales" resultType="com.hightop.benyin.statistics.application.vo.FinanceMachineSalesVO">
        select t.*,
        round(t.amount / 1.13, 2)              noTaxAmount,
        t.amount - round((t.amount / 1.13), 2) taxAmount ,
        round(t.purchaseAmount / t.taxRate, 2)              purchaseNoTaxAmount,
        t.purchaseAmount - round((t.purchaseAmount / t.taxRate), 2) purchaseTaxAmount
        from (select distinct date_format(t.time, '%Y-%m-%d')                        createDate,
        t.source_code code,
        case when t.source_type='shopping_mall' then 0 else 1  end type,
        t1.contract_name contractName,
        t2.seq_id                                                      customerCode,
       case when tb.license is not null  then tb.license else t2.name end                                                     customerName,
        t3.name brand,
        t5.product_name                                                      productName,
        t4.host_type,
        t4.device_on,
        t5.product_id,
        t4.machine_num,
        1 num,
        tb.license,
        round(t4.full_amount / 100, 2)                                       price,
        round(t4.full_amount / 100, 2)                                    amount,
        case when t6.tax is null then 1.13 else 1 + t6.tax / 100 end taxRate,
        ifnull(t6.tax, 13)                                           tax,
        round(ifnull(t5.purchase_price,0) / 100, 2)               purchasePrice,
        round(ifnull(t5.purchase_price,0) / 100, 2) purchaseAmount
        from b_machine_inout_flow t
        left join b_customer_contract t1 on t1.code = t.source_code
        left join b_customer t2 on t2.id = t1.customer_id
        left join b_customer_business tb on tb.customer_id = t2.id
        left join b_customer_contract_buy t4 on t4.contract_code = t.source_code
        left join b_machine t5 on t5.machine_num = t4.machine_num
        left join b_manufacturer t6 on t6.id = t5.manufacturer_id
        left join b_product_tree tp on tp.id = t5.product_id and t5.host_type='2008'
        left join b_product_tree t3 on t3.id = substr(tp.full_id_path, 2, 20)
        where t.source_type in('shopping_mall','buy_return') and t1.contract_type='1201'
          AND t1.status != 'CANCEL') t
        where 1=1
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createDate &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createDate &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type = #{qo.type}
        </if>
        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null != qo.hostType and '' != qo.hostType ">
            and t.host_type = #{qo.hostType}
        </if>
        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and t.customerName like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerCode and '' != qo.customerCode ">
            and t.customerCode like concat ('%',#{qo.customerCode},'%')
        </if>

        order by t.createDate desc
    </select>
</mapper>
