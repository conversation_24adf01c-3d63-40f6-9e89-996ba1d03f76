<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.storage.infrastructure.mapper.MonthMachineInventoryMapper">
    <select id="monthTask" resultType="com.hightop.benyin.storage.infrastructure.entity.MonthMachineInventory">
        SELECT
            #{month} monthly,
            m.machine_num,
            m.product_id,
            m.product_name,
            m.tag_name,
            m.host_type,
            m.manufacturer_id,
            m.device_sequence,
            m.device_on,
            m.percentage,
            m.device_status,
            m.black_white_counter,
            m.color_counter,
            m.five_colour_counter,
            m.location,
            IFNULL(m.purchase_price,0) purchase_price,
            (CASE WHEN mf.tax IS NULL THEN 13 ELSE mf.tax END) tax,
            (IFNULL(m.purchase_price,0) + IFNULL(mr.amount,0) - IFNULL(mb.amount,0)) no_tax_amount,
            (ROUND((IFNULL(m.purchase_price,0) + IFNULL(mr.amount,0) - IFNULL(mb.amount,0))/ (CASE WHEN mf.tax IS NULL THEN 1.13 ELSE (100 + mf.tax)/100 END),2) ) tax_amount,
            m.source,
            m.`status`,
            m.created_at in_storage_time
        FROM b_machine m
                 LEFT JOIN b_manufacturer mf ON m.manufacturer_id = mf.id
                 LEFT JOIN	tb_machine_repair mr ON mr.machine_num = m.machine_num AND mr.`status` = 'COMPLETED'
                 LEFT JOIN tb_machine_disassemble mb ON mb.machine_num = m.machine_num AND mb.`status` = 'PASS'
        WHERE m.deleted = 0
        and m.status = 'INVENTORY'
    </select>
</mapper>