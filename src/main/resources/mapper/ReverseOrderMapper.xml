<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.reverse.infrastructure.mapper.ReverseOrderMapper">

    <select id="listReverseOrder" resultType="com.hightop.benyin.reverse.application.vo.ReverseOrderVo">
        select
            t1.id as id ,t1.apply_time applyTime,
            t1.reverse_order_id as reverseOrderId ,
            t1.trade_order_id as tradeOrderId ,
            t1.seq_id as seqId ,
            t2.tel ,
            t3.actual_amount as paidAmount ,
            (select label from  st_dict_item where value = t1.reverse_type) AS reverseType,
            (select label from  st_dict_item where value = t1.audit_status) AS auditStatus,
            ifnull((select label from  st_dict_item where value = t4.in_status),'/') as returnStatus
        from b_reverse_order t1
        left join b_customer_staff t2 on t1.staff_id = t2.id
        left join tb_trade_order t3 on t1.trade_order_id = t3.id
        left join b_storage_in_warehouse t4 on t1.reverse_order_id = t4.reverse_order_id
        where t1.deleted = 0
        <if test="null != qo.auditStatus and '' != qo.auditStatus">
            and t1.audit_status = #{qo.auditStatus}
        </if>

        <if test="null != qo.reverseType and '' != qo.reverseType">
            and t1.reverse_type = #{qo.reverseType}
        </if>

        <if test="null != qo.returnOrderStatus and '' != qo.returnOrderStatus">
            and t4.in_status = #{qo.returnOrderStatus}
        </if>

        <if test="null != qo.seqIdOrPhone and '' != qo.seqIdOrPhone">
            and ( t2.tel like concat ('%',#{qo.seqIdOrPhone},'%') or t1.seq_id like concat ('%',#{qo.seqIdOrPhone},'%') )
        </if>
        <if test="null != qo.buyerId and '' != qo.buyerId">
            and t1.staff_id = #{qo.buyerId}
        </if>
        order by t1.id desc
    </select>





</mapper>
