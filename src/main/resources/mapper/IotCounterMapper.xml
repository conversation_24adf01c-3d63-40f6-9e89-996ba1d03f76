<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.iot.infrastructure.mapper.IotCounterMapper">

    <select id="getLastIotCounterByCustomerId" resultType="com.hightop.benyin.iot.infrastructure.entity.IotCounter">
        select * from b_iot_counter where customer_id = ${customerId} order by created_at desc limit 1
    </select>

    <select id="initIotPointer" resultType="com.hightop.benyin.iot.infrastructure.entity.IotPrintCount">

        select t.customer_id customerId,
        t.device_group_id deviceGroupId,
        t.firstId,
        tt.lastId,
        (select sum(tr.color_exclude)
        from tb_repair_report tr left join tb_work_order two on two.id=tr.work_order_id where two.current_process in
        ('WAIT_CONFIRM','DONE')
        and tr.customer_id=t.customer_id and tr.device_group_id=t.device_group_id
        and tr.created_at BETWEEN #{qo.reportTimeStart} AND #{qo.reportTimeEnd}) 'colorExclude',
        (select sum(tr.black_white_exclude)
        from tb_repair_report tr left join tb_work_order two on two.id=tr.work_order_id where two.current_process in
        ('WAIT_CONFIRM','DONE')
        and tr.customer_id=t.customer_id and tr.device_group_id=t.device_group_id
        and tr.created_at BETWEEN #{qo.reportTimeStart} AND #{qo.reportTimeEnd}) 'blackWhiteExclude',
        t1.device_group deviceGroup,
        t3.name brand,
        t2.name machine,
        t1.ser_type serType,
        t1.paper_type machinePaperType,
        tp.color colorType,
        'iot' dataSource,
        t4.salesman_id salesmanId,
        t4.salesman salesmanName,
        t1.operat_id enginnerId,
        t1.operat_name enginnerName
        from (SELECT customer_id,
        device_group_id,
        min(id) firstId
        FROM b_iot_counter
        WHERE report_time BETWEEN #{qo.reportTimeStart} AND #{qo.reportTimeEnd}
        <if test="null != qo.customerId ">
            and customer_id = #{qo.customerId}
        </if>
        <if test="null != qo.deviceGroupId ">
            and device_group_id = #{qo.deviceGroupId}
        </if>
         and status = 1
        group by customer_id, device_group_id
        order by report_time asc) t

        left join (SELECT customer_id,
        device_group_id,
        max(id) lastId
        FROM b_iot_counter
        WHERE report_time BETWEEN #{qo.reportTimeStart} AND #{qo.reportTimeEnd}
        and status = 1
        group by customer_id, device_group_id
        order by report_time desc) tt
        on tt.customer_id = t.customer_id and tt.device_group_id = t.device_group_id
        left join b_customer_device_group t1 on t1.id = t.device_group_id
        left join b_product_tree t2 on t2.id = t1.product_id
        left join b_product_device tp on tp.product_id = t2.id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        left join b_customer_business t4 on t4.customer_id = t.customer_id
    </select>

    <select id="getBasicInfo" resultType="com.hightop.benyin.iot.infrastructure.entity.IotPrintCount">
        select t.customer_id customerId,t.id deviceGroupId,t.device_group,t.paper_type,
        t.paper_type machinePaperType,t.ser_type serType,
        tp.color colorType,
        t4.salesman_id salesmanId,
        t4.salesman salesmanName,
        t.operat_id enginnerId,
        t.operat_name enginnerName,
        t3.name brand,
        t2.name machine
        from b_customer_device_group t
             left join b_product_tree t2 on t2.id = t.product_id
             left join b_product_device tp on tp.product_id = t2.id
             left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
             left join b_customer_business t4 on t4.customer_id = t.customer_id
        where t.id in
        <foreach collection="deviceGroupIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
</mapper>
