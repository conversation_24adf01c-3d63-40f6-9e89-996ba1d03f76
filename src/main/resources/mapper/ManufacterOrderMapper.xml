<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.purchase.infrastructure.mapper.ManufacterOrderMapper">

    <select id="monthlyPage" resultType="com.hightop.benyin.purchase.api.dto.ManufacterOrderStatisticsVo">
        SELECT
        DATE_FORMAT(t.created_at,'%Y-%m') AS yearMonth,
        sum(t.number) number,
        sum(t.refund_num) refundNum,
        sum(t.receive_num) receiveNum,
        sum(t.amount) amount,
        sum(t.refund_amount) refundAmount
        FROM b_manufacturer_order_goods t
        left join b_manufacturer_order t1 on t1.code = t.manufacturer_order_code
        WHERE t1.status not in ('REJECT','CLOSED')
        <if test="null != qo.yearMonthStart and '' != qo.yearMonthStart ">
            and DATE_FORMAT(t.created_at,'%Y-%m') &gt;= #{qo.yearMonthStart}
        </if>
        <if test="null != qo.yearMonthEnd and '' != qo.yearMonthEnd ">
            and DATE_FORMAT(t.created_at,'%Y-%m') &lt;= #{qo.yearMonthEnd}
        </if>
        group by DATE_FORMAT(t.created_at,'%Y-%m')
        ORDER BY DATE_FORMAT(t.created_at,'%Y-%m') DESC
    </select>
    <select id="salesSum" resultType="com.hightop.benyin.purchase.api.dto.ManufacterDeliveryVo">
        SELECT
        sum(t.number) number,
        sum(t.refund_num) refundNum,
        sum(t.receive_num) receiveNum,
        sum(t.amount) amount,
        sum(t.refund_amount) refundAmount
        FROM b_manufacturer_order_goods t
        left join b_manufacturer_order tt on tt.code = t.manufacturer_order_code
        LEFT JOIN b_storage_article t1 ON (t1.code = t.article_code)
        LEFT JOIN b_manufacturer t2 ON (t2.id = tt.manufacturer_id)
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            LEFT JOIN tb_part_product_tree t3 ON (t3.part_id = t1.part_id)
        </if>
        WHERE tt.status not in ('REJECT','CLOSED')

        <if test="null != qo.yearMonthStart and '' != qo.yearMonthStart ">
            and DATE_FORMAT(t.created_at,'%Y-%m') &gt;= #{qo.yearMonthStart}
        </if>
        <if test="null != qo.yearMonthEnd and '' != qo.yearMonthEnd ">
            and DATE_FORMAT(t.created_at,'%Y-%m') &lt;= #{qo.yearMonthEnd}
        </if>

        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.receiveCompany and '' != qo.receiveCompany ">
            and tt.receive_company like concat ('%',#{qo.receiveCompany},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>

        <if test="null != qo.type and '' != qo.type ">
            and t1.type like concat ('%',#{qo.type},'%')
        </if>

        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and t1.manufacturer_channel like concat ('%',#{qo.manufacturerChannel},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>

    </select>


    <select id="companyPage" resultType="com.hightop.benyin.purchase.api.dto.ManufacterOrderStatisticsVo">
        SELECT
        tt.receive_company AS receiveCompany,
        sum(t.number) number,
        sum(t.refund_num) refundNum,
        sum(t.receive_num) receiveNum,
        sum(t.amount) amount,
        sum(t.refund_amount) refundAmount
        FROM b_manufacturer_order_goods t
        left join b_manufacturer_order tt on tt.code = t.manufacturer_order_code
        WHERE  tt.status not in ('REJECT','CLOSED')
        <if test="null != qo.receiveCompany and '' != qo.receiveCompany ">
            and tt.receive_company like concat ('%',#{qo.receiveCompany},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        group by tt.receive_company
        ORDER BY tt.receive_company DESC
    </select>

    <select id="articlePage" resultType="com.hightop.benyin.purchase.api.dto.ManufacterOrderStatisticsVo">
        SELECT
        sum(t.number) number,
        sum(t.amount) sumPrice,
        sum(t.receive_num) receiveNum,
        sum( t.refund_num) refundNum,
        sum( t.refund_amount) refundAmount,
        t.article_code,
        t1.part_id,
        t1.number_oem AS oemNumber,
        t1.name AS articleName,
        t.price ,
        t1.unit ,
        t1.type ,
        t1.manufacturer_channel AS manufacturerChannel
        FROM b_manufacturer_order_goods t
        left join b_manufacturer_order tt on tt.code = t.manufacturer_order_code
        LEFT JOIN b_storage_article t1 ON (t1.code = t.article_code)
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            LEFT JOIN tb_part_product_tree t3 ON (t3.part_id = t1.part_id)
        </if>
        WHERE tt.status not in ('REJECT','CLOSED')
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>


        <if test="null != qo.type and '' != qo.type ">
            and t1.type like concat ('%',#{qo.type},'%')
        </if>

        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and t1.manufacturer_channel like concat ('%',#{qo.manufacturerChannel},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>
        group by t.article_code,
        t.price,
        t1.part_id,
        t1.number_oem,
        t1.name,
        t1.unit,
        t1.type,
        t1.manufacturer_channel

        ORDER BY t.article_code asc

    </select>

    <select id="mechinePage" resultType="com.hightop.benyin.purchase.api.dto.PurchaseMechineVo">
        select   ifnull(t.name,'无')  series, sum(t.number) number,sum(t.amount) amount,
        sum(t.receive_num) receiveNum,
        sum(t.refund_num) refundNum, sum(t.refund_amount) refundAmount
        from (select  distinct  t.id,t4.name,t.number,t.amount,t.receive_num,t.refund_num,t.refund_amount
        from b_manufacturer_order_goods t
        left join b_manufacturer_order tt on tt.code = t.manufacturer_order_code
        left join b_storage_article t1 on t1.code = t.article_code
        left join b_product_part_bom t2 on t2.part_id = t1.part_id
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = t3.parent_id
        where tt.status not in ('REJECT', 'CLOSED')
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and  t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and  t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND  t2.product_id in
            <foreach collection="qo.productIds" item="id" separator=" , " open="(" close=")">
                #{id}
            </foreach>
        </if>
        )t
        group by t.name
        order by t.name asc
    </select>
    <select id="mechineSum" resultType="com.hightop.benyin.purchase.api.dto.ManufacterReturnDto">
        select sum(t.number) number,sum(t.amount) amount,
        sum(t.receive_num) receiveNum,
        sum(t.refund_num) refundNum, sum(t.refund_amount) refundAmount
        from b_purchase_order_goods t
        left join b_manufacturer_order tt on t1.code = t.manufacturer_order_code
        left join b_storage_article t1 on t1.code= t.article_code
        left join b_product_part_bom t2 on t2.part_id=t1.part_id
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = t3.parent_id
        where tt.status not in ('REJECT','CLOSED')
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t2.product_id in
            <foreach collection="qo.productIds" item="id" separator=" , " open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getFinancePurchase" resultType="com.hightop.benyin.statistics.application.vo.FinancePurchaseVO">
    select * from (select t.*,
                          round(t.amount / t.taxRate, 2)              noTaxAmount,
                          t.amount - round((t.amount / t.taxRate), 2) taxAmount
                   from (select date_format(t.created_at, '%Y-%m-%d')                        createDate,
                                t.code,
                                1                                                            type,
                                t4.code                                                      manufacturerCode,
                                t4.name                                                      manufacturerName,
                                t3.article_code,
                                t5.name                                                      articleName,
                                t5.manufacturer_channel,
                                t5.unit,
                                t3.number num,
                                round(t3.price / 100, 2)                                     price,
                                round(t3.amount / 100, 2)                                    amount,
                                case when t4.tax is null then 1.13 else 1 + t4.tax / 100 end taxRate,
                                ifnull(t4.tax, 13)                                           tax,
                                t.status,
                                t.invoice_status,
                                t.payment_time
                         from b_purchase_payment t
                                  left join b_purchase_payment_detail t1 on t1.payment_id = t.id
                                  left join b_manufacturer_order t2 on t2.id = t1.manufacter_order_id
                                  join b_manufacturer_order_goods t3 on t3.manufacturer_order_code = t2.code
                                  left join b_manufacturer t4 on t4.id = t2.manufacturer_id
                                  left join b_storage_article t5 on t5.code = t3.article_code
                         where t.status != 'CLOSED') t
                   union all
                   select t.*,
                          round(t.amount / t.taxRate, 2)              noTaxAmount,
                          t.amount - round((t.amount / t.taxRate), 2) taxAmount
                   from (select date_format(t.created_at, '%Y-%m-%d')                        createDate,
                                t.code,
                                0                                                            type,
                                t4.code                                                      manufacturerCode,
                                t4.name                                                      manufacturerName,
                                t6.article_code,
                                t5.name                                                      articleName,
                                t5.manufacturer_channel,
                                t5.unit,
                                t6.number num,
                                round(t6.price / 100, 2)                                     price,
                                round(t6.amount / 100, 2)                                    amount,
                                case when t4.tax is null then 1.13 else 1 + t4.tax / 100 end taxRate,
                                ifnull(t4.tax, 13)                                           tax,
                                t.status,
                                t.invoice_status,
                                t.payment_time
                         from b_purchase_payment t
                                  left join b_purchase_payment_detail t1 on t1.payment_id = t.id
                                  left join b_manufacturer_order t2 on t2.id = t1.manufacter_order_id
                                  left join b_manufacturer_order_goods t3 on t3.manufacturer_order_code = t2.code
                                  join b_manufacturer_return_goods t6 on t6.manufacturer_order_goods_id = t3.id
                                  left join b_manufacturer t4 on t4.id = t2.manufacturer_id
                                  left join b_storage_article t5 on t5.code = t6.article_code
                         where t.status != 'CLOSED') t
                   ) t where 1=1
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createDate &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createDate &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type = #{qo.type}
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t.articleName like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null!=qo.status and !qo.status.isEmpty()">
            AND t.status in
            <foreach collection="qo.status" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t.manufacturerCode like concat ('%',#{qo.manufacturerCode},'%')
        </if>

        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t.manufacturerName like concat ('%',#{qo.manufacturerName},'%')
        </if>
        order by code desc

    </select>
</mapper>
