<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.product.infrastructure.mapper.ProductPartBomMapper">

    <select id="pmInventoryPageList" resultType="com.hightop.benyin.product.application.vo.PmInventoryPageVo">
        select a.*,b.oem_number oemNumber,c.code articleCode,b.type productPartType,
               c.manufacturer_goods_name manufacturerGoodsName,
               c.manufacturer_channel manufacturerChannel,
               d.name manufacturerName,
        sum(case when c.id is null then null else ifnull(e.run_warehouse_number+e.sum_warehouse_number,0) end ) stockNum ,f.name warehouseName
        from b_product_part_bom a
                 left join b_product_part b on a.part_id = b.id
                 left join b_storage_article c on c.part_id = a.part_id
                 left join b_manufacturer d on c.manufacturer_id = d.id
                 left join b_storage_inventory e on c.code = e.code
        left join b_storage_warehouse f on e.warehouse_id = f.id
        where
            a.is_pm = true and a.deleted = false
            <!-- 备件等级 -->
            <if test="null != query.spareLevel and !query.spareLevel.isEmpty()">
                and a.spare_level in
                <foreach collection="query.spareLevel" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 仓库过滤 -->
            <if test="null != query.warehouseId and '' != query.warehouseId">
                and (e.warehouse_id= #{query.warehouseId} or e.warehouse_id is null)
            </if>

            <!--  oem编号或名称 -->
            <if test="null != query.name and '' != query.name">
                and(b.oem_number like concat ('%',#{query.name},'%') or a.ch like concat ('%',#{query.name},'%'))

            </if>
        <!--  更换频次 -->
        <if test="null!=query.repFrequency and !query.repFrequency.isEmpty()">
            and a.rep_frequency in
            <foreach collection="query.repFrequency" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--  所属单元 -->
        <if test="null!=query.unitList and !query.unitList.isEmpty()">
            and a.unit in
            <foreach collection="query.unitList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--  零件小类 -->
        <if test="null!=query.productPartTypeList and !query.productPartTypeList.isEmpty()">
            and b.type in
            <foreach collection="query.productPartTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="null!=query.lastIds and !query.lastIds.isEmpty()">
            and a.product_id in
            <foreach collection="query.lastIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by (a.id)
        <trim prefix="having" prefixOverrides="and">
            <if test="null != query.stockNumMini and '' != query.stockNumMini ">
                stockNum &gt;= #{query.stockNumMini}
            </if>
            <if test="null != query.stockNumMax and '' != query.stockNumMax">
                and stockNum &lt;= #{query.stockNumMax}
            </if>
        </trim>
        order by a.updated_at desc
</select>

    <select id="queryPartInventory" resultType="com.hightop.benyin.product.application.vo.PartInventoryVo">
        select a.number_oem numberOem,a.code articleCode,a.id articleId,
               a.name articleName,b.name inventoryName,a.manufacturer_channel manufacturerChannel,
               ifnull(sum(b.run_warehouse_number+b.sum_warehouse_number),0) stockNum
        from b_storage_article a
                 left join b_storage_inventory b on a.code = b.code and b.warehouse_id = #{warehouseId}
        where a.deleted = false
        <if test="null != partIdList and !partIdList.isEmpty()">
            and a.part_id in
            <foreach collection="partIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by a.number_oem,a.code,a.number_oem, a.code, a.name, b.name,b.name

    </select>

    <select id="querySkuByInvSkuId" resultType="com.hightop.benyin.product.application.vo.InventoryAndSkuVo">
        select a.number_oem numberOem,a.code articleCode,b.name inventoryName,c.name warehouseName,
               b.run_warehouse_number+b.sum_warehouse_number stockNum,e.name,e.code ,d.id skuId
        from b_storage_article a
                 left join b_storage_inventory b on a.code = b.code
                 left join b_storage_warehouse c on  b.warehouse_id = c.id
                 left join tb_sale_sku d on d.inv_sku_id = b.id and d.deleted = false
                 left join tb_item e on e.id = d.item_id and e.deleted = false
        where a.code = #{articleCode} and a.deleted = false
          and b.warehouse_id = #{warehouseId}

    </select>

    <select id="getChildProductIds" resultType="java.lang.Long">
        SELECT au.id
        FROM (SELECT * FROM b_product_tree WHERE parent_id IS NOT NULL) au,
             (SELECT @pid := #{productId}) pd
        WHERE FIND_IN_SET(parent_id, @pid) > 0
                  AND @pid := concat(@pid, ',', id)
        union select id from b_product_tree where id = #{productId}
    </select>

</mapper>
