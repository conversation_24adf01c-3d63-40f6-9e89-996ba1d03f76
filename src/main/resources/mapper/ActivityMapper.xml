<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.activity.infrastructure.mapper.ActivityMapper">

    <resultMap id="activityResult" type="com.hightop.benyin.activity.infrastructure.entity.Activity" autoMapping="true">
    </resultMap>

    <select id="pageList" resultMap="activityResult">
        SELECT *
        FROM tb_activity t
        WHERE  1=1
        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>
        <if test="null != qo.activityName and '' != qo.activityName ">
            and t.activity_name like concat ('%',#{qo.activityName},'%')
        </if>

        <if test="null != qo.activityType and '' != qo.activityType ">
            and t.activity_type = #{qo.activityType}
        </if>

        <if test="null != qo.status and !qo.status.isEmpty()">
            and t.status in
            <foreach collection="qo.status" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="null != qo.beginDateStart and '' != qo.beginDateStart ">
            and t.start_time &gt;= concat(#{qo.beginDateStart},' 00:00:00')
        </if>

        <if test="null != qo.beginDateEnd and '' != qo.beginDateEnd ">
            and t.start_time &lt;= concat(#{qo.beginDateEnd},' 23:59:59')
        </if>

        <if test="null != qo.deadlineDateStart and '' != qo.deadlineDateStart ">
            and t.end_time &gt;= concat(#{qo.deadlineDateStart},' 00:00:00')
        </if>

        <if test="null != qo.deadlineDateEnd and '' != qo.deadlineDateEnd ">
            and t.end_time &lt;= concat(#{qo.deadlineDateEnd},' 23:59:59')
        </if>

        <if test="null != qo.beginDate and '' != qo.beginDate ">
            and t.created_at &gt;= concat(#{qo.beginDate},' 00:00:00')
        </if>

        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>

        <if test="null != qo.customerId and '' != qo.customerId ">
            and t.id in (select activity_id from tb_activity_situation where customer_id = #{qo.customerId})
        </if>

        <if test="null!=qo.ranges and !qo.ranges.isEmpty()">
            AND
            <foreach collection="qo.ranges" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t.activity_range,cast(#{id} as char ))
            </foreach>
        </if>

        ORDER BY  t.created_at  DESC
    </select>

    <select id="activitySituationList" resultType="com.hightop.benyin.activity.infrastructure.entity.ActivitySituation">
        select t.*,ifnull(t.orderCustNum,0)+ifnull(t.workCustNum,0)  orderCustNum,t.granterNum>0 as isGrant
        from (
        select distinct t.*,t1.name customerName,t1.seq_id customerSeqId,t2.activity_name activityName,t2.start_time startTime,t2.end_time endTime,t2.activity_type activityType,t2.status status,
        (select count(1) from b_customer where   deleted=0  and share_customer =t.customer_id and share_activity=t.activity_id) registerNum,
        (select count(1) from tb_activity_involved where  situation_id=t.id) clickNum,
        (select count(1) from tb_activity_award_grant where  activity_id=t.activity_id and customer_id=t.customer_id) granterNum,

        ifnull(t2.orderNum,0) orderNum,ifnull(t2.orderAmount,0) orderAmount,
        (select count(tt.customer_id)  from tb_trade_order tt
        left join b_customer t1 on t1.id = tt.customer_id
        where tt.order_status!='CLOSED' and
        tt.created_at >=#{qo.activityBeginTime} and tt.created_at &lt;=#{qo.activityEndTime} and t1.share_customer=t.customer_id ) orderCustNum,
        (select count(tw.customer_id)  from tb_work_order tw
        left join b_customer t1 on t1.id = tw.customer_id    where tw.status!='close'  and
        tw.created_at >=#{qo.activityBeginTime} and tw.created_at &lt;=#{qo.activityEndTime} and t1.share_customer=t.customer_id ) workCustNum

        from tb_activity_situation t
        left join b_customer t1 on t1.id = t.customer_id
        left join tb_activity t2 on t2.id = t.activity_id
        left join (select t.id,t.share_customer,
                          ifnull(t1.orderNum,0)+ifnull(t2.workNum,0) orderNum,
                          ifnull(t1.orderAmount,0)+ifnull(t2.workAmount,0) orderAmount
        from b_customer t left join  (select count(1) orderNum,sum(actual_amount) orderAmount,customer_id from tb_trade_order where order_status!='CLOSED' and
        created_at >=#{qo.activityBeginTime} and created_at &lt;=#{qo.activityEndTime} group by customer_id ) t1 on t.id=t1.customer_id

        left join  (select count(1) workNum,sum(total_amount) workAmount,customer_id from tb_work_order where status!='close' and
        created_at >=#{qo.activityBeginTime} and created_at &lt;=#{qo.activityEndTime} group by customer_id ) t2 on t.id=t2.customer_id

        where  t.deleted=0 ) t2 on t2.share_customer = t.customer_id
        where 1=1
            <if test="null != qo.activityId ">
                and t.activity_id = #{qo.activityId}
            </if>
            <if test="null != qo.customerId ">
                and t.customer_id= #{qo.customerId}
            </if>
            <if test="null != qo.customerName and '' != qo.customerName ">
                and t1.name like concat ('%',#{qo.customerName},'%')
            </if>

            <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
                and t1.seq_id like concat ('%',#{qo.customerSeqId},'%')
            </if>
            <if test="null != qo.startShareNum ">
                and t.share_num >= #{qo.startShareNum}
            </if>

            <if test="null != qo.endShareNum ">
                and t.share_num &lt;= #{qo.endShareNum}
            </if>
        ) t where 1=1

        <if test="null != qo.startRegisterNum ">
           and t.registerNum >= #{qo.startRegisterNum}
        </if>

        <if test="null != qo.endRegisterNum ">
           and t.registerNum &lt;= #{qo.endRegisterNum}
        </if>

        <if test="null != qo.startOrderNum ">
            and  t.orderNum >= #{qo.startOrderNum}
        </if>

        <if test="null != qo.endOrderNum ">
            and t.orderNum &lt;= #{qo.endOrderNum}
        </if>

        <if test="null != qo.startOrderAmount ">
            and t.orderAmount >= #{qo.startOrderAmount}
        </if>

        <if test="null != qo.endOrderAmount ">
            and   t.orderAmount &lt;= #{qo.endOrderAmount}
        </if>
        <if test="null != qo.isGrant ">
            <choose>
                <when test="qo.isGrant==true">
                    and t.granterNum>0
                </when>
                <otherwise>
                    and t.granterNum=0
                </otherwise>
            </choose>
        </if>

        order by t.customerSeqId desc
    </select>
    <select id="activitySituationPageList" resultType="com.hightop.benyin.activity.application.vo.ActivitySituationVo">
        select t.id customerId,t.seq_id customerSeqId,t.name customerName,t.created_at entryTime,r.name area,r1.name city,r2.name province,
               (select count(1) from b_customer_device_group where customer_id=t.id) machineNum,
               (select count(1) from tb_trade_order where order_status!='CLOSED' and created_at &gt;=concat(#{qo.activityBeginTime},' 00:00:00') and created_at &lt;= concat(#{qo.activityEndTime},' 23:59:59') and customer_id=t.id) orderNum,
               (select sum(actual_amount) from tb_trade_order where order_status!='CLOSED' and created_at &gt;=concat(#{qo.activityBeginTime},' 00:00:00') and created_at &lt;= concat(#{qo.activityEndTime},' 23:59:59') and customer_id=t.id) orderAmount,
               (select count(1) from tb_work_order where status!='close' and created_at &gt;=concat(#{qo.activityBeginTime},' 00:00:00') and created_at &lt;= concat(#{qo.activityEndTime},' 23:59:59') and customer_id=t.id) workNum,
               (select sum(total_amount) from tb_work_order where status!='close' and created_at &gt;=concat(#{qo.activityBeginTime},' 00:00:00') and created_at &lt;= concat(#{qo.activityEndTime},' 23:59:59') and customer_id=t.id) workAmount
        from b_customer t
         left join b_region r on t.region_code = r.code
         left join b_region r1 on r1.code = r.parent_code
         left join b_region r2 on r2.code = r1.parent_code
         where t.deleted=0
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>

        <if test="null != qo.customerId ">
            and t.share_customer= #{qo.customerId}
        </if>

        <if test="null != qo.activityId ">
            and t.share_activity = #{qo.activityId}
        </if>

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.region_code like CONCAT( #{qo.regionPath},'%')
        </if>

        <if test="qo.startDate!= null and qo.startDate!= ''">
            and t.created_at &gt;=concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="qo.endDate!= null and qo.endDate!= ''">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        order by t.created_at desc
    </select>


    <select id="activityCustomerSituationPageList" resultType="com.hightop.benyin.activity.application.vo.ActivitySituationVo">
        select t.customer_id customerId,tc.seq_id customerSeqId,tc.name customerName,tc.created_at entryTime,r.name area,r1.name city,r2.name province,
        (select count(1) from b_customer_device_group where customer_id=t.customer_id) machineNum,
        (select count(1) from tb_trade_order where order_status!='CLOSED' and created_at &gt;=#{qo.activityBeginTime} and created_at &lt;= #{qo.activityEndTime} and customer_id=t.customer_id) orderNum,
        (select sum(actual_amount) from tb_trade_order where order_status!='CLOSED' and created_at &gt;=#{qo.activityBeginTime} and created_at &lt;= #{qo.activityEndTime} and customer_id=t.customer_id) orderAmount,
        (select count(1) from tb_work_order where status!='close' and created_at &gt;=#{qo.activityBeginTime}and created_at &lt;= #{qo.activityEndTime} and customer_id=t.customer_id) workNum,
        (select sum(total_amount) from tb_work_order where status!='close' and created_at &gt;=#{qo.activityBeginTime} and created_at &lt;= #{qo.activityEndTime} and customer_id=t.customer_id) workAmount
        from tb_activity_involved t
        left join b_customer tc on tc.id = t.customer_id
        left join b_region r on tc.region_code = r.code
        left join b_region r1 on r1.code = r.parent_code
        left join b_region r2 on r2.code = r1.parent_code
        where 1=1
        <if test="null != qo.customerName and '' != qo.customerName ">
            and tc.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and tc.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>

        <if test="null != qo.situationId ">
            and t.situation_id= #{qo.situationId}
        </if>

        <if test="null != qo.activityId ">
            and t.activity_id = #{qo.activityId}
        </if>

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and tc.region_code like CONCAT( #{qo.regionPath},'%')
        </if>

        <if test="qo.startDate!= null and qo.startDate!= ''">
            and t.created_at &gt;=concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="qo.endDate!= null and qo.endDate!= ''">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        order by tc.created_at desc
    </select>


    <select id="activitySituationOrderPageList" resultType="com.hightop.benyin.activity.application.vo.ActivitySituationOrderVo">
        select t.id, t.order_num orderNum, t.actual_amount orderAmount, 'SALES_ORDER' orderOrigin, t1.share_customer,t1.seq_id customerSeqId,t1.name customerName,t.created_at createdAt
        ,t2.tel buyerPhone,t2.name buyer
        from tb_trade_order t
                 left join b_customer t1 on t1.id = t.customer_id  and t1.deleted=0
                 left join b_customer_staff t2 on t2.id = t.buyer_id
        where t.order_status != 'CLOSED'
        <if test="qo.activityBeginTime!= null ">
            and t.created_at &gt;= #{qo.activityBeginTime}
        </if>
        <if test="qo.activityEndTime!= null">
            and t.created_at &lt;= #{qo.activityEndTime}
        </if>
        <if test="null != qo.customerId ">
            and t1.share_customer= #{qo.customerId}
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t1.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>

        union all
        select t.id, t.code orderNum, t.total_amount orderAmount, 'REPAIR_ORDER' orderOrigin, t1.share_customer,t1.seq_id customerSeqId,t1.name customerName,t.created_at createdAt
        ,t2.tel buyerPhone,t2.name buyer
        from tb_work_order t
         left join b_customer t1 on t1.id = t.customer_id
        left join b_customer_staff t2 on t2.id = t.customer_staff_id
        where t.status != 'close'

        <if test="null != qo.customerId ">
            and t1.share_customer= #{qo.customerId}
        </if>

        <if test="qo.activityBeginTime!= null ">
            and t.created_at &gt;= #{qo.activityBeginTime}
        </if>
        <if test="qo.activityEndTime!= null">
            and t.created_at &lt;= #{qo.activityEndTime}
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t1.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>
        order by share_customer asc
    </select>

    <select id="activitySituationOrderCustList" resultType="com.hightop.benyin.activity.application.vo.ActivitySituationVo">
       select t.id customerId,t.seq_id customerSeqId,t.name customerName,t.created_at entryTime,r.name area,r1.name city,r2.name province,
        (select count(1) from b_customer_device_group where customer_id=t.customer_id) machineNum,
        (select count(1) from tb_trade_order where order_status!='CLOSED' and created_at &gt;=concat(#{qo.activityBeginTime},' 00:00:00') and created_at &lt;= concat(#{qo.activityEndTime},' 23:59:59') and customer_id=t.customer_id) orderNum,
        (select sum(actual_amount) from tb_trade_order where order_status!='CLOSED' and created_at &gt;=concat(#{qo.activityBeginTime},' 00:00:00') and created_at &lt;= concat(#{qo.activityEndTime},' 23:59:59') and customer_id=t.customer_id) orderAmount,
        (select count(1) from tb_work_order where status!='close' and created_at &gt;=concat(#{qo.activityBeginTime},' 00:00:00') and created_at &lt;= concat(#{qo.activityEndTime},' 23:59:59') and customer_id=t.customer_id) workNum,
        (select sum(total_amount) from tb_work_order where status!='close' and created_at &gt;=concat(#{qo.activityBeginTime},' 00:00:00') and created_at &lt;= concat(#{qo.activityEndTime},' 23:59:59') and customer_id=t.customer_id) workAmount

        from (
        select distinct t1.id,t1.region_code,t1.name,t1.seq_id
        from tb_trade_order t
        left join b_customer t1 on t1.id = t.customer_id
        where t.order_status != 'CLOSED'
        <if test="qo.activityBeginTime!= null ">
            and t.created_at &gt;= #{qo.activityBeginTime}
        </if>
        <if test="qo.activityEndTime!= null">
            and t.created_at &lt;= #{qo.activityEndTime}
        </if>
        <if test="null != qo.customerId ">
            and t1.share_customer= #{qo.customerId}
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t1.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>
            group by t.customer_id
        union all
        select distinct t1.id,t1.region_code,t1.name,t1.seq_id
        from tb_work_order t
        left join b_customer t1 on t1.id = t.customer_id
        where t.status != 'close'

        <if test="null != qo.customerId ">
            and t1.share_customer= #{qo.customerId}
        </if>

        <if test="qo.activityBeginTime!= null ">
            and t.created_at &gt;= #{qo.activityBeginTime}
        </if>
        <if test="qo.activityEndTime!= null">
            and t.created_at &lt;= #{qo.activityEndTime}
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t1.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>
        ) t
        left join b_region r on t.region_code = r.code
        left join b_region r1 on r1.code = r.parent_code
        left join b_region r2 on r2.code = r1.parent_code
    </select>

    <select id="activityGrantPageList" resultType="com.hightop.benyin.activity.application.vo.ActivityGrantVo">
        select award_type,award_name,article_code,price,
               sum(quantity) totalQuantity,
               sum(quantity*ifnull(price,0)) totalAmount
        from tb_activity_award_grant t
        where 1=1
        <if test="null != qo.activityId ">
            and t.activity_id = #{qo.activityId}
        </if>

        <if test="null != qo.awardType and '' != qo.awardType ">
            and t.award_type  = #{qo.awardType}
        </if>

        <if test="null != qo.awardName and '' != qo.awardName ">
            and t.award_name like concat ('%',#{qo.awardName},'%')
        </if>

        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>
        group by award_type,award_name,article_code,price
    </select>

</mapper>