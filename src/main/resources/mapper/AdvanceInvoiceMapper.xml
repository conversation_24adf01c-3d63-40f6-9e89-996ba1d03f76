<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.storage.infrastructure.mapper.AdvanceInvoiceMapper">

    <select id="selectOutWarehouse" resultType="com.hightop.benyin.storage.application.vo.InvoiceOutWarehouseVo">
        SELECT
            t.id AS advanceInvoiceId,
            t.out_type AS outType,
           case when t1.out_type='engineer_apply' then t5.name
        when t1.out_type='shopping_mall' then t3.name end as receiverName,
            t1.out_warehouse_id AS outWarehouseId,
            t1.shop_waybill AS shopWaybill,
            t.logistics_provider AS logisticsProvider ,
            replace(json_extract(t.receiver ,'$.mobile'),'"','') as consigneePhone
        FROM b_advance_invoice t
        JOIN b_storage_out_warehouse t1 ON (t1.out_warehouse_id = t.outbound_order_code)
        left join tb_trade_order  t2 on t2.order_num = t1.shop_waybill
        left join b_customer t3 on t3.id = t2.customer_id
        left join tb_apply_order t4 on t4.code = t1.shop_waybill
        left join st_user_basic t5 on t5.id = t4.engineer_id
        WHERE t.deleted=false
        AND t.expected_number > 0
        AND t1.warehouse_id = #{qo.warehouseId}
        <if test="null != qo.outWarehouseId and '' != qo.outWarehouseId ">
            and t.outbound_order_code like concat ('%',#{qo.outWarehouseId},'%')
        </if>
        <if test="null != qo.shopWaybill and '' != qo.shopWaybill ">
            and t1.shop_waybill like concat ('%',#{qo.shopWaybill},'%')
        </if>
        <if test="null != qo.consigneePhone and '' != qo.consigneePhone ">
            and json_extract(t.receiver ,'$.mobile') like concat ('%',#{qo.consigneePhone},'%')
        </if>
        ORDER BY consigneePhone , t.created_at  DESC
    </select>

</mapper>