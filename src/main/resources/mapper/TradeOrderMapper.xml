<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.order.infrastructure.mapper.TradeOrderMapper">

    <select id="overview" resultType="com.hightop.benyin.order.infrastructure.entity.ConsumeOverview">
        select sum(a.paid_amount_sum) totalAmount, avg(a.paid_amount_sum) avgAmountByMonth
        from (select sum(paid_amount) paid_amount_sum, date_format(order_time, '%Y-%m') date
              from tb_trade_order
              where customer_id = ${customerId}
                and order_status = 'SUCCESS'
              group by date_format(order_time, '%Y-%m')) a;
    </select>

    <select id="overviewMonthly" resultType="com.hightop.benyin.order.infrastructure.entity.ConsumeOverviewMonthly">
        select sum(actual_amount) totalPay,sum(paid_amount) totalPaid ,sum(actual_amount)-sum(paid_amount) needPay from
        tb_trade_order
        where order_status != 'CLOSED'
        and customer_id = ${customerId}
        <if test="null != start ">
            and order_time &gt;= #{start}
        </if>
        <if test="null != end">
            and order_time &lt; #{end}
        </if>
    </select>

    <select id="getTradeOrderAllMoney" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(res.cou) / 100, 0)
        from (SELECT actual_amount - IFNULL(refund_amount, 0) as cou
              FROM tb_trade_order
              WHERE order_status IN ('SUCCESS', 'WAIT_DELIVER', 'WAIT_RECEIVE')
                AND customer_id = ${customerId}) res
    </select>

    <select id="getLastTradeOrderByCustomerId" resultType="com.hightop.benyin.order.infrastructure.entity.TradeOrder">
        SELECT *
        FROM tb_trade_order
        WHERE order_status IN ('SUCCESS', 'WAIT_DELIVER', 'WAIT_RECEIVE')
          AND customer_id = ${customerId}
        order by created_at desc limit 1
    </select>
    <select id="getBatchCodes" resultType="com.hightop.benyin.items.store.infrastructure.entity.BatchNum">
        select distinct t4.id,  t4.batch_code batchCode, t4.number num
        from tb_trade_order t
                 join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                 join b_storage_out_warehouse t2 on t2.shop_waybill = t.order_num
                 join b_storage_warehouse_flow t4 on t2.out_warehouse_id = t4.flow_id and t4.code = t1.article_code
        where t.order_num = #{orderNum}
          and t1.article_code = #{articleCode}
        order by t4.created_at DESC
    </select>
    <select id="getApplyOrdeStatisicsList" resultType="com.hightop.benyin.statistics.application.vo.ApplyOrderDetailVO">
        select tod.id,
        bc.seq_id customerSeqId,
        bc.name customerName,
        tao.order_num orderNum,
        tao.created_at createdAt,
        cs.name buyerName,
        ti.code itemCode,
        tod.article_code,
        ifnull(sa.name, ti.name) name,
        sa.code,
        sa.number_oem numberOem,
        sa.manufacturer_channel manufacturerChannel,
        (tod.item_num-tod.reverse_num) itemNum,
        tod.sale_unit_price saleUnitPrice,
        tod.actual_unit_price actualUnitPrice,
        tod.discount_amount discountAmount,
        tod.pay_amount payAmount,
        sa.unit
        from tb_trade_order_detail tod
        left join tb_trade_order tao on tao.id = tod.trade_order_id
        left join b_customer bc on tao.customer_id = bc.id
        left join b_customer_staff cs on cs.id = tao.buyer_id
        left join tb_item ti on ti.id = tod.item_id
        left join b_storage_article sa on sa.code = tod.article_code
        where tao.order_type='APPLY' and tao.order_status!='CLOSED'
        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and bc.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and bc.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.orderNum and '' != qo.orderNum ">
            and tao.order_num like concat ('%',#{qo.orderNum},'%')
        </if>

        <if test="null != qo.itemCode and '' != qo.itemCode ">
            and ti.code like concat ('%',#{qo.itemCode},'%')
        </if>
        <if test="null != qo.name and '' != qo.name ">
            and sa.name like concat ('%',#{qo.name},'%')
        </if>
        <if test="null != qo.code and '' != qo.code ">
            and sa.code like concat ('%',#{qo.code},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and sa.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and tod.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and tod.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null != qo.customerId">
            and tao.customer_id = #{qo.customerId}
        </if>


        order by tao.created_at desc
    </select>

    <select id="getApplyOrdeStatisicsSummary" resultType="com.hightop.benyin.statistics.application.vo.ApplyOrderDetailVO">
        select
        sum(tod.item_num-tod.reverse_num) itemNum,
        sum(tod.pay_amount) payAmount
        from tb_trade_order_detail tod
        left join tb_trade_order tao on tao.id = tod.trade_order_id
        left join b_customer bc on tao.customer_id = bc.id
        left join b_customer_staff cs on cs.id = tao.buyer_id
        left join tb_item ti on ti.id = tod.item_id
        left join b_storage_article sa on sa.code = tod.article_code
        where tao.order_type='APPLY' and tao.order_status!='CLOSED'
        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and bc.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and bc.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.orderNum and '' != qo.orderNum ">
            and tao.order_num like concat ('%',#{qo.orderNum},'%')
        </if>

        <if test="null != qo.itemCode and '' != qo.itemCode ">
            and ti.code like concat ('%',#{qo.itemCode},'%')
        </if>
        <if test="null != qo.name and '' != qo.name ">
            and sa.name like concat ('%',#{qo.name},'%')
        </if>
        <if test="null != qo.code and '' != qo.code ">
            and sa.code like concat ('%',#{qo.code},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and sa.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and tod.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and tod.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null != qo.customerId">
            and tao.customer_id = #{qo.customerId}
        </if>
    </select>

    <select id="getMonthSalesSummary" resultType="com.hightop.benyin.order.application.vo.MonthSalesSummaryVo">
        select t.*,round(t.totalAmount/100/t.customerNum,2) avgCustOrderAmount
        from (
        select DATE_FORMAT(t.created_at,'%Y-%m') currMonth,count(1) orderCount,sum(paid_amount) totalAmount,
        count(distinct customer_id) customerNum,round(avg(paid_amount/100),2) avgOrderAmount,1 source
        from tb_trade_order t where order_status!='CLOSED' and t.is_mechine = 0
        <if test="null != qo.startMonth">
            and DATE_FORMAT(t.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and DATE_FORMAT(t.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        group by DATE_FORMAT(t.created_at,'%Y-%m')
        union all
        select DATE_FORMAT(t.send_report_time,'%Y-%m') currMonth,count(1) orderCount,sum(item_pay) totalAmount,
        count(distinct customer_id) customerNum,round(avg(item_pay/100),2) avgOrderAmount,2 source
        from tb_work_order t where t.status!='close' and t.item_pay>0
        <if test="null != qo.startMonth">
            and DATE_FORMAT(t.send_report_time,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and DATE_FORMAT(t.send_report_time,'%Y-%m') &lt;= #{qo.endMonth}
        </if>

        group by DATE_FORMAT(t.send_report_time,'%Y-%m')
        ) t
        <where>
        <if test="null != qo.source">
            t.source = #{qo.source}
        </if>
        </where>
        order by t.currMonth desc ,t.source asc
    </select>
    <select id="getMonthSalesSummaryTotal" resultType="com.hightop.benyin.order.application.vo.MonthSalesSummaryVo">
        select sum(totalAmount) totalAmount,sum(orderCount) orderCount,sum(customerNum) customerNum
        from (
        select DATE_FORMAT(t.created_at,'%Y-%m') currMonth,count(1) orderCount,sum(paid_amount) totalAmount,
        count(distinct customer_id) customerNum,round(avg(paid_amount/100),2) avgOrderAmount,1 source
        from tb_trade_order t where order_status!='CLOSED'  and t.is_mechine = 0
        <if test="null != qo.startMonth">
            and DATE_FORMAT(t.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and DATE_FORMAT(t.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        group by DATE_FORMAT(t.created_at,'%Y-%m')
        union all
        select DATE_FORMAT(t.send_report_time,'%Y-%m') currMonth,count(1) orderCount,sum(item_pay) totalAmount,
        count(distinct customer_id) customerNum,round(avg(item_pay/100),2) avgOrderAmount,2 source
        from tb_work_order t where t.status!='close' and t.item_pay>0
        <if test="null != qo.startMonth">
            and DATE_FORMAT(t.send_report_time,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and DATE_FORMAT(t.send_report_time,'%Y-%m') &lt;= #{qo.endMonth}
        </if>

        group by DATE_FORMAT(t.send_report_time,'%Y-%m')
        ) t
        <where>
        <if test="null != qo.source">
            t.source = #{qo.source}
        </if>
        </where>
        order by t.currMonth desc
    </select>


    <select id="getMonthMechineStatisTotal"   resultType="com.hightop.benyin.order.application.vo.MonthMechineStatisVo" >
        select
        sum(t.paid_amount) amount,sum(t1.item_num) number
        from tb_trade_order t
        join tb_trade_order_detail t1 on t1.trade_order_id = t.id
        join b_storage_article t2 on t2.code= t1.article_code
        join b_product_tree t3 on t3.id = t2.product_id
        join b_product_tree t4 on t4.id = substr(t3.full_id_path, 2, 20)
        join b_product_tree t5 on t5.id =t3.parent_id
        where t.is_mechine = 1
        and t.order_status!='CLOSED'
        <if test="null != qo.startMonth">
            and date_format(t.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t2.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.brand and '' != qo.brand ">
            and t4.name like concat ('%',#{qo.brand},'%')
        </if>
        <if test="null != qo.series and '' != qo.series ">
            and t5.name like concat ('%',#{qo.series},'%')
        </if>
    </select>
    <select id="getMonthMechineStatis"   resultType="com.hightop.benyin.order.application.vo.MonthMechineStatisVo" >
        select  date_format(t.created_at,'%Y-%m') currMonth,
                t4.name brand ,t5.name series,
                sum(t.paid_amount) amount,sum(t1.item_num) number,
        round( sum(t.paid_amount)/sum(t1.item_num)/100,2) avgAmount
        from tb_trade_order t
                 join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                 join b_storage_article t2 on t2.code= t1.article_code
                 join b_product_tree t3 on t3.id = t2.product_id
                 join b_product_tree t4 on t4.id = substr(t3.full_id_path, 2, 20)
                 join b_product_tree t5 on t5.id =t3.parent_id
        where t.is_mechine = 1
          and t.order_status!='CLOSED'
              <if test="null != qo.startMonth">
                  and date_format(t.created_at,'%Y-%m') &gt;= #{qo.startMonth}
              </if>
              <if test="null != qo.endMonth">
                  and date_format(t.created_at,'%Y-%m') &lt;= #{qo.endMonth}
              </if>
            <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
                AND t2.product_id in
                <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        <if test="null != qo.brand and '' != qo.brand ">
            and t4.name like concat ('%',#{qo.brand},'%')
        </if>
        <if test="null != qo.series and '' != qo.series ">
            and t5.name like concat ('%',#{qo.series},'%')
        </if>

        group by date_format(t.created_at,'%Y-%m'),t4.name,t5.name
        order by date_format(t.created_at,'%Y-%m') desc
    </select>

    <select id="getMonthMechineDistributionTotal"   resultType="com.hightop.benyin.order.application.vo.MonthMechineStatisVo" >
        select
        sum(t.paid_amount) amount,sum(t1.item_num) number
        from tb_trade_order t
        join tb_trade_order_detail t1 on t1.trade_order_id = t.id
        where t.is_mechine = 1
        and t.order_status!='CLOSED'

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
    </select>
    <select id="getMonthMechineDistribution"   resultType="com.hightop.benyin.order.application.vo.MonthMechineDistributionVo" >
        select  date_format(t.created_at,'%Y-%m') currMonth,
                br2.name province ,br1.name city,br.name area,
                sum(t.paid_amount) amount,sum(t1.item_num) number,
                round( sum(t.paid_amount)/sum(t1.item_num)/100,2) avgAmount
        from tb_trade_order t
                 join tb_trade_order_detail t1 on t1.trade_order_id = t.id
                 join b_storage_article t2 on t2.code= t1.article_code
                 left join b_region br on  br.code=t.consignee_region_code
                 left join b_region br1 on  br1.code = br.parent_code
                 left join b_region br2 on  br2.code = br1.parent_code
        where t.is_mechine = 1
          and t.order_status!='CLOSED'

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        group by date_format(t.created_at,'%Y-%m'),br2.name  ,br1.name ,br.name
    </select>
    <select id="getMonthMechineBrandDistribution"   resultType="com.hightop.benyin.order.application.vo.MonthMechineDistributionVo" >
        select  date_format(t.created_at,'%Y-%m') currMonth,
        br2.name province ,br1.name city,br.name area, t4.name brand ,t5.name series,
        sum(t.paid_amount) amount,sum(t1.item_num) number,
        round( sum(t.paid_amount)/sum(t1.item_num)/100,2) avgAmount
        from tb_trade_order t
        join tb_trade_order_detail t1 on t1.trade_order_id = t.id
        join b_storage_article t2 on t2.code= t1.article_code
        left join b_region br on  br.code=t.consignee_region_code
        left join b_region br1 on  br1.code = br.parent_code
        left join b_region br2 on  br2.code = br1.parent_code
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = substr(t3.full_id_path, 2, 20)
        left  join b_product_tree t5 on t5.id =t3.parent_id
        where t.is_mechine = 1
        and t.order_status!='CLOSED'
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t2.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.brand and '' != qo.brand ">
            and t4.name like concat ('%',#{qo.brand},'%')
        </if>
        <if test="null != qo.series and '' != qo.series ">
            and t5.name like concat ('%',#{qo.series},'%')
        </if>

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        group by date_format(t.created_at,'%Y-%m'),br2.name  ,br1.name ,br.name, t4.name  ,t5.name
    </select>
    <select id="getMonthMechineBrandDistributionSummary"   resultType="com.hightop.benyin.order.application.vo.MonthMechineStatisVo" >
        select
        sum(t.paid_amount) amount,sum(t1.item_num) number
        from tb_trade_order t
        join tb_trade_order_detail t1 on t1.trade_order_id = t.id
        join b_storage_article t2 on t2.code= t1.article_code
        left join b_region br on  br.code=t.consignee_region_code
        left join b_region br1 on  br1.code = br.parent_code
        left join b_region br2 on  br2.code = br1.parent_code
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = substr(t3.full_id_path, 2, 20)
        left  join b_product_tree t5 on t5.id =t3.parent_id
        where t.is_mechine = 1
        and t.order_status!='CLOSED'
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t2.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.brand and '' != qo.brand ">
            and t4.name like concat ('%',#{qo.brand},'%')
        </if>
        <if test="null != qo.series and '' != qo.series ">
            and t5.name like concat ('%',#{qo.series},'%')
        </if>

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.created_at,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
    </select>
</mapper>
