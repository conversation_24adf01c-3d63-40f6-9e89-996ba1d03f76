<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.customer.infrastructure.mapper.CustomerDeviceGroupMapper">
    <select id="selectById" resultType="com.hightop.benyin.customer.infrastructure.entity.CustomerDeviceGroup">
        select * from b_customer_device_group where id=#{id}
    </select>

    <select id="mechineDistribution" resultType="com.hightop.benyin.customer.application.vo.MechineDistributionVo" >
        select t.*,ifnull(t1.printCount,0) printCount,ifnull(t2.orderPay,0) orderPay,ifnull(t3.workPay,0) workPay,ifnull(t4.mechinePay,0) mechinePay,
        ifnull(t.number,0)/t.totalNum numberScale,
        ifnull(t1.printCount,0)/t.totalCount countScale,
        ifnull(t2.orderPay,0)/t.orderTotalPay orderScale,
        ifnull(t3.workPay,0)/t.workTotalPay workScale,
        ifnull(t4.mechinePay,0)/t.mechineTotalPay mechineScale
        from (
        select t1.region_code,t2.name AS area ,t3.name AS city,t4.name AS province ,t6.name,
        t8.name serial,t9.name brand,t8.id serialId,
        count(1) number,tt.totalNum,tt1.totalCount,tt2.workPay workTotalPay,tt3.orderPay orderTotalPay,tt4.mechinePay mechineTotalPay
        from b_customer_device_group t
        left join b_customer t1 on t1.id = t.customer_id
        left join b_region t2 on t2.code = t1.region_code
        left join b_region t3 on t3.code = t2.parent_code
        left join b_region t4 on t4.code = t3.parent_code
        left join b_product_tree t5 on t5.id = t.product_id
        left join b_product_tree t6 on t6.id = substr(t5.full_id_path, 2, 20)
        left join b_product_tree t7 on t7.id = t.product_id
        left join b_product_tree t8 on t8.id = t7.parent_id
        left join b_product_tree t9 on t9.id = substr(t8.full_id_path, 2, 20)

        left join (select count(1) totalNum from b_customer_device_group t
        where t.deleted=0 ) tt on 1=1

        left join (select sum(total_count) totalCount from r_day_count t
        ) tt1 on 1=1

        left join (select sum(total_amount) workPay  from tb_work_order t where status!='close'
        ) tt2 on 1=1

        left join (select sum(t.actual_pay_amount) orderPay  from tb_trade_order_detail t
        join tb_trade_order tto on t.trade_order_id = tto.id
        join b_storage_article t1 on t1.code= t.article_code
        where tto.order_status!='CLOSED'  and t1.product_id is null
        ) tt3 on 1=1

        left join (select sum(t.actual_pay_amount) mechinePay  from tb_trade_order_detail t
        join tb_trade_order tto on t.trade_order_id = tto.id
        join b_storage_article t1 on t1.code= t.article_code
        where tto.order_status!='CLOSED'  and t1.product_id is not null
        ) tt4 on 1=1

        where t.deleted=0

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t1.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by t1.region_code,t2.name, t3.name, t4.name ,t6.name, t8.name,t8.id,t9.name
        ) t
        left join (select sum(total_count) printCount ,t2.id,tc.region_code from r_day_count t
        left join  b_customer_device_group tg on tg.id = t.device_group_id
        left join b_customer tc on tc.id = tg.customer_id
        left join b_product_tree t1 on t1.id = t.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        group by t2.name,t2.id,tc.region_code) t1 on t1.region_code=t.region_code and t1.id = t.serialId
        left join (select t.seriesId,t.consignee_region_code, sum(t.pay_amount) orderPay
        from (
        select distinct t.id,t.pay_amount,t4.name,t4.id seriesId,tt.consignee_region_code
        from tb_trade_order_detail t
        left join tb_trade_order tt on tt.id = t.trade_order_id
        left join b_storage_article t1 on t1.code= t.article_code
        left join b_product_part_bom t2 on t2.part_id=t1.part_id
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = t3.parent_id
        where t.order_status !='CLOSED' and t1.product_id is null ) t group by t.name,t.seriesId,t.consignee_region_code) t2
        on t2.consignee_region_code=t.region_code and t2.seriesId = t.serialId
        left join (
        select sum(total_amount) workPay,t2.name,t2.id,tc.region_code  from tb_work_order t
        left join b_customer tc on tc.id = t.customer_id
        left join b_product_tree t1 on t1.id = t.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        where t.status!='close' group by t2.name,t2.id,tc.region_code
        ) t3 on t3.region_code = t.region_code and t3.id = t.serialId

        left join (
        select t.seriesId,t.consignee_region_code, sum(t.pay_amount) mechinePay
        from (
        select distinct t.id,t.pay_amount,t4.name,t4.id seriesId,tt.consignee_region_code
        from tb_trade_order_detail t
        left join tb_trade_order tt on tt.id = t.trade_order_id
        left join b_storage_article t1 on t1.code= t.article_code
        left join b_product_tree t3 on t3.id = t1.product_id
        left join b_product_tree t4 on t4.id = t3.parent_id
        where t.order_status !='CLOSED' and t1.product_id is not null ) t group by t.name,t.seriesId,t.consignee_region_code
        ) t4 on t4.consignee_region_code = t.region_code and t4.seriesId = t.serialId
    </select>

    <select id="selectWorkPay" resultType="java.math.BigDecimal">
        select sum(total_amount)/100   from tb_work_order t
        left join b_customer tc on tc.id = t.customer_id
        left join b_product_tree t1 on t1.id = t.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        where t.status!='close'
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and tc.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectPrintCount" resultType="java.lang.Long">
        select sum(total_count) printCount  from r_day_count t
                                                                          left join  b_customer_device_group tg on tg.id = t.device_group_id
                                                                          left join b_customer tc on tc.id = tg.customer_id
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and tc.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND tg.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectDeviceCount" resultType="java.lang.Integer">
        select count(1)   from  b_customer_device_group tg
        left join b_customer tc on tc.id = tg.customer_id
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and tc.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND tg.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectOrderPay" resultType="java.math.BigDecimal">
        select  sum(t.pay_amount)/100
        from (
        select distinct t.id,t.pay_amount,tt.consignee_region_code
        from tb_trade_order_detail t
        left join tb_trade_order tt on tt.id = t.trade_order_id
        left join b_storage_article t1 on t1.code= t.article_code
        left join b_product_part_bom t2 on t2.part_id=t1.part_id
        where t.order_status !='CLOSED' and t1.product_id is null
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and tt.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t2.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        ) t
    </select>
    <select id="selectMechinePay" resultType="java.math.BigDecimal">
        select sum(t.actual_pay_amount) mechinePay  from tb_trade_order_detail t
        join tb_trade_order tto on t.trade_order_id = tto.id
        join b_storage_article t1 on t1.code= t.article_code
        where tto.order_status!='CLOSED'  and t1.product_id is not null
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and tto.consignee_region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t1.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="queryCustomerIds" resultType="java.lang.Long">
        SELECT DISTINCT
            customer_id
        FROM
            b_customer_device_group
        WHERE
            operat_id = ( #{engineerId} )
          AND deleted = 0
    </select>
</mapper>
