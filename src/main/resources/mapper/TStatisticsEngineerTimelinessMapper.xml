<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.TStatisticsEngineerTimelinessMapper">
    <select id="queryEngineerMonthTimelinessList"
            resultType="com.hightop.benyin.statistics.application.vo.TStatisticsEngineerTimelinessVO">
        SELECT
        monthly,
        engineer_name,
        engineer_id,
        count( id ) orderNums,
        SUM( receive_num ) receive_num,
        ROUND( AVG( IFNULL( receive_time, 0 )), 2 ) receive_time_avg,
        ROUND( AVG( IFNULL( prepare_time, 0 )), 2 ) prepare_time_avg,
        ROUND( AVG( IFNULL( on_road_time, 0 )), 2 ) on_road_time_avg,
        ROUND( AVG( IFNULL( repair_time, 0 )), 2 ) repair_time_avg,
        ROUND( AVG( IFNULL( confirm_time, 0 )), 2 ) confirm_time_avg,
        ROUND( AVG( IFNULL( professional_evaluate, 0 )), 2 ) professional_evaluate_avg,
        ROUND( AVG( IFNULL( service_evaluate, 0 )), 2 ) service_evaluate_avg,
        SUM( receive_time ) receive_time,
        SUM( prepare_time ) prepare_time,
        SUM( on_road_time ) on_road_time,
        SUM( repair_time ) repair_time,
        SUM( confirm_time ) confirm_time,
        SUM( professional_evaluate ) professional_evaluate,
        SUM( service_evaluate ) service_evaluate
        FROM
        t_statistics_engineer_timeliness
        <where>
            <if test="null != query.startMonth and '' != query.startMonth">
                <![CDATA[ AND monthly >= #{query.startMonth} ]]>
            </if>
            <if test="null != query.endMonth and '' != query.endMonth">
                <![CDATA[  AND monthly <= #{query.endMonth} ]]>
            </if>
            <if test="null != query.name and '' != query.name">
                AND engineer_name LIKE CONCAT( '%', #{query.name}, '%' )
            </if>
        </where>
        GROUP BY
        monthly,
        engineer_id
        ORDER BY
        monthly DESC
    </select>
</mapper>