<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.customer.infrastructure.mapper.CustomerCallRecordMapper">
    <select id="statistics" resultType="com.hightop.benyin.customer.application.vo.CustomerCallRecordStatisticsVo">
        select t.*,ifnull(t1.signCount,0)  signCount
        from (
        select t.operat_id,t.opt_user_name userName,yearMonth,count(1) custCount
        from (
        select operat_id,opt_user_name,date_format(create_time,'%Y-%m') yearMonth,customer_id
        from b_customer_call_record
        where create_time is not null
        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and DATE_FORMAT(create_time,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth and '' != qo.endMonth ">
            and DATE_FORMAT(create_time,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.userName and '' != qo.userName ">
            and opt_user_name like concat ('%',#{qo.userName},'%')
        </if>
        group by operat_id,opt_user_name,date_format(create_time,'%Y-%m'),customer_id) t
        group by opt_user_name,yearMonth) t
        left join (select agent_id,date_format(created_at,'%Y-%m') yearMonth,count(1) signCount from b_customer_contract group by agent_id,date_format(created_at,'%Y-%m'))
        t1 on t1.agent_id = t.operat_id and t1.yearMonth = t.yearMonth

        order by t.userName,yearMonth
    </select>

    <select id="statisticsCallGoal" resultType="com.hightop.benyin.customer.application.vo.CustomerCallGoalInfoVo">
        select call_goal,count(1) number
        from b_customer_call_record
        where deleted=0
        <if test="null != qo.startMonth and '' != qo.startMonth ">
            and DATE_FORMAT(create_time,'%Y-%m') &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth and '' != qo.endMonth ">
            and DATE_FORMAT(create_time,'%Y-%m') &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.userName and '' != qo.userName ">
            and opt_user_name like concat ('%',#{qo.userName},'%')
        </if>
        group by call_goal
    </select>
    <select id="conversionList" resultType="com.hightop.benyin.customer.application.vo.CustomerConversionVo">
        select t.id,t.customerSeqId,t.customerName,t.created_at enterDate,t.recentlyInterview,t.recentlySearch,t.recentlyVisit,
               t.hasAddr,
               t.deviceNum>0 hasDevice,
               t.dataIntegrity,
        case when t.recentlyTrading=0 then null else t.recentlyTrading end recentlyTrading,
               t.cliMechineNum>0 hasCliMechine,
               t.tradingNum>0 hasTrading,
               t.mechineNum>0 hasMechine,
               t.orderNum>0 hasOrder,
               t.retailNum>0 hasRetail,
               t.halfAllNum>0 hasHalfAll,
               t.monthNum>0 hasMonth,
               t.rentNum>0 hasRent
        from (
                 select t.id,t.seq_id customerSeqId,t.name customerName,
                        case when t.address is null || t.address='未登地址' then false else true end hasAddr,
                        (select count(1) from b_customer_device_group where customer_id=t.id) deviceNum,
                        false dataIntegrity,
                        (select count(1) from b_customer_device_group where reg_cli_state=1 and customer_id=t.id) cliMechineNum,
                        ifnull(t1.orderNum,0)+ifnull(t2.workNum,0) tradingNum,
                        ifnull(t1.orderNum,0) orderNum,
                        GREATEST( ifnull(t1.orderRecently,0),ifnull(t2.workRecently,0))    recentlyTrading,
                        (select count(1) from tb_trade_order where is_mechine=1 and  customer_id=t.id) mechineNum,
                        (select count(1) from tb_work_order where status != 'close' and (ser_type not in ('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF') or ser_type is null) and customer_id=t.id) retailNum,
                        (select count(1) from tb_work_order where  status != 'close' and ser_type in('BUY_FULL','BUY_HALF','HALF','ALL') and customer_id=t.id) halfAllNum,
                        (select count(1) from tb_work_order where  status != 'close' and ser_type in('PACKAGE_ALL','PACKAGE_HALF') and customer_id=t.id) monthNum,
                        (select count(1) from tb_work_order where  status != 'close' and ser_type in ('RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL') and customer_id=t.id) rentNum,
                        t.created_at,t3.recentlyInterview,t4.recentlySearch,t7.recentlyVisit
                 from b_customer t
                          left join (select t1.customer_id, count(1) orderNum, max(t1.created_at) orderRecently
                                     from tb_trade_order t1
                                     where t1.order_status != 'CLOSED'
                                     group by t1.customer_id) t1 on t1.customer_id = t.id
                          left join (select count(1) workNum, max(t2.created_at) workRecently, t2.customer_id
                                     from tb_work_order t2
                                     where t2.status != 'close'
                                     group by t2.customer_id) t2 on t2.customer_id = t.id

                          left join (select max(t3.finished_at) recentlyInterview, t3.customer_id
                                     from b_customer_page_view t3
                                     group by t3.customer_id) t3 on t3.customer_id = t.id
                          left join (select max(t4.create_time) recentlySearch, t4.customer_id
                                     from b_customer_search_out_log t4
                                     group by t4.customer_id) t4 on t4.customer_id = t.id
                          left join (select t5.customer_id, max(t5.created_at) orderRecently
                                     from tb_trade_order t5
                                     where t5.order_status != 'CLOSED'
                                     group by t5.customer_id) t5 on t5.customer_id = t.id
                          left join (select max(t6.created_at) workRecently, t6.customer_id
                                     from tb_work_order t6
                                     where t6.status != 'close'
                                     group by t6.customer_id) t6 on t6.customer_id = t.id
                          left join (select max(ifnull(t7.reach_shop_time,t7.create_time)) recentlyVisit, t7.customer_id
                                     from b_customer_call_record t7
                                     group by t7.customer_id) t7 on t7.customer_id = t.id
                   where  t.deleted = 0
                     <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
                         and t.seq_id like concat ('%',#{qo.customerSeqId},'%')
                     </if>
        <if test="null != qo.recentlyInterviewStart and '' != qo.recentlyInterviewStart ">
            and t3.recentlyInterview &gt;= concat(#{qo.recentlyInterviewStart},' 00:00:00')
        </if>
        <if test="null != qo.recentlyInterviewEnd and '' != qo.recentlyInterviewEnd ">
            and t3.recentlyInterview &lt;= concat(#{qo.recentlyInterviewEnd},' 23:59:59')
        </if>

        <if test="null != qo.recentlySearchStart and '' != qo.recentlySearchStart ">
            and t4.recentlySearch &gt;= concat(#{qo.recentlySearchStart},' 00:00:00')
        </if>
        <if test="null != qo.recentlySearchEnd and '' != qo.recentlySearchEnd ">
            and t4.recentlySearch &lt;= concat(#{qo.recentlySearchEnd},' 23:59:59')
        </if>

        <if test="null != qo.recentlyVisitStart and '' != qo.recentlyVisitStart ">
            and t7.recentlyVisit &gt;= concat(#{qo.recentlyVisitStart},' 00:00:00')
        </if>
        <if test="null != qo.recentlyVisitEnd and '' != qo.recentlyVisitEnd ">
            and t7.recentlyVisit &lt;= concat(#{qo.recentlyVisitEnd},' 23:59:59')
        </if>
                     <if test="null != qo.customerName and '' != qo.customerName ">
                         and t.name like concat ('%',#{qo.customerName},'%')
                     </if>

                     <if test="null != qo.enterStartDate and '' != qo.enterStartDate ">
                         and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.enterStartDate}
                     </if>
                     <if test="null != qo.enterEndDate and '' != qo.enterEndDate ">
                         and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.enterEndDate}
                     </if>
                     <if test="null != qo.regionPath and '' != qo.regionPath ">
                         and t.region_code like CONCAT( #{qo.regionPath},'%')
                     </if>
                     <if test="null != qo.hasAddr ">
                        <choose>
                            <when test="qo.hasAddr == true">
                                and t.address is not null and t.address != '未登地址'
                            </when>
                            <otherwise>
                                and (t.address is null or t.address = '未登地址')
                            </otherwise>
                        </choose>
                     </if>
             ) t
            where 1=1
        <if test="null != qo.hasDevice ">
            <choose>
                <when test="qo.hasDevice == true">
                    and t.deviceNum>0
                </when>
                <otherwise>
                    and t.deviceNum=0
                </otherwise>
            </choose>
        </if>
        <if test="null != qo.hasCliMechine ">
            <choose>
                <when test="qo.hasCliMechine == true">
                    and t.cliMechineNum>0
                </when>
                <otherwise>
                    and t.cliMechineNum=0
                </otherwise>
            </choose>
        </if>

        <if test="null != qo.hasTrading ">
            <choose>
                <when test="qo.hasTrading == true">
                    and t.tradingNum>0
                </when>
                <otherwise>
                    and t.tradingNum=0
                </otherwise>
            </choose>
        </if>

        <if test="null != qo.hasOrder ">
            <choose>
                <when test="qo.hasOrder == true">
                    and t.orderNum>0
                </when>
                <otherwise>
                    and t.orderNum=0
                </otherwise>
            </choose>
        </if>

        <if test="null != qo.hasMechine ">
            <choose>
                <when test="qo.hasMechine == true">
                    and t.mechineNum>0
                </when>
                <otherwise>
                    and t.mechineNum=0
                </otherwise>
            </choose>
        </if>

        <if test="null != qo.hasRetail ">
            <choose>
                <when test="qo.hasRetail == true">
                    and t.retailNum>0
                </when>
                <otherwise>
                    and t.retailNum=0
                </otherwise>
            </choose>
        </if>

        <if test="null != qo.hasHalfAll ">
            <choose>
                <when test="qo.hasHalfAll == true">
                    and t.halfAllNum>0
                </when>
                <otherwise>
                    and t.halfAllNum=0
                </otherwise>
            </choose>
        </if>

        <if test="null != qo.hasMonth ">
            <choose>
                <when test="qo.hasMonth == true">
                    and t.monthNum>0
                </when>
                <otherwise>
                    and t.monthNum=0
                </otherwise>
            </choose>
        </if>

        <if test="null != qo.hasRent ">
            <choose>
                <when test="qo.hasRent == true">
                    and t.rentNum>0
                </when>
                <otherwise>
                    and t.rentNum=0
                </otherwise>
            </choose>
        </if>

        <if test="null != qo.recentlyTradingStart and '' != qo.recentlyTradingStart ">
            and t.recentlyTrading &gt;= concat(#{qo.recentlyTradingStart},' 00:00:00')
        </if>
        <if test="null != qo.recentlyTradingEnd and '' != qo.recentlyTradingEnd ">
            and t.recentlyTrading &lt;= concat(#{qo.recentlyTradingEnd},' 23:59:59')
        </if>



         order by t.customerName
    </select>

    <select id="viscosityList" resultType="com.hightop.benyin.customer.application.vo.CustomerViscosityVo">
        select t.id,t.customerSeqId,
               t.customerName,
               t.enterDate,
               t.tradingDays,
               case when t.recentlyTrading=0 then null else t.recentlyTrading end recentlyTrading,
               t.recentlyInterview,
               t.interviewDays,
               t.recentlySearch,
               t.searchDays,
               t.recentlyVisit,
               t.visitDays,
               t.reportDays,
             case when  t.recentlyReport=0 then null else t.recentlyReport end recentlyReport,
        case when t.recentlyCorrelation=0  then null else t.recentlyCorrelation end recentlyCorrelation
        from (
        select t.id,
        t.seq_id                                  customerSeqId,
        t.name                                    customerName,
        t.created_at                              enterDate,
        DATEDIFF(CURDATE(), GREATEST( ifnull(t1.orderRecently,0),ifnull(t2.workRecently,0)) ) tradingDays,
        GREATEST( ifnull(t1.orderRecently,0),ifnull(t2.workRecently,0))    recentlyTrading,
        GREATEST( ifnull(t1.orderRecently,0),ifnull(t2.workRecently,0),ifnull(t4.recentlySearch,0),ifnull(t8.recentlyVisit,0),ifnull(t3.recentlyInterview,0))    recentlyCorrelation,
        t3.recentlyInterview,
        DATEDIFF(CURDATE(), t3.recentlyInterview) interviewDays,
        t4.recentlySearch,
        DATEDIFF(CURDATE(), t4.recentlySearch)    searchDays,
        t8.recentlyVisit,
        DATEDIFF(CURDATE(), t8.recentlyVisit)    visitDays,
        DATEDIFF(CURDATE(),  GREATEST( ifnull(t5.countRecently,0),ifnull(t6.powderRecently,0),ifnull(t7.stateRecently,0))) reportDays,
        GREATEST( ifnull(t5.countRecently,0),ifnull(t6.powderRecently,0),ifnull(t7.stateRecently,0)) recentlyReport
        from b_customer t
        left join (select t1.customer_id, max(t1.created_at) orderRecently
        from tb_trade_order t1
        where t1.order_status != 'CLOSED'
        group by t1.customer_id) t1 on t1.customer_id = t.id
        left join (select max(t2.created_at) workRecently, t2.customer_id
        from tb_work_order t2
        where t2.status != 'close'
        group by t2.customer_id) t2 on t2.customer_id = t.id
        left join (select max(t3.finished_at) recentlyInterview, t3.customer_id
        from b_customer_page_view t3
        group by t3.customer_id) t3 on t3.customer_id = t.id
        left join (select max(t4.create_time) recentlySearch, t4.customer_id
        from b_customer_search_out_log t4
        group by t4.customer_id) t4 on t4.customer_id = t.id
        left join (select max(t5.created_at) countRecently, t5.customer_id
        from b_iot_counter t5
        group by t5.customer_id) t5 on t5.customer_id = t.id
        left join (select max(t6.created_at) powderRecently, t6.customer_id
        from b_iot_powder t6
        group by t6.customer_id) t6 on t6.customer_id = t.id
        left join (select max(t7.created_at) stateRecently, t7.customer_id
        from b_iot_state t7
        group by t7.customer_id) t7 on t7.customer_id = t.id
        left join (select max(ifnull(t8.reach_shop_time,t8.create_time)) recentlyVisit, t8.customer_id
        from b_customer_call_record t8
        group by t8.customer_id) t8 on t8.customer_id = t.id
    where t.deleted = 0
        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and t.name like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.enterStartDate and '' != qo.enterStartDate ">
            and t.created_at &gt;= concat(#{qo.enterStartDate},' 00:00:00')
        </if>
        <if test="null != qo.enterEndDate and '' != qo.enterEndDate ">
            and t.created_at &lt;= concat(#{qo.enterEndDate},' 23:59:59')
        </if>
             order by t.seq_id desc
         ) t
        where 1=1

        <if test="null != qo.tradingStartDays ">
            and t.tradingDays &gt;= #{qo.tradingStartDays}
        </if>

        <if test="null != qo.tradingEndDays ">
            and t.tradingDays &lt;= #{qo.tradingEndDays}
        </if>

        <if test="null != qo.reportStartDays ">
            and t.reportDays &gt;= #{qo.reportStartDays}
        </if>

        <if test="null != qo.reportEndDays ">
            and t.reportDays &lt;= #{qo.reportEndDays}
        </if>

        <if test="null != qo.interviewStartDays ">
            and t.interviewDays &gt;= #{qo.interviewStartDays}
        </if>

        <if test="null != qo.interviewEndDays ">
            and t.interviewDays &lt;= #{qo.interviewEndDays}
        </if>

        <if test="null != qo.searchStartDays ">
            and t.searchDays &gt;= #{qo.searchStartDays}
        </if>

        <if test="null != qo.searchEndDays ">
            and  t.searchDays &lt;= #{qo.searchEndDays}
        </if>


        <if test="null != qo.visitStartDays ">
            and t.visitDays &gt;= #{qo.visitStartDays}
        </if>

        <if test="null != qo.visitEndDays ">
            and t.visitDays &lt;= #{qo.visitEndDays}
        </if>

    </select>
</mapper>
