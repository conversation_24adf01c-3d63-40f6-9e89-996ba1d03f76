<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.TStatisticsEngineerMonthGrossProfitMapper">

    <select id="queryEngineerInfo"
            resultType="com.hightop.benyin.statistics.infrastructure.entity.TStatisticsEngineerMonthGrossProfit">
        SELECT
            b.id AS engineer_id,
            b.CODE AS engineer_code,
            b.<PERSON>AME AS engineer_name,
            #{day} monthly,
            COUNT(DISTINCT gr.id) AS customerNum,
            COUNT(DISTINCT gr.customer_id) AS machineNum
        FROM
            st_user_basic b
                LEFT JOIN st_user_role ur ON b.id = ur.user_id
                LEFT JOIN st_role r ON ur.role_id = r.id
                LEFT JOIN b_customer_device_group gr ON gr.operat_id = b.id AND gr.deleted = 0
        WHERE
            b.is_available = 1
          AND r.CODE = 'engineer'
        GROUP BY
            b.id, b.CODE, b.NAME
    </select>

    <select id="engineerEveryDaysMonthGrossProfitTask"
            resultType="com.hightop.benyin.statistics.infrastructure.entity.TStatisticsEngineerMonthGrossProfit">
        SELECT
        t.id workOrderId,
        t.engineer_id,
        t.device_group_id,
        b.CODE AS engineer_code,
        b.NAME AS engineer_name,
            #{day} monthly,
        t.STATUS,
        t.ser_type,
        t.created_at exc_report_time,
        t.actual_arrive_time,
            IF(t.actual_arrive_time IS NOT NULL , 1, 0) arrive_nums,
            IF(t.order_receive_time IS NOT NULL , 1, 0) receive_nums,
            IF(t.departure_time IS NOT NULL , 1, 0) departure_time_num,
            IF(t.send_report_time IS NOT NULL,  1, 0) repair_time_num,
            IF(t.confirm_report_time IS NOT NULL,  1, 0) confirmd_num,
            ( CASE WHEN t.ser_type = 'SCATTERED' THEN 1 ELSE 0 END ) scattered_nums,
            ( CASE WHEN t.STATUS IN ( 'engineer_receive', 'engineer_departure', 'engineer_arrive', 'wait_confirmed_report', 'completed', 'to_be_settled', 'wait_audit') THEN 1 ELSE 0 END ) AS receive_count,
            ( CASE WHEN t.STATUS = 'customer_confirming' THEN 1 ELSE 0 END ) AS unconfirmed_nums,
            ( CASE WHEN t.STATUS IN ( 'customer_confirming','wait_confirmed_report','to_be_settled','wait_audit','completed') THEN 1 ELSE 0 END ) AS submit_report_num,
            ( CASE WHEN t.STATUS IN ( 'to_be_settled' , 'wait_audit', 'completed' ) THEN 1 ELSE 0 END ) AS confirmed_num,
            TIMESTAMPDIFF(MINUTE, t.created_at, t.order_receive_time) receive_time,
            TIMESTAMPDIFF(MINUTE, t.departure_time, t.actual_arrive_time) on_road_time,
            TIMESTAMPDIFF(MINUTE, t.actual_arrive_time, t.send_report_time) repair_time,
            TIMESTAMPDIFF(MINUTE, t.send_report_time, t.confirm_report_time) confirm_time,
            t.repair_pay,
            t.item_pay,
            t.total_pay
        FROM
            tb_work_order t LEFT JOIN st_user_basic b ON t.engineer_id = b.id AND b.is_available = 1
        WHERE
            t.engineer_id IN (
                <foreach collection="engineerIds" item="item" separator=",">
                    #{item}
                </foreach>
            )
          AND DATE_FORMAT( t.created_at, "%Y-%m" ) = #{day}
          AND t.deleted = 0
          AND t.STATUS != 'close'
    </select>

    <select id="queryEngineerApply" resultType="com.hightop.benyin.statistics.infrastructure.TO.CommonData">
        SELECT
            user_id "key",
            COUNT(id) "value"
        FROM
            tb_item_store
        WHERE
            user_id IN (
            <foreach collection="engineerIds" item="item" separator=",">
                #{item}
            </foreach>
                )
          AND user_type = 'ENGINEER'
          AND DATE_FORMAT( created_at, "%Y-%m" ) = #{day}
          AND deleted = 0
        GROUP BY
            user_id
    </select>

    <select id="queryEngineerMonthGrossProfitList"
            resultType="com.hightop.benyin.statistics.infrastructure.entity.TStatisticsEngineerMonthGrossProfit">
        SELECT
            monthly,
            engineer_id,
            engineer_code,
            engineer_name,
            SUM( actual_receipt ) actual_receipt,
            SUM( black_nums ) black_nums,
            SUM( color_nums ) color_nums,
            SUM(arrive_out_time_nums) arrive_out_time_nums,
            SUM(unconfirmed_nums) unconfirmed_nums,
            SUM(repair_nums) repair_nums,
            SUM(receive_nums) receive_nums,
            SUM(scattered_nums) scattered_nums,
            SUM(total_pay) total_pay,
            SUM(repair_pay) repair_pay,
            SUM(item_pay) item_pay,
            SUM(departure_time_num) departure_time_num,
            SUM(arrive_nums) arrive_nums,
            SUM(submit_report_num) submit_report_num,
            SUM(confirmed_num) confirmed_num,
            (CASE WHEN SUM(receive_time) is not null and ( SUM(receive_nums) is not null or SUM(receive_nums) != 0 ) THEN FLOOR(SUM(receive_time) / SUM(receive_nums))  ELSE NULL END )  receive_time,
            (CASE WHEN SUM(on_road_time) is not null and ( SUM(departure_time_num) is not null or SUM(departure_time_num) != 0 ) THEN FLOOR(SUM(on_road_time) / SUM(departure_time_num))  ELSE NULL END )  on_road_time,
            (CASE WHEN SUM(repair_time) is not null and ( SUM(repair_nums) is not null or SUM(repair_nums) != 0 ) THEN FLOOR(SUM(repair_time) / SUM(repair_nums))  ELSE NULL END ) repair_time,
            (CASE WHEN SUM(confirm_time) is not null and ( SUM(confirmed_num) is not null or SUM(confirmed_num) != 0 ) THEN FLOOR(SUM(confirm_time) / SUM(confirmed_num))  ELSE NULL END )  confirm_time,
            SUM(apply_num) apply_num
        FROM
            (
                SELECT
                    p.monthly,
                    p.engineer_id,
                    b.CODE engineer_code,
                    b.NAME engineer_name,
                    SUM( actual_receipt ) actual_receipt,
                    SUM( black_nums ) black_nums,
                    SUM( color_nums ) color_nums,
                    0 arrive_out_time_nums,
                    0 unconfirmed_nums,
                    0 repair_nums,
                    0 receive_nums,
                    0 scattered_nums,
                    0 total_pay,
                    0 repair_pay,
                    0 item_pay,
                    0 departure_time_num,
                    0 arrive_nums,
                    0 submit_report_num,
                    0 confirmed_num,
                    0 receive_time,
                    0 on_road_time,
                    0 repair_time,
                    0 confirm_time,
                    0 apply_num
                FROM
                    t_statistics_machine_gross_profit p
                        INNER JOIN st_user_basic b ON p.engineer_id = b.id
                WHERE
                    p.engineer_id IS NOT NULL
                <if test="query.monthly != null and query.monthly != '' ">
                    AND p.monthly = #{query.monthly}
                </if>
                GROUP BY
                    p.engineer_id
                UNION ALL
                SELECT
                    monthly,
                    engineer_id,
                    engineer_code,
                    engineer_name,
                    0 actual_receipt,
                    0 black_nums,
                    0 color_nums,
                    arrive_out_time_nums,
                    unconfirmed_nums,
                    repair_nums,
                    receive_nums,
                    scattered_nums,
                    total_pay,
                    repair_pay,
                    item_pay,
                    departure_time_num,
                    arrive_nums,
                    submit_report_num,
                    confirmed_num,
                    receive_time,
                    on_road_time,
                    repair_time,
                    confirm_time,
                    apply_num
                FROM
                    t_statistics_engineer_month_gross_profit
                <where>
                    <if test="query.monthly != null and query.monthly != '' ">
                        AND monthly = #{query.monthly}
                    </if>
                </where>
            ) t
        <where>
            <if test="query.monthly != null and query.monthly != '' ">
                AND monthly = #{query.monthly}
            </if>
            <if test="query.engineerName != null and query.engineerName != '' ">
                AND engineer_name like concat('%', #{query.engineerName},'%')
            </if>
            <if test="query.arriveOutTimeNums != null">
                <![CDATA[ AND arrive_out_time_nums >= #{query.arriveOutTimeNums} ]]>
            </if>
        </where>
        GROUP BY
            engineer_id,
            monthly
        ORDER BY
            monthly DESC,
            actual_receipt DESC
    </select>
    <select id="queryMachineMonthGrossProfit"
            resultType="com.hightop.benyin.statistics.infrastructure.entity.TStatisticsEngineerMonthGrossProfit">
        SELECT
        monthly,
        engineer_name,
        SUM( actual_receipt ) actual_receipt,
        SUM( black_nums ) black_nums,
        SUM( color_nums ) color_nums,
        SUM( arrive_out_time_nums) arrive_out_time_nums,
        SUM( unconfirmed_nums) unconfirmed_nums,
        SUM( repair_nums) repair_nums,
        SUM( receive_nums) receive_nums,
        SUM( scattered_nums) scattered_nums,
        SUM( total_pay) total_pay,
        SUM( repair_pay) repair_pay,
        SUM( item_pay ) item_pay
        FROM
        (
        SELECT
        p.monthly,
        b.`name` engineer_name,
        SUM( actual_receipt ) actual_receipt,
        SUM( black_nums ) black_nums,
        SUM( color_nums ) color_nums,
        0 arrive_out_time_nums,
        0 unconfirmed_nums,
        0 repair_nums,
        0 receive_nums,
        0 scattered_nums,
        0 total_pay,
        0 repair_pay,
        0 item_pay
        FROM
        t_statistics_machine_gross_profit p
        INNER JOIN st_user_basic b ON p.engineer_id = b.id
        WHERE
        p.engineer_id IS NOT NULL
        <if test="query.monthly != null and query.monthly != '' ">
            AND p.monthly = #{query.monthly}
        </if>
        GROUP BY
        p.engineer_id
        UNION ALL
        SELECT
        monthly,
        engineer_name,
        0 actual_receipt,
        0 black_nums,
        0 color_nums,
        arrive_out_time_nums,
        unconfirmed_nums,
        repair_nums,
        receive_nums,
        scattered_nums,
        total_pay,
        repair_pay,
        item_pay
        FROM
        t_statistics_engineer_month_gross_profit
        <where>
            <if test="query.monthly != null and query.monthly != '' ">
                AND monthly = #{query.monthly}
            </if>
        </where>
        ) t
        <where>
            <if test="query.monthly != null and query.monthly != '' ">
                AND monthly = #{query.monthly}
            </if>
            <if test="query.engineerName != null and query.engineerName != '' ">
                AND engineer_name like concat('%', #{query.engineerName},'%')
            </if>
            <if test="query.arriveOutTimeNums != null">
                <![CDATA[ AND arrive_out_time_nums >= #{query.arriveOutTimeNums} ]]>
            </if>
        </where>
    </select>

    <select id="queryEngineerMonthStatistics"
            resultType="com.hightop.benyin.statistics.infrastructure.entity.TStatisticsEngineerMonthGrossProfit">
        SELECT
        monthly,
        SUM( actual_receipt ) actual_receipt,
        SUM( black_nums ) black_nums,
        SUM( color_nums ) color_nums,
        SUM( repair_nums ) repair_nums,
        SUM( receive_nums ) receive_nums,
        SUM( scattered_nums ) scattered_nums,
        SUM( total_pay ) total_pay,
        SUM( repair_pay ) repair_pay,
        SUM( item_pay ) item_pay,
        SUM( submit_report_num ) submit_report_num,
        SUM( confirmed_num ) confirmed_num
        FROM
        (
        SELECT
        p.monthly,
        SUM( actual_receipt ) actual_receipt,
        SUM( black_nums ) black_nums,
        SUM( color_nums ) color_nums,
        0 repair_nums,
        0 receive_nums,
        0 scattered_nums,
        0 total_pay,
        0 repair_pay,
        0 item_pay,
        0 submit_report_num,
        0 confirmed_num
        FROM
        t_statistics_machine_gross_profit p
        INNER JOIN st_user_basic b ON p.engineer_id = b.id
        WHERE
        p.engineer_id IS NOT NULL
        GROUP BY
        <!--p.engineer_id-->
        p.monthly
        UNION ALL
        SELECT
        monthly,
        0 actual_receipt,
        0 black_nums,
        0 color_nums,
        repair_nums,
        receive_nums,
        scattered_nums,
        total_pay,
        repair_pay,
        item_pay,
        submit_report_num,
        confirmed_num
        FROM
        t_statistics_engineer_month_gross_profit
        ) t
        <where>
            <if test="query.startMonthly != null and query.startMonthly != '' ">
                <![CDATA[ AND monthly >= #{query.startMonthly} ]]>
            </if>
            <if test="query.endMonthly != null and query.endMonthly != '' ">
                <![CDATA[ AND monthly <= #{query.endMonthly} ]]>
            </if>
        </where>
        GROUP BY
        monthly
        ORDER BY
        monthly DESC,
        actual_receipt DESC
    </select>
</mapper>
