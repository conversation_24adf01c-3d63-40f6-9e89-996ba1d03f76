<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.purchase.infrastructure.mapper.PurchaseOrderGoodsMapper">

    <select id="detailPage" resultType="com.hightop.benyin.purchase.infrastructure.entity.PurchaseOrderGoods">
        SELECT t.id,
        t.purchase_order_code,
        tg.manufacturer_order_code,
        t.article_id,
        t.article_code,
        t.article_type,
        t.manufacturer_id,
        t.plan_num,
        t.approve_num,
        t.number,
        t.delivery_num,
        t.receive_num,
        t.refund_num,
        t.refund_amount,
        t.receipt_code,
        t.price,
        t.price_distribution,
        t.sum_price,
        t.created_at,
        t.updated_at,
        t.deleted,
        t.pay_status,
        t1.part_id ,
        t1.number_oem AS oemNumber,
        t1.name AS articleName,
        t1.unit AS unit,
        t1.type AS type,
        t1.manufacturer_channel AS manufacturerChannel,
        t2.name AS manufacturerName
        FROM b_purchase_order_goods t
            left join b_manufacturer_order_goods tg on tg.purchase_order_goods_id = t.id
        LEFT JOIN b_storage_article t1 ON (t1.code = t.article_code)
        LEFT JOIN b_manufacturer t2 ON (t2.id = t.manufacturer_id)
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            LEFT JOIN tb_part_product_tree t3 ON (t3.part_id = t1.part_id)
        </if>
        WHERE t.deleted = false and t.number &gt; 0
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>

        <if test="null != qo.manufacturerOrderCode and '' != qo.manufacturerOrderCode ">
            and tg.manufacturer_order_code like concat ('%',#{qo.manufacturerOrderCode},'%')
        </if>

        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t2.name like concat ('%',#{qo.manufacturerName},'%')
        </if>

        <if test="null != qo.type and '' != qo.type ">
            and t1.type like concat ('%',#{qo.type},'%')
        </if>

        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and t1.manufacturer_channel like concat ('%',#{qo.manufacturerChannel},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>
        <choose>
            <when test="null != qo.isStatistics and  qo.isStatistics == 1">
                order by t.number desc
            </when>
            <otherwise>
                ORDER BY tg.manufacturer_order_code,t.created_at DESC
            </otherwise>
        </choose>
    </select>
    <select id="monthlyPage" resultType="com.hightop.benyin.purchase.api.dto.PurchaseMonthlyVo">
        SELECT
        DATE_FORMAT(t.created_at,'%Y-%m') AS yearMonth,
        sum(t.number) number,
        sum(t.refund_num) refundNum,
        sum(t.receive_num) receiveNum,
        sum(t.sum_price) amount,
        sum(t.refund_amount) refundAmount
        FROM b_purchase_order_goods t
        left join b_purchase_order t1 on t1.purchase_code = t.purchase_order_code
        WHERE t1.order_status!='CLOSE'
        <if test="null != qo.yearMonth and '' != qo.yearMonth ">
            and DATE_FORMAT(t.created_at,'%Y-%m') = #{qo.yearMonth}
        </if>
        <if test="null != qo.yearMonthStart and '' != qo.yearMonthStart ">
            and DATE_FORMAT(t.created_at,'%Y-%m') &gt;= #{qo.yearMonthStart}
        </if>
        <if test="null != qo.yearMonthEnd and '' != qo.yearMonthEnd ">
            and DATE_FORMAT(t.created_at,'%Y-%m') &lt;= #{qo.yearMonthEnd}
        </if>
        group by DATE_FORMAT(t.created_at,'%Y-%m')
        ORDER BY DATE_FORMAT(t.created_at,'%Y-%m') DESC
    </select>
    <select id="purchaseSum" resultType="com.hightop.benyin.purchase.api.dto.ManufacterDeliveryVo">
        SELECT
        sum(t.number) number,
        sum(t.refund_num) returnNum,
        sum(t.receive_num) receiveNum,
        sum(t.sum_price) amount,
        sum(t.refund_amount) returnAmount
        FROM b_purchase_order_goods t
        LEFT JOIN b_storage_article t1 ON (t1.code = t.article_code)
        LEFT JOIN b_manufacturer t2 ON (t2.id = t.manufacturer_id)
        left join b_purchase_order tt on tt.purchase_code = t.purchase_order_code
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            LEFT JOIN tb_part_product_tree t3 ON (t3.part_id = t1.part_id)
        </if>
        WHERE tt.order_status!='CLOSE'

        <if test="null != qo.yearMonth and '' != qo.yearMonth ">
            and DATE_FORMAT(t.created_at,'%Y-%m') = #{qo.yearMonth}
        </if>
        <if test="null != qo.yearMonthStart and '' != qo.yearMonthStart ">
            and DATE_FORMAT(t.created_at,'%Y-%m') &gt;= #{qo.yearMonthStart}
        </if>
        <if test="null != qo.yearMonthEnd and '' != qo.yearMonthEnd ">
            and DATE_FORMAT(t.created_at,'%Y-%m') &lt;= #{qo.yearMonthEnd}
        </if>

        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>

        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t2.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t2.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>

        <if test="null != qo.type and '' != qo.type ">
            and t1.type like concat ('%',#{qo.type},'%')
        </if>

        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and t1.manufacturer_channel like concat ('%',#{qo.manufacturerChannel},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
    </select>


    <select id="supplierPage" resultType="com.hightop.benyin.purchase.api.dto.PurchaseMonthlyVo">
        SELECT
        bm.name AS manufacturerName,
        sum(t.number) number,
        sum(t.refund_num) refundNum,
        sum(t.receive_num) receiveNum,
        sum(t.sum_price) amount,
        sum(t.refund_amount) refundAmount
        FROM b_purchase_order_goods t
        left join b_purchase_order tt on tt.purchase_code = t.purchase_order_code
        left join b_manufacturer bm on t.manufacturer_id = bm.id
        WHERE tt.order_status!='CLOSE'
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and bm.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and bm.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        group by bm.name
        ORDER BY bm.name DESC
    </select>

    <select id="articlePage" resultType="com.hightop.benyin.purchase.infrastructure.entity.PurchaseOrderGoods">
        SELECT
        sum(t.number) number,
        sum(t.sum_price) sumPrice,
        sum(t.receive_num) receiveNum,
        sum( t.refund_num) refundNum,
        sum( t.refund_amount)refundAmount,
        t.article_code,
        t1.part_id,
        t1.number_oem AS oemNumber,
        t1.name AS articleName,
        t.price ,
        t1.unit ,
        t1.type ,
        t1.manufacturer_channel AS manufacturerChannel
        FROM b_purchase_order_goods t
        left join b_purchase_order tt on tt.purchase_code = t.purchase_order_code
        LEFT JOIN b_storage_article t1 ON (t1.code = t.article_code)
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            LEFT JOIN tb_part_product_tree t3 ON (t3.part_id = t1.part_id)
        </if>
        WHERE  tt.order_status!='CLOSE' and t.deleted = false and t.number &gt; 0
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>

        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t2.name like concat ('%',#{qo.manufacturerName},'%')
        </if>

        <if test="null != qo.type and '' != qo.type ">
            and t1.type like concat ('%',#{qo.type},'%')
        </if>

        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and t1.manufacturer_channel like concat ('%',#{qo.manufacturerChannel},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>
        group by t.article_code,
        t.price,
        t1.part_id,
        t1.number_oem,
        t1.name,
        t1.unit,
        t1.type,
        t1.manufacturer_channel

        ORDER BY t.article_code asc

    </select>

    <select id="mechinePage" resultType="com.hightop.benyin.purchase.api.dto.PurchaseMechineVo">
        select ifnull(t.series,'无') series, sum(t.number) number,sum(t.sum_price) amount,
        sum(t.receive_num) receiveNum,
        sum(t.refundNum) refundNum, sum(t.refundAmount) refundAmount from (
        select distinct  t.id,t4.name series, t.number,t.sum_price,
        t.receive_num,
        tt.number refundNum, tt.amount refundAmount
        from b_purchase_order_goods t
        left join b_purchase_order tp on tp.purchase_code = t.purchase_order_code
        left join b_manufacturer_return_goods tt on tt.purchase_order_goods_id = t.id
        left join b_storage_article t1 on t1.code= t.article_code
        left join b_product_part_bom t2 on t2.part_id=t1.part_id
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = t3.parent_id
        where tp.order_status!='CLOSE'
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and  t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and  t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND  t2.product_id in
            <foreach collection="qo.productIds" item="id" separator=" , " open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        ) t
        group by t.series
    </select>
    <select id="mechineSum" resultType="com.hightop.benyin.purchase.api.dto.ManufacterReturnDto">
        select sum(t.number) number,sum(t.amount) amount,
        sum(t.receive_num) receiveNum,
        sum(tt.number) returnNum, sum(tt.amount) returnAmount
        from b_purchase_order_goods t
        left join b_purchase_order tt on tt.purchase_code = t.purchase_order_code
        left join b_manufacturer_return_goods tt on tt.purchase_order_goods_id = t.id
        left join b_storage_article t1 on t1.code= t.article_code
        left join b_product_part_bom t2 on t2.part_id=t1.part_id
        left join b_product_tree t3 on t3.id = t2.product_id
        left join b_product_tree t4 on t4.id = t3.parent_id
        where tt.order_status!='CLOSE'
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t1.name like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.oemNumber and '' != qo.oemNumber ">
            and t1.number_oem like concat ('%',#{qo.oemNumber},'%')
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t2.product_id in
            <foreach collection="qo.productIds" item="id" separator=" , " open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
    </select>

    <select id="getFinanceMachinePurchase" resultType="com.hightop.benyin.statistics.application.vo.FinanceMachinePurchaseVO">
        select t.*,
        round(t.amount / t.taxRate, 2) noTaxAmount,
        t.amount - round((t.amount / t.taxRate), 2) taxAmount
        from (select distinct t.id,date_format(t1.created_at, '%Y-%m-%d') createDate,
        t.purchase_code code,
        1 type,
        t4.code manufacturerCode,
        t4.name manufacturerName,
        t3.name brand,
        case when t.host_type ='2008' then tp.name else ta.mode_type end productName,
        t.host_type,
        tt.device_on,
        t.number num,
        t.product_id,
        round(t.price / 100, 2) price,
        round(t.amount / 100, 2) amount,
        case when t4.tax is null then 1.13 else 1 + t4.tax / 100 end taxRate,
        ifnull(t4.tax, 13) tax,
        t1.pay_time payment_time
        from b_machine_purchase_detail t
        left join b_machine_purchase t1 on t1.purchase_code = t.purchase_code
        left join b_manufacturer t4 on t4.id = t1.manufacturer_id
        left join b_product_accessory ta on ta.id = t.product_id
        left join b_product_tree tp on tp.id = t.product_id and t.host_type='2008'
        left join b_product_tree t3 on t3.id = substr(tp.full_id_path, 2, 20)
         join b_machine tt on tt.source_code = t1.purchase_code and tt.source_id=t.id
        where t1.status != 'CLOSED') t
            where 1=1
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createDate &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createDate &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t.type = #{qo.type}
        </if>

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="null != qo.hostType and '' != qo.hostType ">
            and t.hostType = #{qo.hostType}
        </if>

        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t.manufacturerCode like concat ('%',#{qo.manufacturerCode},'%')
        </if>

        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t.manufacturerName like concat ('%',#{qo.manufacturerName},'%')
        </if>
        order by t.code desc
    </select>
</mapper>
