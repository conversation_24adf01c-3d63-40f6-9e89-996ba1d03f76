<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.ReplacePartCountMapper">


    <select id="getReplaceInfo" resultType="com.hightop.benyin.statistics.infrastructure.entity.ReplacePartCount">
        select t.*,
               t.customerName,
               t.customerSeq,
        t1.ser_type serType,
        t1.device_group deviceGroup,
        t1.reg_cli_state,
        t3.name brand,
        t2.name machine,
        t1.product_id productId,
        t.replacer
        from (
        select tto.customer_id customerId,
               c.seq_id customerSeq,
               c.name customerName,
        tto.device_group_id deviceGroupId,
        t1.machine_num,
        tto.created_at,
        tto.color_count color,
        tto.black_white_count blackWhite,
        tto.code workCode,
        tto.id workId,
        t.item_id,
        sa.code articleCode,
        sa.name articleName,
        sa.number_oem numberOem,
        t.sale_sku_id,
        t.sale_unit_price,
        t.location,
        t.batch_code,
        sum(t.num) num,
        sum(t.sale_unit_price*t.num) amount,
        t.is_pm,
        t.part_id,
        t.id replaceId,
        'SELF' dataSource,
        c.name replacer
        from tb_self_repair_report_replace_detail t
        join tb_self_repair_report tto on t.self_repair_report_id = tto.id
        join b_customer_device_group t1 on t1.id = tto.device_group_id
        join b_customer c on tto.customer_id = c.id
        join tb_sale_sku ss on t.sale_sku_id = ss.id
        join b_storage_article sa on ss.article_code = sa.code
        where (black_white_count is not null or color_count is not null)
        <if test="null != qo.deviceGroupId ">
            and tto.device_group_id = #{qo.deviceGroupId}
        </if>
        <if test="null != qo.workId ">
            and tto.id = #{qo.workId}
        </if>
        <if test="null != qo.workCode and qo.workCode!= ''">
            and tto.code = #{qo.workCode}
        </if>
        <if test="null != qo.customerId ">
            and tto.customer_id = #{qo.customerId}
        </if>
        <if test="null != qo.customerSeq and '' != qo.customerSeq ">
            and c.seq_id like concat ('%',#{qo.customerSeq},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and c.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t1.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>
        <if test="null != qo.reportTimeStart">
            and tto.created_at &gt;= #{qo.reportTimeStart}
        </if>
        <if test="null != qo.reportTimeEnd">
            and tto.created_at &lt;= #{qo.reportTimeEnd}
        </if>
        <if test="null != qo.beginDate and '' != qo.beginDate ">
        and t.created_at &gt;= concat(#{qo.beginDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and sa.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and sa.code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and sa.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>

        group by tto.customer_id ,
        tto.device_group_id ,
        tto.created_at,
        tto.color_count ,
        tto.black_white_count ,
        tto.code ,
        t.item_id,
        sa.code ,
        sa.name ,
        t.sale_sku_id,
        t.sale_unit_price,
        t.location,
        t.is_pm,
        t.part_id,
        tto.id
        union all
        select trr.customer_id customerId,
        c.seq_id customerSeq,
        c.name customerName,
        trr.device_group_id deviceGroupId,
        t1.machine_num,
        t.created_at,
        trr.color_count color,
        trr.black_white_count blackWhite,
        twr.code workCode,
        twr.id workId,
        trd.item_id,
        sa.code articleCode,
        sa.name articleName,
        sa.number_oem numberOem,
        trd.sale_sku_id,
        trd.sale_unit_price,
        trd.location,
        trd.batch_code,
        sum(trd.num) num,
        sum(trd.sale_unit_price*trd.num) amount,
        trd.is_pm,
        trd.part_id,
        trr.id replaceId,
        'WORK' dataSource,
        ub.name replacer
        from tb_replace_order t
        join tb_work_order twr on t.work_order_id = twr.id
        join b_customer c on t.customer_id = c.id
        join tb_repair_report trr on twr.id = trr.work_order_id
        join b_customer_device_group t1 on t1.id = twr.device_group_id
        join tb_replace_detail trd on t.id = trd.replace_order_id
        join tb_sale_sku ss on trd.sale_sku_id = ss.id
        join b_storage_article sa on ss.article_code = sa.code
        left join st_user_basic ub on ub.id = t.engineer_id
        where twr.status = 'completed' and trd.deleted=0 and  t.deleted=0
        <if test="null != qo.deviceGroupId ">
            and t.device_group_id = #{qo.deviceGroupId}
        </if>
        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t1.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>
        <if test="null != qo.customerId ">
            and t.customer_id = #{qo.customerId}
        </if>
        <if test="null != qo.customerSeq and '' != qo.customerSeq ">
            and c.seq_id like concat ('%',#{qo.customerSeq},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and c.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.reportTimeStart">
            and twr.created_at &gt;= #{qo.reportTimeStart}
        </if>
        <if test="null != qo.workId ">
            and twr.id = #{qo.workId}
        </if>
        <if test="null != qo.workCode and qo.workCode!= ''">
            and twr.code = #{qo.workCode}
        </if>
        <if test="null != qo.reportTimeEnd">
            and twr.created_at &lt;= #{qo.reportTimeEnd}
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and sa.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and sa.code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and sa.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>

        <if test="null != qo.beginDate and '' != qo.beginDate ">
            and t.created_at &gt;= concat(#{qo.beginDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>

        group by trr.customer_id ,
        trr.device_group_id ,
        twr.created_at,
        trr.color_count ,
        trr.black_white_count ,
        twr.code ,
        trd.item_id,
        sa.code ,
        sa.name ,
        trd.sale_sku_id,
        trd.sale_unit_price,
        trd.location,
        trd.is_pm,
        trd.part_id,
        trr.id
        ) t
        left join b_customer_device_group t1 on t1.id = t.deviceGroupId
        left join b_product_tree t2 on t2.id = t1.product_id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        where 1=1
        <if test="null != qo.dataSource and qo.dataSource!= ''">
            and t.dataSource = #{qo.dataSource}
        </if>
        <if test="null != qo.deviceGroup and qo.deviceGroup!= ''">
            and t1.device_group = #{qo.deviceGroup}
        </if>
        <if test="null != qo.regCliState and qo.regCliState!= ''">
            and t1.reg_cli_state = #{qo.regCliState}
        </if>
        <if test="null!=qo.serTypes and !qo.serTypes.isEmpty()">
            AND t1.ser_type in
            <foreach collection="qo.serTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t1.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        order by created_at desc
    </select>

    <select id="getReplaceSummary" resultType="com.hightop.benyin.statistics.infrastructure.entity.ReplacePartCount">
        select sum(t.num) num,
               sum(t.amount) amount
        from (
        select tto.customer_id customerId,
        c.seq_id customerSeq,
        c.name customerName,
        tto.device_group_id deviceGroupId,
        tto.created_at,
        tto.color_count color,
        tto.black_white_count blackWhite,
        tto.code workCode,
        t.item_id,
        sa.code articleCode,
        sa.name articleName,
        sa.number_oem numberOem,
        t.sale_sku_id,
        t.sale_unit_price,
        t.location,
        t.batch_code,
        sum(t.num) num,
        sum(t.sale_unit_price*t.num) amount,
        t.is_pm,
        t.part_id,
        t.id replaceId,
        'SELF' dataSource
        from tb_self_repair_report_replace_detail t
        join tb_self_repair_report tto on t.self_repair_report_id = tto.id
        join b_customer c on tto.customer_id = c.id
        join tb_sale_sku ss on t.sale_sku_id = ss.id
        join b_storage_article sa on ss.article_code = sa.code
        where (black_white_count is not null or color_count is not null)
        <if test="null != qo.deviceGroupId ">
            and tto.device_group_id = #{qo.deviceGroupId}
        </if>
        <if test="null != qo.workId ">
            and tto.id = #{qo.workId}
        </if>
        <if test="null != qo.workCode and qo.workCode!= ''">
            and tto.code = #{qo.workCode}
        </if>
        <if test="null != qo.customerId ">
            and tto.customer_id = #{qo.customerId}
        </if>
        <if test="null != qo.customerSeq and '' != qo.customerSeq ">
            and c.seq_id like concat ('%',#{qo.customerSeq},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and c.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.reportTimeStart">
            and tto.created_at &gt;= #{qo.reportTimeStart}
        </if>
        <if test="null != qo.reportTimeEnd">
            and tto.created_at &lt;= #{qo.reportTimeEnd}
        </if>
        <if test="null != qo.beginDate and '' != qo.beginDate ">
            and t.created_at &gt;= concat(#{qo.beginDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and sa.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and sa.code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and sa.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>

        group by tto.customer_id ,
        tto.device_group_id ,
        tto.created_at,
        tto.color_count ,
        tto.black_white_count ,
        tto.code ,
        t.item_id,
        sa.code ,
        sa.name ,
        t.sale_sku_id,
        t.sale_unit_price,
        t.location,
        t.is_pm,
        t.part_id,
        tto.id
        union all
        select trr.customer_id customerId,
        c.seq_id customerSeq,
        c.name customerName,
        trr.device_group_id deviceGroupId,
        twr.created_at,
        trr.color_count color,
        trr.black_white_count blackWhite,
        twr.code workCode,
        trd.item_id,
        sa.code articleCode,
        sa.name articleName,
        sa.number_oem numberOem,
        trd.sale_sku_id,
        trd.sale_unit_price,
        trd.location,
        trd.batch_code,
        sum(trd.num) num,
        sum(trd.sale_unit_price*trd.num) amount,
        trd.is_pm,
        trd.part_id,
        trr.id replaceId,
        'WORK' dataSource
        from tb_replace_order t
        join tb_work_order twr on t.work_order_id = twr.id
        join b_customer c on t.customer_id = c.id
        join tb_repair_report trr on twr.id = trr.work_order_id
        join tb_replace_detail trd on t.id = trd.replace_order_id
        join tb_sale_sku ss on trd.sale_sku_id = ss.id
        join b_storage_article sa on ss.article_code = sa.code
        where twr.status = 'completed' and trd.deleted=0 and  t.deleted=0
        <if test="null != qo.deviceGroupId ">
            and t.device_group_id = #{qo.deviceGroupId}
        </if>
        <if test="null != qo.customerId ">
            and t.customer_id = #{qo.customerId}
        </if>
        <if test="null != qo.customerSeq and '' != qo.customerSeq ">
            and c.seq_id like concat ('%',#{qo.customerSeq},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and c.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.reportTimeStart">
            and twr.created_at &gt;= #{qo.reportTimeStart}
        </if>
        <if test="null != qo.workId ">
            and twr.id = #{qo.workId}
        </if>
        <if test="null != qo.workCode and qo.workCode!= ''">
            and twr.code = #{qo.workCode}
        </if>
        <if test="null != qo.reportTimeEnd">
            and twr.created_at &lt;= #{qo.reportTimeEnd}
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and sa.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and sa.code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and sa.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>

        <if test="null != qo.beginDate and '' != qo.beginDate ">
            and t.created_at &gt;= concat(#{qo.beginDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>

        group by trr.customer_id ,
        trr.device_group_id ,
        twr.created_at,
        trr.color_count ,
        trr.black_white_count ,
        twr.code ,
        trd.item_id,
        sa.code ,
        sa.name ,
        trd.sale_sku_id,
        trd.sale_unit_price,
        trd.location,
        trd.is_pm,
        trd.part_id,
        trr.id
        ) t
        left join b_customer_device_group t1 on t1.id = t.deviceGroupId
        left join b_product_tree t2 on t2.id = t1.product_id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        where 1=1
        <if test="null != qo.dataSource and qo.dataSource!= ''">
            and t.dataSource = #{qo.dataSource}
        </if>
        <if test="null != qo.deviceGroup and qo.deviceGroup!= ''">
            and t1.device_group = #{qo.deviceGroup}
        </if>
        <if test="null != qo.regCliState and qo.regCliState!= ''">
            and t1.reg_cli_state = #{qo.regCliState}
        </if>
        <if test="null!=qo.serTypes and !qo.serTypes.isEmpty()">
            AND t1.ser_type in
            <foreach collection="qo.serTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t1.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
