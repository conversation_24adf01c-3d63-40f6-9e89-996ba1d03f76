<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.knowledge.infrastructure.mapper.KnowledgeBaseInfoMapper">
    <select id="pageList" resultType="com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeBaseInfo">
        select t.*,t1.name operatorName from b_knowledge_base_info t
                 left join st_user_basic t1 on t1.id = t.operator_id
                 where deleted=false
        <if test="null!=query.type and query.type!=''">
            and t.`type` = #{query.type}
        </if>
        <if test="null!=query.title and query.title!=''">
            and t.`title` like concat('%',#{query.title},'%')
        </if>
        <if test="null!=query.operateBy and query.operateBy!=''">
            and t1.name like concat('%',#{query.operateBy},'%')
        </if>
        <if test="null != query.operateStart">
            and t.updated_at  &gt;= concat(#{query.operateStart},' 00:00:00')
        </if>
        <if test="null != query.operateEnd">
            and t.updated_at &lt;= concat(#{query.operateEnd},' 23:59:59')
        </if>
        <if test="null!=query.level and query.level!=''">
            and t.`level` = #{query.level}
        </if>
        <if test="null!=query.productList and !query.productList.isEmpty()">
            and
            <foreach collection="query.productList" item="pl" separator=" or " open="(" close=")">
                JSON_CONTAINS(t.product_list,#{pl})
            </foreach>
        </if>
        order by t.id desc
    </select>

    <resultMap id="abi" type="com.hightop.benyin.knowledge.application.vo.AppletBaseInfoVo">
        <id property="id" column="id"/>
        <result property="productList" column="product_list"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="codeExplain" column="code_explain"/>
    </resultMap>

    <select id="appletPageList" resultMap="abi">
        select `id`,`product_list`,`type`,`title`,`code_explain`
        from b_knowledge_base_info
        where deleted=false and is_enable=true
        <if test="null!=query.type and !query.type.isEmpty()">
            and `type` in
            <foreach collection="query.type" item="tp" separator="," open="(" close=")">
                #{tp}
            </foreach>
        </if>
        <if test="null!=query.keyword and query.keyword!=''">
            and (title like concat ('%',#{query.keyword},'%') or tags like concat ('%',#{query.keyword},'%'))
        </if>
        <if test="null!=query.excludeLevel and query.excludeLevel!=''">
            and level != #{query.excludeLevel}
        </if>
        <if test="null!=query.productList and !query.productList.isEmpty()">
            and
            <foreach collection="query.productList" item="pl" separator=" or " open="(" close=")">
                JSON_CONTAINS(product_list,#{pl})
            </foreach>
        </if>
        <if test="null!=query.serialList and !query.serialList.isEmpty()">
            and
            <foreach collection="query.serialList" item="pl" separator=" or " open="(" close=")">
                JSON_CONTAINS(product_serials,#{pl})
            </foreach>
        </if>
        order by created_at asc
    </select>
</mapper>
