<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.storage.infrastructure.mapper.MonthStorageInventoryMapper">
    <select id="monthTask" resultType="com.hightop.benyin.storage.infrastructure.entity.MonthStorageInventory">
        SELECT
            si.code,
            si.name,
            si.location,
            sa.part_brand,
            sa.number_oem,
            sa.manufacturer_id,
            si.warehouse_id,
            sa.unit,
            si.sum_warehouse_number,
            si.alarm_number,
            sa.manufacturer_channel,
            ifnull(en.number,0) engineerNum,
            si.average_price no_tax_price,
            #{month} monthly,
            (CASE WHEN m.tax IS NULL THEN 13 ELSE m.tax END) tax,
            (si.sum_warehouse_number * si.average_price) no_tax_amount,
            ROUND(ROUND(si.average_price/100,2)/(CASE WHEN m.tax IS NULL THEN 1.13 ELSE (100 + m.tax)/100 END) , 2) tax_price,
            (ROUND(ROUND(si.average_price/100,2)/(CASE WHEN m.tax IS NULL THEN 1.13 ELSE (100 + m.tax)/100 END) , 2) * si.sum_warehouse_number) tax_amount
        FROM
            b_storage_inventory si
                INNER JOIN b_storage_article sa ON si.code = sa.`code`
                LEFT JOIN b_manufacturer m on sa.manufacturer_id = m.id
                LEFT JOIN (select sum(after_lock_num) number,article_code
                           from tb_item_store where user_type='ENGINEER' and after_lock_num>0 group by article_code) en on en.article_code= si.code
        WHERE si.deleted = 0
    </select>
</mapper>