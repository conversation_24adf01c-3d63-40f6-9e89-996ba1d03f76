<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.storage.infrastructure.mapper.ManufacturerMapper">

    <select id="pageList" resultType="com.hightop.benyin.storage.infrastructure.entity.Manufacturer">
        select t.*,t2.name area,t3.name city,t4.name province,t5.name updatedName from b_manufacturer t
                    left join b_region t2 on t.region_code = t2.code
                    left join b_region t3 on t3.code = t2.parent_code
                    left join b_region t4 on t4.code = t3.parent_code
                    left join st_user_basic t5 on t5.id = t.updated_by
        where t.deleted=false
        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.name and '' != qo.name ">
            and t.name like concat ('%',#{qo.name},'%')
        </if>
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.groupName and '' != qo.groupName ">
            and t.group_name like concat ('%',#{qo.groupName},'%')
        </if>
        <if test="null != qo.legalPerson and '' != qo.legalPerson ">
            and t.legal_person like concat ('%',#{qo.legalPerson},'%')
        </if>
        <if test="null != qo.legalPersonTel and '' != qo.legalPersonTel ">
            and t.legal_person_tel like concat ('%',#{qo.legalPersonTel},'%')
        </if>
        order by t.created_at desc
    </select>

</mapper>