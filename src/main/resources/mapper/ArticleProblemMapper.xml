<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.storage.infrastructure.mapper.ArticleProblemMapper">

    <select id="pageList" resultType="com.hightop.benyin.storage.infrastructure.entity.ArticleProblem">
        SELECT t.*,
        t1.manufacturer_channel AS manufacturerChannel,
        t1.type AS type,
        t1.image_files AS imageFiles
        FROM b_article_problem t
        LEFT JOIN b_storage_article t1 ON (t1.code = t.article_code)
        LEFT JOIN st_user_basic t2 ON (t2.id = t.created_by)
        LEFT JOIN b_manufacturer tm on tm.id = t1.manufacturer_id
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            LEFT JOIN tb_part_product_tree t3 ON (t3.part_id = t1.part_id)
        </if>
        where 1=1

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND
            <foreach collection="qo.productIds" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(t3.product_tree_ids,cast(#{id} as char ))
            </foreach>
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and t.article_name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and t.number_oem like CONCAT( #{qo.numberOem},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and tm.code like CONCAT( #{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and tm.name like CONCAT( #{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.batchCode and '' != qo.batchCode ">
            and t.batch_code like concat ('%',#{qo.batchCode},'%')
        </if>
        <if test="null != qo.description and '' != qo.description ">
            and t.description like concat ('%',#{qo.description},'%')
        </if>
        <if test="null != qo.createdBy and '' != qo.createdBy ">
            and t2.name like concat ('%',#{qo.createdBy},'%')
        </if>
        <if test="null != qo.manufacturerChannel and '' != qo.manufacturerChannel ">
            and t1.manufacturer_channel  = #{qo.manufacturerChannel}
        </if>
        <if test="null != qo.type and '' != qo.type ">
            and t1.type  = #{qo.type}
        </if>
        <if test="null != qo.source and '' != qo.source ">
            and t.source = #{qo.source}
        </if>

        <if test="null != qo.startDate and '' != qo.startDate ">
            and t1.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t1.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>

        ORDER BY t.created_at DESC
    </select>

</mapper>