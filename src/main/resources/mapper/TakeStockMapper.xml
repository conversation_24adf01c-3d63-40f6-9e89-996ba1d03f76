<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.storage.infrastructure.mapper.TakeStockMapper">

    <select id="page" resultType="com.hightop.benyin.storage.infrastructure.entity.TakeStock">
        SELECT distinct t.id,
               t.company_code,
               t.code,
               t.warehouse_id,
               t.stock_type,
               t.type,
               t.inventory_num,
               t.inventory_amount,
               t.stock_num,
               t.stock_amount,
               t.status stockStatus,
               t.discrepancy_status,
               t.is_discrepancy,
               t.audit_at,
               t.audit_by,
               t.recheck_at,
               t.recheck_by,
               t.created_by,
               t.created_at,
               t.updated_at,
               t.deleted,
               t1.name AS warehouseName,
               t2.name AS recheckName
        FROM b_take_stock t
                 LEFT JOIN b_storage_warehouse t1 ON (t1.id = t.warehouse_id)
                 LEFT JOIN st_user_basic t2 ON (t2.id = t.recheck_by)
                 LEFT JOIN st_user_basic t3 ON (t3.id = t.created_by)
        WHERE t.deleted = false
        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.name and '' != qo.name ">
            and t1.name like concat ('%',#{qo.name},'%')
        </if>

        <if test="null != qo.createdBy and '' != qo.createdBy ">
            and t3.name like concat ('%',#{qo.createdBy},'%')
        </if>

        <if test="null != qo.recheckName and '' != qo.recheckName ">
            and t2.name like concat ('%',#{qo.recheckName},'%')
        </if>

        <if test="null != qo.type ">
            and t.type = #{qo.type}
        </if>

        <if test="null != qo.stockType ">
            and t.stock_type = #{qo.stockType}
        </if>

        <if test="null != qo.stockStatus ">
            and t.status = #{qo.stockStatus}
        </if>

        <if test="null != qo.discrepancyStatus ">
            and t.discrepancy_status = #{qo.discrepancyStatus}
        </if>

        <if test="null != qo.isDiscrepancy ">
            and t.is_discrepancy = #{qo.isDiscrepancy}
        </if>
        <if test="null != qo.startTime and '' != qo.startTime ">
            and t.created_at &gt;= #{qo.startTime}
        </if>
        <if test="null != qo.endTime  and '' != qo.endTime ">
            and t.created_at &lt; #{qo.endTime}
        </if>
        ORDER BY t.id DESC
    </select>
    <select id="s">
        SELECT t.id,
               t.company_code,
               t.code,
               t.warehouse_id,
               t.stock_type,
               t.type,
               t.inventory_num,
               t.inventory_amount,
               t.stock_num,
               t.stock_amount,
               t.status,
               t.discrepancy_status,
               t.is_discrepancy,
               t.audit_at,
               t.audit_by,
               t.recheck_at,
               t.recheck_by,
               t.created_by,
               t.created_at,
               t.updated_at,
               t.deleted,
               t1.name AS warehouseName,
               t2.name AS auditorByName
        FROM b_take_stock t
                 LEFT JOIN b_storage_warehouse t1 ON (t1.id = t.warehouse_id)
                 LEFT JOIN st_user_basic t2 ON (t2.id = t.audit_by)
        WHERE t.deleted = false
          AND (t.status = ? AND t.stock_type = ?)
        ORDER BY t.id DESC
        LIMIT ?

    </select>
</mapper>