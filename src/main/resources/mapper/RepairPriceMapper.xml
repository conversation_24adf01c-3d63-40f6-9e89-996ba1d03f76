<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.repair.price.infrastructure.mapper.RepairPriceMapper">
    <select id="queryByProductId" resultType="com.hightop.benyin.repair.price.infrastructure.entity.RepairPrice">
        select *
        from tb_repair_price
        where status = 1
          and deleted = 0
          and audit_status = 'APPROVE'
          and JSON_CONTAINS(product_ids, cast(#{productId} as char))
    </select>

    <select id="queryList" resultType="com.hightop.benyin.repair.price.infrastructure.entity.RepairPrice">
        select * from tb_repair_price
        where status =1 and deleted = 0
        <if test="null!=pageQuery.productIdList and !pageQuery.productIdList.isEmpty()">
            AND
            <foreach collection="pageQuery.productIdList" item="id" separator=" OR " open="(" close=")">
                JSON_CONTAINS(product_ids,cast(#{id} as char ))
            </foreach>
        </if>

        <if test="null != pageQuery.auditStatus">
            and audit_status = #{pageQuery.auditStatus}
        </if>
        <!-- 基础上门费 -->
        <if test="null != pageQuery.visitPriceStart and '' != pageQuery.visitPriceStart ">
            and visit_price &gt;= #{pageQuery.visitPriceStart}
        </if>
        <if test="null != pageQuery.visitPriceEnd and '' != pageQuery.visitPriceEnd ">
            and visit_price &lt;= #{pageQuery.visitPriceEnd}
        </if>

        <!-- 普通维修价格 -->
        <if test="null != pageQuery.normalPriceStart and '' != pageQuery.normalPriceStart ">
            and normal_price &gt;= #{pageQuery.normalPriceStart}
        </if>
        <if test="null != pageQuery.normalPriceEnd and '' != pageQuery.normalPriceEnd ">
            and normal_price &lt;= #{pageQuery.normalPriceEnd}
        </if>

        <!-- 客户端折扣价格 -->
        <if test="null != pageQuery.discountPriceStart and '' != pageQuery.discountPriceStart ">
            and discount_price &gt;= #{pageQuery.discountPriceStart}
        </if>
        <if test="null != pageQuery.normalPriceEnd and '' != pageQuery.normalPriceEnd ">
            and discount_price &lt;= #{pageQuery.normalPriceEnd}
        </if>

        <!-- VIP折扣价格 -->
        <if test="null != pageQuery.vipPriceStart and '' != pageQuery.vipPriceStart ">
            and vip_price &gt;= #{pageQuery.vipPriceStart}
        </if>
        <if test="null != pageQuery.vipPriceEnd and '' != pageQuery.vipPriceEnd ">
            and vip_price &lt;= #{pageQuery.vipPriceEnd}
        </if>

        <!-- 编辑时间 -->
        <if test="null != pageQuery.editTimeStart">
            and updated_at &gt;= #{pageQuery.editTimeStart}
        </if>
        <if test="null != pageQuery.editTimeEnd">
            and updated_at &lt;= #{pageQuery.editTimeEnd}
        </if>

        <!-- 审核时间 -->
        <if test="null != pageQuery.auditTimeStart">
            and audit_time &gt;= #{pageQuery.auditTimeStart}
        </if>
        <if test="null != pageQuery.auditTimeEnd">
            and audit_time &lt;= #{pageQuery.auditTimeEnd}
        </if>


    </select>

    <resultMap id="queryMap" type="com.hightop.benyin.repair.price.infrastructure.entity.RepairPrice">
        <id property="id" column="id"/>
        <result property="productIdList" column="product_ids"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="selectProductByProductId" resultMap="queryMap">
        select id,product_ids from tb_repair_price
        where status =1 and deleted = 0
        <if test="null != id">
            and id != #{id}
        </if>
        <if test="null!=productIdList and !productIdList.isEmpty()">
            AND
            <foreach collection="productIdList" item="pid" separator=" OR " open="(" close=")">
                JSON_CONTAINS(product_ids,cast(#{pid} as char ))
            </foreach>
        </if>
    </select>
</mapper>