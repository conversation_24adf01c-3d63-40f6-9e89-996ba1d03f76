<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.FinancePaymentMapper">

    <select id="getFinancePayList" resultType="com.hightop.benyin.statistics.infrastructure.entity.FinancePayment">
        select t.*,
               t1.code manufacturerCode,
               t1.name   manufacturerName,
               t2.name area,t3.name city,t4.full_name province
        from r_finance_payment t
                 left join b_manufacturer t1 on t1.id = t.manufacturer_id
                 left join b_region t2 on t2.code = t1.region_code
                 left join b_region t3 on t3.code = t2.parent_code
                 left join b_region t4 on t4.code = t3.parent_code
        where 1=1
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t1.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t1.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t1.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.yearMonths and '' != qo.yearMonths ">
            and t.year_months = #{qo.yearMonths}
        </if>
        order by t.year_months desc
    </select>

    <select id="getCurrMonthFinancePay" resultType="com.hightop.benyin.statistics.infrastructure.entity.FinancePayment">
        select t.manufacturer_id,
               t1.code manufacturerCode,
               t1.name   manufacturerName,
        #{qo.yearMonths} yearMonths,
               t2.name area,t3.name city,t4.full_name province,
               sum(t.amount)  terminalAmount,
        ifnull(tc.period_adjust,0) periodAdjust,
        ifnull(tt.terminal_amount,0) periodAmount
        from (
            select distinct t1.manufacturer_id,
            (t1.amount-t1.refund_amount-t1.paid_amount) as amount,
            t1.id
            from b_machine_purchase t1
            left join b_manufacturer t2 on t2.id = t1.manufacturer_id
            where  t1.pay_status != 'PAID'
            and t1.status not in ('REJECT', 'CLOSED')
                <if test="null != qo.yearMonths and '' != qo.yearMonths ">
                    and  date_format(t1.created_at,'%Y-%m')&lt;= #{qo.yearMonths}
                </if>
             union all
                select distinct t.manufacturer_id, t.amount,t.id
                from b_manufacturer_order t
                where t.settle_status = 'NO_SETTLE'
                and t.status not in ('REJECT', 'CLOSED')
                <if test="null != qo.yearMonths and '' != qo.yearMonths ">
                    and  date_format(t.created_at,'%Y-%m')&lt;= #{qo.yearMonths}
                </if>
             ) t
        left join r_finance_payment tt on tt.manufacturer_id = t.manufacturer_id and tt.year_months=#{qo.lastMonths}
        left join r_finance_payment tc on tc.manufacturer_id = t.manufacturer_id and tc.year_months=#{qo.yearMonths}
                 left join b_manufacturer t1 on t1.id = t.manufacturer_id
                 left join b_region t2 on t2.code = t1.region_code
                 left join b_region t3 on t3.code = t2.parent_code
                 left join b_region t4 on t4.code = t3.parent_code

        where 1=1
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t1.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t1.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t1.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        group by t.manufacturer_id,
                 t1.code ,
                 t1.name   ,
                 t2.name ,t3.name ,t4.name
    </select>

    <select id="getCurrMonthPayPayment" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t.manufacturer_id,
        sum(t.amount)  amount
        from (
        select distinct t.manufacturer_id,t.id,
        t.amount
        from b_machine_purchase_pay t
        where
          date_format(t.created_at,'%Y-%m')= #{yearMonths}
        <if test="null!=manufacturerIds and !manufacturerIds.isEmpty()">
            AND t.manufacturer_id in
            <foreach collection="manufacturerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        union all
        select distinct
        t.manufacturer_id ,t.id,
        t.amount
        from b_manufacturer_order t
        join b_purchase_payment_detail t1 on t1.manufacter_order_id = t.id
        join b_purchase_payment t2 on t2.id = t1.payment_id
        where t.settle_status = 'SETTLED'
        and t2.status  in ('COMPLETED','PART_PAY')
        and  date_format(t.created_at,'%Y-%m')&lt; #{yearMonths}
        and  date_format(t2.payment_time,'%Y-%m')= #{yearMonths}
        <if test="null!=manufacturerIds and !manufacturerIds.isEmpty()">
            AND t.manufacturer_id in
            <foreach collection="manufacturerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        ) t
        group by t.manufacturer_id
    </select>

    <select id="getCurrMonthNewPayment" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">

        select t.manufacturer_id,
               sum(t.amount)  amount
        from (
            select distinct t1.manufacturer_id,
            (t1.amount-t1.refund_amount-t1.paid_amount) as amount,
            t1.id
            from b_machine_purchase t1
            left join b_manufacturer t2 on t2.id = t1.manufacturer_id
            where  t1.pay_status != 'PAID'
            and t1.status not in ('REJECT', 'CLOSED')
               and  date_format(t1.created_at,'%Y-%m')=  #{yearMonths}
                <if test="null!=manufacturerIds and !manufacturerIds.isEmpty()">
                    AND t1.manufacturer_id in
                    <foreach collection="manufacturerIds" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </if>
                 union all
                 select distinct
                     t.manufacturer_id ,
                     t.amount,t.id
                 from b_manufacturer_order t
                 where t.settle_status = 'NO_SETTLE'
                   and t.status not in ('REJECT','CLOSED')
                   and  date_format(t.created_at,'%Y-%m')= #{yearMonths}
        <if test="null!=manufacturerIds and !manufacturerIds.isEmpty()">
            AND t.manufacturer_id in
            <foreach collection="manufacturerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
             ) t
                 left join b_manufacturer t1 on t1.id = t.manufacturer_id
                 left join b_region t2 on t2.code = t1.region_code
                 left join b_region t3 on t3.code = t2.parent_code
                 left join b_region t4 on t4.code = t3.parent_code
        where 1=1

        group by t.manufacturer_id
    </select>

    <select id="getCurrMonthMachineCollection" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t.manufacturer_id,
        sum(t.amount) amount
        from (
            select distinct t1.manufacturer_id,
            (t1.amount-t1.refund_amount-t1.paid_amount) as amount,
            t1.id
            from b_machine_purchase t1
            left join b_manufacturer t2 on t2.id = t1.manufacturer_id
            where  t1.pay_status != 'PAID'
            and t1.status not in ('REJECT', 'CLOSED')
            <if test="null != yearMonths and '' != yearMonths ">
                and  date_format(t1.created_at,'%Y-%m')&lt;= #{yearMonths}
            </if>
            <if test="null!=manufacturerIds and !manufacturerIds.isEmpty()">
                AND t1.manufacturer_id in
                <foreach collection="manufacturerIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        ) t
        group by t.manufacturer_id
    </select>

    <select id="getCurrMonthMachinePreCollection" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select t1.manufacturer_id,
        sum( t.purchase_price ) amount
        from b_machine t
        left join b_machine_purchase t1 on t1.purchase_code = t.source_code
        where t.status='WAIT_IN'
        and t1.pay_status='PAID'
        and t1.status not in ('REJECT','CLOSED')
        <if test="null != yearMonths and '' != yearMonths ">
            and  date_format(t1.created_at,'%Y-%m')&lt;= #{yearMonths}
            and  date_format(t1.pay_time,'%Y-%m')&lt;= #{yearMonths}
        </if>
        <if test="null!=manufacturerIds and !manufacturerIds.isEmpty()">
            AND t1.manufacturer_id in
            <foreach collection="manufacturerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by t1.manufacturer_id
    </select>

    <select id="getCurrMonthMaterialPreCollection" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        SELECT tt.manufacturer_id, sum(tt.amount) amount
        FROM (
            select t.*,
            round(t.price * t.num, 2)      amount
            from (select distinct t2.id,
                t.manufacturer_id,
                t1.code                                                      orderCode,
                t2.article_code,
                (t2.number - t2.receive_num - t2.refund_num)                 num,
                t2.price
                from b_purchase_payment t
                left join b_purchase_payment_detail tt on tt.payment_id = t.id
                left join b_manufacturer_order t1 on t1.id = tt.manufacter_order_id
                join b_manufacturer_order_goods t2 on t2.manufacturer_order_code = t1.code
                where t.status = 'COMPLETED'
            and t1.status not in ('REJECT', 'CLOSED')
            and t2.receive_num + t2.refund_num &lt; (t2.number)
            and t.manufacturer_id=1000022
        <if test="null != yearMonths and '' != yearMonths ">
            and  date_format(t.created_at,'%Y-%m')&lt;= #{yearMonths}
        </if>
        <if test="null!=manufacturerIds and !manufacturerIds.isEmpty()">
            AND t.manufacturer_id in
            <foreach collection="manufacturerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    ) t
      ) tt
    group by tt.manufacturer_id
    </select>

    <select id="getCurrMonthMaterialCollection" resultType="com.hightop.benyin.statistics.application.vo.FinanceCustomerSummaryVO">
        select
        t.manufacturer_id ,
        sum(t2.price*(t2.number-t2.refund_num)) amount
        from b_manufacturer_order t
        left join b_manufacturer_order_goods t2 on t2.manufacturer_order_code = t.code
        where t.settle_status = 'NO_SETTLE'
        and t.status not in ('REJECT','CLOSED')
        <if test="null != yearMonths and '' != yearMonths ">
            and  date_format(t.created_at,'%Y-%m')&lt;= #{yearMonths}
        </if>
        <if test="null!=manufacturerIds and !manufacturerIds.isEmpty()">
            AND t.manufacturer_id in
            <foreach collection="manufacturerIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by t.manufacturer_id
    </select>
    <select id="machinePaymentList" resultType="com.hightop.benyin.statistics.application.vo.MachinePaymentVO">
        select t.*,
               round(t.amount / 1.13, 2) noTaxAmount,
               t.amount - round((t.amount / 1.13), 2) taxAmount
        from (select
        t1.id,
        t1.purchase_code                                                purchaseCode,
        t2.id manufacturerId,
        t2.code                                                      manufacturerCode,
        t2.name                                                      manufacturerName,
        t1.number                                                        num,
        round(ifnull(t1.amount-t1.refund_amount-t1.paid_amount, 0) / 100, 2)                           amount,
        case when t2.tax is null then 1.13 else 1 + t2.tax / 100 end taxRate,
        ifnull(t2.tax, 13)                                           tax
        from  b_machine_purchase t1
        left join b_manufacturer t2 on t2.id = t1.manufacturer_id
        where  t1.pay_status != 'PAID'
        and t1.status not in ('REJECT', 'CLOSED')
            <if test="null != qo.purchaseCode and '' != qo.purchaseCode ">
                and t1.purchase_code like concat ('%',#{qo.purchaseCode},'%')
            </if>
            <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
                and t2.code like concat ('%',#{qo.manufacturerCode},'%')
            </if>
            <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
                and t2.name like concat ('%',#{qo.manufacturerName},'%')
            </if>
            <if test="null != qo.startDate and '' != qo.startDate ">
                and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
            </if>
            <if test="null != qo.endDate and '' != qo.endDate ">
                and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
            </if>
        ) t
        order by t.purchaseCode desc
    </select>

    <select id="machinePaymentPreList" resultType="com.hightop.benyin.statistics.application.vo.MachinePaymentVO">
        select t.*, round(t.amount / 1.13, 2) noTaxAmount,
        t.amount - round((t.amount / 1.13), 2) taxAmount
        from (
        select t.source_code purchaseCode,t2.code manufacturerCode,t2.name
        manufacturerName,t.machine_num,t.product_name,1 num,
        round(ifnull(tt.price,0) / 100, 2) amount,
        case when t2.tax is null then 1.13 else 1 + t2.tax / 100 end taxRate,
        ifnull(t2.tax, 13) tax
        from b_machine t
        left join b_machine_purchase_detail tt on tt.purchase_code = t.source_code and tt.product_id = t.product_id AND tt.deleted = 0
        left join b_machine_purchase t1 on t1.purchase_code = t.source_code
        left join b_manufacturer t2 on t2.id = t1.manufacturer_id
        where t.status='WAIT_IN'
        and t1.pay_status='PAID'
        and t1.status not in ('REJECT','CLOSED')

        <if test="null != qo.purchaseCode and '' != qo.purchaseCode ">
            and t.source_code like concat ('%',#{qo.purchaseCode},'%')
        </if>
        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t2.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t2.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t1.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t1.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>

        ) t
        order by t.purchaseCode,t.machine_num desc
    </select>

    <select id="materialPaymentList" resultType="com.hightop.benyin.statistics.application.vo.MaterialPaymentVO">
        <!--select t.*,
        round(round(t.price*t.num ,2) / 1.13, 2) noTaxAmount,
        t.amount - round((t.amount / 1.13), 2) taxAmount
        from (select t2.id,
        t1.in_warehouse_id flowCode,
        t.code purchaseCode,
        t3.code manufacturerCode,
        t3.name manufacturerName,
        t2.article_code,
        t4.name articleName,
        t4.manufacturer_channel,
        t4.unit,
        (t2.number-t2.refund_num) num,
        round(t2.price*(t2.number-t2.refund_num)/100 ,2) amount,
        round(t2.price / 100, 2) price,
        case when t3.tax is null then 1.13 else 1 + t3.tax / 100 end taxRate,
        ifnull(t3.tax, 13) tax
        from b_manufacturer_order t
        join b_storage_in_warehouse t1 on t1.shop_waybill = t.code
        left join b_manufacturer_order_goods t2 on t2.manufacturer_order_code = t.code
        left join b_manufacturer t3 on t3.id = t.manufacturer_id
        left join b_storage_article t4 on t4.code = t2.article_code
        where t.settle_status = 'NO_SETTLE'
        and t.status not in ('REJECT','CLOSED')-->
        SELECT
        tem.id,
        tem.flowCode,
        tem.purchaseCode,
        GROUP_CONCAT(DISTINCT tem.manufacturerId) manufacturerId,
        GROUP_CONCAT(DISTINCT tem.manufacturerCode) manufacturerCode,
        GROUP_CONCAT(DISTINCT tem.manufacturerName) manufacturerName,
        <!-- 	tem.manufacturer_channel,-->
        SUM(tem.num) num,
        SUM(tem.amount) amount,
        SUM(tem.noTaxAmount) noTaxAmount,
        SUM(tem.taxAmount) taxAmount,
        tem.taxRate,
        tem.tax FROM (
        SELECT
        t1.id,
        t1.in_warehouse_id AS flowCode,
        t.purchase_code AS purchaseCode,
        t3.id manufacturerId,
        t3.CODE AS manufacturerCode,
        t3.NAME AS manufacturerName,
        <!--     t2.article_code,
             t4.manufacturer_channel,
             t4.unit,-->
        (t2.number - t2.refund_num) AS num,
        ROUND(t2.price * (t2.number - t2.refund_num) / 100, 2) AS amount,
        ROUND(t2.price / 100, 2) AS price,
        ROUND(ROUND(t2.price * (t2.number - t2.refund_num) / 100, 2) /
        CASE WHEN t3.tax IS NULL THEN 1.13 ELSE 1 + t3.tax/100 END, 2) AS noTaxAmount,
        ROUND(t2.price * (t2.number - t2.refund_num) / 100, 2) -
        ROUND(ROUND(t2.price * (t2.number - t2.refund_num) / 100, 2) /
        CASE WHEN t3.tax IS NULL THEN 1.13 ELSE 1 + t3.tax/100 END, 2) AS taxAmount,
        CASE WHEN t3.tax IS NULL THEN 1.13 ELSE 1 + t3.tax/100 END taxRate,
        ifnull( t3.tax, 13 ) tax
        FROM
        b_manufacturer_order t
        JOIN b_storage_in_warehouse t1 ON t1.shop_waybill = t.CODE
        LEFT JOIN b_manufacturer_order_goods t2 ON t2.manufacturer_order_code = t.CODE
        LEFT JOIN b_manufacturer t3 ON t3.id = t.manufacturer_id
        LEFT JOIN b_storage_article t4 ON t4.CODE = t2.article_code
        WHERE
        t.settle_status = 'NO_SETTLE'
        AND t.STATUS NOT IN ('REJECT', 'CLOSED')
        <if test="null != qo.purchaseCode and '' != qo.purchaseCode ">
            and t.purchase_code like concat ('%',#{qo.purchaseCode},'%')
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t2.article_code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and t4.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.flowCode and '' != qo.flowCode ">
            and t1.in_warehouse_id like concat ('%',#{qo.flowCode},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t3.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t3.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        ORDER BY
        t.CODE,
        t2.article_code DESC) tem
        GROUP BY
        tem.purchaseCode
        ORDER BY
        tem.purchaseCode DESC
    </select>
    <select id="materialPrePaymentList" resultType="com.hightop.benyin.statistics.application.vo.MaterialPrePaymentVO">
        select t.*,
        round(t.price*t.num ,2) amount,
        round(round(t.price*t.num ,2) / 1.13, 2) noTaxAmount,
        round(t.price*t.num ,2) - round((round(t.price*t.num ,2) / 1.13), 2) taxAmount
        from (select t2.id,t.code,
        t1.code orderCode ,
        t3.code manufacturerCode,
        t3.name manufacturerName,
        t2.article_code,
        t4.name articleName,
        t4.manufacturer_channel,
        t4.unit,
        (t2.number - t2.receive_num-t2.refund_num)  num,
        round(ifnull(t2.price, 0) / 100, 2) price,
        case when t3.tax is null then 1.13 else 1 + t3.tax / 100 end taxRate,
        ifnull(t3.tax, 13) tax
        from b_purchase_payment t
        left join b_purchase_payment_detail tt on tt.payment_id = t.id
        left join b_manufacturer_order t1 on t1.id = tt.manufacter_order_id
        join b_manufacturer_order_goods t2 on t2.manufacturer_order_code = t1.code
        left join b_manufacturer t3 on t3.id = t.manufacturer_id
        left join b_storage_article t4 on t4.code = t2.article_code
        where t.status = 'COMPLETED'
        and t1.status not in ('REJECT','CLOSED')
        and t2.receive_num+t2.refund_num &lt; (t2.number)

        <if test="null != qo.orderCode and '' != qo.orderCode ">
            and t1.code like concat ('%',#{qo.orderCode},'%')
        </if>
        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>
        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t2.article_code like concat ('%',#{qo.articleCode},'%')
        </if>
        <if test="null != qo.articleName and '' != qo.articleName ">
            and t4.name like concat ('%',#{qo.articleName},'%')
        </if>
        <if test="null != qo.manufacturerCode and '' != qo.manufacturerCode ">
            and t3.code like concat ('%',#{qo.manufacturerCode},'%')
        </if>
        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and t3.name like concat ('%',#{qo.manufacturerName},'%')
        </if>
        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.created_at &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.created_at &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>
        )t
        order by t.orderCode,t.article_code desc
    </select>
</mapper>
