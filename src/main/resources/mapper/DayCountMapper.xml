<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.DayCountMapper">


    <select id="getDayIotCountInfo" resultType="com.hightop.benyin.statistics.infrastructure.entity.DayCount">
        select t.*,t1.ser_type ,
        t1.device_group deviceGroup,
        t1.reg_cli_state,
        t3.name brand,
        'IOT' data_source,
        t2.name machine,
        t1.product_id
        from (
        select date_format(created_at,'%Y-%m-%d') currDate,
        date_format(created_at,'%Y-%m') currMonth,
        customer_id customerId,device_group_id deviceGroupId,max(id) maxId,min(id) minId
        from b_iot_counter
        where status = 1
        <if test="null != qo.deviceGroupId ">
            and device_group_id = #{qo.deviceGroupId}
        </if>
        <if test="null != qo.customerId ">
            and customer_id = #{qo.customerId}
        </if>
        <if test="null != qo.reportTimeStart">
            and created_at &gt;= #{qo.reportTimeStart}
        </if>
        <if test="null != qo.reportTimeEnd">
            and created_at &lt;= #{qo.reportTimeEnd}
        </if>
        group by date_format(created_at,'%Y-%m-%d'),customer_id,device_group_id
        order by created_at asc) t
        left join b_customer_device_group t1 on t1.id = t.deviceGroupId
        left join b_product_tree t2 on t2.id = t1.product_id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        order by t.currDate asc
    </select>

    <select id="getDayCountInfoBySingleCost" resultType="com.hightop.benyin.statistics.application.vo.SingleCostVO">
        select t.customer_id,t1.seq_id customerSeq,t1.name customerName,t.device_group,t.device_group_id,t.brand,t.machine,t.ser_type,t.reg_cli_state
             ,sum(t.total_count) totalPrintNum
        from r_day_count t left join b_customer t1 on t1.id = t.customer_id
        left join st_dict_item t2 on t.device_group = t2.value and t2.dict_id = 20700

        where 1=1
        <if test="null != qo.startDate">
            and t.curr_date &gt;= #{qo.startDate}
        </if>
        <if test="null != qo.endDate">
            and t.curr_date &lt;= #{qo.endDate}
        </if>
        <if test="null != qo.customerSeq and '' != qo.customerSeq ">
            and t1.seq_id like concat ('%',#{qo.customerSeq},'%')
        </if>

        <if test="null != qo.deviceGroup and '' != qo.deviceGroup ">
            and t2.label like concat ('%',#{qo.deviceGroup},'%')
        </if>

        <if test="null != qo.customerName and '' != qo.customerName ">
            and t1.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null!=qo.serTypes and !qo.serTypes.isEmpty()">
            AND t.ser_type in
            <foreach collection="qo.serTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by  t.customer_id,t1.seq_id ,t1.name ,t.device_group,t.device_group_id,t.brand,t.machine,t.ser_type,t.reg_cli_state
        order by t.customer_id,sum(t.total_count) desc
    </select>

    <select id="getMonlyCountIStatistics" resultType="com.hightop.benyin.statistics.application.vo.MonthPrintCountVO">
        select t1.device_seq_id ,t.machine,t.device_group,t2.seq_id customerSeqId,t2.name customerName,data_source,curr_month,sum(black_white_count) blackWhiteCount
             ,sum(color_count) colorCount,sum(total_count) totalCount
             ,count(1) dayCount,max(curr_date) endDate,max(curr_date) finalDate
        from r_day_count  t
                 left join b_customer_device_group t1 on t1.id = t.device_group_id
                 left join b_customer t2 on t2.id = t.customer_id
         where 1=1
        <if test="null != qo.startMonth">
            and t.curr_month &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and t.curr_month &lt;= #{qo.endMonth}
        </if>

        <if test="null != qo.dataSource">
            and t.data_source = #{qo.dataSource}
        </if>

        <if test="null != qo.deviceGroup">
            and t.device_group = #{qo.deviceGroup}
        </if>

        <if test="null != qo.deviceSeqId and '' != qo.deviceSeqId ">
            and t1.device_seq_id like concat ('%',#{qo.deviceSeqId},'%')
        </if>
        <if test="null != qo.machine and '' != qo.machine ">
            and t.machine like concat ('%',#{qo.machine},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t2.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t2.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>
        group by t1.device_seq_id,t.machine,t.device_group,t2.seq_id,t2.name,data_source,curr_month
        order by finalDate desc
    </select>

    <select id="getMonlyCountTotal" resultType="com.hightop.benyin.statistics.application.vo.MonthPrintCountVO">
        select count(1) machineCount,sum(blackWhiteCount) blackWhiteCount,sum(colorCount) colorCount,sum(totalCount) totalCount
        from (
                 select  t.device_group_id,sum(black_white_count) blackWhiteCount
                      ,sum(color_count) colorCount,sum(total_count) totalCount
                 from r_day_count  t
                          left join b_product_tree t1 on t1.id = t.product_id
                          left join b_customer t2 on t2.id = t.customer_id
                          left join b_product_tree t3 on t3.id = t1.parent_id
                          left join b_customer_device_group t4 on t4.id = t.device_group_id
                 where 1=1
        <if test="null != qo.startMonth">
            and t.curr_month &gt;= #{qo.startMonth}
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.endMonth">
            and t.curr_month &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.dataSource">
            and t.data_source = #{qo.dataSource}
        </if>
        <if test="null != qo.deviceGroup">
            and t.device_group = #{qo.deviceGroup}
        </if>
        <if test="null != qo.machine and '' != qo.machine ">
            and t.machine like concat ('%',#{qo.machine},'%')
        </if>
        <if test="null != qo.brand and '' != qo.brand ">
            and t.brand like concat ('%',#{qo.brand},'%')
        </if>
        <if test="null != qo.series and '' != qo.series ">
            and t3.name like concat ('%',#{qo.series},'%')
        </if>
        <if test="null != qo.deviceSeqId and '' != qo.deviceSeqId ">
            and t4.device_seq_id like concat ('%',#{qo.deviceSeqId},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t2.name like concat ('%',#{qo.customerName},'%')
        </if>
        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t2.seq_id like concat ('%',#{qo.customerSeqId},'%')
        </if>
         group by t.device_group_id) t
    </select>


    <select id="getMonlyCountISeriesStatistics" resultType="com.hightop.benyin.statistics.application.vo.MonthPrintCountVO">
        select  t.brand,t3.name series,t.machine,data_source,curr_month,sum(black_white_count) blackWhiteCount
        ,sum(color_count) colorCount,sum(total_count) totalCount
        ,count(1) dayCount,max(curr_date) endDate,max(curr_date)  finalDate
        from r_day_count  t
        left join b_product_tree t1 on t1.id = t.product_id
        left join b_product_tree t3 on t3.id = t1.parent_id
        where 1=1
        <if test="null != qo.startMonth">
            and t.curr_month &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and t.curr_month &lt;= #{qo.endMonth}
        </if>
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.dataSource">
            and t.data_source = #{qo.dataSource}
        </if>
        <if test="null != qo.machine and '' != qo.machine ">
            and t.machine like concat ('%',#{qo.machine},'%')
        </if>
        <if test="null != qo.brand and '' != qo.brand ">
            and t.brand like concat ('%',#{qo.brand},'%')
        </if>
        <if test="null != qo.series and '' != qo.series ">
            and t3.name like concat ('%',#{qo.series},'%')
        </if>
        group by t.brand,t3.name,t.machine,data_source,curr_month
        order by finalDate desc
    </select>
</mapper>
