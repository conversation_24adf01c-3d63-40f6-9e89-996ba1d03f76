<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.website.infrastructure.mapper.WebsiteInquiryMapper">

    <resultMap id="ServiceDistributionDtoResult" type="com.hightop.benyin.website.api.dto.ServiceDistributionDto">
        <result property="serviceType" column="service_type"/>
        <result property="count" column="count"/>
    </resultMap>

    <select id="selectServiceDistribution" resultMap="ServiceDistributionDtoResult">
        SELECT
            CASE
                WHEN subject IS NULL OR subject = '' OR subject = 'None' THEN '其它类型'
                ELSE subject
            END AS service_type,
            COUNT(*) AS count
        FROM
            b_website_inquiry
        WHERE
            deleted = false
        GROUP BY
            service_type
    </select>

</mapper> 