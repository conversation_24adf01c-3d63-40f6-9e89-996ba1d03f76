<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.statistics.infrastructure.mapper.PartLifeSpanMapper">


    <select id="getLifeSpanInfo" resultType="com.hightop.benyin.statistics.application.vo.PartLifeSpanInfoVO">
        select t.article_code,t1.ch articleName,t.location,t.batch_code,t.number_oem,t.serial,t.brand,count(1) counter ,max(total_count) maxLifeSpan,min(total_count) minLifeSpan,round(avg(total_count),0) avgLifeSpan
        from r_part_life_span t
        left join b_product_part t1 on t1.id = t.part_id
        where t.status = 1

        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and t.number_oem like concat ('%',#{qo.numberOem},'%')
        </if>

        <if test="null != qo.articleName and '' != qo.articleName ">
            and t1.ch  like concat ('%',#{qo.articleName},'%')
        </if>

        <if test="null != qo.articleCode and '' != qo.articleCode ">
            and t.article_code like concat ('%',#{qo.articleCode},'%')
        </if>

        <if test="null != qo.serial and '' != qo.serial ">
            and t.serial like concat ('%',#{qo.serial},'%')
        </if>


        <if test="null != qo.brand and '' != qo.brand ">
            and t.brand like concat ('%',#{qo.brand},'%')
        </if>


        <if test="null != qo.batchCode and '' != qo.batchCode ">
            and t.batch_code like concat ('%',#{qo.batchCode},'%')
        </if>



        group by article_code,t1.ch ,t.location,t.batch_code,t.number_oem,t.serial,t.brand

    </select>
</mapper>
