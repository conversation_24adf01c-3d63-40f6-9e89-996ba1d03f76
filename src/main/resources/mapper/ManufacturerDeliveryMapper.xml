<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.purchase.infrastructure.mapper.ManufacturerDeliveryMapper">

    <select id="pageList" resultType="com.hightop.benyin.purchase.infrastructure.entity.ManufacturerDelivery">
        SELECT t.id,
        t.company_code,
        t.code,
        t.manufacturer_order_code,
        tt.name AS manufacturerName,
        t.purchase_code,
        t.manufacturer_id,
        t.initiator_id,
        t.initiator_name,
        t.initiator_phone,
        t.address,
        t.contact_phone,
        t.contact,
        t.receive_company,
        t.province,
        t.city,
        t.county,
        t.amount,
        t.status,
        t.invoice_status,
        t.created_at,
        t.updated_at,
        t.deleted,
        t1.delivery_time AS deliveryTime ,br.name provinceName,br1.name cityName,br2.name countyName
        FROM b_manufacturer_delivery t
            left join b_manufacturer tt on t.manufacturer_id = tt.id
        LEFT JOIN b_purchase_order t1 ON (t1.purchase_code = t.purchase_code)
        left join b_region br on t.province = br.code
        left join b_region br1 on t.city = br1.code
        left join b_region br2 on t.county = br2.code
        WHERE t.deleted = false
        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>

        <if test="null != qo.manufacturerName and '' != qo.manufacturerName ">
            and tt.name like concat ('%',#{qo.manufacturerName},'%')
        </if>

        <if test="null != qo.manufacturerOrderCode and '' != qo.manufacturerOrderCode ">
            and t.manufacturer_order_code like concat ('%',#{qo.manufacturerOrderCode},'%')
        </if>

        <if test="null != qo.receiveCompany and '' != qo.receiveCompany ">
            and t.receive_company like concat ('%',#{qo.receiveCompany},'%')
        </if>
        <if test="null != qo.initiatorName and '' != qo.initiatorName ">
            and t.initiator_name like concat ('%',#{qo.initiatorName},'%')
        </if>
        <if test="null != qo.initiatorPhone and '' != qo.initiatorPhone ">
            and t.initiator_phone like concat ('%',#{qo.initiatorPhone},'%')
        </if>
        <if test="null != qo.status">
            and t.status = #{qo.status}
        </if>
        <if test="null != qo.id">
            and t.id = #{qo.id}
        </if>
        <if test="null != qo.deliveryTimeStart and '' != qo.deliveryTimeStart ">
            and t1.delivery_time &gt;= concat(#{qo.deliveryTimeStart},' 00:00:00')
        </if>
        <if test="null != qo.deliveryTimeEnd and '' != qo.deliveryTimeEnd ">
            and t1.delivery_time &lt;= concat(#{qo.deliveryTimeEnd},' 23:59:59')
        </if>
        ORDER BY t.created_at DESC
    </select>

</mapper>