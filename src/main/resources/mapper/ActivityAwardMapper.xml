<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.activity.infrastructure.mapper.ActivityAwardMapper">

    <resultMap id="activityAwardResultMap" type="com.hightop.benyin.activity.infrastructure.entity.ActivityAward"  autoMapping="true">

        <id property="id" column="id"/>
        <result property="activityId" column="activity_id"/>
        <result property="awardType" column="award_type"/>
        <result property="awardName" column="award_name"/>
        <result property="skuId" column="sku_id"/>
        <result property="articleCode" column="article_code"/>
        <result property="expireDate" column="expire_date"/>
        <result property="maxBuy" column="max_buy"/>
        <result property="price" column="price"/>
        <result property="limitType" column="limit_type"/>
        <result property="limitInfo" column="limit_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="minAmount" column="min_amount"/>
        <result property="quantity" column="quantity"/>
        <result property="amount" column="amount"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdAt" column="created_at"/>
        <result property="name" column="name"/>
        <result property="picsUrl" column="pics_url"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

    </resultMap>
    <select id="getAwardList" resultMap="activityAwardResultMap">
        SELECT t.id,
               t.activity_id,
               t.award_type,
               t.award_name,
               t.sku_id,
               t.article_code,
               t.max_buy,
               t.price,
               t.limit_type,
               t.limit_info,
               t.min_amount,
               t.quantity,
               t.amount,
               t.expire_date,
               t.created_by,
               t.created_at,
               t2.pics_url,
               t2.name AS name
        FROM tb_activity_award t
                 LEFT JOIN tb_sale_sku t1 ON (t1.id = t.sku_id)
                 LEFT JOIN tb_item t2 ON (t2.id = t1.item_id)
        WHERE (t.activity_id = #{activityId})

    </select>

</mapper>