<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.work.order.infrastructure.mapper.WorkOrderMapper">

    <select id="overview" resultType="com.hightop.benyin.work.order.infrastructure.entity.WorkOrderConsumeOverview">
        select sum(a.sum_total_pay) totalAmount,avg(a.sum_total_pay) avgAmountByMonth
        from  (
                  select sum(total_amount) sum_total_pay,date_format(created_at,'%Y-%m') createDate from tb_work_order
                  where status = 'completed' and ser_type not in ('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF')
                    and customer_id= ${customerId}
                  group by date_format(created_at,'%Y-%m')
              )a
    </select>

    <select id="overviewMonthly" resultType="com.hightop.benyin.work.order.infrastructure.entity.WorkOrderConsumeOverviewMonthly">
        select a.*,b.*,a.totalPay - b.totalPaid needPay
        from (
                 select sum(total_amount) totalPay,customer_id from tb_work_order
                 where status != 'close' and ser_type not in ('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF')
                    and customer_id = ${customerId}
                    <if test="null != start">
                        and created_at &gt;= #{start}
                    </if>
                    <if test="null != end">
                        and created_at &lt; #{end}
                    </if>
             ) a
                 left join (
            select customer_id,sum(total_amount) totalPaid from tb_work_order
            where status = 'completed'
              and customer_id = ${customerId}
                <if test="null != start">
                    and created_at &gt;= #{start}
                </if>
                <if test="null != end">
                    and created_at &lt; #{end}
                </if>
        )b on a.customer_id = b.customer_id
    </select>
    <select id="getSumConsumableNum" resultType="java.math.BigDecimal">
            select ifnull(sum(item_pay),0) / 100 from tb_work_order where status = 'completed'  and ser_type not in ('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF') and customer_id = ${customerId}
    </select>
    <select id="getManualWorkNum" resultType="java.math.BigDecimal">
            select ifnull(sum(total_amount - ifnull(item_pay,0)),0) /100 from tb_work_order where status = 'completed' and ser_type not in ('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF') and customer_id = ${customerId}
    </select>

    <select id="getLastWorkOrderCountByCustomerId"
            resultType="com.hightop.benyin.work.order.infrastructure.entity.WorkOrder">
        select * from tb_work_order where status = 'completed'  and ser_type not in ('BUY_FULL','BUY_HALF','RENT_HALF','RENT_FULL','FINANCING_HALF','FINANCING_FULL','HALF','ALL','PACKAGE_ALL','PACKAGE_HALF') and customer_id = ${customerId} order by created_at desc limit 1
    </select>

    <select id="getMonthRepairList" resultType="com.hightop.benyin.work.order.api.dto.MonthRepairVo">
        select date_format(t.send_report_time, '%Y-%m')                                   currMonth
             , sum(t.total_amount)                                                     totalPay
             , count(1)                                                             repairCount
             , sum(TIME_TO_SEC(TIMEDIFF(t.send_report_time, t.actual_arrive_time))) repairTime
             , round(avg(t.total_amount/100), 2)                                           avgAmount
             ,round( avg(TIME_TO_SEC(TIMEDIFF(t.send_report_time, t.actual_arrive_time))),0) avgRepairTime
        from tb_work_order t
        where t.status = 'completed'
        <if test="null!=qo.serTypes and !qo.serTypes.isEmpty()">
            AND t.ser_type in
            <foreach collection="qo.serTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.send_report_time, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.send_report_time, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>
        group by date_format(t.send_report_time, '%Y-%m')
    </select>

    <select id="getMonthRepairSummary" resultType="com.hightop.benyin.work.order.api.dto.MonthRepairVo">
        select
        sum(t.total_amount)                                                     totalPay
        , count(1)                                                             repairCount
        , count(distinct t.device_group_id)  mechineCount
        , sum(t.print_count)                 printCount
        , sum(TIME_TO_SEC(TIMEDIFF(t.send_report_time, t.actual_arrive_time))) repairTime
        from tb_work_order t
        where t.status = 'completed'
        <if test="null!=qo.serTypes and !qo.serTypes.isEmpty()">
            AND t.ser_type in
            <foreach collection="qo.serTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.send_report_time, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.send_report_time, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>
    </select>

    <select id="getMonthRepairBrandList" resultType="com.hightop.benyin.work.order.api.dto.MonthRepairVo">
        select date_format(t.created_at, '%Y-%m') currMonth
        , t2.name brand
        , t3.name series
        , sum(t.total_amount)                   totalPay
        , count(distinct t.device_group_id)  mechineCount
        , count(1)                           repairCount
        , sum(t.print_count)                 printCount
        , round(avg(t.total_amount / 100), 2)   avgAmount
        , round(avg(t.print_count), 0)   avgPrintCount
        from tb_work_order t
        left join b_product_tree t1 on t1.id = t.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        where t.status = 'completed'

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>
        group by date_format(t.created_at, '%Y-%m') desc
    </select>

    <select id="getMonthRepairBrandSummary" resultType="com.hightop.benyin.work.order.api.dto.MonthRepairVo">
        select
        sum(t.total_amount)                   totalPay
        , count(distinct t.device_group_id)  mechineCount
        , count(1)                           repairCount
        , sum(t.print_count)                 printCount
        from tb_work_order t
        left join b_product_tree t1 on t1.id = t.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        where t.status = 'completed'

        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>
    </select>
    <select id="getMonthRepairDistributionList" resultType="com.hightop.benyin.work.order.api.dto.MonthRepairVo">
        select date_format(t.created_at, '%Y-%m') currMonth,t2.name AS area ,t3.name AS city,t4.name AS province,
        sum(t.total_amount)                   totalPay,sum(t.print_count)                 printCount
        , count(distinct t.device_group_id)  mechineCount
        ,count(1)                           repairCount,round(avg(t.total_amount / 100), 2)   avgAmount
        ,round(avg(t.print_count), 0)   avgPrintCount
        from tb_work_order t
        left join b_customer t1 on t1.id = t.customer_id
        left join b_region t2 on t2.code = t1.region_code
        left join b_region t3 on t3.code = t2.parent_code
        left join b_region t4 on t4.code = t3.parent_code
        where t.status = 'completed'
        <if test="null != qo.startMonth">
            and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t1.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        group by date_format(t.created_at, '%Y-%m'),t2.name,t3.name,t4.name
        order by date_format(t.created_at, '%Y-%m') desc
    </select>

    <select id="getMonthRepairDistributionSummary" resultType="com.hightop.benyin.work.order.api.dto.MonthRepairVo">
        select
        sum(t.total_amount)                   totalPay
        , count(distinct t.device_group_id)  mechineCount
        , count(1)                           repairCount
        , sum(t.print_count)                 printCount
        from tb_work_order t
        left join b_customer t1 on t1.id = t.customer_id
        where t.status = 'completed'

        <if test="null != qo.regionPath and '' != qo.regionPath ">
            and t1.region_code like CONCAT( #{qo.regionPath},'%')
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>
    </select>

    <select id="getMonthRepairTreatyList" resultType="com.hightop.benyin.work.order.api.dto.MonthRepairVo">
        select date_format(t.created_at, '%Y-%m')                                   currMonth,t.ser_type
        , count(distinct t.device_group_id)  mechineCount
        , sum(t.print_count)                 printCount
        , sum(t.total_amount)                                                     totalPay
        , count(1)                                                             repairCount
        , round(avg(t.print_count), 0)   avgPrintCount
        , round(sum(t.print_count)/count(distinct t.device_group_id), 0)   avgMechinePrintCount
        , sum(TIME_TO_SEC(TIMEDIFF(t.send_report_time, t.actual_arrive_time))) repairTime
        , round(avg(t.total_amount/100), 2)                                           avgAmount
        ,round( avg(TIME_TO_SEC(TIMEDIFF(t.send_report_time, t.actual_arrive_time))),0) avgRepairTime
        from tb_work_order t
        where t.status = 'completed'
        <if test="null!=qo.serTypes and !qo.serTypes.isEmpty()">
            AND t.ser_type in
            <foreach collection="qo.serTypes" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="null != qo.startMonth">
            and date_format(t.created_at, '%Y-%m')  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and date_format(t.created_at, '%Y-%m')  &lt;= #{qo.endMonth}
        </if>
        group by date_format(t.created_at, '%Y-%m'),t.ser_type
    </select>
    <select id="getMonthRepairProblemList" resultType="com.hightop.benyin.work.order.api.dto.MonthProblemRepairVo">

        select t.*
        , t1.exc_type
        , t2.reason_type
        , t3.resolve_type
        , t4.exc_unit
        , round(t1.repairCount / t.repairCount, 4)  excTypeScale
        , round(t2.repairCount / t.repairCount, 4) reasonTypeScale
        , round(t3.repairCount / t.repairCount, 4) resolveTypeScale
        , round(t4.repairCount / t.repairCount, 4) excUnitScale
        from (select date_format(tt.created_at, '%Y-%m') currMonth, count(1) repairCount
        , t2.name brand,t2.id
        , t3.name series
        from tb_repair_report t
        left join tb_work_order tt on tt.id = t.work_order_id
        left join b_product_tree t1 on t1.id = tt.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        group by date_format(tt.created_at, '%Y-%m'), t2.name,t2.id, t3.name) t
        left join (select date_format(tt.created_at, '%Y-%m') currMonth, t.exc_type,t2.id, count(1) repairCount
        from tb_repair_report t
        left join tb_work_order tt on tt.id = t.work_order_id
        left join b_product_tree t1 on t1.id = tt.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        where exc_type is not null
        group by date_format(tt.created_at, '%Y-%m'), t.exc_type,t2.id) t1 on t1.currMonth = t.currMonth and t1.id = t.id
        left join (select date_format(tt.created_at, '%Y-%m') currMonth, t.reason_type,t2.id, count(1) repairCount
        from tb_repair_report t
        left join tb_work_order tt on tt.id = t.work_order_id
        left join b_product_tree t1 on t1.id = tt.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        where reason_type is not null
        group by date_format(tt.created_at, '%Y-%m'), t.reason_type,t2.id) t2 on t2.currMonth = t.currMonth and t2.id = t.id
        left join (select date_format(tt.created_at, '%Y-%m') currMonth, resolve_type,t2.id, count(1) repairCount
        from tb_repair_report t
        left join tb_work_order tt on tt.id = t.work_order_id
        left join b_product_tree t1 on t1.id = tt.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        where resolve_type is not null
        group by date_format(tt.created_at, '%Y-%m'), resolve_type,t2.id) t3 on t3.currMonth = t.currMonth  and t3.id = t.id
        left join (select date_format(tt.created_at, '%Y-%m') currMonth, exc_unit,t2.id, count(1) repairCount
        from tb_repair_report t
        left join tb_work_order tt on tt.id = t.work_order_id
        left join b_product_tree t1 on t1.id = tt.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        where exc_unit is not null
        group by date_format(tt.created_at, '%Y-%m'), exc_unit,t2.id) t4 on t4.currMonth = t.currMonth  and t4.id = t.id
        where 1=1
        <if test="null != qo.startMonth">
            and t.currMonth  &gt;= #{qo.startMonth}
        </if>
        <if test="null != qo.endMonth">
            and t.currMonth  &lt;= #{qo.endMonth}
        </if>
        <if test="null != qo.excType and '' != qo.excType ">
            and t1.exc_type like CONCAT('%', #{qo.excType},'%')
        </if>

        <if test="null != qo.reasonType and '' != qo.reasonType ">
            and t2.reason_type like CONCAT('%', #{qo.reasonType},'%')
        </if>

        <if test="null != qo.resolveType and '' != qo.resolveType ">
            and t3.resolve_type like CONCAT('%', #{qo.resolveType},'%')
        </if>

        <if test="null != qo.excUnit and '' != qo.excUnit ">
            and t4.exc_unit like CONCAT('%', #{qo.excUnit},'%')
        </if>

        order by t.currMonth desc
    </select>

    <select id="getRepairBrandList" resultType="com.hightop.benyin.work.order.api.dto.MonthRepairVo">
        select
        t2.name brand
        , t3.name series
        , sum(t.total_amount)                   totalPay
        , count(distinct t.device_group_id)  mechineCount
        , count(1)                           repairCount
        , sum(t.print_count)                 printCount
        , round(avg(t.total_amount / 100), 2)   avgAmount
        , round(avg(t.print_count), 0)   avgPrintCount
        from tb_work_order t
        left join b_product_tree t1 on t1.id = t.product_id
        left join b_product_tree t2 on t2.id = t1.parent_id
        left join b_product_tree t3 on t3.id = substr(t2.full_id_path, 2, 20)
        where t.status = 'completed'
        <if test="null!=qo.productIds and !qo.productIds.isEmpty()">
            AND t.product_id in
            <foreach collection="qo.productIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by t2.name,
        t3.name
    </select>

    <select id="queryWorkStatistics" resultType="java.lang.String">
        <if test=" type == 1 ">
            SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL n MONTH), '%Y-%m') AS month
            FROM (
            SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4
            UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9
            UNION SELECT 10 UNION SELECT 11 UNION SELECT 12
            ) AS numbers
            <![CDATA[ WHERE n <= 12 ]]>
            ORDER BY month DESC
        </if>
        <if test=" type == 2 ">
            SELECT
            DATE_FORMAT(DATE_SUB(LAST_DAY(DATE_ADD(
            MAKEDATE(YEAR(CURDATE()), 1),
            INTERVAL QUARTER(CURDATE()) * 3 - 1 MONTH
            )), INTERVAL n MONTH), '%Y-%m') AS quarter
            FROM (
            SELECT 0 AS n UNION SELECT 3 UNION SELECT 6 UNION SELECT 9
            UNION SELECT 12 UNION SELECT 15 UNION SELECT 18 UNION SELECT 21
            UNION SELECT 24
            ) AS numbers
            <![CDATA[ WHERE n <= 24 ]]>
            GROUP BY quarter
            ORDER BY quarter DESC
        </if>
        <if test=" type == 3 ">
            SELECT
            DATE_FORMAT( DATE_SUB( CURDATE(), INTERVAL n YEAR ), '%Y' ) AS MONTH
            FROM
            ( SELECT 0 AS n UNION SELECT 1 ) AS numbers
            WHERE
            <![CDATA[ n <= 2 ]]>
            ORDER BY
            MONTH DESC
        </if>
    </select>
    <select id="queryByDay" resultType="com.hightop.benyin.statistics.api.dto.WorkStatisticsVo">
        SELECT
            DATE_FORMAT(created_at,'%Y-%m') TIME,
		COUNT( 1 ) num
        FROM
            tb_work_order
        WHERE
            completed_at IS NOT NULL
          AND `status` != 'close'
          AND deleted = 0
        GROUP BY
            DATE_FORMAT(created_at,'%Y-%m')
    </select>
    <select id="queryByQuarter" resultType="com.hightop.benyin.statistics.api.dto.WorkStatisticsVo">
        SELECT
            CONCAT( YEAR ( created_at ), '-', IF(QUARTER ( created_at ) * 3 > 9,QUARTER ( created_at ) * 3,CONCAT('0', QUARTER( created_at ) * 3) )  ) TIME,
	COUNT( 1 ) num
        FROM
            tb_work_order
        WHERE
            completed_at IS NOT NULL
          AND `status` != 'close'
          AND deleted = 0
        GROUP BY
            CONCAT(
            YEAR ( created_at ),
            '-',
            (
            QUARTER ( created_at )))
    </select>
    <select id="queryByYear" resultType="com.hightop.benyin.statistics.api.dto.WorkStatisticsVo">
        SELECT YEAR
            ( created_at ) time,
            COUNT( 1 ) num
        FROM
            tb_work_order
        WHERE
            completed_at IS NOT NULL
          AND `status` != 'close'
          AND deleted = 0
          AND YEAR( created_at ) >= YEAR(NOW()) -2
        GROUP BY
            YEAR ( created_at )
    </select>

    <select id="queryTotalWorkTime" resultType="com.hightop.benyin.statistics.infrastructure.TO.CommonData">
        SELECT
        customer_id "key",
        SUM(TIMESTAMPDIFF( MINUTE, actual_arrive_time, confirm_report_time )) "value"
        FROM
        tb_work_order
        WHERE
        confirm_report_time IS NOT NULL
        AND customer_id IN (
        <foreach collection="customerIds" item="id" separator=",">
            #{id}
        </foreach>
        )
        AND `status` != 'close'
        AND DATE_FORMAT( created_at, "%Y-%m" ) = #{month}
        and deleted = 0
        GROUP BY customer_id
    </select>
    <select id="queryTotalCost" resultType="com.hightop.benyin.statistics.infrastructure.TO.CommonData">

        SELECT
        w.engineer_id "key",
        <!-- 	a.`name`,
                 a.price,
                a.cost_price,
                s.sale_unit_price,
                s.article_code,-->
        <!--SUM(d.num * a.cost_price) "value"-->
        SUM(d.num * ba.price)  "value"
        FROM
        tb_work_order w
        INNER JOIN tb_replace_order r ON r.work_order_id = w.id AND r.deleted = 0
        INNER JOIN tb_replace_detail d ON r.id = d.replace_order_id
        <!--INNER JOIN tb_item_store s ON d.item_store_id = s.id AND s.deleted = 0
        AND s.sku_source = 'ENGINEER_APPLY'
        left JOIN b_storage_article a ON a.code = s.article_code-->
        INNER JOIN tb_item_store s ON d.item_store_id = s.id AND s.deleted = 0 AND s.sku_source = 'ENGINEER_APPLY'
        INNER JOIN b_storage_inventory_batch ba ON ba.batch_code = s.batch_code AND s.article_code = ba.`code` AND ba.deleted = 0
        WHERE
        w.id IN (
        <foreach collection="workOrderIds" item="id" separator=",">
            #{id}
        </foreach>
        )
        AND w.`status` != 'close'
        and w.deleted = 0
        GROUP BY w.engineer_id
    </select>
    <select id="queryMechanicalPlaceOrderMap"
            resultType="com.hightop.benyin.statistics.infrastructure.TO.CommonData">
        SELECT
        t.id "key",
        SUM(num) "value",
        SUM(amount) amount,
        SUM(cost) cost
        FROM
        (
        SELECT DISTINCT
        t.source_code,
        t1.contract_name,
        t2.id,
        t4.machine_num,
        1 num,
        ifnull( t4.full_amount, 0 ) amount,
        ifnull( t5.purchase_price, 0 ) cost
        FROM
        b_machine_inout_flow t
        LEFT JOIN b_customer_contract t1 ON t1.CODE = t.source_code
        LEFT JOIN b_customer t2 ON t2.id = t1.customer_id
        LEFT JOIN b_customer_contract_buy t4 ON t4.contract_code = t.source_code
        LEFT JOIN b_machine t5 ON t5.machine_num = t4.machine_num
        AND t5.host_type = '2008'
        LEFT JOIN b_manufacturer t6 ON t6.id = t5.manufacturer_id
        WHERE
        t.source_type = 'shopping_mall'
        AND t1.contract_type = '1201'
        AND t1.STATUS = 'EFFECTED'
        AND t2.id IN (
        <foreach collection="customerIds" item="id" separator=",">
            #{id}
        </foreach>
        )
        AND date_format( t.TIME, '%Y-%m' ) = #{month}
        ) t
    </select>
    <select id="queryScatteredRepairPay"
            resultType="com.hightop.benyin.statistics.infrastructure.TO.CommonData">
        SELECT
        customer_id "key",
        SUM(repair_pay) cost
        <include refid="queryScattered"></include>
    </select>

    <sql id="queryScattered">
        FROM
        tb_work_order
        WHERE
        ser_type IN ('SCATTERED','NO_WARRANTY', 'WARRANTY','QA','QA_COMPONENT','MAINTENANCE','OTHER')
        AND `status` != 'close'
        AND DATE_FORMAT( completed_at, '%Y-%m' ) = #{month}
        AND repair_pay IS NOT NULL
        AND repair_pay != 0
        AND deleted = 0
        <if test="1 == type ">
            AND customer_id IN (
            <foreach collection="customerIds" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="2 == type ">
            AND device_group_id IN (
            <foreach collection="customerIds" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        )
        GROUP BY customer_id
    </sql>
</mapper>