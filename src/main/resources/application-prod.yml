spring:
  datasource:
    url: *******************************************************************
    username: sczjzy
    password: c2ijz3JyFpmreRf2
    hikari:
      # 生产环境为8核心
      maximum-pool-size: 16

  redis:
    host: localhost
    port: 6379
    database: 1

  elasticsearch:
    rest:
      uris: http://127.0.0.1:9200
      username: elastic
      password: SirP*nEsajkMLlVowd38
      read-timeout: 6000
    data:
      elasticsearch:
        repositories:
          enabled: true
es:
  host: 127.0.0.1
  port: 9200


benyin:
  tencent:
    cos:
      prefix: prod/
  pay:
    wechat:
      # 生产环境支付回调请求域名
      notify-server: "https://plat.sczjzy.com.cn"
  logistics:
    # 京东生产环境
    jd:
      host: api.jdl.com
      customer-code: 028K3566096
      app-key: 95606bc91ae948629765f63a684489ec
      app-secret: e70fd8621f4949b4b2e9dee978228157
      access-token: 58a50cd4f5e242c1ab9da3ae1f0887ae
    # 闪送生产环境
    iss:
      host: open.ishansong.com
      ssl: true
      shop-id: 20000000000544369

logging:
  file:
    # 独立硬盘记录日志
    path: /www/wwwroot/logs
    name: api
