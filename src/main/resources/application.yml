server:
  servlet:
    context-path: /api

spring:
  profiles:
    active: dev
    include: ddos
  application:
    name: benyin-api
  datasource:
    hikari:
      pool-name: HIKARI-POOL
      # 核心数的2倍 16核配置 默认10
      maximum-pool-size: 32
      # 参考数据库连接超时时间 默认30分钟
      max-lifetime: 1800000
      # 数据库连接心跳检测时间间隔 每分钟检测一次 默认不检测
      keepalive-time: 60000
      # 连接有效性测试语句 若不配置使用JDBC4.0 isValid
      connection-test-query: SELECT 1

# mybatis逻辑删除配置
mybatis-plus:
  typeEnumsPackage: com.hightop.benyin.*
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
      logic-delete-field: 'deleted'

benyin:
  tencent:
    # 腾讯云cos
    cos:
      bucket: sczjzy-1332842668
      region: ap-chengdu
      # 默认临时目录 prod应该修改为其他目录便于区分
      prefix: temp/
      secret-id: AKIDvjMn24yytgLvgM8pNn4C3nNnDXfbMyOd
      secret-key: 72mNlz3xlQuEcq4IOI8wb5NTN8kEcEnC
#      试用版本id key
#      bucket: benyin-**********
#      region: ap-chengdu
#      # 默认临时目录 prod应该修改为其他目录便于区分
#      prefix: temp/
#      secret-id: AKIDDtSoywjJgqqfHgsS3ZxLchdOyLUQCoDO
#      secret-key: 42qQp46wSoxugEZvJYq29Ui5GEyfFcnm
    # 腾讯地图
    lbs:
#      app-key: SXEBZ-CKGCU-5HFVN-GHNV7-VTJ5J-ADBQB
      app-key: NKWBZ-JSNL3-6DC3G-OSDDG-5LX2T-RDBFW
      app-secret: nwJxrCkQA3gPZ4gIoUbadxvKElFN0imV
    # 小程序配置
    wechat:
      # 公众号
      official-account:
        app-id: wx99a490ac6b6b83c2
        app-secret: 4e54554b8daad80197530cb745df308c
      # 图文店客户版小程序
      customer:
        app-id: wx8b0f321d785c335c
        app-secret: ef048e506fdf82b955a30876c2b1a41c
#        试用版本id secret
#        app-id: wx350d5daa414d47e0
#        app-secret: 5937e5ad4fe432fb4e33d84c1cc2035c
      # 员工版小程序配置
      staff:
        app-id: wxa8ab97d814a26cc6
        app-secret: 5dfacfc7c4f653a05a3d5409090d275b
  pay:
    # 支付模式
    mode: wechat
    # 微信支付
    wechat:
      merchant-id: **********
      merchant-serial-number: 127CE2B13D3FF04B83E6C8F96DA5DBFBC06CF83F
      private-key: "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDCFFLXQGSn43lLAXo/K0QBrncwDKNSzAmZob2ZcbtanBW0J24WtkkQ+D7O7vIPudyFiPaJJX6dCiuT5Tv1LbCDe+bXZLbzBfQQoIjlvn2owfTuSLLfhESR48XHbQBP0D/R+zh4wWKGSZHvM5v7io4kxceRvKdtr9zBK6yeGPjDwcEqLxwhT3xCGxRLGPMsJPPhzc6oGA4f/SSAKjGG6WNTsO6EcZ4p2O4aJliQ85wcMYH+qSYV5bFJwNBsFUrLPOTZ1PqUSm9+70Qk7XPTHuX1zOGuCX5fs6XI6GU4hWQ8qvy2UcWW8hmK5rWjNuaHQTF2MhHOuU/UtSTqvhpI+dQfAgMBAAECggEASFflDUF+g6piMxhI5jUNc4cB/JIpaxqNVYwZrZHcYYke+flcqvClycKPPvmIl14Tml8UbsjgKk95MPbY46p1Sn2r7YwoOex1LqF4hhL8qiQqVYwZyQF3wd3iglTaMeYRdAct5K1JOX9A+6YENb5O/9wNaQCmdFeCdSr/fCsYx3bR2EeoDbkgtIIem6rV7+DstXtip7DBj9VlvfKQIx+qAkZgC5NYTxOtj9Gb1wo4RxGjhsbArdhBXJ0Divi+VvPpPW8fTBcIgXaWHCzES8HGBZKmKWv1Juvz8Bj/yv8g0FhGWBmx+WUD19tfXNntgCHIRduuih8/SH1LPBc3SQeYIQKBgQD0PvnrNAkode7a4SpQf+ouxYF40B8yPNz+iCulcdUPHopgq1TIOizH5wn1wNHqwK7V6DuTS7IpnowzxJVHcRkyfHW2hfDbFcpREqc7sRwscxiI5bXwT2kzDh800NUsrEhZ31fTbNJ/hsso1GjG/oau+lDOOa1UUF6mlvVB0eodMQKBgQDLa1ADxyDDzfzN0xZ4uhhe3qhdECh2xroa2LXUgiaKaE6HikBfU5fawO0V9g5FcCfXalVPFkWFVVAWTyf5CyoY/EGXVU1YFh2Q50IDGhO5dwtevpmtPa+uHZX6R3xzeDADfKIrjxpBMcY8QDBM7hTz5Sqa0yj+3NR1R5JGkatyTwKBgQDaVVFjB2kIWiPiVVVWEIJuvO836BN6/R0wCtm5U0B5+3sHXyCyvA47xDQLEwqTJxsi7C4VRZ1FxwzdWeV3r4our0MqniDoN1pbjqkfmvrsYRaViYJSByqrEPykYemtvXQPoq8HuEeS6BPDs9zVxcDvuaNyNezTaeSyVk93PwZggQKBgBPnu1uzR5yBcqR9bGyB4VKfPSBmLV96EMoBvME/vx+6Fz0iJSxu1KQ+TrTgcAMRd4SXziNADat/bqfQNRwkoIjzXkO9wCTHfTsptgbxrU5vvLgXtJgEOkWydXOZPS9bam9c6c2TYkFQ6pIIwfE1Nu2q1iFyWkeeOlgDmee352v3AoGBAKeiItIzpVp8nC3FZreerHLZQi3BhXq1LB5O1Y0a+TrO2gxrRQctkBj8gMvw68ZdviwyLOydHmb12Dr8p2QYLGybSQawV30NRVlOX5lYAU1DBLRlDK+dtJU3iVNum15lDhJAXsD2PWw1ns+kDtIfU8oUIKV2GWxnn8W8GHYilAwZ"
      public-key-id: PUB_KEY_ID_01**********2025011000458300000203
      public-key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvLNzuyHmCenq0obNG6qrbpFOD83cdTulk/jyi+iFq8A5QvBOMGNauS8qFc8EvniwpygAkljEg5SwkGUutL4X7cQBibRrWDDphNfNPyoHr0gwo2A2ObY6XahBQ6SgNmQYwQGmeXrz4SeR1koSvSoaLmGkAG7Olj1GKFJLWhjMGpu+i63DcYlyaQ+FiWKWJgmVM8JdF+Dzy0fWPvEyE+sVn7OACLLWqoPnpjegN4NDi8TtIrJLkJloag3x8oY48Qset1zQimf8YOaCyYEM+eYbmP+hQJo4OdiuJm9OfYN2XmEkPaHUBU/3IBaNbRby7ggkE/DoaiLVrdaorbyZ43W0zwIDAQAB"
      api-v3-key: Wm1eine2x3u9mqe4m3bi5c3m4pqtxyc4
      notify-server: "https://sczjzy.com.cn"
    # 工行支付
    icbc:
      host: gw.open.icbc.com.cn
      app-id: 11000000000000050505
      merchant-id: 440257320651
      merchant-protocol-number: 4402573206510201
      gateway-public-key: "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC0hlzEc/fZmNiu4xc1OMR8sMH7MA9/tQAnvHgS04X3n+9hLc3894j7MImmmdBNdbErDyacMBAlVSJ5iMErIhUvnAr0+XGxnmoqAQ3yzOYoDrcnxv+3GTr1OO5OZhiWEBo0Jg5untUZ1AxjtZl7YPwTY3EuW7+o49/oBhSgeXy5nBzS6/T8QLt6+GZS9r55L9gR7i+AQGWufiu7zmiZRP96wcvH8OXx1Nd4MwAJ9zFE4thhv3WP46y2EqEy7Iz+bS21mT42Geba7nG/dMCg+TY38N7nmPjiMRVvcZstbPX0rkTMjpzSmZFnaeRYQkrJMVcSKmikKBR1kFaO4KWp4s6bAgMBAAECggEADQC6kNnricxVGy+3PbmjiXmv1zhsfV3br4EHQw/iT+ZAt0a11hk8UB98YrgkhVfLVZcnSEu79fxuWcOsaj9vuDisn12SP2FyMylVSF5S26LHtYFDbPxPv1cE7zeyYKizQntVQcmF/vDDnbD4Z6ciMQFSJy0rnm4fdBZwCOkHP1Us0kn8789/ieqg+AzR3jaGdShPMWChP5mfDht1tIOS9QIl/ChJFm2pMLTtdiW79nDuOr5qud77SLBK3aO4s/KWLocAXe7nTbQP0Wwn8xtoPOlFLvhEDj+0QriIv9Xpm1OyfoH4QgwWsvCsD8Awsv45XZAioPAo/9kP9K6PU/ajSQKBgQDb1aeWRbqND/mxb8UYQb62N5+QDjkp6mH1ExcZ0vt6fDZ4W9bEmX6WJlYT3dSWsulwfEaZPJqF0MD37BJHooCkmAxNkwVDLMVP3BM0ZfEEKeK8Kfwy8eGs8cX0nrmc9lF3ET/U9d3vKcyZpfHfhZhfiU97vE0bOuMUdkgvkmgZSQKBgQDSOS0Xp0niTwCcZGl8L3LUzpQg8LIVRrvjztcLGFMJ6U6PTe9GKBySz1I+TVBzWe6F7mKgmc1374KoD2xaJc/y1WmkF77AOjiPYe6HlFvv+85Mz7OMj5nr+rZj3BSIn368NCoo9oBRdhOSGLd9H5eJl3h1KJCnEWc6kl8jVUcswwKBgHxCRDwYpd3eTdER0GJppxbwUeznXUBoD36rj781FpihZVia8ManzHmqqbUJCoU0E+dVF8K+EsnYuXppoduD6xseJrYiiDL0N4NwbhqMFLO5yUN6p+dT+a/76Vzf6yLhyCJ5SY9ielQZWGDw/8rMhNhbkOXsBdmCa6DBSwQ4yUahAoGAUFCDx7uj4JAqPZWnL0i5aFAcC5Gpl7NUaS5GvfhnESxPVTSDK5x7fiMy2paTG97J3+U5TP41n5Kjqi1qSe6UScP6/jQMLkiP/0d859NoZIEhSfs4L4VVWvCH6hLUzXWR57lOOiQi1fx0caZ7w5PkQy7ZWHlM+mcNtzTgoBEehh0CgYB8Xm5fpEygVtG0Gi5u/ODd+hKHm8MFr9zSBxa2HdXMFz75d64eZXmWsds56hO18s+hsxvtNi5vpylcdoF0ahjRS4NDqPC6Ly/+9mlQLBYqnEWDFXVkXytmUoi96NmAdDI4ag/1x9FI5uMu7vshnRokQXZbl6P9rWOSMWpSrStevg=="
      private-key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtIZcxHP32ZjYruMXNTjEfLDB+zAPf7UAJ7x4EtOF95/vYS3N/PeI+zCJppnQTXWxKw8mnDAQJVUieYjBKyIVL5wK9PlxsZ5qKgEN8szmKA63J8b/txk69TjuTmYYlhAaNCYObp7VGdQMY7WZe2D8E2NxLlu/qOPf6AYUoHl8uZwc0uv0/EC7evhmUva+eS/YEe4vgEBlrn4ru85omUT/esHLx/Dl8dTXeDMACfcxROLYYb91j+OsthKhMuyM/m0ttZk+Nhnm2u5xv3TAoPk2N/De55j44jEVb3GbLWz19K5EzI6c0pmRZ2nkWEJKyTFXEipopCgUdZBWjuClqeLOmwIDAQAB"
      callback-server-url: ${benyin.pay.wechat.notify-server}
  logistics:
    # 开发测试环境使用的京东沙箱环境
    jd:
      host: test-api.jdl.com
      customer-code: 028K3566096
      app-key: 95606bc91ae948629765f63a684489ec
      app-secret: e70fd8621f4949b4b2e9dee978228157
      access-token: 58a50cd4f5e242c1ab9da3ae1f0887ae
      # 测试、生产产品编码都一样
      product-code: ed-m-0001
    # 开放测试环境使用闪送测试环境
    iss:
      host: open.s.bingex.com
      # 测试、生产环境app key、secret都一样
      app-key: ssQMBRH3r5nPq4M8i
      app-secret: Y56scaSITQKAaZjY7oXbwFb1sZ5TSBtw
      # 测试环境门店id
      shop-id: 20000000000544369
  # 天猫精灵配置 仅生产环境会通知
  aligenie:
    access-key-id: 4b24d5d9d3e4a8270421daac9da6ad5a
    access-key-secret: 4d2d33bff5e88715ffffdae39050f6dc
    notification:
      skill-id: 104888
      # 消息模版id
      message-template-id: 6LUaNIBzcMYEHcET
      # 设备open id
      device-open-id: Xl0Veob5bgltEyomHGV3ThAlguecwSdCjw3BAPeRYEHZiyDOxcviWQ==
logging:
  level:
    feign: debug
