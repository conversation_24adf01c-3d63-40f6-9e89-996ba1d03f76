spring:
  datasource:
    # mysql 可以省略驱动名称
    url: *******************************************************************
    username: root
    password: root
#    url: jdbc:mysql://**************:3306/benyin?useOldAliasMetadataBehavior=true
#    username: root
#    password: By@ws%^uf&@#Mysql

  redis:
    host: 127.0.0.1
    port: 6379
    database: 3
  elasticsearch:
    rest:
      uris: http://127.0.0.1:9200
      #      username:     #用户名
      #      password:     #密码
      connection-timeout: 6000
      read-timeout: 6000
  data:
    elasticsearch:
      repositories:
        enabled: true
es:
  host: 127.0.0.1
  port: 9200