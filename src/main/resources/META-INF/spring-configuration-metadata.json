{"groups": [{"name": "benyin.pay.icbc", "description": "本印工商银行聚合支付配置."}, {"name": "benyin.pay.wechat", "description": "微信支付配置."}, {"name": "benyin.logistics.jd", "description": "京东物流配置."}, {"name": "benyin.logistics.iss", "description": "闪送物流配置."}, {"name": "benyin.tencent.cos", "description": "腾讯cos配置."}, {"name": "benyin.tencent.mini-program", "description": "腾讯小程序配置(客户商城版)."}, {"name": "benyin.aligenie", "description": "天猫精灵配置."}], "properties": [{"name": "benyin.pay.mode", "type": "java.lang.String", "description": "支付接入模式."}, {"name": "benyin.pay.wechat.merchant-id", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.wechat.WeChatPaymentProperties", "type": "java.lang.String", "description": "商户id."}, {"name": "benyin.pay.wechat.merchant-serial-number", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.wechat.WeChatPaymentProperties", "type": "java.lang.String", "description": "证书编号."}, {"name": "benyin.pay.wechat.private-key", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.wechat.WeChatPaymentProperties", "type": "java.lang.String", "description": "商户签名私钥."}, {"name": "benyin.pay.wechat.public-key", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.wechat.WeChatPaymentProperties", "type": "java.lang.String", "description": "微信支付公钥."}, {"name": "benyin.pay.wechat.public-key-id", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.wechat.WeChatPaymentProperties", "type": "java.lang.String", "description": "微信支付公钥ID."}, {"name": "benyin.pay.wechat.api-v3-key", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.wechat.WeChatPaymentProperties", "type": "java.lang.String", "description": "V3版本api密钥."}, {"name": "benyin.pay.wechat.notify-server", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.wechat.WeChatPaymentProperties", "type": "java.lang.String", "description": "支付回调服务器地址."}, {"name": "benyin.pay.icbc.host", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.icbc.IcbcProperties", "type": "java.lang.String", "description": "工行api服务主机地址(仅域名)."}, {"name": "benyin.pay.icbc.app-id", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.icbc.IcbcProperties", "type": "java.lang.String", "description": "工行appId."}, {"name": "benyin.pay.icbc.gateway-public-key", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.icbc.IcbcProperties", "type": "java.lang.String", "description": "工行网关签名公钥."}, {"name": "benyin.pay.icbc.private-key", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.icbc.IcbcProperties", "type": "java.lang.String", "description": "工行验签私钥."}, {"name": "benyin.pay.icbc.merchant-id", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.icbc.IcbcProperties", "type": "java.lang.String", "description": "商户id."}, {"name": "benyin.pay.icbc.merchant-protocol-number", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.icbc.IcbcProperties", "type": "java.lang.String", "description": "商户协议编号."}, {"name": "benyin.pay.icbc.callback-server-url", "sourceType": "com.hightop.benyin.payment.infrastructure.restful.icbc.IcbcProperties", "type": "java.lang.String", "description": "支付回调服务器地址."}, {"name": "benyin.logistics.jd.host", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.String", "description": "京东主机地址."}, {"name": "benyin.logistics.jd.customer-code", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.String", "description": "京东客户编码."}, {"name": "benyin.logistics.jd.app-key", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.String", "description": "京东app key."}, {"name": "benyin.logistics.jd.app-secret", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.String", "description": "京东app密钥."}, {"name": "benyin.logistics.jd.schema-code", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.String", "description": "京东LOP-DN."}, {"name": "benyin.logistics.jd.access-token", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.String", "description": "京东访问令牌."}, {"name": "benyin.logistics.jd.order-origin", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.Integer", "description": "京东下单来源."}, {"name": "benyin.logistics.jd.product-code", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.String", "description": "京东产品编码."}, {"name": "benyin.logistics.jd.version", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.jd.JdProperties", "type": "java.lang.String", "description": "京东api版本号.", "defaultValue": "2.0"}, {"name": "benyin.logistics.iss.host", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.iss.IssProperties", "type": "java.lang.String", "description": "闪送主机地址."}, {"name": "benyin.logistics.iss.ssl", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.iss.IssProperties", "type": "java.lang.Bo<PERSON>an", "description": "闪送主机地址是否使用ssl.", "defaultValue": false}, {"name": "benyin.logistics.iss.app-key", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.iss.IssProperties", "type": "java.lang.String", "description": "闪送app key."}, {"name": "benyin.logistics.iss.app-secret", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.iss.IssProperties", "type": "java.lang.String", "description": "闪送app密钥."}, {"name": "benyin.logistics.iss.shop-id", "sourceType": "com.hightop.benyin.logistics.infrastructure.restful.iss.IssProperties", "type": "java.lang.String", "description": "闪送商户id."}, {"name": "benyin.tencent.cos.region", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties", "type": "java.lang.String", "description": "腾讯cos所属区域."}, {"name": "benyin.tencent.cos.bucket", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties", "type": "java.lang.String", "description": "腾讯cos存储桶名称."}, {"name": "benyin.tencent.cos.prefix", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties", "type": "java.lang.String", "description": "腾讯cos存储目录."}, {"name": "benyin.tencent.cos.secret-id", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties", "type": "java.lang.String", "description": "腾讯cos密钥id."}, {"name": "benyin.tencent.cos.secret-key", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties", "type": "java.lang.String", "description": "腾讯cos密钥."}, {"name": "benyin.tencent.cos.max-transfer-timeout", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.CosProperties", "type": "java.lang.Long", "description": "腾讯cos文件最大传输时长.", "defaultValue": 1800}, {"name": "benyin.tencent.lbs.host", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.LbsProperties", "type": "java.lang.String", "description": "腾讯地图服务主机地址.", "defaultValue": "apis.map.qq.com"}, {"name": "benyin.tencent.lbs.app-key", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.LbsProperties", "type": "java.lang.String", "description": "腾讯地图app-key."}, {"name": "benyin.tencent.lbs.app-secret", "sourceType": "com.hightop.benyin.share.infrastructure.restful.tencent.LbsProperties", "type": "java.lang.String", "description": "腾讯地图app密钥."}, {"name": "benyin.tencent.wechat.official-account.app-id", "sourceType": "com.hightop.benyin.share.infrastructure.restful.wechat.WeChatProperties", "type": "java.lang.String", "description": "公众号AppId."}, {"name": "benyin.tencent.wechat.official-account.app-secret", "sourceType": "com.hightop.benyin.share.infrastructure.restful.wechat.WeChatProperties", "type": "java.lang.String", "description": "公众号密钥."}, {"name": "benyin.tencent.wechat.customer.app-id", "sourceType": "com.hightop.benyin.share.infrastructure.restful.wechat.WeChatProperties", "type": "java.lang.String", "description": "客户版小程序AppId."}, {"name": "benyin.tencent.wechat.customer.app-secret", "sourceType": "com.hightop.benyin.share.infrastructure.restful.wechat.WeChatProperties", "type": "java.lang.String", "description": "客户版小程序密钥."}, {"name": "benyin.tencent.wechat.staff.app-id", "sourceType": "com.hightop.benyin.share.infrastructure.restful.wechat.WeChatProperties", "type": "java.lang.String", "description": "员工版小程序AppId."}, {"name": "benyin.tencent.wechat.staff.app-secret", "sourceType": "com.hightop.benyin.share.infrastructure.restful.wechat.WeChatProperties", "type": "java.lang.String", "description": "员工版小程序密钥."}, {"name": "benyin.aligenie.access-key-id", "sourceType": "com.hightop.benyin.share.infrastructure.restful.aligenie.AligenieProperties", "type": "java.lang.String", "description": "天猫精灵AccessKeyId"}, {"name": "benyin.aligenie.access-key-secret", "sourceType": "com.hightop.benyin.share.infrastructure.restful.aligenie.AligenieProperties", "type": "java.lang.String", "description": "天猫精灵accessKeySecret"}, {"name": "benyin.aligenie.notification.skill-id", "sourceType": "com.hightop.benyin.share.infrastructure.restful.aligenie.AligenieProperties.AligenieNotification", "type": "java.lang.String", "description": "天猫精灵技能id"}, {"name": "benyin.aligenie.notification.message-template-id", "sourceType": "com.hightop.benyin.share.infrastructure.restful.aligenie.AligenieProperties.AligenieNotification", "type": "java.lang.String", "description": "天猫精灵消息模版id"}, {"name": "benyin.aligenie.notification.device-open-id", "sourceType": "com.hightop.benyin.share.infrastructure.restful.aligenie.AligenieProperties.AligenieNotification", "type": "java.lang.String", "description": "天猫精灵设备id"}], "hints": [{"name": "benyin.pay.mode", "values": [{"value": "icbc", "description": "工行支付"}, {"value": "wechat", "description": "微信支付"}]}]}