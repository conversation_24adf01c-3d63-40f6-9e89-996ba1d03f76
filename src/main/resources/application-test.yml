spring:
  datasource:
    # mysql 可以省略驱动名称
    url: *******************************************************************
    username: root
    password: de1dcfc693462acf
  #    url: jdbc:mysql://**************:3306/benyin?useOldAliasMetadataBehavior=true
  #    username: root
  #    password: By@ws%^uf&@#Mysql

  redis:
    host: 127.0.0.1
    port: 6379
    database: 2
  elasticsearch:
    rest:
      uris: http://127.0.0.1:9200
#      username: elastic
#      password: SirP*nEsajkMLlVowd38
      connection-timeout: 6000
      read-timeout: 6000
  data:
    elasticsearch:
      repositories:
        enabled: true
es:
  host: 127.0.0.1
  port: 9200
benyin:
  pay:
    wechat:
      # 生产环境支付回调请求域名
      notify-server: "https://sczjzy.com.cn"