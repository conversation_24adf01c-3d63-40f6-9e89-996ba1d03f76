# DDoS防护配置
website:
  ddos-protection:
    # 是否启用DDoS防护
    enabled: true

    # 全局限流配置
    global:
      # 全局每秒最大请求数
      max-requests-per-second: 2000
      # 全局每分钟最大请求数
      max-requests-per-minute: 20000

    # IP限流配置
    ip:
      # 单IP每秒最大请求数
      max-requests-per-second: 20
      # 单IP每分钟最大请求数
      max-requests-per-minute: 600
      # 单IP每小时最大请求数
      max-requests-per-hour: 7200
      # 单IP每天最大请求数
      max-requests-per-day: 100000
      # 触发临时封禁的阈值（每分钟）
      ban-threshold-per-minute: 800
      # 临时封禁时长（分钟）
      ban-duration-minutes: 3

    # 接口限流配置
    api:
      # 首页接口限流（每分钟）
      homepage-per-minute: 200
      # 内容接口限流（每分钟）
      content-per-minute: 200
      # 图片接口限流（每分钟）
      image-per-minute: 300
      # 配置接口限流（每分钟）
      config-per-minute: 200
      # 咨询提交限流（每小时）
      inquiry-per-hour: 100
    
    # 黑名单配置
    black-list:
      # 是否启用黑名单
      enabled: true
      # 黑名单检查间隔（秒）
      check-interval-seconds: 60
      # 自动加入黑名单的阈值
      auto-add-threshold: 1000
      # 黑名单过期时间（小时）
      expire-hours: 24

# 开发环境配置
---
spring:
  profiles: dev
website:
  ddos-protection:
    enabled: false  # 开发环境关闭DDoS防护

# 测试环境配置
---
spring:
  profiles: test
website:
  ddos-protection:
    ip:
      max-requests-per-minute: 2000  # 测试环境放宽限制
      max-requests-per-hour: 20000
      ban-threshold-per-minute: 1500
      ban-duration-minutes: 1
    api:
      config-per-minute: 500

# 生产环境配置
---
spring:
  profiles: prod
website:
  ddos-protection:
    enabled: true
    ip:
      max-requests-per-minute: 300   # 生产环境适度限制
      max-requests-per-hour: 3600
      ban-threshold-per-minute: 500
      ban-duration-minutes: 10       # 从60分钟改为10分钟
    api:
      config-per-minute: 150
