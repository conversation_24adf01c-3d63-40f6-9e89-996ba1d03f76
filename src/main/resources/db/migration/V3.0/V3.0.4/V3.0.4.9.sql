
alter table tb_apply_detail
    add cancel_num bigint null  default 0 comment '取消数量' after received_num;

alter table tb_apply_detail
    add status varchar(32) null comment '状态' after sale_unit_price;

update tb_apply_detail set status='PARTIAL_DONE' where received_num<>tb_apply_detail.num;
update tb_apply_detail set status='DONE' where received_num=tb_apply_detail.num;
update tb_apply_detail set status='CREATE' where received_num=0;