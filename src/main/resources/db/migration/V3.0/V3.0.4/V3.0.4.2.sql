drop table if exists r_sales_monthly;
create table r_sales_monthly
(
    id                bigint auto_increment comment 'id'
        primary key,
    customer_id         bigint        null comment '客户id',
    curr_month        varchar(8) not null comment '月份',
    order_num         int        null comment '销售订单数',
    order_amount      bigint     null comment '销售订单金额',
    apply_num         bigint     null comment '领料订单数',
    apply_amount      bigint     null comment '领料订单金额',
    work_num          bigint     null comment '散修工单数',
    work_part_amount      bigint     null comment '散修工单耗材费',
    work_labor_amount bigint     null comment '散修人工费',
    pact_work_num       bigint     null comment '全保/半保工单数',
    pact_part_amount       bigint     null comment '全保/半保耗材费',
    pact_labor_amount   bigint     null comment '全保/半保人工费',
    created_at        datetime   null comment '创建时间'
)
    comment '月度销售统计' collate = utf8mb4_bin;
create index r_sales_monthly_curr_month_index
    on r_sales_monthly (curr_month);
create index r_sales_monthly_curr_month_cust_index
    on r_sales_monthly (curr_month,customer_id);


alter table b_iot_numeric_code
    add is_notify tinyint(1)  default 0 null  comment '是否通知' after state;
alter table b_iot_numeric_code
    add is_statistics tinyint(1)  default 0 null  comment '是否统计' after state;
alter table b_iot_numeric_code
    add  product_tree_ids json   null comment '产品机器ID'  after series;
alter table b_iot_numeric_code
    add  unmatch_series  varchar(1000)   null comment '未匹配产品机型'  after product_tree_ids;

