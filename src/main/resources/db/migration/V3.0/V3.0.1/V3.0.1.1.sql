
drop table if exists b_pay_voucher;
create table b_pay_voucher
(
    id bigint not null
        primary key,
    trade_order_number varchar(64) null comment '支付订单编号',
    trade_order_origin varchar(32) not null comment '支付订单类型',
    customer_id bigint(20) null comment '客户id',
    order_amount bigint not null comment '订单应付金额',
    pay_amount bigint not null comment '实付金额',
    voucher_img json not null comment '凭证照片',
    status varchar(32) not null comment '状态 待审核/通过/拒绝',
    remark varchar(255) null comment '备注',
    create_by bigint(20) null comment '支付人',
    audit_by bigint(20) null comment '审核人',
    created_at datetime(3) not null comment '创建时间',
    updated_at datetime(3) null comment '修改时间',
    audit_at datetime(3) null comment '审核时间'
)
    comment '支付凭证';

create index idx_bpv_ton_too
    on b_pay_voucher (trade_order_number, trade_order_origin);
