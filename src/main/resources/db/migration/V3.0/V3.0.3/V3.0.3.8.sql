alter table b_storage_out_warehouse_goods
    add out_status varchar(32) null comment '适用场景' after auidt_out_warehouse_number;

update b_storage_out_warehouse_goods
set out_status = 'yck'
where out_warehouse_number = auidt_out_warehouse_number;
update b_storage_out_warehouse_goods
set out_status = 'bfck'
where auidt_out_warehouse_number > 0
  and out_warehouse_number > auidt_out_warehouse_number;

update b_storage_out_warehouse_goods
set out_status = 'dck'
where auidt_out_warehouse_number = 0;

update b_storage_out_warehouse_goods
set out_status = 'GB'
where benyin.b_storage_out_warehouse_goods.out_warehouse_id in
      (select id from b_storage_out_warehouse where out_status = 'gb');

alter table b_storage_out_warehouse_goods
    add cancel_out_warehouse_number int(10) default 0 null comment '取消出库数量' after auidt_out_warehouse_number;

alter table b_storage_out_warehouse_goods
    add remarks varchar(255)  null comment '备注' after cancel_out_warehouse_number;

