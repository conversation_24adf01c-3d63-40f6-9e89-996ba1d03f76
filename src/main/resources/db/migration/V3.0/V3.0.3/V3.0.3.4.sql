create table IF NOT EXISTS r_replace_part_count
(
    id                    bigint auto_increment comment 'id'
        primary key,
    customer_id           bigint(20)   not null comment '客户id',
    device_group_id       bigint(20)   not null comment '设备组id',
    product_id            bigint(20)   not null comment '机器id',
    device_group          varchar(64)  null comment '设备组名称(字典项码)',
    brand                 varchar(32)  not null comment '品牌',
    machine               varchar(32)  not null comment '机型',
    reg_cli_state         varchar(2)   not null comment '是否安装客户端1是0否',
    treaty_type           varchar(8)   not null comment '合约类型',
    item_id               bigint       null comment '商品编码',
    article_code          varchar(32)  null comment '物品编码',
    article_name          varchar(256) null comment '物品名称',
    sale_sku_id           bigint       null comment 'sku id',
    sale_unit_price       bigint       null comment '商品销售单价',
    location              varchar(32)  null comment '更换位置',
    num                   int          null comment '数量',
    is_pm                 tinyint(1)   null comment '是否是PM件',
    part_id               bigint       null comment '零件id',
    curr_repair_type      varchar(32)   null comment '本次维修类型WORK工单SELF自修',
    last_repair_type      varchar(32)     not null comment '上次维修类型WORK工单SELF自修',
    curr_replace_date     datetime      null comment '本次更换日期',
    last_replace_date     datetime     not null comment '上次更换日期',
    last_work_code        varchar(20)  not null comment '上次工单号',
    curr_work_code        varchar(20)   null comment '本次工单号',
    last_replace_id       bigint(20)   not null comment '上次换件id',
    curr_replace_id       bigint(20)    null comment '本次换件id',
    interval_days         int           null comment '间隔天数',
    black_white_inception bigint       null comment '起始黑白打印数',
    black_white_cutoff    bigint       null comment '截止黑白打印数',
    color_inception       bigint       null comment '起始彩色打印数',
    color_cutoff          bigint       null comment '截止彩色打印数',
    black_white_count     bigint       null comment '黑白打印数量',
    color_count           bigint       null comment '彩色打印数量',
    total_count           bigint       null comment '上次维修到本次总打印量',
    created_at            datetime     null comment '创建时间'
)
    comment '零件更换-印量统计' collate = utf8mb4_bin;
create index r_replace_part_count_customer_id_device_id_index
    on r_replace_part_count (customer_id, device_group_id);
create table IF NOT EXISTS r_repair_count
(
    id                    bigint auto_increment comment 'id'
        primary key,
    customer_id           bigint(20)  not null comment '客户id',
    product_id            bigint(20)  not null comment '机器id',
    device_group_id       bigint(20)  not null comment '设备组id',
    device_group          varchar(64) null comment '设备组名称(字典项码)',
    brand                 varchar(32) not null comment '品牌',
    machine               varchar(32) not null comment '机型',
    treaty_type           varchar(8)  not null comment '合约类型',
    reg_cli_state         varchar(2)  not null comment '是否安装客户端1是0否',
    curr_repair_type      varchar(32)  null comment '本次维修类型WORK工单SELF自修',
    last_repair_type      varchar(32)    not null comment '上次维修类型WORK工单SELF自修',
    curr_repair_date      datetime     null comment '本次维修日期',
    last_repair_date      datetime    not null comment '上次维修日期',
    interval_days         int          null comment '间隔天数',
    black_white_inception bigint      null comment '起始黑白打印数',
    black_white_cutoff    bigint      null comment '截止黑白打印数',
    color_inception       bigint      null comment '起始彩色打印数',
    color_cutoff          bigint      null comment '截止彩色打印数',
    black_white_count     bigint      null comment '黑白打印数量',
    color_count           bigint      null comment '彩色打印数量',
    total_count           bigint      null comment '上次维修到本次总打印量',
    curr_work_code       varchar(20)  null comment '本次工单号',
    last_work_code        varchar(20) not null comment '上次工单号',
    created_at            datetime    null comment '创建时间'
)
    comment '维修-印量统计' collate = utf8mb4_bin;
create index r_repair_count_customer_id_device_id_index
    on r_repair_count (customer_id, device_group_id);
create table r_day_count
(
    id                    bigint auto_increment comment 'id'
        primary key,
    customer_id           bigint(20)  not null comment '客户id',
    device_group_id       bigint(20)  not null comment '设备组id',
    product_id            bigint(20)  not null comment '机器id',
    device_group          varchar(64) null comment '设备组名称(字典项码)',
    brand                 varchar(32) not null comment '品牌',
    machine               varchar(32) not null comment '机型',
    treaty_type           varchar(8)  not null comment '合约类型',
    reg_cli_state         varchar(2)  not null comment '是否安装客户端1是0否',
    data_source           varchar(16)  not null comment '统计类型IOT物联网REPAIR维修',
    curr_date             varchar(16) not null comment '日期',
    curr_month            varchar(8)  not null comment '月份',
    interval_days         int          null comment '间隔天数',
    black_white_inception bigint      null comment '起始黑白打印数',
    black_white_cutoff    bigint      null comment '截止黑白打印数',
    color_inception       bigint      null comment '起始彩色打印数',
    color_cutoff          bigint      null comment '截止彩色打印数',
    black_white_count     bigint      null comment '黑白打印量',
    color_count           bigint      null comment '彩色打印量',
    total_count           bigint      null comment '总打印量',
    repair_count_id       bigint      null comment '引用来源指维修区间r_repair_count的id',
    created_at            datetime    null comment '创建时间'
)
    comment '日印量统计' collate = utf8mb4_bin;
create index r_day_count_customer_id_device_id_index
    on r_day_count (customer_id, device_group_id);
create table r_month_count
(
    id                bigint auto_increment comment 'id'
        primary key,
    customer_id       bigint(20)  not null comment '客户id',
    device_group_id   bigint(20)  not null comment '设备组id',
    product_id        bigint(20)  not null comment '机器id',
    device_group      varchar(64) null comment '设备组名称(字典项码)',
    brand             varchar(32) not null comment '品牌',
    machine           varchar(32) not null comment '机型',
    treaty_type       varchar(8)  not null comment '合约类型',
    reg_cli_state     varchar(2)  not null comment '是否安装客户端1是0否',
    curr_month        varchar(32) not null comment '月份',
    black_white_count bigint      null comment '黑白打印量',
    color_count       bigint      null comment '彩色打印量',
    total_count       bigint      null comment '总打印量',
    created_at        datetime    null comment '创建时间'
)
    comment '月印量统计' collate = utf8mb4_bin;
create index r_month_count_customer_id_device_id_index
    on r_month_count (customer_id, device_group_id);
alter table b_customer_device_group
    add average_daily int null comment '日均打印量' after color_price;

alter table b_customer_device_group
    add average_monthly int null comment '月均打印量' after average_daily;

create table IF NOT EXISTS r_print_cost_part
(
    id                bigint auto_increment comment 'id'
        primary key,
    customer_id       bigint(20)     not null comment '客户id',
    device_group_id   bigint(20)     not null comment '设备组id',
    product_id        bigint(20)     not null comment '机器id',
    device_group      varchar(64)    null comment '设备组名称(字典项码)',
    brand             varchar(32)    not null comment '品牌',
    machine           varchar(32)    not null comment '机型',
    reg_cli_state     varchar(2)     not null comment '是否安装客户端1是0否',
    treaty_type       varchar(8)     not null comment '合约类型',
    part_name         varchar(256)   null comment '物品名称',
    part_id           bigint         null comment '零件id',
    sale_sku_id       bigint         null comment 'sku id',
    position          json           null comment '位置',
    sale_unit_price   bigint         null comment '商品销售单价',
    is_pm             tinyint(1)     null comment '是否是PM件',
    lifespan          bigint         null comment '运营修正寿命',
    ori_cost          decimal(18, 9) null comment '原成本',
    actual_cost       decimal(18, 9) null comment '实际成本',
    replace_count     tinyint        null comment '更换次数',
    black_white_count bigint         null comment '黑白打印数量',
    color_count       bigint         null comment '彩色打印数量',
    total_count       bigint         null comment '总打印量',
    created_at        datetime       null comment '创建时间',
    updated_at        datetime       null comment '修改时间'
)
    comment '零件-打印成本表' collate = utf8mb4_bin;
create index r_print_cost_part_customer_id_device_id_index
    on r_print_cost_part (customer_id, device_group_id);

update tb_self_repair_report set black_white_count = 0 where  black_white_count is null;
update tb_self_repair_report set color_count = 0 where  color_count is null;
update tb_repair_report set black_white_count = 0 where  black_white_count is null;
update tb_repair_report set color_count = 0 where  color_count is null;

alter table tb_self_repair_report
    modify black_white_count bigint default 0 not null comment '黑白计数器';

alter table tb_self_repair_report
    modify color_count bigint default 0 not null comment '彩色计数器';

alter table tb_repair_report
    modify black_white_count bigint default 0 not null comment '黑白计数器';

alter table tb_repair_report
    modify color_count bigint default 0 not null comment '彩色计数器';


