alter table tb_trade_order_detail
    add reverse_num int(10) default 0 null  comment '申请售后数量' after receive_num;
update tb_trade_order_detail t ,(select t1.trade_order_detail_id,sum(reverse_item_num) num
                                 from benyin.b_reverse_order t
                                          left join benyin.tb_reverse_order_detail t1 on t.id = t1.reverse_order_id
                                 where t.audit_status not in ('CLOSED','REFUSE')
                                 group by t1.trade_order_detail_id) t1
set t.reverse_num = t1.num
where t.id = t1.trade_order_detail_id;

alter table tb_reverse_order_detail
    add reduce_item_num int(10) default 0 null  comment '需要扣减库存商品数量' after return_back_item_num;
alter table tb_reverse_order_detail
    add refund_item_num int(10) default 0 null  comment '仅退款数量' after return_back_item_num;
alter table tb_reverse_order_detail
    add out_back_item_num int(10) default 0 null  comment '已出库未发货商品数量' after refund_item_num;