
alter table  b_iot_print_receipt
    add pay_mode varchar(32)  null comment '支付方式0在线支付1对公支付' after status;

alter table  b_pay_order
    add customer_id varchar(32)  null comment '客户id' after trade_order_origin;

alter table  b_pay_voucher
    add serial_number varchar(64)  null comment '交易流水号' after pay_amount;

alter table tb_repair_report
    add color_exclude bigint default 0 null comment '彩色废张数量' after print_count;

alter table tb_repair_report
    add black_white_exclude bigint default 0 null comment '黑白废张数量' after print_count;

alter table b_iot_print_count
    add color_exclude bigint default 0 null comment '彩色废张数量' after color_point;

alter table b_iot_print_count
    add black_white_exclude bigint default 0 null comment '黑白废张数量' after black_white_point;


update b_pay_order t ,benyin.tb_trade_order t1
set t.customer_id = t1.customer_id
where t.trade_order_number = t1.order_num and t.trade_order_origin = 'SALES_ORDER';

update b_pay_order t ,benyin.tb_work_order t1
set t.customer_id = t1.customer_id
where t.trade_order_number = t1.code and t.trade_order_origin = 'REPAIR_ORDER';
