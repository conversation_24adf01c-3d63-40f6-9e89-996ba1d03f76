alter table b_iot_pointer
    add first_id bigint(20) null comment '第一次上报的数据id';

alter table b_iot_pointer
    add last_id bigint(20) null comment '最后一次上报的数据id';

alter table b_iot_pointer
    add begin_time datetime null comment '统计日期起';

alter table b_iot_pointer
    add end_time datetime null comment '统计时间止';

alter table b_iot_pointer
    add black_white_amount decimal(10, 4) null comment '黑白金额' after amount;

alter table b_iot_pointer
    add color_amount decimal(10, 4) null comment '彩色金额' after amount;


alter table b_iot_pointer
    change year_months cycle varchar(32) not null comment '周期';

alter table tb_cart
    add order_type varchar(32) not null comment '订单类型';
update tb_cart set order_type = 'SALE' where id>1;