rename table b_iot_pointer to b_iot_print_count;

alter table b_customer
    add taxpayer varchar(64) null comment '纳税人识别号' after legal_person_tel;


create table b_iot_print_receipt
(
    id           bigint auto_increment comment 'id'
        primary key,
    customer_id  bigint         not null comment '客户id',
    cycle        varchar(32)    not null comment '周期',
    status       varchar(32)    not null comment '状态',
    total_amount bigint(20) null comment '总金额',
    paid_amount    bigint(20) null comment '已付金额',
    amount       bigint(20) null comment '待付金额',
    created_at   datetime       null comment '创建时间',
    updated_at   datetime       null comment '更新时间'
)
    comment '物联网-打印服务收款单' collate = utf8mb4_bin;