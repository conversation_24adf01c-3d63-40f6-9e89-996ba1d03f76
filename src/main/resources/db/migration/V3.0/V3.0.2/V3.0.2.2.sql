alter table b_customer_device_group
    add black_white_price decimal(6,4) null comment '黑白打印单价';
alter table b_customer_device_group
    add color_price decimal(6,4) null comment '彩色打印单价';

-- auto-generated definition
drop table if exists b_iot_pointer;
create table b_iot_pointer
(
    id                bigint auto_increment comment 'id'
        primary key,
    customer_id       bigint      not null comment '客户id',
    device_group_id   bigint      not null comment '设备组id',
    device_group     varchar(64)       null comment '设备组名称(字典项码)',
    brand       varchar(32) not null comment '品牌',
    machine       varchar(32) not null comment '机型',
    ser_type          varchar(2)  not null comment '服务类型1散修2全保3租赁4半保5签约6数据7否',
    year_months       varchar(32) not null comment '年月',
    data_source       varchar(32) not null comment '数据来源',
    salesman_id          bigint(20)  null comment '销售人员id',
    salesman_name          varchar(20)  null comment '销售人员姓名',
    enginner_id       bigint(20)  null comment '工程师id',
    enginner_name       varchar(20)  null comment '工程师名称',
    black_white_price decimal(6,4)  null comment '黑白打印单价',
    color_price       decimal(6,4)  null comment '彩色打印单价',
    black_white_inception bigint      null comment '起始黑白打印数',
    black_white_cutoff bigint      null comment '截止黑白打印数',
    color_inception bigint      null comment '起始彩色打印数',
    color_cutoff bigint      null comment '截止彩色打印数',
    black_white_point bigint      null comment '黑白打印数量',
    color_point       bigint      null comment '彩色打印数量',
    amount           decimal(10,4)  null comment '核算金额',
    created_at        datetime    null comment '创建时间'
)
    comment '物联网-打印数量统计' collate = utf8mb4_bin;
drop table if exists b_customer_address;
create table b_customer_address
(
    id bigint not null
        primary key auto_increment,
    customer_id bigint(20) null comment '客户id',
    contact varchar(32) not null comment '联系人',
    phone varchar(32) not null comment '联系电话',
    is_default tinyint not null comment '是否默认',
    region_code INT(8) not null comment '区域编码',
    address varchar(255) not null comment '详细地址',
    created_at datetime(3) not null comment '创建时间',
    updated_at datetime(3) null comment '修改时间'
)
    comment '客户收货地址';

create index idx_bsa_cust_addr
    on b_pay_voucher (customer_id);
