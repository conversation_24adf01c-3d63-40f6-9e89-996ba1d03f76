
alter table b_iot_repair_print
    change ser_type treaty_type varchar(64) not null comment '合约类型(字典项码)';

alter table b_iot_print_count
    change ser_type treaty_type varchar(64) not null comment '合约类型(字典项码)';

alter table tb_work_order
    change ser_type treaty_type varchar(64)  null comment '合约类型(字典项码)';

update tb_work_order t,b_customer_device_group t1
set t.treaty_type = t1.treaty_type
where t.device_group_id = t1.id;

update b_iot_print_count t,b_customer_device_group t1
set t.treaty_type = t1.treaty_type
where t.device_group_id = t1.id;

update b_iot_repair_print t,b_customer_device_group t1
set t.treaty_type = t1.treaty_type
where t.device_group_id = t1.id;