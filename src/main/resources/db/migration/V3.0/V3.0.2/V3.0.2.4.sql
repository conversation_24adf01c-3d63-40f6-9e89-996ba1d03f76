create table b_iot_repair_print
(
    id                bigint auto_increment comment 'id'
        primary key,
    customer_id       bigint(20)      not null comment '客户id',
    device_group_id   bigint(20)      not null comment '设备组id',
    device_group     varchar(64)       null comment '设备组名称(字典项码)',
    brand       varchar(32) not null comment '品牌',
    machine       varchar(32) not null comment '机型',
    ser_type          varchar(2)  not null comment '服务类型1散修2全保3租赁4半保5签约6数据7否',
    data_source       varchar(32)  not null comment '数据来源WORK工单SELF自修',
    begin_date       varchar(32) not null comment '开始日期',
    end_date       varchar(32) not null comment '结束日期',
    black_white_inception bigint      null comment '起始黑白打印数',
    black_white_cutoff bigint      null comment '截止黑白打印数',
    color_inception bigint      null comment '起始彩色打印数',
    color_cutoff bigint      null comment '截止彩色打印数',
    black_white_point bigint      null comment '黑白打印数量',
    color_point       bigint      null comment '彩色打印数量',
    first_report_id       bigint(20)      not null comment '开始报告id',
    last_report_id       bigint(20)      not null comment '结束报告id',
    created_at        datetime    null comment '创建时间'
)
    comment '维修工单-打印数量统计' collate = utf8mb4_bin;


alter table b_customer_business
    add salesman_id bigint null comment '销售人员id' after businessman;

alter table b_customer_business
    add businessman_id bigint null comment '商务人员id' after businessman;

alter table b_customer_address
    add location json null comment '位置信息' after address;

alter table b_customer_address
    add precision_address varchar(64) null comment '精确地址' after address;
