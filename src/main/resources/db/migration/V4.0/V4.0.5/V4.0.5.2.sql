alter table tb_work_order
    add ticket_code varchar(32) null comment '优惠券' after item_pay;

alter table tb_work_order
    add ticket_amount varchar(32) null comment '优惠券抵扣金额' after ticket_code;

alter table tb_work_order
add point_num int null comment '积分数量' after ticket_amount;

alter table tb_work_order
    add point_deduction int null comment '积分抵扣' after point_num;

alter table tb_trade_order
    add ticket_code varchar(32) null comment '优惠券' after actual_goods_amount;

alter table tb_trade_order
    add ticket_amount varchar(32) null comment '优惠券抵扣金额' after ticket_code;

alter table tb_trade_order
    add point_num int null comment '积分数量' after discount_amount;
alter table tb_trade_order
    add point_deduction int null comment '积分抵扣' after point_num;

alter table b_customer
    add service_type varchar(32) null comment '服务类型' after type;

alter table b_customer_contract
    add is_supplement tinyint default 0 null comment '是否补录' after contract_type;
