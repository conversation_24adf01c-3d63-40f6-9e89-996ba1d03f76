alter table b_machine
    add bind_machine varchar(32) null comment '绑定机器' after source_id;

drop table if exists b_customer_device_accessory;
create table b_customer_device_accessory
(
    id           bigint         not null primary key,
    customer_id bigint not null comment '客户id',
    device_group_id bigint not null comment '设备组id',
    product_id bigint not null comment '产品树id',
    accessory_id    bigint not null comment '选配件id',
    accessory_code    varchar(32) null comment '选配件编码',
    created_at  DATETIME COMMENT '创建时间'
)
    comment '客户设备选配件表' collate = utf8mb4_bin
                          row_format = DYNAMIC;

create index idx_dad_group_id
    on b_customer_device_accessory (device_group_id);

alter table b_customer_contract_buy
    change warranty_part_type warranty_part_types json null comment '质保部件类型';

alter table b_customer_contract_buy
    drop column warranty_part_id;

