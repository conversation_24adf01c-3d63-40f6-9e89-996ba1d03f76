alter table b_customer_contract_serve
    add package_use int default 0 null comment '包量已使用' after package_number;

alter table b_customer_contract_serve
    add remark text null comment '备注条款' after status;

alter table b_customer_contract_buy
    add remark text null comment '备注条款' after status;

alter table b_customer_contract_buy
    add warranty_part_type varchar(32) null comment '质保部件类型' after warranty_repair_count;

alter table b_customer_contract_buy
    add warranty_part_id bigint null comment '质保零件id' after warranty_part_type;

