create table tb_machine_disassemble
(
    id                bigint               not null primary key,
    code              varchar(32)          not null comment '拆机单号',
    machine_num       varchar(32)          not null comment '机器编码',
    product_id        bigint               not null comment '机器id',
    host_type        varchar(32)               not null comment '设备类型',
    location          varchar(32)          null comment '储位',
    part_id          bigint(32)          null comment '零件id',
    article_code          varchar(32)          null comment '物品编码',
    number_oem          varchar(32)          null comment 'OEM编码',
    cost_price bigint     default 0 null comment '成本价',
    sale_price       bigint     default 0 null comment '售价',
    quantity int        default 0 null comment '数量',
    amount   int     default 0 null comment '金额',
    pic_urls          json                 null comment '图片',
    description       varchar(255)          null comment '备注',
    status       varchar(32)          null comment '状态',
    engineer_id        bigint               null comment '工程师id',
    audit_by        bigint               null comment '审核人',
    created_at        datetime             null comment '创建时间',
    updated_at        datetime             null comment '更新时间',
    deleted           tinyint(1) default 0 null
)
    comment '机器拆机单';