alter table b_customer_contract_give
    change give_y_powder article_code varchar(32) null comment '物品编码';

alter table b_customer_contract_give
    change sku_y_power sku_id bigint null comment '赠品skuId';

alter table b_customer_contract_give
    drop column give_m_powder;

alter table b_customer_contract_give
    add quantity int null comment '数量' after sku_id;

alter table b_customer_contract_give
    drop column sku_c_power;

alter table b_customer_contract_give
    drop column sku_m_power;

alter table b_customer_contract_give
    drop column give_c_powder;

alter table b_customer_contract_give
    drop column give_k_powder;

alter table b_customer_contract_give
    drop column sku_k_power;

alter table b_customer_contract_give
    drop column give_y_powder_num;

alter table b_customer_contract_give
    drop column give_m_powder_num;

alter table b_customer_contract_give
    drop column give_c_powder_num;

alter table b_customer_contract_give
    drop column give_k_powder_num;

alter table b_customer_contract_give
    drop column give_color_count;

alter table b_customer_contract_give
    drop column give_black_count;

alter table b_customer_contract_give
    drop column give_count;

alter table b_customer_contract_give
    drop column give_repair_count;

alter table b_customer_contract_give
    drop column give_points;

