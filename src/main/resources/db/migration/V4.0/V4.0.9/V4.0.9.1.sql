create table tb_machine_repair
(
    id                bigint               not null primary key,
    code              varchar(32)          not null comment '维修单号',
    engineer_id      bigint               not null comment '工程师ID',
    machine_num       varchar(32)          not null comment '机器编码',
    product_id        bigint               not null comment '机器id',
    host_type        varchar(32)               not null comment '设备类型',
    location          varchar(32)          null comment '储位',
    device_sequence          varchar(32)          null comment '序列号',
    black_white_counter bigint     default 0 null comment '黑白计数器',
    color_counter       bigint     default 0 null comment '彩色计数器',
    five_colour_counter int        default 0 null comment '五色计数器',
    amount   int     default 0 null comment '耗材金额',
    pic_urls          json                 null comment '图片',
    description       varchar(32)          null comment '描述',
    status       varchar(32)          null comment '状态',
    audit_by        bigint               null comment '审核人',
    created_at        datetime             null comment '创建时间',
    updated_at        datetime             null comment '更新时间',
    deleted           tinyint(1) default 0 null
)
    comment '毛机维修';

create table tb_machine_repair_replace
(
    id              bigint               not null
        primary key,
    repair_id       bigint               null comment '维修id',
    machine_num     varchar(32)          null comment '机器编码',
    item_store_id   bigint               null comment '耗材仓库ID',
    item_id         bigint               null comment '商品id',
    item_name       varchar(256)         null comment '商品名称',
    sale_sku_id     bigint               null comment 'sku id',
    sale_unit_price bigint               null comment '商品销售单价',
    sku_info        text                 null comment '商品快照',
    num             int                  null comment '数量',
    is_pm           tinyint(1)           null comment '是否是PM件',
    part_id         bigint               null comment '零件id',
    location        varchar(50)          null comment '零件位置',
    amount          bigint               null comment '金额',
    batch_code      varchar(64)          null comment '批次号',
    created_at      datetime             null comment '创建时间',
    updated_at      datetime             null comment '更新时间',
    deleted         tinyint(1) default 0 null
)
    comment '毛机维修换件明细' ;

