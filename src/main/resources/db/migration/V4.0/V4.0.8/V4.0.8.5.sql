alter table b_part_require
    add pic_urls json null comment '图片' after require_date;

create table tb_activity_situation
(
    id              bigint      not null
        primary key,
    activity_id     bigint      not null comment '活动ID',
    customer_id     bigint      not null comment '客户ID',
    customer_staff_id     bigint      not null comment '客户员工ID',
    share_num        int    default 0   null comment '分享次数',
    click_num    int    default 0   null comment '点击次数',
    collect_num    int    default 0   null comment '收藏次数',
    created_at      datetime(3) not null comment '创建时间'
)
    comment '活动参与情况表';

