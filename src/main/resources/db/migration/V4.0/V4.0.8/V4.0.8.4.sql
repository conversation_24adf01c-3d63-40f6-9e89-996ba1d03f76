alter table tb_activity_award
    modify price decimal(8, 2) null comment '单价';

alter table tb_activity_award
    modify sku_id bigint null comment 'skuID';

alter table tb_activity_award
    modify article_code varchar(32) null comment '物品编码';

alter table b_customer_contract_give
    add sku_y_power bigint null comment '赠品skuId' after give_y_powder;

alter table b_customer_contract_give
    add sku_m_power bigint null comment '赠品skuId' after give_m_powder;

alter table b_customer_contract_give
    add sku_c_power bigint null comment '赠品skuId' after give_m_powder;

alter table b_customer_contract_give
    add sku_k_power bigint null comment '赠品skuId' after give_k_powder;

create table b_part_require
(
    id              bigint            not null primary key,
    part_id      bigint       not     null comment '零件id',
    article_code    varchar(36)       null comment '物品编码',
    number_oem      varchar(64)       null comment 'oem',
    manufacturer_channel      varchar(64)       null comment '渠道',
    number          int               not null comment '数量',
    require_date          date               not null comment '需求日期',
    remark          varchar(255)          null comment '备注',
    status          varchar(32)       not null comment '状态',
    created_by      bigint            null comment '创建人',
    created_at      datetime(3)       not null comment '创建时间',
    deleted         tinyint default 0 not null comment '是否删除'
)
    comment '零件需求' row_format = DYNAMIC;

