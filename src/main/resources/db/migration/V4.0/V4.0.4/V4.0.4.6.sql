
alter table b_customer_contract
    change address address_id BIGINT null comment '地址';

alter table tb_install_order
    change address address_id BIGINT null comment '地址';

create table b_customer_ticket
(
    id                     bigint             not null
        primary key,
    code                   varchar(32)        not null comment '券编号',
    customer_id        bigint             null comment '设备组id',
    source_type            varchar(32)        null comment '来源类型',
    source_code            varchar(32)        null comment '来源单号',
    ticket_type            varchar(32)        null comment '券类型',
    expire_date           date               not null comment '过期时间',
    article_code           varchar(32)  null comment '物品类型',
    number           int  null comment '数量或金额',
    use_type           varchar(32)  null comment '使用类型',
    use_code           varchar(32)  null comment '使用单号',
    status                 varchar(20)        null comment '状态',
    created_by             bigint             null comment '创建人',
    created_at             datetime(3)        not null comment '创建时间',
    updated_at             datetime(3)        null comment '修改时间',
    deleted                tinyint  default 0 null comment '是否删除'
)
    comment '客户优惠券' collate = utf8mb4_bin
                         row_format = DYNAMIC;