alter table b_iot_print_count
    add settment_status tinyint null comment '是否结算' after last_id;

alter table b_iot_print_receipt
    add cycle_type varchar(32) null comment '周期类型' after cycle;

alter table b_iot_print_receipt
    add start_month varchar(32) null comment '开始月份' after cycle_type;

alter table b_iot_print_receipt
    add end_month varchar(32) null comment '结束月份' after start_month;

alter table b_customer_contract_buy
    add product_id bigint null comment '机型id' after machine_num;


alter table b_customer_contract
    add consignee_phone varchar(32) null comment '联系电话' after sign_name;

alter table b_customer_contract
    add consignee varchar(32) null comment '联系人' after consignee_phone;

alter table b_customer_contract
    add address varchar(32) null comment '联系地址' after consignee;


alter table tb_install_order
    add consignee_phone varchar(32) null comment '联系电话' after engineer_id;

alter table tb_install_order
    add consignee varchar(32) null comment '联系人' after consignee_phone;

alter table tb_install_order
    add address varchar(32) null comment '联系地址' after consignee;

alter table b_customer_contract_buy
    add delivery_status tinyint null comment '是否发货' after has_give;

