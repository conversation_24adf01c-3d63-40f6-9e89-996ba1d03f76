alter table tb_activity_award
    add award_name varchar(64) null comment '奖品名称' after award_type;

alter table tb_activity_award
    change min_buy max_buy int null comment '单个客户最高获得数量';

alter table tb_activity_award
    add price int null comment '单价' after article_code;

alter table tb_activity_award
    add amount int null comment '总金额' after quantity;

alter table tb_activity_sku
    add sale_unit_price int null comment '原价' after article_code;

create table tb_activity_award_grant
(
    id bigint not null  primary key,
    activity_id bigint not null comment '活动ID',
    customer_id bigint not null comment '客户ID',
    award_id bigint not null comment '奖品ID',
    award_type varchar(32) not null comment '奖品类型',
    quantity int  null comment '总数量',
    create_by bigint(20) null comment '创建人',
    created_at datetime(3) not null comment '创建时间'
)
    comment '活动奖品发放表';