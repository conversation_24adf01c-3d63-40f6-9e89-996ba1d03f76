alter table b_customer
    add share_activity bigint null comment '通过活动分享入驻' after share_customer;

drop table if exists tb_activity;
create table tb_activity
(
    id bigint not null  primary key,
    code varchar(64) null comment '活动编码',
    activity_name varchar(32) not null comment '活动名称',
    activity_type varchar(32) null comment '活动类型',
    start_time datetime not null comment '开始时间',
    end_time datetime not null comment '结束时间',
    activity_range json not null comment '活动范围',
    status varchar(32) not null comment '状态',
    description varchar(255) null comment '活动描述',
    main_pic       json COMMENT '活动主图',
    promotion_pic    json COMMENT '推广主图',
    create_by bigint(20) null comment '创建人',
    audit_by bigint(20) null comment '审核人',
    created_at datetime(3) not null comment '创建时间',
    updated_at datetime(3) null comment '修改时间',
    audit_at datetime(3) null comment '审核时间'
)
    comment '活动主表';
drop table if exists tb_activity;

create table tb_activity_sku
(
    id bigint not null  primary key,
    activity_id bigint not null comment '活动ID',
    sku_id bigint not null comment 'skuID',
    article_code varchar(32) not null comment '物品编码',
    promotion_price int not null comment '促销价格',
    min_buy int  null comment '最低购买数量',
    max_buy int  null comment '最高购买数量',
    quantity int  null comment '总数量',
    create_by bigint(20) null comment '创建人',
    created_at datetime(3) not null comment '创建时间'
)
    comment '活动商品表';
