alter table b_customer_contract
    add total_amount int null comment '总金额' after first_amount;

alter table b_customer_contract
    add give_amount int null comment '赠品金额' after total_amount;

create table tb_activity
(
    id bigint not null  primary key,
    code varchar(64) null comment '活动编码',
    activity_name varchar(32) not null comment '活动名称',
    activity_type varchar(32) null comment '活动类型',
    start_time datetime not null comment '开始时间',
    end_time datetime not null comment '结束时间',
    budget_amount int not null comment '预算金额',
    activity_range json not null comment '活动范围',
    status varchar(32) not null comment '状态',
    description varchar(255) null comment '活动描述',
    main_pic       json COMMENT '活动主图',
    promotion_pic    json COMMENT '推广主图',
    create_by bigint(20) null comment '创建人',
    audit_by bigint(20) null comment '审核人',
    created_at datetime(3) not null comment '创建时间',
    updated_at datetime(3) null comment '修改时间',
    audit_at datetime(3) null comment '审核时间'
);

create table tb_activity_award
(
    id bigint not null  primary key,
    activity_id bigint not null comment '活动ID',
    award_type varchar(32) not null comment '奖品类型',
    sku_id bigint not null comment 'skuID',
    article_code varchar(32) not null comment '物品编码',
    min_buy int  null comment '单个客户最高获得数量',
    quantity int  null comment '总数量',
    create_by bigint(20) null comment '创建人',
    created_at datetime(3) not null comment '创建时间'
)
    comment '活动奖品表';
