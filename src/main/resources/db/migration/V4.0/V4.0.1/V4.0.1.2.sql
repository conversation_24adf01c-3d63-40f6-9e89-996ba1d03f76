
alter table b_customer_device_group
    drop column statistics_black_white_counter;

alter table b_customer_device_group
    drop column statistics_colours_counter;

alter table b_customer_device_group
    drop column statistics_start_date;

alter table b_customer_device_group
    drop column statistics_operat_id;

alter table b_customer_device_group
    drop column statistics_operat_name;

alter table b_customer_device_group
    drop column sign_black_white_counter;

alter table b_customer_device_group
    drop column sign_colours_counter;

alter table b_customer_device_group
    drop column sign_five_colours_counter;

alter table b_customer_device_group
    drop column color_guarantee;

alter table b_customer_device_group
    drop column black_guarantee;

alter table b_customer_device_group
    drop column price_type;

alter table b_customer_device_group
    drop column account_mode;

alter table b_customer_device_group
    drop column black_white_price;

alter table b_customer_device_group
    drop column color_price;

alter table b_customer_device_group
    drop column five_colour_price;


#合同

alter table b_customer_contract
    add color_guarantee int default 0 null comment '彩色印量保底' after prepayment;

alter table b_customer_contract
    add black_guarantee int default 0 null comment '黑白印量保底' after color_guarantee;

alter table b_customer_contract
    add price_type varchar(32) null comment '价格类型' after black_guarantee;

alter table b_customer_contract
    add account_mode varchar(32) null comment '核算方式' after price_type;

alter table b_customer_contract
    add black_white_price decimal(6,4) default 0 null comment '黑白印量单价' after account_mode;
alter table b_customer_contract
    add color_price decimal(6,4) default 0 null comment '彩色印量单价' after black_white_price;
alter table b_customer_contract
    add five_colour_price decimal(6,4) default 0 null comment '五色印量单价' after color_price;

alter table b_customer_contract
    add sign_colours_counter decimal(6,4) default 0 null comment '签约彩色计数器' after five_colour_price;
alter table b_customer_contract
    add sign_black_whit_counter decimal(6,4) default 0 null comment '签约黑白计数器' after sign_colours_counter;
alter table b_customer_contract
    add sign_five_colours_counter decimal(6,4) default 0 null comment '签约五色计数器' after sign_black_whit_counter;

alter table tb_repair_monthly_price
    add contract_id bigint not null comment '合同id' after id;

alter table b_customer_contract
    change sign_black_whit_counter sign_black_white_counter decimal(6, 4) default 0.0000 null comment '签约黑白计数器';

alter table b_customer_contract
    modify sign_colours_counter int default 0 null comment '签约彩色计数器';

alter table b_customer_contract
    modify sign_black_white_counter int default 0 null comment '签约黑白计数器';

alter table b_customer_contract
    modify sign_five_colours_counter int default 0 null comment '签约五色计数器';
