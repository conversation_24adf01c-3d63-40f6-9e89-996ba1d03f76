alter table b_customer_contract
    modify start_time date null comment '生效日期';

alter table b_customer_contract
    modify end_time date null comment '失效时间';

alter table b_customer_device_group
    add machine_num varchar(32) null comment '机器编码' after device_sequence;

rename table b_machine_order to tb_machine_order;

alter table tb_machine_order
    add ser_type varchar(32) null comment '服务类型' after actual_amount;


alter table tb_machine_order
    add quality_start date null comment '质保开始' after actual_amount;
alter table tb_machine_order
    add quality_end date null comment '质保结束' after actual_amount;