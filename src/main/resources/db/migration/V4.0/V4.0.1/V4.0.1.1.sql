#客户信息
alter table b_customer
    add share_customer BIGINT(20) null comment '分享客户';
alter table b_customer
    add points_balance int(10) null comment '积分余额';

alter table b_iot_print_count
    add five_colour_price decimal(6,4) null comment '五色打印单价' after color_price;

alter table b_iot_print_count
    add five_colour_incption int null comment '五色起始计数器' after color_cutoff;

alter table b_iot_print_count
    add five_colour_cutoff int null comment '五色截止计数器' after five_colour_incption;

alter table b_iot_print_count
    add five_exclude int null comment '彩色废张数量' after five_colour_cutoff;

alter table b_iot_print_count
    add five_colour_point int null default 0 comment '五色印量' after five_exclude;

alter table b_iot_print_count
    add five_colour_amount decimal(10,4) null default 0 comment '五色印量金额' after black_white_amount;

alter table b_iot_print_count
    add five_adjust_inception int null default 0 comment '五色计数器起数调整值' after color_exclude;

alter table b_iot_print_count
    add five_adjust_cutoff int null default 0 comment '五色计数器止数调整值' after five_adjust_inception;

alter table b_customer_device_group
    add sign_five_colours_counter bigint null comment '五色签约计数器' after sign_colours_counter;
alter table b_customer_device_group
    add color_guarantee int null comment '彩色保底印量' after sign_five_colours_counter;
alter table b_customer_device_group
    add black_guarantee int null comment '黑白保底印量' after color_guarantee;

#
drop table if exists b_customer_points_record;
create table b_customer_points_record
(
    id         bigint                               not null comment 'id' primary key,
    customer_id       BIGINT(20)                          not null comment '客户id',
    type       tinyint(1)                          not null comment '类型0扣减1增加',
    source varchar(32)                               null comment '来源',
    business_code varchar(32)                               null comment '业务单号',
    points int(10)                               null comment '积分数',
    created_at  timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '客户积分日志';


#订单信息

alter table tb_trade_order
    add point_num int null comment '积分数量' after discount_amount;
alter table tb_trade_order
    add point_deduction int null comment '积分抵扣' after point_num;
alter table tb_work_order
    add point_num int null comment '积分数量' after discount_amount;
alter table tb_work_order
    add point_deduction int null comment '积分抵扣' after point_num;

#积分配置
drop table if exists b_customer_points_config;
create table b_customer_points_config
(
    id         bigint                               not null comment 'id' primary key,
    share_get tinyint(1)  null default 0 comment '分享获得',
    share_get_num int(6) null comment '分享获得数量',
    register_get tinyint(1) null  default 0 comment '注册获得',
    register_get_num int(6) null comment '注册获得数量',
    consume_get tinyint(1) null  default 0 comment '首次消费获得',
    consume_get_num int(6) null comment '首次消费获得数量',
    exchange_get tinyint(1) null  default 0 comment '消费兑换获得',
    exchange_scale int(6) null comment '消费兑换比例',
    order_use  tinyint(1) null comment '开启订单使用',
    order_use_condition  varchar(255) null comment '订单使用条件',
    order_upper_limit int(6) null comment '订单使用上限',
    repair_use  tinyint(1) null comment '开启工单使用',
    repair_use_condition varchar(255) null comment '工单使用条件',
    repair_upper_limit int(6) null comment '工单使用上限',
    deduction_scale int(6) null comment '抵扣比例',
    created_at  timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    updated_by    BIGINT(20) COMMENT '更新人'

) comment '积分配置';