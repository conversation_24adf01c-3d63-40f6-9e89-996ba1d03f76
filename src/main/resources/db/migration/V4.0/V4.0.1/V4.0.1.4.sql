
drop table if exists b_machine_purchase;

create table b_machine_purchase
(
    id              bigint            not null
        primary key,
    purchase_code   varchar(20)       null comment '采购单号',
    manufacturer_id varchar(20)       null comment '供应商',
    number          int               null comment '采购数量',
    amount          int               null comment '采购金额',
    receive_number  int               null comment '收货数量',
    receive_date    date              null comment '收货时间',
    status          varchar(20)       null comment '订单状态',
    pay_status      varchar(20)       null comment '付款状态',
    pay_time        datetime          null comment '付款时间',
    created_by      bigint            null comment '创建人',
    created_at      datetime(3)       not null comment '创建时间',
    updated_at      datetime(3)       null comment '修改时间',
    updated_by      bigint            null comment '修改人',
    deleted         tinyint default 0 null comment '是否删除'
)
    comment '机器采购' collate = utf8mb4_bin
                       row_format = DYNAMIC;
drop table if exists b_machine_purchase_detail;

create table b_machine_purchase_detail
(
    id            bigint            not null
        primary key,
    purchase_code varchar(20)       null comment '采购单号',
    host_type     varchar(20)       null comment '主机类型',
    product_id    bigint(20)        null comment '产品ID',
    machine_num   varchar(20)       not null comment '机器编码',
    status        varchar(20)       null comment '状态',
    created_by    bigint            null comment '创建人',
    created_at    datetime(3)       not null comment '创建时间',
    deleted       tinyint default 0 null comment '是否删除'
)
    comment '机器采购明细' collate = utf8mb4_bin
                           row_format = DYNAMIC;
drop table if exists b_machine;
create table b_machine
(
    id                  bigint      not null primary key,
    machine_num         varchar(20) not null comment '机器编码',
    host_type           varchar(20) null comment '主机类型',
    source              varchar(20) null comment '来源',
    source_code         varchar(20) null comment '采购单号',
    product_id          bigint(20)  null comment '产品ID',
    tag_name            varchar(32) null comment '标签型号',
    device_sequence     varchar(32) null comment '机器序列号',
    device_on           varchar(32) null comment '设备新旧',
    place_origin        varchar(32) null comment '产地',
    electric            varchar(20) null comment '电源(V)',
    black_white_counter bigint      null comment '黑白计数器',
    color_counter       bigint      null comment '彩色计数器',
    five_colour_counter bigint      null comment '五色计数器',
    intention_amount    bigint      null comment '意向金',
    total_amount        bigint      null comment '总金额',
    is_sale             tinyint     not null default 0 comment '是否上架',
    is_installment      tinyint     not null default 1 comment '是否支持分期',
    installment         json      null comment '支持分期数',
    pics_url            json        null comment '图片',
    status              varchar(20) null comment '状态',
    created_by          bigint      null comment '创建人',
    created_at          datetime(3) not null comment '创建时间',
    updated_at          datetime(3) null comment '修改时间',
    updated_by          bigint      null comment '修改人',
    deleted             tinyint              default 0 null comment '是否删除'
)
    comment '机器信息' collate = utf8mb4_bin
                       row_format = DYNAMIC;
drop table if exists b_machine_order;

create table b_machine_order
(
    id                     bigint            not null
        primary key,
    order_num              varchar(20)       not null comment '订单号',
    customer_id            bigint(20)        not null comment '客户ID',
    buyer_remark           varchar(255)      null comment '买家备注',
    consignee_name         varchar(20)       null comment '收货人姓名',
    consignee_phone        varchar(11)       null comment '收货人手机号',
    consignee_region_code  int(8)            null comment '收货地址(最终地区编码)',
    consignee_address      varchar(255)      null comment '详细收货地址',
    consignee_full_address varchar(255)      null comment '收货地址',
    location               json              null comment '定位',
    total_amount           bigint            null comment '总额',
    intention_amount       bigint            null comment '意向金',
    discount_amount        bigint            null comment '优惠总额',
    shipping_fee           bigint            null comment '运费',
    actual_amount          bigint            null comment '实际总额',
    settle_method              varchar(10)          null comment '结算方式',
    pay_mode              varchar(10)          null comment '支付方式',
    pay_status              varchar(10)          null comment '支付状态',
    pay_time          datetime       null comment '支付时间',
    pay_expire_time         datetime      null comment '支付过期时间',
    delivery_time          datetime          null comment '发货时间',
    receive_time           datetime          null comment '收货时间',
    install_time           datetime          null comment '安装时间',
    status                 varchar(20)       null comment '订单状态',
    is_exchange            tinyint           null comment '是否退换货',
    close_reason            tinyint           null comment '退换货原因',
    created_by             bigint            null comment '创建人',
    created_at             datetime(3)       not null comment '创建时间',
    updated_at             datetime(3)       null comment '修改时间',
    updated_by             bigint            null comment '修改人',
    deleted                tinyint default 0 null comment '是否删除'
)
    comment '机器销售单' collate = utf8mb4_bin
                         row_format = DYNAMIC;


drop table if exists tb_machine_order_detail;

create table tb_machine_order_detail
(
    id                bigint            not null
        primary key,
    order_num         varchar(20)       not null comment '订单号',
    machine_num   int(11)           not null comment '机器编码',
    total_amount           bigint            null comment '总额',
    intention_amount       bigint            null comment '意向金',
    discount_amount        bigint            null comment '优惠总额',
    shipping_fee           bigint            null comment '运费',
    actual_amount          bigint            null comment '实际总额',
    is_installment      tinyint     not null default 1 comment '是否支持分期',
    installment         json        null comment '支持分期数',
    contract_type          varchar(20)              null comment '合约类型',
    status            varchar(20)       null comment '状态',
    created_by          bigint      null comment '创建人',
    created_at          datetime(3) not null comment '创建时间',
    updated_at          datetime(3) null comment '修改时间',
    updated_by          bigint      null comment '修改人',
    deleted           tinyint default 0 null comment '是否删除'
)
    comment '机器销售单明细' collate = utf8mb4_bin
                             row_format = DYNAMIC;
drop table if exists tb_machine_order_installment;

create table tb_machine_order_installment
(
    id                bigint            not null
        primary key,
    order_num         varchar(20)       not null comment '订单号',
    installment_num   int(11)           not null comment '分期数量',
    installment_index int(11)           not null comment '期数',
    amount int(11)           not null comment '金额',
    plan_pay_date          date              null comment '应付日期',
    actual_pay_time          datetime              null comment '实付时间',
    status            varchar(20)       null comment '状态',
    created_by          bigint      null comment '创建人',
    created_at          datetime(3) not null comment '创建时间',
    updated_at          datetime(3) null comment '修改时间',
    updated_by          bigint      null comment '修改人',
    deleted           tinyint default 0 null comment '是否删除'
)
    comment '机器销售单分期计划' collate = utf8mb4_bin
                                 row_format = DYNAMIC;

