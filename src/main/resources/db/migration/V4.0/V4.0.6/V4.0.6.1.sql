
alter table b_take_detail
    add host_type varchar(32) null comment '主机类型' after act_location;

alter table b_take_detail
    add origin_code varchar(32) null comment '源机器编码' after host_type;

alter table b_take_detail
    add device_on varchar(32) null comment '新旧' after origin_code;

alter table b_take_detail
    add tag_name varchar(32) null comment '机器标签' after device_on;

alter table b_take_detail
    add machine_status varchar(32) null comment '机器状态' after tag_name;

update b_take_detail t ,b_machine t1
set t.article_name = t1.product_name,
    t.host_type = t1.host_type,
    t.origin_code = t1.origin_code,
    t.device_on = t1.device_on,
    t.tag_name = t1.tag_name,
    t.machine_status = t1.status
where t.article_code = t1.machine_num;

