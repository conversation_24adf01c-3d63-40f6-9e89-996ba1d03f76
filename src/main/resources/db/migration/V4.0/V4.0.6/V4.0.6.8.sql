alter table tb_item_store
    drop column price_info;
update tb_item_store set batch_codes=null where batch_codes is not null;
alter table tb_item_store
    change batch_codes batch_code varchar(64) null comment '批次号';

alter table tb_item_store_log
    add batch_code varchar(64) null comment '批次号' after operate_code;

alter table tb_replace_detail
    drop column price_info;

update tb_replace_detail set batch_codes=null where batch_codes is not null;

alter table tb_replace_detail
    change batch_codes batch_code varchar(64) null comment '批次号';

alter table tb_self_repair_report_replace_detail
    drop column price_info;

update tb_self_repair_report_replace_detail set batch_codes=null where batch_codes is not null;
alter table tb_self_repair_report_replace_detail
    change batch_codes batch_code varchar(64) null comment '批次号';

