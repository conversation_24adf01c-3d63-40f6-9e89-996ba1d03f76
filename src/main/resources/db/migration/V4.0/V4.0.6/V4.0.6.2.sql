alter table b_take_detail
    add full_id_path varchar(128) null comment '产品完整路径' after product_id;

alter table b_take_detail
    add device_status varchar(32) null comment '设备状态' after tag_name;

update b_take_detail t ,b_machine t1
set t.article_name = t1.product_name,
    t.host_type = t1.host_type,
    t.origin_code = t1.origin_code,
    t.device_on = t1.device_on,
    t.device_status = t1.device_status,
    t.tag_name = t1.tag_name,
    t.machine_status = t1.status
where t.article_code = t1.machine_num;
