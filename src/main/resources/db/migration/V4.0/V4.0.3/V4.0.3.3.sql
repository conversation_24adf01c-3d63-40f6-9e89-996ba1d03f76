alter table b_customer_contract
    add init_black_white_counter int default 0 null comment '初始黑白计数器' after sign_five_colours_counter;

alter table b_customer_contract
    add init_colour_counter int default 0 null comment '初始彩色计数器' after init_black_white_counter;

alter table b_customer_contract
    add init_five_colour_counter int default 0 null comment '初始五色计数器' after init_colour_counter;

alter table b_customer_device_group
    add five_colour_counter int default 0 null comment '初始五色计数器' after color_counter;

alter table b_customer_contract
    add begin_count_date date null comment '计数开始时间' after end_time;

alter table b_customer_contract
    add paper_type varchar(32) null comment '纸张类型' after contract_type;
