
drop table if exists b_customer_contract;
create table b_customer_contract
(
    id            bigint            not null
        primary key,
    code          varchar(32)       null comment '合同编号',
    customer_id   bigint            null comment '客户id',
    contract_name varchar(64)       null comment '合同名称',
    contract_type varchar(32)       null comment '合同类型',
    sign_time     datetime(3)       null comment '签定时间',
    start_time    date              null comment '生效日期',
    end_time      date              null comment '结束日期',
    attachments   json              null comment '附件',
    remark        varchar(255)      null comment '摘要',
    prepayment    bigint            null comment '预付费用',
    agent_id      bigint            null comment '经办人id',
    agent_name    varchar(20)       null comment '经办人姓名',
    sign_id       bigint            null comment '签约人id',
    sign_name     varchar(20)       null comment '签约人姓名',
    status        varchar(20)       null comment '状态',
    settle_status varchar(20)       null comment '结算状态',
    created_by    bigint            null comment '创建人',
    created_at    datetime(3)       not null comment '创建时间',
    updated_at    datetime(3)       null comment '修改时间',
    deleted       tinyint default 0 null comment '是否删除'
)
    comment '客户合同主表' collate = utf8mb4_bin
                           row_format = DYNAMIC;

create index b_customer_contract_cust
    on b_customer_contract (customer_id);

create table b_customer_contract_buy
(
    id                     bigint             not null
        primary key,
    code                   varchar(32)        not null comment '项目编号',
    contract_code          varchar(32)        not null comment '合同编号',
    device_group_id        bigint             null comment '设备组id',
    machine_num            varchar(32)        null comment '机器编码',
    auto_renewal           tinyint  default 0 null comment '自动续约',
    settle_method          varchar(32)        not null comment '结算方式',
    treaty_type            varchar(32)        not null comment '服务类型',
    install_date           date               not null comment '安装日期',
    guarantee_type         varchar(32)        null comment '质保类型',
    guarantee_count        varchar(32)        null comment '质保印量',
    guarantee_repair_count smallint default 0 null comment '质保维修次数',
    guarantee_expire_date  date               null comment '质保到期时间',
    init_black_white_counter int           default 0      null comment '初始黑白印量',
    init_color_counter     int           default 0      null comment '初始彩色印量',
    init_five_counter      int           default 0      null comment '初始五色印量',
    settle_status varchar(20)       null comment '结算状态',
    status                 varchar(20)        null comment '状态',
    created_by             bigint             null comment '创建人',
    created_at             datetime(3)        not null comment '创建时间',
    updated_at             datetime(3)        null comment '修改时间',
    deleted                tinyint  default 0 null comment '是否删除'
)
    comment '客户购机合同' collate = utf8mb4_bin
                           row_format = DYNAMIC;

create table b_customer_contract_serve
(
    id                        bigint                       not null
        primary key,
    code                      varchar(32)                  null comment '编号',
    contract_code          varchar(32)        not null comment '合同编号',
    device_group_id           bigint                       null comment '设备组id',
    machine_num               varchar(32)                  null comment '机器编码',
    cycle_type                varchar(32)                  null comment '付款周期',
    statement_date            varchar(32)                  null comment '账单日',
    pay_expire_date             varchar(32)                           null comment '付款截止日',
    force_stop           tinyint  default 0 null comment '强制停保',
    sign_colours_counter      int           default 0      null comment '签约彩色计数器',
    sign_black_white_counter  int           default 0      null comment '签约黑白计数器',
    sign_five_colours_counter int           default 0      null comment '签约五色计数器',
    waste_type                varchar(32)                  null comment '废张类型',
    color_waste_number        int                          null comment '彩色废张数量',
    bw_waste_number           int                          null comment '黑白废张数量',
    five_waste_number         int                          null comment '五色废张数量',
    color_waste_scale         int                          null comment '彩色废张比例',
    bw_waste_scale            int                          null comment '黑白废张比例',
    five_waste_scale          int                          null comment '五色废张比例',
    guarantee_type           varchar(32)                  null comment '保底类型',
    color_guarantee_count           int           default 0      null comment '彩色印量保底',
    black_guarantee_count           int           default 0      null comment '黑白印量保底',
    guarantee_amount           int           default 0      null comment '金额保底',
    guarantee_count           int           default 0      null comment '总印量保底',
    give_type                 varchar(32)                  null comment '赠送类型',
    give_y_powder                 varchar(32)                  null comment '赠送黄粉类型',
    give_m_powder                 varchar(32)                  null comment '赠送品红粉类型',
    give_c_powder                 varchar(32)                  null comment '赠送青色粉类型',
    give_k_powder                 varchar(32)                  null comment '赠送黑色粉类型',
    give_y_powder_num               int                  null comment '赠送黄粉数量',
    give_m_powder_num             int                  null comment '赠送品红粉数量',
    give_c_powder_num              int              null comment '赠送青色粉数量',
    give_k_powder_num             int                 null comment '赠送黑色粉数量',
    give_color_count           int           default 0      null comment '赠彩色印量',
    give_black_count           int           default 0      null comment '赠黑白印量',
    give_count           int           default 0      null comment '赠总印量',
    give_expire_date           date              null comment '截止日期',
    give_repair_count           date              null comment '质保维修次数',
    give_points           date              null comment '赠送积分',
    begin_count_date          date                         null comment '计数开始时间',
    price_type                varchar(32)                  null comment '价格类型',
    account_mode              varchar(32)                  null comment '核算方式',
    paper_type                varchar(32)                  null comment '纸张类型',
    black_white_price         decimal(6, 4) default 0.0000 null comment '黑白印量单价',
    color_price               decimal(6, 4) default 0.0000 null comment '彩色印量单价',
    five_colour_price         decimal(6, 4) default 0.0000 null comment '五色印量单价',
    merge_type                varchar(32)                  null comment '合并类型',

    status                    varchar(20)                  null comment '状态',
    created_by                bigint                       null comment '创建人',
    created_at                datetime(3)                  not null comment '创建时间',
    updated_at                datetime(3)                  null comment '修改时间',
    deleted                   tinyint       default 0      null comment '是否删除'
)
    comment '客户服务合同' collate = utf8mb4_bin
                           row_format = DYNAMIC;


alter table tb_trade_order_detail
    drop column machine_num;

alter table tb_trade_order_detail
    drop column machine_name;

alter table tb_trade_order_detail
    drop column product_id;


alter table tb_trade_order_detail
    drop column host_type;

alter table tb_trade_order_detail
    drop column device_on;

alter table tb_trade_order_detail
    drop column percentage;

alter table tb_trade_order_detail
    drop column full_amount;

alter table tb_trade_order_detail
    drop column arrears_amount;


alter table tb_trade_order_detail
    drop column delivery_date;



alter table tb_trade_order
    drop column full_amount;


alter table tb_trade_order
    drop column arrears_amount;

alter table tb_trade_order
    drop column install_amount;

alter table tb_trade_order
    drop column freight;

alter table tb_trade_order
    drop column free_shipping;

alter table tb_trade_order
    drop column settle_status;

alter table tb_trade_order
    drop column settle_method;


alter table tb_trade_order
    drop column installment_num;

alter table tb_trade_order
    drop column install_date;

alter table tb_trade_order
    drop column treaty_type;

alter table tb_trade_order
    drop column contract_date;


alter table tb_trade_order
    drop column guarantee_date;



alter table tb_trade_order
    drop column guarantee_count;


alter table tb_trade_order
    drop column guarantee_repair_count;



alter table tb_trade_order
    drop column black_white_price;

alter table tb_trade_order
    drop column color_price;

alter table tb_trade_order
    drop column five_colour_price;

alter table tb_trade_order
    drop column price_type;


alter table tb_trade_order
    drop column sales_man;





