alter table tb_repair_monthly_price
    change contract_id contract_code varchar(32) not null comment '合同编码';

alter table tb_repair_monthly_price
    change order_num item_code varchar(20) null comment '合同明细编码';

alter table tb_trade_order_installment
    change trade_order_id contract_code varchar(32) not null comment '合同编号';

alter table tb_trade_order_installment
    change order_num item_code varchar(32) null comment '子合同编号';

alter table b_iot_print_count
    change contract_id contract_item_code varchar(32) null comment '合同明细编码';

alter table tb_install_order
    change order_num contract_code varchar(32) null comment '合同编码';

alter table tb_install_order
    change order_detail_id contract_item_code varchar(32) null comment '合同明细编码';

alter table b_customer_contract_buy
    add has_give tinyint(1) null comment '有无赠送' after init_five_counter;

alter table b_customer_contract_serve
    change give_type has_give tinyint default 0 null comment '有无赠送';


alter table b_customer_contract_serve
    add auto_renewal tinyint default 0 null comment '自动续约' after force_stop;



create table b_customer_contract_give
(
    id                     bigint             not null
        primary key,
    code                   varchar(32)        not null comment '编号',
    contract_code          varchar(32)        not null comment '合同编号',
    customer_id          bigint(20)        not null comment '客户ID',
    item_code          varchar(20)        not null comment '项目编号',
    item_type          varchar(20)        not null comment '项目类型buy或serve',
    give_type                 varchar(32)                  null comment '赠送类型',
    give_y_powder                 varchar(32)                  null comment '赠送黄粉类型',
    give_m_powder                 varchar(32)                  null comment '赠送品红粉类型',
    give_c_powder                 varchar(32)                  null comment '赠送青色粉类型',
    give_k_powder                 varchar(32)                  null comment '赠送黑色粉类型',
    give_y_powder_num               int                  null comment '赠送黄粉数量',
    give_m_powder_num             int                  null comment '赠送品红粉数量',
    give_c_powder_num              int              null comment '赠送青色粉数量',
    give_k_powder_num             int                 null comment '赠送黑色粉数量',
    give_color_count           int           default 0      null comment '赠彩色印量',
    give_black_count           int           default 0      null comment '赠黑白印量',
    give_count           int           default 0      null comment '赠总印量',
    give_repair_count           date              null comment '质保维修次数',
    give_points           date              null comment '赠送积分',
    give_expire_date           date              null comment '截止日期',
    status                 varchar(20)        null comment '状态',
    created_by             bigint             null comment '创建人',
    created_at             datetime(3)        not null comment '创建时间',
    updated_at             datetime(3)        null comment '修改时间',
    deleted                tinyint  default 0 null comment '是否删除'
)
    comment '客户合同赠送项' collate = utf8mb4_bin
                           row_format = DYNAMIC;

alter table b_customer_contract_buy
    add give_type varchar(32) null comment '赠送类型' after guarantee_expire_date;

alter table b_customer_contract_serve
    drop column give_y_powder;

alter table b_customer_contract_serve
    drop column give_m_powder;

alter table b_customer_contract_serve
    drop column give_c_powder;

alter table b_customer_contract_serve
    drop column give_k_powder;

alter table b_customer_contract_serve
    drop column give_y_powder_num;

alter table b_customer_contract_serve
    drop column give_m_powder_num;

alter table b_customer_contract_serve
    drop column give_c_powder_num;

alter table b_customer_contract_serve
    drop column give_k_powder_num;

alter table b_customer_contract_serve
    drop column give_color_count;

alter table b_customer_contract_serve
    drop column give_black_count;

alter table b_customer_contract_serve
    drop column give_count;

alter table b_customer_contract_serve
    drop column give_expire_date;

alter table b_customer_contract_serve
    drop column give_repair_count;

alter table b_customer_contract_serve
    drop column give_points;

create table b_customer_contract_merge
(
    id                     bigint             not null
        primary key,
    contract_code          varchar(32)        not null comment '合同编号',
    customer_id          bigint(20)        not null comment '客户ID',
    device_group_id       json       not null comment '设备组编码',
    item_code          varchar(20)        not null comment '项目编号',
    merge_type                 varchar(32)                  null comment '合并类型',
    created_at             datetime(3)        not null comment '创建时间',
    deleted                tinyint  default 0 null comment '是否删除'
)
    comment '客户合同合并计算' collate = utf8mb4_bin
                             row_format = DYNAMIC;
alter table b_customer_contract_buy
    add force_stop tinyint null comment '强制停保' after treaty_type;

alter table b_customer_contract_buy
    add install_amount int default 0 null comment '安装费' after install_date;

alter table b_customer_contract_buy
    add free_shipping tinyint default 0 null comment '是否包邮' after install_amount;



