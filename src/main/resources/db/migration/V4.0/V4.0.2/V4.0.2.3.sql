alter table tb_trade_order
    add install_date date null comment '安装日期' after settle_method;

alter table tb_trade_order
    add guarantee_date date null comment '质保期截止日期' after install_date;

alter table tb_trade_order
    add guarantee_count int null comment '质报印量' after guarantee_date;

alter table tb_trade_order
    add guarantee_repair_count  smallint null default 0  comment '质报维修次数' after guarantee_count;

alter table tb_trade_order
    add treaty_type varchar(32) null comment '服务类型' after guarantee_count;

alter table tb_trade_order
    add contract_date date null comment '服务截止时间' after treaty_type;


alter table tb_trade_order_detail
    add delivery_date date null comment '发货时间' after pay_time;


alter table tb_trade_order_installment
    add customer_id BIGINT null comment '客户id' after trade_order_id;

#抄表减免上传凭证
alter table b_iot_print_receipt
    add pics_url json null comment '减免上传凭证' after color_print;

#质保
alter table b_customer_contract
    add guarantee_count varchar(32) null comment '质保印量' after contract_type;

alter table b_customer_contract
    add guarantee_repair_count smallint null default 0 comment '质保维修次数' after guarantee_count;


alter table b_customer_contract
    add device_group_id bigint(20) null comment '设备组id' after customer_id;

alter table b_customer_contract
    add order_num varchar(32) null comment '订单号' after device_group_id;

alter table b_customer_contract
    add order_detail_id bigint null comment '订单明细id' after order_num;

#废张计算
alter table b_customer_contract
    add waste_type varchar(32) null comment '废张类型' after sign_five_colours_counter;

alter table b_customer_contract
    add color_waste_number int null comment '彩色废张数量' after waste_type;

alter table b_customer_contract
    add bw_waste_number int null comment '黑白废张数量' after color_waste_number;

alter table b_customer_contract
    add color_waste_scale int null comment '彩色废张比例' after bw_waste_number;

alter table b_customer_contract
    add bw_waste_scale int null comment '黑白废张比例' after color_waste_scale;


