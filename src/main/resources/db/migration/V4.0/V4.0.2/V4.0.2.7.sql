drop table if exists tb_install_order;
create table tb_install_order
(
    id                      bigint               not null comment 'id'
        primary key,
    code                    varchar(50)          null comment '编码',
    customer_id             bigint               null comment '客户ID',
    order_num              varchar(32)               null comment '订单号',
    order_detail_id           bigint            null comment '订单明细id',
    product_id         bigint               null comment '机器型号id',
    machine_num         varchar(32)               null comment '机器编号',
    treaty_type         varchar(32)               null comment '合约类型',
    status                  varchar(50)          null comment '工单状态',
    engineer_id             bigint               null comment '工程师ID',
    expect_install_time      date             null comment '期望安装时间',
    departure_time          datetime             null comment '工程师出发时间',
    disassembly_pics                json                 null comment '安装图片',
    disassembly_desc                varchar(255)         null comment '备注描述',
    installed_time        datetime             null comment '安装完成时间',
    confirm_time     varchar(32)          null comment '确认时间',
    completed_at            datetime             null comment '工单完成时间',
    free_shipping           tinyint     default 0 null comment '是否包邮',
    freight           bigint     default 0 null comment '运费',
    install_amount           bigint     default 0 null comment '安装费',
    discount_amount         int        default 0 null comment '折扣费用',
    additional_pay          int        default 0 null comment '追加费用',
    derate_amount           bigint     default 0 null comment '减免',
    total_amount               int        default 0 null comment '总支付',
    pay_mode                varchar(32)          null comment '支付方式0在线支付1对公支付',
    black_white_count       bigint               null comment '黑白计数器',
    color_count             bigint               null comment '彩色计数器',
    five_colours_counter             bigint               null comment '五色计数器',
    created_at              datetime             null comment '创建时间',
    updated_at              datetime             null comment '更新时间',
    updated_by              bigint             null comment '修改人',
    deleted                 tinyint(1) default 0 null
)
    comment '装机工单表' collate = utf8mb4_bin
                         row_format = DYNAMIC;