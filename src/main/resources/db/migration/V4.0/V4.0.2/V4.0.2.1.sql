alter table tb_trade_order
    add settle_status tinyint null default 0 comment '是否结算完成' after pay_mode;

alter table tb_trade_order
    add settle_method varchar(32) null comment '结算方式' after settle_status;

alter table tb_trade_order
    add free_shipping tinyint null default 0 comment '是否包邮' after settle_method;

alter table tb_trade_order
    add install_amount int null default 0 comment '安装费' after actual_goods_amount;

alter table tb_trade_order_detail
    add machine_num varchar(32) null comment '机器编码' after item_num;
alter table tb_trade_order_detail
    add machine_name varchar(32) null comment '机器名称' after machine_num;
alter table tb_trade_order_detail
    add product_id BIGINT(20) null comment '机器型号' after machine_name;

alter table tb_trade_order
    add sales_man bigint(20) null comment '销售人员' after is_mechine;


rename table tb_machine_order_installment to tb_trade_order_installment;

alter table tb_trade_order_installment
    change order_num trade_order_id bigint not null comment '订单id';

drop table tb_machine_order;
drop table tb_machine_order_detail;

alter table tb_trade_order_detail
    modify item_id bigint null comment '商品id';

alter table tb_trade_order_detail
    modify sale_sku_id bigint null comment '销售商品ID';

alter table tb_trade_order_detail
    modify inv_sku_id bigint null comment '库存SKU id';

alter table tb_trade_order_detail
    modify sale_sku_info json null comment '记录下单时的sku关键信息';

