CREATE TABLE `b_customer_call_record` (
      `id` bigint(20) NOT NULL COMMENT '主健',
      `call_type` varchar(255) DEFAULT NULL COMMENT '拜访方式1到店2电话',
      `call_goal` varchar(255) DEFAULT NULL COMMENT '拜访目的:1维修2销售3回访',
      `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
      `reach_shop_time` datetime DEFAULT NULL COMMENT '到店时间',
      `reach_shop_name` varchar(255) DEFAULT NULL COMMENT '店员名字',
      `reach_shop_tel` varchar(255) DEFAULT NULL COMMENT '店员电话',
      `reach_shop_role` varchar(64) DEFAULT NULL COMMENT '店员角色',
      `operat_id` bigint(11) DEFAULT NULL COMMENT '员工ID',
      `operat_name` varchar(255) DEFAULT NULL COMMENT '员工名字',
      `operat_role_name` varchar(500) DEFAULT NULL COMMENT '拜访人的角色',
      `deleted` tinyint(1) DEFAULT NULL COMMENT '是否删除',
      `remark` text COMMENT '拜访备注说明',
      `next_notice_remark` text COMMENT '下次注意事项',
      `call_imgs` json DEFAULT NULL COMMENT '拜访图片备注',
      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
      `opt_user_name` varchar(255) DEFAULT NULL COMMENT '登记人员',
      `call_state` varchar(255) DEFAULT NULL COMMENT '跟进状态1需要电话2需要上门3继续微信4暂缓跟进5放弃',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户的拜访记录';
CREATE TABLE `b_customer_page_view_event` (
      `id` bigint(20) NOT NULL COMMENT '主健',
      `event_source` varchar(255) DEFAULT NULL COMMENT '事件来源（首页,知识库）',
      `event_type` varchar(255) DEFAULT NULL COMMENT '事件類型（）',
      `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
      `staff_id` bigint(20) NOT NULL COMMENT '客户员工id',
      `path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '页面路径',
      `origin_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '源页面参数',
      `started_at` datetime(3) NOT NULL COMMENT '开始时间',
      `finished_at` datetime(3) DEFAULT NULL COMMENT '结束时间',
      `duration` bigint(20) DEFAULT NULL COMMENT '持续时长(秒)',
      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='具体事件结果统计';
CREATE TABLE `b_customer_search_out_log` (
     `id` bigint(20) NOT NULL COMMENT '主健',
     `event_source` varchar(255) DEFAULT NULL COMMENT '事件来源（首页,知识库）',
     `event_type` varchar(255) DEFAULT NULL COMMENT '事件類型（）',
     `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
     `origin_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '源页面参数',
     `total` int(11) DEFAULT NULL COMMENT '返回结果数',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户进行搜索具体事件结果';
CREATE TABLE `b_customer_tag_properties` (
     `id` bigint(20) NOT NULL COMMENT 'id',
     `customer_id` bigint(20) DEFAULT NULL COMMENT '客户id',
     `customer_cost` tinyint(1) DEFAULT NULL COMMENT '客户价值:1高端2中端3低端',
     `turnover` decimal(10,2) DEFAULT NULL COMMENT '营业额',
     `month_black_white_num` int(11) DEFAULT NULL COMMENT '月黑白用量',
     `month_colours_num` int(11) DEFAULT NULL COMMENT '月彩色用量',
     `real_machine_num` int(11) DEFAULT NULL COMMENT '真实机器数',
     `peer_process_num` int(11) DEFAULT NULL COMMENT '同行加工人数',
     `picture_ratio` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图文占比',
     `channel_ability` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道能力',
     `consumable_competitor` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '耗材竞品',
     `consumable_competitor_money` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '耗材竞品价格',
     `ser_competitor` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务竞品',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `update_time` datetime DEFAULT NULL COMMENT '修改时间',
     PRIMARY KEY (`id`) USING BTREE,
     KEY `customer_id` (`customer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='客户标签属性';

-- 添加字段 b_customer
ALTER TABLE b_customer  ADD COLUMN shop_size decimal(10,2) DEFAULT null comment '店铺大小';
ALTER TABLE b_customer  ADD COLUMN shop_person_num int DEFAULT null comment '店铺人数';
ALTER TABLE b_customer  ADD COLUMN legal_native_place decimal(10,2) DEFAULT null comment '法人籍贯1川籍2湖南3外地';
ALTER TABLE b_customer  ADD COLUMN business_scope varchar(500) DEFAULT null comment '业务范围';

-- 添加字段 b_customer_business
ALTER TABLE b_customer_business  ADD COLUMN bill_email varchar(255) DEFAULT null comment '售票邮箱';
ALTER TABLE b_customer_business  ADD COLUMN reg_plat_time datetime DEFAULT null comment '入住平台时间';
ALTER TABLE b_customer_business  ADD COLUMN finance_account tinyint(1) DEFAULT null comment '财务核算1单店私账支付2多店私账支付3单店对公支付4多店对公支付';
ALTER TABLE b_customer_business  ADD COLUMN settle_method tinyint(1) DEFAULT null comment '结算方式1私转现结2公转现接3私转月付4公转月付5预充抵扣';
ALTER TABLE b_customer_business  ADD COLUMN open_bill_type tinyint(1) DEFAULT null comment '开票类型1增值税普通发票2增值税专用发票默认1';

-- 添加字段 b_customer_device_group
ALTER TABLE b_customer_device_group  ADD COLUMN reg_cli_state varchar(255) DEFAULT null comment '是否安装客户端1是0否';
ALTER TABLE b_customer_device_group  ADD COLUMN data_show_state tinyint(1) DEFAULT '1' COMMENT '移动端是否可见1可见0不可见';
ALTER TABLE b_customer_device_group  ADD COLUMN `operat_id` bigint(20) DEFAULT NULL COMMENT '工程师维修员工ID';
ALTER TABLE b_customer_device_group  ADD COLUMN `operat_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '工程师维修员工姓名';
ALTER TABLE b_customer_device_group  ADD COLUMN `ser_type` varchar(2) COLLATE utf8mb4_bin DEFAULT '1' COMMENT '服务类型1散修2全保3租赁4半保5签约6数据7否';

ALTER TABLE b_customer_device_group  ADD COLUMN `statistics_black_white_counter` bigint(20) DEFAULT NULL COMMENT '统计启始黑白计数器';
ALTER TABLE b_customer_device_group  ADD COLUMN `statistics_colours_counter` bigint(20) DEFAULT NULL COMMENT '统计启始彩色计数器';
ALTER TABLE b_customer_device_group  ADD COLUMN `statistics_start_date` date DEFAULT NULL COMMENT '统计开始时间';
ALTER TABLE b_customer_device_group  ADD COLUMN `statistics_operat_id` bigint(20) DEFAULT NULL COMMENT '统计操作员ID';
ALTER TABLE b_customer_device_group  ADD COLUMN `statistics_operat_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '统计操作员名称';

ALTER TABLE b_customer_device_group  ADD COLUMN `sign_black_white_counter` bigint(20) DEFAULT NULL COMMENT '签约黑白计数器';
ALTER TABLE b_customer_device_group  ADD COLUMN `sign_colours_counter` bigint(20) DEFAULT NULL COMMENT '签约彩色计数器';
ALTER TABLE b_customer_device_group  ADD COLUMN `sign_operat_id` bigint(20) DEFAULT NULL COMMENT '签约操作人ID';
ALTER TABLE b_customer_device_group  ADD COLUMN `sign_operat_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '签约操作人员名称';
ALTER TABLE b_customer_device_group  ADD COLUMN `contract_file` json DEFAULT NULL COMMENT '合同JSON';
ALTER TABLE b_customer_device_group  ADD COLUMN `sign_date` date DEFAULT NULL COMMENT '签约时间';

-- 添加字段 b_customer_staff
ALTER TABLE b_customer_staff  ADD COLUMN `vx_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信ID';
ALTER TABLE b_customer_staff  ADD COLUMN `vx_nike_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '昵称';
ALTER TABLE b_customer_staff  ADD COLUMN `repair_power` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '维修能力';
ALTER TABLE b_customer_staff  ADD COLUMN `score_num` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '评分数';
ALTER TABLE b_customer_staff  ADD COLUMN `vx_group_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信群组名';

-- 添加字段 b_iot_time_config
ALTER TABLE b_iot_time_config  ADD COLUMN `ser_version` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务端版本';
ALTER TABLE b_iot_time_config  ADD COLUMN `cli_version` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '客户端版本';
ALTER TABLE b_iot_time_config  ADD COLUMN `reg_date` date DEFAULT NULL COMMENT '安装日期';
ALTER TABLE b_iot_time_config  ADD COLUMN `up_date` date DEFAULT NULL COMMENT '更新日期';
