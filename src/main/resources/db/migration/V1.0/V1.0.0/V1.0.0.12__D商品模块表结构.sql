CREATE TABLE `tb_item_classify`
(
    `id`         BIGINT(20) PRIMARY KEY AUTO_INCREMENT COMMENT 'id',
    `parent_id`  BIGINT(20) COMMENT '父id',
    `name`       VARCHAR(255) COMMENT '名称',
    `is_switch`  INT(1)     DEFAULT '0' COMMENT '是否开启品牌/产品树搜索  0是   1否',
    `created_at` DATETIME COMMENT '创建时间',
    `updated_at` DATETIME COMMENT '更新时间',
    `deleted`    TINYINT(1) DEFAULT '0' COMMENT '是否删除  0未删除  1删除'
) COMMENT ='商品分类';

CREATE TABLE `tb_item_classify_tags`
(
    `id`          BIGINT(20) PRIMARY KEY AUTO_INCREMENT COMMENT 'id',
    `classify_id` BIGINT(20) COMMENT '分类管理id',
    `name`        VARCHAR(255) COMMENT '名称',
    `value`       JSON COMMENT '值',
    `created_at`  DATETIME COMMENT '创建时间',
    `updated_at`  DATETIME COMMENT '更新时间',
    `deleted`     TINYINT(1) DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    KEY (`classify_id`)
) COMMENT ='商品分类属性';

CREATE TABLE `tb_brand`
(
    `id`            BIGINT(20) PRIMARY KEY AUTO_INCREMENT COMMENT 'id',
    `brand_name`    VARCHAR(100) NOT NULL COMMENT '品牌名称',
    `brand_en_name` VARCHAR(100) COMMENT '品牌英文名称',
    `remark`        VARCHAR(512) COMMENT '备注',
    `logo_url`      VARCHAR(512) COMMENT '图片URL',
    `data_status`   TINYINT(1)            DEFAULT '1' COMMENT '启用状态 1:启用;0:未启用',
    `deleted`       TINYINT(1)            DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    `create_by`     BIGINT(20) COMMENT '创建人',
    `update_by`     BIGINT(20) COMMENT '更新人',
    `create_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_at`     TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT ='商品品牌';

CREATE TABLE `tb_item`
(
    `id`             BIGINT(20) PRIMARY KEY COMMENT 'id',
    `code`           VARCHAR(50) COMMENT '销售商品编码',
    `name`           VARCHAR(256) COMMENT '商品名称',
    `category_id`    BIGINT(20) NOT NULL COMMENT '类目Id',
    `brand_id`       VARCHAR(100) COMMENT '品牌ID',
    `pics_url`       JSON COMMENT '商品图片url',
    `sale_attr_vals` JSON COMMENT '销售属性',
    `detail_html`    TEXT COMMENT '商品详情',
    `sale_status`    VARCHAR(50) COMMENT '商品销售状态',
    `warehouse_id`   BIGINT(20) COMMENT '仓库ID',
    `shipping_fee`   FLOAT COMMENT '运费',
    `sold_out_num`   INT(11)    NOT NULL DEFAULT '0' COMMENT '已售数量',
    `mini_price`     BIGINT(20) COMMENT 'sku的最低价格',
    `deleted`        TINYINT(1)          DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    `create_by`      BIGINT(20) COMMENT '创建人',
    `update_by`      BIGINT(20) COMMENT '更新人',
    `create_at`      TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_at`      TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY (`category_id`)
) COMMENT ='销售商品';

CREATE TABLE `tb_sale_sku`
(
    `id`              BIGINT(20) PRIMARY KEY COMMENT 'id',
    `item_id`         BIGINT(20) COMMENT '商品ID',
    `inv_sku_id`      BIGINT(20) COMMENT '产品 SKUid',
    `name`            VARCHAR(256) COMMENT '销售sku名称',
    `sku_pic_url`     JSON COMMENT 'SKU图片',
    `sale_attr_vals`  JSON COMMENT '销售属性',
    `attr_label`      VARCHAR(256) COMMENT '属性值组合',
    `sale_unit_price` FLOAT COMMENT '销售单价',
    `cost_unit_price` FLOAT COMMENT '成本价',
    `available_num`   INT(11) COMMENT '可用销售库存',
    `total_num`       INT(11) COMMENT '总库存',
    `remark`          VARCHAR(512) COMMENT '备注',
    `deleted`         TINYINT(1)         DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    `create_by`       BIGINT(20) COMMENT '创建人',
    `update_by`       BIGINT(20) COMMENT '更新人',
    `create_at`       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_at`       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `oem`             VARCHAR(50) COMMENT 'oem 编号',
    `inv_sku_name`    VARCHAR(150) COMMENT '库存SKU名称',
    KEY (`item_id`),
    KEY (`inv_sku_id`)
) COMMENT '销售sku';

CREATE TABLE `tb_cart`
(
    `id`               BIGINT(20) PRIMARY KEY COMMENT 'id',
    `buyer_partner_id` BIGINT(20) COMMENT '买家id（员工id）',
    `sale_sku_id`      BIGINT(20) COMMENT 'skuid',
    `num`              INT(11) COMMENT '数量',
    `sku_snapshot`     JSON COMMENT 'sku快照(sku图片+名称)',
    `created_at`       DATETIME COMMENT '创建时间',
    `updated_at`       DATETIME COMMENT '修改时间',
    KEY (`buyer_partner_id`, `sale_sku_id`)
) COMMENT ='购物车';
