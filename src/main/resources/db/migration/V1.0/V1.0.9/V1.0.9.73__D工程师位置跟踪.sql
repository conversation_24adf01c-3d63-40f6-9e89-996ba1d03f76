create table tb_engineer_track(
                                  id bigint     not null comment 'id' primary key,
                                  engineer_id             bigint               null comment '工程师ID',
                                  work_order_id bigint comment '工单ID',
                                  current_process         varchar(50)          null comment '当前维修进度',
                                  location             json                 null comment '客户所在位置',
                                  created_at           datetime             null comment '创建时间',
                                  updated_at           datetime             null comment '更新时间',
                                  deleted              tinyint(1) default 0 null comment '是否删除'
) comment '工程师活动位置跟踪';
