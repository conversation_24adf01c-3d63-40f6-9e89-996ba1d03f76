DROP TABLE IF EXISTS `b_iot_configure`;

CREATE TABLE `b_iot_configure`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `brand`      varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌',
    `item`       varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '项',
    `value`      varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '匹配值',
    `way`        varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '方式（精确、模糊）',
    `remark`     varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
    `created_at` datetime                         DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime                         DEFAULT NULL COMMENT '更新时间',
    `deleted`    tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-取值配置';

INSERT INTO b_iot_configure (brand, item, value, way, remark, created_at, updated_at, deleted)
VALUES ('RICOH', '黑白机-计数器', '计数器：机器总数', '精确', NULL, NULL, NULL, 0)
     , ('RICOH', '黑白计数器', '打印件总数：黑白;Total Prints: Black & White', '精确', NULL, NULL, NULL, 0)
     , ('RICOH', '彩色计数器', '打印件总数：彩色;Total Prints: Color', '精确', NULL, NULL, NULL, 0)
     , ('RICOH', '第五色计数器', '计数器：第五色组总数;Counter: Fifth Station Total', '精确', NULL, NULL, NULL, 0)
     , ('RICOH', '黑色粉瓶', 'black;黑色', '模糊', NULL, NULL, NULL, 0)
     , ('RICOH', '第五色粉瓶', 'Special Toner;特殊', '模糊', NULL, NULL, NULL, 0)
     , ('RICOH', '异常-卡纸', '卡纸', '模糊', NULL, NULL, NULL, 0)
     , ('RICOH', '异常-sc', 'SC;联系服务中心', '模糊', NULL, NULL, NULL, 0)
     , ('FUJI XEROX', '黑白机-计数器', 'Total Impressions', '精确', NULL, NULL, NULL, 0)
     , ('FUJI XEROX', '黑白计数器', 'Black Impressions', '精确', NULL, NULL, NULL, 0)
     , ('FUJI XEROX', '彩色计数器', 'Color Impressions', '精确', NULL, NULL, NULL, 0)
     , ('FUJI XEROX', '黑色粉瓶', 'black', '模糊', NULL, NULL, NULL, 0)
     , ('Xerox', '黑白机-计数器', 'Total Impressions', '精确', NULL, NULL, NULL, 0)
     , ('Xerox', '黑白计数器', 'Black Impressions', '精确', NULL, NULL, NULL, 0)
     , ('Xerox', '彩色计数器', 'Color Impressions', '精确', NULL, NULL, NULL, 0)
     , ('Xerox', '黑色粉瓶', 'black', '模糊', NULL, NULL, NULL, 0)
     , ('KONICA MINOLTA', '黑色粉瓶', 'black', '模糊', NULL, NULL, NULL, 0);



DROP TABLE IF EXISTS `b_iot_counter`;

CREATE TABLE `b_iot_counter`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id`         bigint(20) DEFAULT NULL COMMENT '客户id',
    `device_group_id`     bigint(20) DEFAULT NULL COMMENT '设备组id',
    `report_time`         datetime DEFAULT NULL COMMENT '上报时间',
    `black_white_counter` bigint(20) DEFAULT NULL COMMENT '第1色计数器（K）',
    `cyan_counter`        bigint(20) DEFAULT NULL COMMENT '第2色计数器（C）',
    `magenta_counter`     bigint(20) DEFAULT NULL COMMENT '第3色计数器（M）',
    `yellow_counter`      bigint(20) DEFAULT NULL COMMENT '第4色计数器（Y）',
    `fifth_counter`       bigint(20) DEFAULT NULL COMMENT '第5色计数器',
    `sixth_counter`       bigint(20) DEFAULT NULL COMMENT '第6色计数器',
    `seventh_counter`     bigint(20) DEFAULT NULL COMMENT '第7色计数器',
    `eightth_counter`     bigint(20) DEFAULT NULL COMMENT '第8色计数器',
    `ninth_counter`       bigint(20) DEFAULT NULL COMMENT '第9色计数器',
    `tenth_counter`       bigint(20) DEFAULT NULL COMMENT '第10色计数器',
    `eleventh_counter`    bigint(20) DEFAULT NULL COMMENT '第11色计数器',
    `twelfth_counter`     bigint(20) DEFAULT NULL COMMENT '第12色计数器',
    `thirteenth_counter`  bigint(20) DEFAULT NULL COMMENT '第13色计数器',
    `fourteenth_counter`  bigint(20) DEFAULT NULL COMMENT '第14色计数器',
    `fifteenth_counter`   bigint(20) DEFAULT NULL COMMENT '第15色计数器',
    `sixteenth_counter`   bigint(20) DEFAULT NULL COMMENT '第16色计数器',
    `seventeenth_counter` bigint(20) DEFAULT NULL COMMENT '第17色计数器',
    `eightteenth_counter` bigint(20) DEFAULT NULL COMMENT '第18色计数器',
    `created_at`          datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at`          datetime DEFAULT NULL COMMENT '更新时间',
    `deleted`             tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-计数器上报';

INSERT INTO b_iot_counter (customer_id, device_group_id, report_time, black_white_counter, cyan_counter,
                           magenta_counter, yellow_counter, fifth_counter, sixth_counter, seventh_counter,
                           eightth_counter, ninth_counter, tenth_counter, eleventh_counter, twelfth_counter,
                           thirteenth_counter, fourteenth_counter, fifteenth_counter, sixteenth_counter,
                           seventeenth_counter, eightteenth_counter, created_at, updated_at, deleted)
VALUES (1730132451806642178, 1746850696201805826, '2024-01-17 20:06:29', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-17 20:06:30', '2024-01-17 20:06:30',
        0)
     , (1730132451806642178, 1746850696201805826, '2024-01-18 12:06:03', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-18 12:06:03', '2024-01-18 12:06:03',
        0)
     , (1730132451806642178, 1746850696201805826, '2024-01-18 14:06:03', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-18 14:06:04', '2024-01-18 14:06:04',
        0)
     , (1730132451806642178, 1746850696201805826, '2024-01-19 13:47:07', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-19 13:47:08', '2024-01-19 13:47:08',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-19 20:38:40', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-19 20:38:44', '2024-01-19 20:38:44',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-19 20:57:31', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-19 20:57:36', '2024-01-19 20:57:36',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-19 21:04:25', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-19 21:04:29', '2024-01-19 21:04:29',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-19 23:05:15', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-19 23:05:20', '2024-01-19 23:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 01:05:15', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 01:05:20', '2024-01-20 01:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 03:05:15', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 03:05:20', '2024-01-20 03:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 05:05:15', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 05:05:20', '2024-01-20 05:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 07:05:15', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 07:05:20', '2024-01-20 07:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 09:05:15', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 09:05:20', '2024-01-20 09:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 11:05:15', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 11:05:20', '2024-01-20 11:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 13:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 13:05:20', '2024-01-20 13:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 15:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 15:05:20', '2024-01-20 15:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 17:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 17:05:20', '2024-01-20 17:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 19:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 19:05:20', '2024-01-20 19:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 21:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 21:05:23', '2024-01-20 21:05:23',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-20 23:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-20 23:05:20', '2024-01-20 23:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 01:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 01:05:20', '2024-01-21 01:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 03:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 03:05:20', '2024-01-21 03:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 05:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 05:05:20', '2024-01-21 05:05:20',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 07:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 07:05:19', '2024-01-21 07:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 09:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 09:05:19', '2024-01-21 09:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 11:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 11:05:19', '2024-01-21 11:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 13:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 13:05:19', '2024-01-21 13:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 15:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 15:05:19', '2024-01-21 15:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 17:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 17:05:19', '2024-01-21 17:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 19:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 19:05:19', '2024-01-21 19:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 21:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 21:05:19', '2024-01-21 21:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-21 23:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-21 23:05:19', '2024-01-21 23:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-22 01:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-22 01:05:19', '2024-01-22 01:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-22 03:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-22 03:05:19', '2024-01-22 03:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-22 05:05:19', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-22 05:05:19', '2024-01-22 05:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-22 07:05:20', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-22 07:05:19', '2024-01-22 07:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-22 09:05:20', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-22 09:05:19', '2024-01-22 09:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-22 11:05:20', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-22 11:05:19', '2024-01-22 11:05:19',
        0)
     , (1732762057314492417, 1748232034767130625, '2024-01-22 13:05:18', 280549, 69848, 69848, 69848, 0, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-22 13:05:19', '2024-01-22 13:05:19',
        0);


DROP TABLE IF EXISTS `b_iot_exception_log`;

CREATE TABLE `b_iot_exception_log`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id`        bigint(20) DEFAULT NULL COMMENT '客户id',
    `ip_address`         varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'IP地址',
    `port`               varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '端口',
    `exception_describe` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '异常描述',
    `brand`              varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌',
    `mode`               varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '机型',
    `report_time`        datetime                         DEFAULT NULL COMMENT '上报时间',
    `created_at`         datetime                         DEFAULT NULL COMMENT '创建时间',
    `updated_at`         datetime                         DEFAULT NULL COMMENT '更新时间',
    `deleted`            tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-异常日志';

INSERT INTO b_iot_exception_log (customer_id, ip_address, port, exception_describe, brand, mode, report_time,
                                 created_at, updated_at, deleted)
VALUES (1730132451806642178, '***********', '161', '不支持查询到的品牌：SHARP 机器ip:*********** 机器端口:161', NULL, '',
        '2024-01-17 16:03:29', '2024-01-17 16:03:29', '2024-01-17 16:03:29', 0)
     , (1730132451806642178, '***********', '161', '不支持查询到的品牌：SHARP 机器ip:*********** 机器端口:161', NULL, '',
        '2024-01-17 16:03:29', '2024-01-17 16:11:47', '2024-01-17 16:11:47', 0)
     , (1730132451806642178, '***********', '161', '不支持查询到的品牌：SHARP 机器ip:*********** 机器端口:161', NULL, '',
        '2024-01-17 16:03:29', '2024-01-17 16:11:52', '2024-01-17 16:11:52', 0)
     , (1730132451806642178, '***********', '161', '不支持查询到的品牌：- 机器ip:*********** 机器端口:161', '-', NULL,
        '2024-01-17 16:03:29', '2024-01-17 16:54:44', '2024-01-17 16:54:44', 0)
     , (1730132451806642178, '***********', '161', '不支持查询到的品牌：- 机器ip:*********** 机器端口:161', '-', NULL,
        '2024-01-17 16:03:29', '2024-01-17 20:06:29', '2024-01-17 20:06:29', 0)
     , (1730132451806642178, '***********', '161', '不支持查询到的品牌：- 机器ip:*********** 机器端口:161', '-', NULL, NULL,
        '2024-01-19 13:47:08', '2024-01-19 13:47:08', 0);



DROP TABLE IF EXISTS `b_iot_install_software`;

CREATE TABLE `b_iot_install_software`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `version_number`      varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本号',
    `upload_time`         datetime                         DEFAULT NULL COMMENT '上传时间',
    `install_file`        json                             DEFAULT NULL COMMENT '安装文件',
    `version_affiliation` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '版本所属',
    `type`                int(10) DEFAULT NULL COMMENT '类型  1客户端  2服务端',
    `created_at`          datetime                         DEFAULT NULL COMMENT '创建时间',
    `updated_at`          datetime                         DEFAULT NULL COMMENT '更新时间',
    `deleted`             tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-软件信息';


DROP TABLE IF EXISTS `b_iot_numeric_code`;

CREATE TABLE `b_iot_numeric_code`
(
    `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `num_code`       varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数字代码',
    `brand`          varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌',
    `series`         varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '系列',
    `num_data`       varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '抓取的数据',
    `state`          varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '过滤状态',
    `data_describe`  varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '中文描述',
    `fault_code`     varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '故障代码',
    `paper_code`     varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡纸代码',
    `fault_describe` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '故障手册中的描述',
    `created_at`     datetime                         DEFAULT NULL COMMENT '创建时间',
    `updated_at`     datetime                         DEFAULT NULL COMMENT '更新时间',
    `deleted`        tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-数字代码记录';

INSERT INTO b_iot_numeric_code (num_code, brand, series, num_data, state, data_describe, fault_code, paper_code,
                                fault_describe, created_at, updated_at, deleted)
VALUES ('10003', 'RICOH', '', '调整中...', '否', '调整中...', '', '', NULL, NULL, NULL, 0)
     , ('10003', 'RICOH', '', 'Warming Up...', '是', '预热中...', '', '', NULL, NULL, NULL, 0)
     , ('10003', 'RICOH', '', '预热中...', '是', '预热中...', '', '', NULL, NULL, NULL, 0)
     , ('10004', 'RICOH', '', '调整中...', '是', '调整中...', '', '', NULL, NULL, NULL, 0)
     , ('10004', 'RICOH', '', 'Adjusting...', '是', '调整中...', '', '', NULL, NULL, NULL, 0)
     , ('10006', 'RICOH', 'Pro 8100', '不足：碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10006', 'RICOH', '', '不足：碳粉', '否', '不足：碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10006', 'RICOH', 'Pro 8200', '不足：碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10006', 'RICOH', 'MP 7502', '不足: 碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10006', 'RICOH', '', '不足: 碳粉', '否', '不足: 碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10006', 'RICOH', 'MP 6054', '不足：碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10006', 'RICOH', 'MP 7001', '不足: 碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10032', 'RICOH', '', '快满：废弃碳粉', '是', '快满：废弃碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10033', 'RICOH', '', '节能模式', '是', '节能模式', '', '', NULL, NULL, NULL, 0)
     , ('10033', 'RICOH', 'MP 7001', '节能模式', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10033', 'RICOH', '', 'Energy Saver Mode', '是', '节能模式', '', '', NULL, NULL, NULL, 0)
     , ('10033', 'RICOH', '', '节能模式', '是', '节能模式', '', '', NULL, NULL, NULL, 0)
     , ('10033', 'RICOH', 'Pro 8100', '节能模式', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10034', 'RICOH', '', '低功率模式', '是', '低功率模式', '', '', NULL, NULL, NULL, 0)
     , ('10036', 'RICOH', '', 'Adjusting...', '是', '调整中...', '', '', NULL, NULL, NULL, 0)
     , ('10036', 'RICOH', '', '调整中...', '是', '调整中...', '', '', NULL, NULL, NULL, 0)
     , ('10037', 'RICOH', '', '添加墨水中...', '是', '添加墨水中...', '', '', NULL, NULL, NULL, 0)
     , ('10037', 'RICOH', '', 'Loading Toner...', '是', '添加墨水中...', '', '', NULL, NULL, NULL, 0)
     , ('10037', 'RICOH', '', '正在装填碳粉...', '是', '正在装填碳粉...', '', '', NULL, NULL, NULL, 0)
     , ('10047', 'RICOH', '', '作业暂停', '是', '作业暂停', '', '', NULL, NULL, NULL, 0)
     , ('10072', 'RICOH', '', '空：黑色碳粉', '是', '空：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10072', 'RICOH', '', '空：黑色碳粉盒', '是', '空：黑色碳粉盒', '', '', NULL, NULL, NULL, 0)
     , ('10072', 'RICOH', '', 'Low: Black Toner', '是', '空：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10073', 'RICOH', '', '空：青色碳粉', '是', '空：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10073', 'RICOH', '', 'Low: Cyan Toner', '是', '空：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10073', 'RICOH', '', '不足：青色墨水', '否', '不足：青色墨水', '', '', NULL, NULL, NULL, 0)
     , ('10073', 'RICOH', '', '空：青色碳粉盒', '是', '空：青色碳粉盒', '', '', NULL, NULL, NULL, 0)
     , ('10074', 'RICOH', '', 'Low: Magenta Toner', '是', '空：品红色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10074', 'RICOH', '', '空：品红色碳粉', '是', '空：品红色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10074', 'RICOH', '', '空：品红色碳粉盒', '是', '空：品红色碳粉盒', '', '', NULL, NULL, NULL, 0)
     , ('10075', 'RICOH', '', '空：黄色碳粉盒', '是', '空：黄色碳粉盒', '', '', NULL, NULL, NULL, 0)
     , ('10075', 'RICOH', '', '空：黄色碳粉', '是', '空：黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10075', 'RICOH', '', '不足：黄色墨水', '否', '不足：黄色墨水', '', '', NULL, NULL, NULL, 0)
     , ('10075', 'RICOH', '', 'Low: Yellow Toner', '是', '空：黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10081', 'RICOH', '', '检查碳粉更换', '是', '检查碳粉更换', '', '', NULL, NULL, NULL, 0)
     , ('10081', 'RICOH', '', 'Check Toner Replacement', '是', '检查碳粉更换', '', '', NULL, NULL, NULL, 0)
     , ('10082', 'RICOH', 'Pro C5200', 'Low: Black Toner', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10082', 'RICOH', '', 'Low: Black Toner', '否', '不足：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10082', 'RICOH', 'Pro C9200', '不足：黑色碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10082', 'RICOH', 'Pro C9100', 'Low: Black Toner', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10082', 'RICOH', '', '不足：黑色碳粉', '否', '不足：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10083', 'RICOH', 'Pro C9200', '不足：青色碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10083', 'RICOH', '', '接近不足：青色墨水', '否', '接近不足：青色墨水', '', '', NULL, NULL, NULL, 0)
     , ('10083', 'RICOH', 'Pro C5200', 'Low: Cyan Toner', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10083', 'RICOH', '', '不足：青色碳粉', '否', '不足：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10083', 'RICOH', 'Pro C9100', 'Low: Cyan Toner', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10083', 'RICOH', '', 'Low: Cyan Toner', '否', '不足：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10084', 'RICOH', 'Pro C9200', '不足：品红色碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10084', 'RICOH', 'Pro C5200', 'Low: Magenta Toner', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10084', 'RICOH', '', '不足：品红色碳粉', '否', '不足：品红色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10084', 'RICOH', 'Pro C9100', 'Low: Magenta Toner', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10084', 'RICOH', '', 'Low: Magenta Toner', '否', '不足：品红色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10085', 'RICOH', '', '不足：黄色碳粉', '否', '不足：黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10085', 'RICOH', 'Pro C9100', 'Low: Yellow Toner', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10085', 'RICOH', '', 'Low: Yellow Toner', '否', '不足：黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('10085', 'RICOH', 'Pro C9200', '不足：黄色碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10085', 'RICOH', 'Pro C5200', 'Low: Yellow Toner', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10092', 'RICOH', '', '不足：PCU的润滑油块(K)', '否', '不足：PCU的润滑油块(K)', '', '', NULL, NULL, NULL, 0)
     , ('10092', 'RICOH', 'Pro C9100', 'Low: Lubricant Bar for PCU (K)', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10093', 'RICOH', 'Pro C5200', '不足：PCU的润滑油块(C)', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10093', 'RICOH', '', '不足：PCU的润滑油块(C)', '否', '不足：PCU的润滑油块(C)', '', '', NULL, NULL, NULL, 0)
     , ('10094', 'RICOH', 'Pro C9100', 'Low: Lubricant Bar for PCU (M)', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10094', 'RICOH', '', '不足：PCU的润滑油块(M)', '否', '不足：PCU的润滑油块(M)', '', '', NULL, NULL, NULL, 0)
     , ('10095', 'RICOH', '', '不足：PCU的润滑油块(Y)', '否', '不足：PCU的润滑油块(Y)', '', '', NULL, NULL, NULL, 0)
     , ('10096', 'RICOH', 'Pro 8100', '接近补充：PCU的清洁单元', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('10096', 'RICOH', '', '接近补充：PCU的清洁单元', '否', '接近补充：PCU的清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('12001', 'RICOH', '', '盖打开：LCT 上盖', '是', '盖打开：LCT 上盖', '', '', NULL, NULL, NULL, 0)
     , ('12201', 'RICOH', '', '未检测到：纸盘1', '是', '未检测到：纸盘1', '', '', NULL, NULL, NULL, 0)
     , ('12201', 'RICOH', '', 'Not Detected: Tray 1', '是', '未检测到: 纸盘1', '', '', NULL, NULL, NULL, 0)
     , ('12201', 'RICOH', '', '未检测到: 纸盘 1', '是', '未检测到: 纸盘 1', '', '', NULL, NULL, NULL, 0)
     , ('12201', 'RICOH', '', '未检测到: 纸张输入1', '是', '未检测到: 纸张输入1', '', '', NULL, NULL, NULL, 0)
     , ('12301', 'RICOH', '', '未检测到: 纸盘 2', '是', '未检测到: 纸盘 2', '', '', NULL, NULL, NULL, 0)
     , ('12301', 'RICOH', '', 'Not Detected: Tray 2', '是', '未检测到: 纸盘 2', '', '', NULL, NULL, NULL, 0)
     , ('12301', 'RICOH', '', '未检测到: 纸张输入2', '是', '未检测到: 纸张输入2', '', '', NULL, NULL, NULL, 0)
     , ('12301', 'RICOH', '', '未检测到：纸盘2', '是', '未检测到：纸盘2', '', '', NULL, NULL, NULL, 0)

     , ('12401', 'RICOH', '', '未检测到: 纸盘 3', '是', '未检测到: 纸盘 3', '', '', NULL, NULL, NULL, 0)
     , ('12401', 'RICOH', '', '未检测到：纸盘3', '是', '未检测到：纸盘3', '', '', NULL, NULL, NULL, 0)
     , ('12401', 'RICOH', '', 'Not Detected: Tray 3', '是', '未检测到：纸盘3', '', '', NULL, NULL, NULL, 0)
     , ('12501', 'RICOH', '', 'Not Detected: Tray 4', '是', '未检测到：纸盘4', '', '', NULL, NULL, NULL, 0)
     , ('12501', 'RICOH', '', '未检测到：纸盘4', '是', '未检测到：纸盘4', '', '', NULL, NULL, NULL, 0)
     , ('12601', 'RICOH', '', '未检测到：纸盘5', '是', '未检测到：纸盘5', '', '', NULL, NULL, NULL, 0)
     , ('12701', 'RICOH', '', '未检测到：纸盘6', '是', '未检测到：纸盘6', '', '', NULL, NULL, NULL, 0)
     , ('13000', 'RICOH', '', '没有纸张：大容量纸盘', '是', '没有纸张：大容量纸盘', '', '', NULL, NULL, NULL, 0)
     , ('13200', 'RICOH', 'MP 7001', '没有纸张: 纸盘 1', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('13200', 'RICOH', '', 'No Paper: Tray 1', '是', '没有纸张：纸盘1', '', '', NULL, NULL, NULL, 0)

     , ('13200', 'RICOH', '', '没有纸张：纸盘1', '是', '没有纸张：纸盘1', '', '', NULL, NULL, NULL, 0)
     , ('13200', 'RICOH', 'Pro 8100', '没有纸张：纸盘1', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('13200', 'RICOH', '', '没有纸张: 纸盘 1', '是', '没有纸张: 纸盘 1', '', '', NULL, NULL, NULL, 0)
     , ('13200', 'RICOH', '', '没有纸张: 纸张输入1', '是', '没有纸张: 纸张输入1', '', '', NULL, NULL, NULL, 0)
     , ('13300', 'RICOH', '', '没有纸张: 纸盘 2', '是', '没有纸张: 纸盘 2', '', '', NULL, NULL, NULL, 0)
     , ('13300', 'RICOH', '', 'No Paper: Tray 2', '是', '没有纸张: 纸盘 2', '', '', NULL, NULL, NULL, 0)
     , ('13300', 'RICOH', 'MP 7502', '没有纸张: 纸盘 2', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('13300', 'RICOH', '', '没有纸张: 纸张输入2', '是', '没有纸张: 纸张输入2', '', '', NULL, NULL, NULL, 0)
     , ('13300', 'RICOH', '', '没有纸张：纸盘2', '是', '没有纸张：纸盘2', '', '', NULL, NULL, NULL, 0)
     , ('13400', 'RICOH', '', 'No Paper: Tray 3', '是', '没有纸张：纸盘3', '', '', NULL, NULL, NULL, 0)

     , ('13400', 'RICOH', '', '没有纸张：纸盘3', '是', '没有纸张：纸盘3', '', '', NULL, NULL, NULL, 0)
     , ('13400', 'RICOH', 'Pro 8100', '没有纸张：纸盘3', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('13400', 'RICOH', '', '没有纸张: 纸盘 3', '是', '没有纸张: 纸盘 3', '', '', NULL, NULL, NULL, 0)
     , ('13500', 'RICOH', '', '没有纸张：纸盘4', '是', '没有纸张：纸盘4', '', '', NULL, NULL, NULL, 0)
     , ('13500', 'RICOH', '', 'No Paper: Tray 4', '是', '没有纸张: 纸盘 4', '', '', NULL, NULL, NULL, 0)
     , ('13500', 'RICOH', 'Pro 8100', '没有纸张：纸盘4', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('13600', 'RICOH', 'Pro 8100', '没有纸张：纸盘5', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('13600', 'RICOH', '', '没有纸张：纸盘5', '是', '没有纸张：纸盘5', '', '', NULL, NULL, NULL, 0)
     , ('13700', 'RICOH', '', '没有纸张：纸盘6', '是', '没有纸张：纸盘6', '', '', NULL, NULL, NULL, 0)
     , ('13700', 'RICOH', 'Pro 8100', '没有纸张：纸盘6', '否', '', '', '', NULL, NULL, NULL, 0)

     , ('13800', 'RICOH', '', '没有纸张：纸盘7', '是', '没有纸张：纸盘7', '', '', NULL, NULL, NULL, 0)
     , ('14200', 'RICOH', '', '没有纸张：纸盘T1', '是', '没有纸张：纸盘T1', '', '', NULL, NULL, NULL, 0)
     , ('14300', 'RICOH', '', '没有纸张：纸盘A', '是', '没有纸张：纸盘A', '', '', NULL, NULL, NULL, 0)
     , ('14300', 'RICOH', '', '没有纸张：纸盘T2', '是', '没有纸张：纸盘T2', '', '', NULL, NULL, NULL, 0)
     , ('16201', 'RICOH', '', '未检测到：纸盘T1', '是', '未检测到：纸盘T1', '', '', NULL, NULL, NULL, 0)
     , ('16301', 'RICOH', '', '未检测到：纸盘T2', '是', '未检测到：纸盘T2', '', '', NULL, NULL, NULL, 0)
     , ('16301', 'RICOH', '', '未检测到：纸盘A', '是', '未检测到：纸盘A', '', '', NULL, NULL, NULL, 0)
     , ('230001', 'RICOH', '', '向左打开单元：ADF', '是', '向左打开单元：ADF', '', '', NULL, NULL, NULL, 0)
     , ('240101', 'RICOH', '', '盖打开: ADF', '是', '盖打开: ADF', '', '', NULL, NULL, NULL, 0)
     , ('240101', 'RICOH', '', '盖打开：ADF', '是', '盖打开：ADF', '', '', NULL, NULL, NULL, 0)

     , ('242001', 'RICOH', 'MP 7502', '', '否', '', '', '卡纸: ADF', NULL, NULL, NULL, 0)
     , ('242001', 'RICOH', 'Pro 8100', '', '否', '', '', '卡纸: ADF', NULL, NULL, NULL, 0)
     , ('242001', 'RICOH', 'MP 7001', '', '否', '', '', '卡纸: ADF', NULL, NULL, NULL, 0)
     , ('30013', 'RICOH', '', '空：青色碳粉', '是', '空：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30013', 'RICOH', '', 'Empty: Cyan Toner', '是', '空: 青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30013', 'RICOH', '', '空：青色墨水', '是', '空：青色墨水', '', '', NULL, NULL, NULL, 0)
     , ('30014', 'RICOH', '', 'Empty: Magenta Toner', '是', '空: 品红色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30014', 'RICOH', '', '空：品红色碳粉', '是', '空：品红色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30015', 'RICOH', '', 'Empty: Yellow Toner', '是', '空：黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30015', 'RICOH', '', '空：黄色碳粉', '是', '空：黄色碳粉', '', '', NULL, NULL, NULL, 0)

     , ('30020', 'RICOH', 'Pro 8100', '空：两个瓶子中的一个', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30020', 'RICOH', '', 'Empty: one of two bottles', '是', '空: 两个瓶子中的一个', '', '', NULL, NULL, NULL, 0)
     , ('30020', 'RICOH', '', '空：两个瓶子中的一个', '是', '空：两个瓶子中的一个', '', '', NULL, NULL, NULL, 0)
     , ('30073', 'RICOH', 'Pro C5200', '空：PCU的润滑油块(C)', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30132', 'RICOH', '', '未检测到：黑色碳粉', '是', '未检测到：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30132', 'RICOH', '', 'Not Detected: Black Toner', '是', '未检测到：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30133', 'RICOH', '', '未检测到：青色碳粉', '是', '未检测到：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30133', 'RICOH', '', 'Not Detected: Cyan Toner', '是', '未检测到：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30134', 'RICOH', '', 'Not Detected: Magenta Toner', '是', '未检测到: 品红色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30134', 'RICOH', '', '未检测到：品红色碳粉', '是', '未检测到：品红色碳粉', '', '', NULL, NULL, NULL, 0)

     , ('30135', 'RICOH', '', 'Not Detected: Yellow Toner', '是', '未检测到: 黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30135', 'RICOH', '', '未检测到：黄色碳粉', '是', '未检测到：黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30136', 'RICOH', '', '独立供应商碳粉', '是', '独立供应商碳粉', '', '', NULL, NULL, NULL, 0)
     , ('30150', 'RICOH', '', '未检测到：大容量纸盘', '是', '未检测到：大容量纸盘', '', '', NULL, NULL, NULL, 0)
     , ('30159', 'RICOH', '', 'Not Detected: WasteToner Bottle', '是', '未检测到: 废弃碳粉瓶', '', '', NULL, NULL, NULL, 0)
     , ('30159', 'RICOH', '', '未检测到：废弃碳粉瓶', '是', '未检测到：废弃碳粉瓶', '', '', NULL, NULL, NULL, 0)
     , ('30280', 'RICOH', '', 'Cover Open: LCT Front', '是', '盖打开：宽LCT左前盖板', '', '', NULL, NULL, NULL, 0)
     , ('30280', 'RICOH', '', '盖打开：LCT正面', '是', '盖打开：LCT正面', '', '', NULL, NULL, NULL, 0)
     , ('30280', 'RICOH', '', '盖打开：宽LCT左前盖板', '是', '盖打开：宽LCT左前盖板', '', '', NULL, NULL, NULL, 0)
     , ('30280', 'RICOH', '', '盖打开：宽大容量纸盘左前盖', '是', '盖打开：宽大容量纸盘左前盖', '', '', NULL, NULL, NULL, 0)

     , ('30288', 'RICOH', 'Pro C9200', 'Cvr. Open:Tray A/LCT Front Left', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30288', 'RICOH', '', 'Cvr. Open:Tray A/LCT Front Left', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30296', 'RICOH', '', 'Cover Open: Front Lower Left', '是', '盖打开：前左下', '', '', NULL, NULL, NULL, 0)
     , ('30340', 'RICOH', '', '满：文件制成机上纸盘', '是', '满：文件制成机上纸盘', '', '', NULL, NULL, NULL, 0)
     , ('30341', 'RICOH', '', '满：文件制成机移动纸盘', '是', '满：文件制成机移动纸盘', '', '', NULL, NULL, NULL, 0)
     , ('30341', 'RICOH', '', 'Full: Finisher Shift Tray', '是', '满：文件制成机移动纸盘', '', '', NULL, NULL, NULL, 0)
     , ('30343', 'RICOH', '', 'Full: Finisher Booklet Tray', '是', '满：文件制成机小册纸盘', '', '', NULL, NULL, NULL, 0)
     , ('30343', 'RICOH', '', '满：文件制成机小册纸盘', '是', '满：文件制成机小册纸盘', '', '', NULL, NULL, NULL, 0)
     , ('30371', 'RICOH', '', 'Suspend / Resume Key Error', '是', '暂停/恢复键错误', '', '', NULL, NULL, NULL, 0)
     , ('30371', 'RICOH', '', '暂停/恢复键错误', '是', '暂停/恢复键错误', '', '', NULL, NULL, NULL, 0)

     , ('30393', 'RICOH', '', 'Full: Waste Staples', '是', '满：废订书钉', '', '', NULL, NULL, NULL, 0)
     , ('30393', 'RICOH', 'Pro 8100', '满：废订书钉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30393', 'RICOH', '', '满：废订书钉', '是', '满：废订书钉', '', '', NULL, NULL, NULL, 0)
     , ('30402', 'RICOH', '', '接近更换：清洁网', '否', '接近更换：清洁网', '', '', NULL, NULL, NULL, 0)
     , ('30402', 'RICOH', '', 'Replace Cleaning Web', '否', '更换清洁网', '', '', NULL, NULL, NULL, 0)
     , ('30402', 'RICOH', 'Pro 8100', '更换清洁网', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30402', 'RICOH', 'Pro C5200', '接近更换：清洁网', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30402', 'RICOH', '', '更换清洁网', '否', '更换清洁网', '', '', NULL, NULL, NULL, 0)
     , ('30608', 'RICOH', '', 'RC Gate连接错误', '否', 'RC Gate连接错误', '', '', NULL, NULL, NULL, 0)
     , ('30722', 'RICOH', 'MP 7001', '故障: 纸盘 1', '否', '', '', '', NULL, NULL, NULL, 0)

     , ('30723', 'RICOH', 'MP 7001', '故障: 纸盘 2', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30724', 'RICOH', 'MP 7502', '故障: 纸盘 3', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30724', 'RICOH', '', '故障: 纸盘 3', '否', '故障: 纸盘 3', '', '', NULL, NULL, NULL, 0)
     , ('30740', 'RICOH', 'Pro 8200', '故障：装订单元', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30740', 'RICOH', '', '故障：装订单元', '否', '故障：装订单元', '', '', NULL, NULL, NULL, 0)
     , ('30743', 'RICOH', '', '故障：输出纸盘', '否', '故障：输出纸盘', '', '', NULL, NULL, NULL, 0)
     , ('30743', 'RICOH', 'Pro 8200', '故障：输出纸盘', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('30766', 'RICOH', '', '故障：小册订书机', '否', '故障：小册订书机', '', '', NULL, NULL, NULL, 0)
     , ('30766', 'RICOH', 'Pro 8200', '故障：小册订书机', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('310033', 'RICOH', '', '面板关闭模式>>可以打印', '是', '面板关闭模式>>可以打印', '', '', NULL, NULL, NULL, 0)

     , ('40010', 'RICOH', '', '空: 黑色碳粉', '是', '空: 黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40010', 'RICOH', '', '空：黑色碳粉', '是', '空：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40011', 'RICOH', '', 'Full: Waste Toner', '是', '满：废弃碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40011', 'RICOH', '', '满：废弃碳粉', '是', '满：废弃碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40012', 'RICOH', '', 'Empty: Black Toner', '是', '空：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40012', 'RICOH', '', '空：黑色碳粉', '是', '空：黑色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40013', 'RICOH', '', '空：青色碳粉', '是', '空：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40013', 'RICOH', '', 'Empty: Cyan Toner', '是', '空：青色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40014', 'RICOH', '', 'Empty: Magenta Toner', '是', '空：品红色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40014', 'RICOH', '', '空：品红色碳粉', '是', '空：品红色碳粉', '', '', NULL, NULL, NULL, 0)

     , ('40015', 'RICOH', '', 'Empty: Yellow Toner', '是', '空：黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40015', 'RICOH', '', '空：黄色碳粉', '是', '空：黄色碳粉', '', '', NULL, NULL, NULL, 0)
     , ('40072', 'RICOH', '', '空：PCU的润滑油块(K)', '否', '空：PCU的润滑油块(K)', '', '', NULL, NULL, NULL, 0)
     , ('40076', 'RICOH', '', '更换PCU的清洁单元', '否', '更换PCU的清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40100', 'RICOH', '', '未检测到：定影单元', '是', '未检测到：定影单元', '', '', NULL, NULL, NULL, 0)
     , ('40100', 'RICOH', '', 'Not Detected: Fusing Unit', '是', '未检测到：定影单元', '', '', NULL, NULL, NULL, 0)
     , ('40133', 'RICOH', 'MP C6003', '未检测到：青色碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('40134', 'RICOH', 'MP C6003', '未检测到：品红色碳粉', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('40137', 'RICOH', '', '未检测到: B2控制杆', '是', '未检测到: B2控制杆', '', '', NULL, NULL, NULL, 0)
     , ('40142', 'RICOH', '', 'Not Detected: Develop. Unit (K)', '是', '未检测到：显影单元(K)', '', '', NULL, NULL, NULL, 0)

     , ('40142', 'RICOH', '', '未检测到：显影单元(K)', '是', '未检测到：显影单元(K)', '', '', NULL, NULL, NULL, 0)
     , ('40146', 'RICOH', '', 'Not Detected: Belt Cleaner Unit', '是', '未检测到：转印带清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40146', 'RICOH', '', 'Not Detected: ITB Cleaning Unit', '是', '未检测到：转印带清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40159', 'RICOH', '', '未检测到：废弃碳粉瓶', '是', '未检测到：废弃碳粉瓶', '', '', NULL, NULL, NULL, 0)
     , ('40178', 'RICOH', 'Pro C5200', 'Not Detected: Feed Unit', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('40180', 'RICOH', '', 'Not Detected: Cleaning Web', '是', '未检测到：清洁纸', '', '', NULL, NULL, NULL, 0)
     , ('40182', 'RICOH', '', 'Set PCU clean Unt/Wrong PCDU(K)', '是', '未正确放置黑色鼓清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40182', 'RICOH', '', 'Not Detected:K DrumCleaner Unit', '是', '未检测到：黑色鼓清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40183', 'RICOH', '', 'Not Detected:C DrumCleaner Unit', '是', '未检测到：青色鼓清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40183', 'RICOH', '', 'Set PCU clean Unt/Wrong PCDU(C)', '是', '未正确放置青色鼓清洁单元', '', '', NULL, NULL, NULL, 0)

     , ('40184', 'RICOH', '', 'Set PCU clean Unt/Wrong PCDU(M)', '是', '未正确放置品红色鼓清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40185', 'RICOH', '', 'Set PCU clean Unt/Wrong PCDU(Y)', '是', '未正确放置黄色鼓清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40185', 'RICOH', '', 'Not Detected:Y DrumCleaner Unit', '是', '未检测到：黄色鼓清洁单元', '', '', NULL, NULL, NULL, 0)
     , ('40186', 'RICOH', '', '未检测到：抽屉', '是', '未检测到：抽屉', '', '', NULL, NULL, NULL, 0)
     , ('40188', 'RICOH', '', '未检测到：碳粉单元', '是', '未检测到：碳粉单元', '', '', NULL, NULL, NULL, 0)
     , ('40200', 'RICOH', '', 'Cover Open: Front Cover', '是', '盖打开：前盖', '', '', NULL, NULL, NULL, 0)
     , ('40200', 'RICOH', '', '盖打开: 前盖', '是', '盖打开: 前盖', '', '', NULL, NULL, NULL, 0)
     , ('40200', 'RICOH', '', '盖打开：前盖', '否', '盖打开：前盖', '', '', NULL, NULL, NULL, 0)
     , ('40201', 'RICOH', '', '盖打开：右下盖', '是', '盖打开：右下盖', '', '', NULL, NULL, NULL, 0)
     , ('40201', 'RICOH', '', 'Cover Open: Lower Right Cover', '是', '盖打开：右盖', '', '', NULL, NULL, NULL, 0)

     , ('40205', 'RICOH', '', '盖打开: 打印盒盖', '是', '盖打开: 打印盒盖', '', '', NULL, NULL, NULL, 0)
     , ('40205', 'RICOH', '', '盖打开：碳粉盖', '是', '盖打开：碳粉盖', '', '', NULL, NULL, NULL, 0)
     , ('40206', 'RICOH', '', '盖打开：左下盖', '是', '盖打开：左下盖', '', '', NULL, NULL, NULL, 0)
     , ('40206', 'RICOH', '', '盖打开：前下盖', '是', '盖打开：前下盖', '', '', NULL, NULL, NULL, 0)
     , ('40206', 'RICOH', '', 'Cover Open: Lower Front Cover', '是', '盖打开：前下盖', '', '', NULL, NULL, NULL, 0)
     , ('40206', 'RICOH', '', 'Cover Open: Front Lower Left', '是', '盖打开：左下盖', '', '', NULL, NULL, NULL, 0)
     , ('40206', 'RICOH', '', '释放: 纸张固定杆', '是', '释放: 纸张固定杆', '', '', NULL, NULL, NULL, 0)
     , ('40219', 'RICOH', '', '盖打开: 纸张输入盖', '是', '盖打开: 纸张输入盖', '', '', NULL, NULL, NULL, 0)
     , ('40241', 'RICOH', '', 'Cover Open: Finisher Front', '是', '盖打开：文件制成机正面', '', '', NULL, NULL, NULL, 0)
     , ('40241', 'RICOH', '', '盖打开：文件制成机正面', '是', '盖打开：文件制成机正面', '', '', NULL, NULL, NULL, 0)

     , ('40242', 'RICOH', '', 'Cover Open:Finisher Shift Tray1', '是', '盖打开：分页装订器移动纸盘1', '', '', NULL, NULL, NULL, 0)
     , ('40260', 'RICOH', '', '未检测到：双面单元', '是', '未检测到：双面单元', '', '', NULL, NULL, NULL, 0)
     , ('40280', 'RICOH', '', '盖打开：LCT正面', '是', '盖打开：LCT正面', '', '', NULL, NULL, NULL, 0)
     , ('40280', 'RICOH', '', '盖打开：宽大容量纸盘左前盖', '是', '盖打开：宽大容量纸盘左前盖', '', '', NULL, NULL, NULL, 0)
     , ('40288', 'RICOH', '', '盖打开：右上侧盖', '是', '盖打开：右上侧盖', '', '', NULL, NULL, NULL, 0)
     , ('40288', 'RICOH', '', 'Cvr. Open:Tray A/LCT Front Left', '是', '盖打开：右上侧盖', '', '', NULL, NULL, NULL, 0)
     , ('40289', 'RICOH', '', '盖打开：抽屉', '是', '盖打开：抽屉', '', '', NULL, NULL, NULL, 0)
     , ('40340', 'RICOH', '', '满：文件制成机上纸盘', '是', '满：文件制成机上纸盘', '', '', NULL, NULL, NULL, 0)
     , ('40341', 'RICOH', '', '满：文件制成机移动纸盘', '是', '满：文件制成机移动纸盘', '', '', NULL, NULL, NULL, 0)
     , ('40342', 'RICOH', '', '满：打孔容器', '是', '满：打孔容器', '', '', NULL, NULL, NULL, 0)

     , ('40371', 'RICOH', '', '暂停/恢复键错误', '是', '暂停/恢复键错误', '', '', NULL, NULL, NULL, 0)
     , ('40371', 'RICOH', '', 'Suspend / Resume Key Error', '是', '暂停/恢复键错误', '', '', NULL, NULL, NULL, 0)
     , ('40402', 'RICOH', 'Pro 8200', '更换清洁网', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('40402', 'RICOH', '', 'Replace Cleaning Web', '否', '更换清洁网', '', '', NULL, NULL, NULL, 0)
     , ('40402', 'RICOH', 'Pro C9200', '更换清洁网', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('40402', 'RICOH', 'Pro C9100', 'Replace Cleaning Web', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('40402', 'RICOH', 'MP 7502', '更换清洁网', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('40402', 'RICOH', '', '更换清洁网', '否', '更换清洁网', '', '', NULL, NULL, NULL, 0)
     , ('40440', 'RICOH', '', '需要更多订书钉', '是', '需要更多订书钉', '', '', NULL, NULL, NULL, 0)
     , ('40440', 'RICOH', 'Pro 8100', '需要更多订书钉', '否', '', '', '', NULL, NULL, NULL, 0)

     , ('40763', 'RICOH', '', 'Malfunction: Ext. Charge Unit', '否', '故障：外部收费单元', '', '', NULL, NULL, NULL, 0)
     , ('40763', 'RICOH', 'Pro C9100', 'Malfunction: Ext. Charge Unit', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('40763', 'RICOH', '', '故障：外部收费单元', '否', '故障：外部收费单元', '', '', NULL, NULL, NULL, 0)
     , ('40800', 'RICOH', 'Pro C9200', '', '否', '', 'SC562-01', '', NULL, NULL, NULL, 0)
     , ('40800', 'RICOH', '', '联系服务中心:374-02', '否', '联系服务中心:374-02', 'SC374-02', '', NULL, NULL, NULL, 0)
     , ('40800', 'RICOH', '907', '', '否', '', 'SC562-01', '', NULL, NULL, NULL, 0)
     , ('40800', 'RICOH', 'MP 7001', '', '否', '', 'SC562-01', '', NULL, NULL, NULL, 0)
     , ('40800', 'RICOH', 'Pro 8200', '', '否', '', 'SC562-01', '', NULL, NULL, NULL, 0)
     , ('40800', 'RICOH', 'Pro 8100', '', '否', '', 'SC562-01', '', NULL, NULL, NULL, 0)
     , ('40800', 'RICOH', 'Pro C9100', '', '否', '', 'SC562-01', '', NULL, NULL, NULL, 0)

     , ('40800', 'RICOH', 'MP 7502', '', '否', '', 'SC562-01', '', NULL, NULL, NULL, 0)
     , ('40800', 'RICOH', 'Pro C5200', '', '否', '', 'SC562-01', '', NULL, NULL, NULL, 0)
     , ('40902', 'RICOH', 'Pro C5100', '纸盘错误：双面打印', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('41000', 'RICOH', 'MP 7001', '未检测到: 输入纸盘', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('41104', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41107', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41154', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41199', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41200', 'RICOH', '', '未检测到: 输入纸盘', '是', '未检测到: 输入纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41200', 'RICOH', '', '未检测到：输入纸盘', '是', '未检测到：输入纸盘', '', '', NULL, NULL, NULL, 0)

     , ('41204', 'RICOH', '', '没有纸张: 选择纸盘', '是', '没有纸张: 选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41204', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41299', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41300', 'RICOH', 'Pro 8100', '未检测到：输入纸盘', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('41300', 'RICOH', 'Pro C9100', 'Not Detected: Input Tray', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('41304', 'RICOH', 'Pro C9100', 'No Paper: Selected Tray', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('41307', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41307', 'RICOH', '', '没有纸张: 选择纸盘', '是', '没有纸张: 选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41354', 'RICOH', 'Pro C5100', '没有纸张：选择纸盘', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('41399', 'RICOH', 'Pro C9100', 'No Paper: Selected Tray', '否', '', '', '', NULL, NULL, NULL, 0)

     , ('41499', 'RICOH', 'Pro C9200', '没有纸张：选择纸盘', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('41500', 'RICOH', '', '未检测到：输入纸盘', '是', '未检测到：输入纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41504', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41600', 'RICOH', '', '未检测到：输入纸盘', '是', '未检测到：输入纸盘', '', '', NULL, NULL, NULL, 0)
     , ('41604', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', 'MP 7502', '', '否', '', '', '卡纸: 输入纸盘', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', 'Pro 8200', '', '否', '', '', '卡纸: 输入纸盘', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', 'Pro C9100', 'Paper Misfeed: Input Tray', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', 'Pro C9200', '', '否', '', '', '卡纸: 输入纸盘', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', 'Pro 8100', '', '否', '', '', '卡纸: 输入纸盘', NULL, NULL, NULL, 0)

     , ('42000', 'RICOH', 'Pro C7100', '', '否', '', '', '卡纸: 输入纸盘', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', '907', '', '否', '', '', '卡纸: 输入纸盘', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', 'MP 7001', '', '否', '', '', '卡纸: 输入纸盘', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', 'MP 6054', '', '否', '', '', '卡纸: 输入纸盘', NULL, NULL, NULL, 0)
     , ('42000', 'RICOH', '', 'Paper Misfeed: Input Tray', '否', '卡纸：输入纸盘', '', '', NULL, NULL, NULL, 0)
     , ('42001', 'RICOH', 'Pro C9100', 'Paper Misfeed: Internal Path', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('42001', 'RICOH', 'MP 7001', '', '否', '', '', '卡纸: 内纸盘/输出纸盘', NULL, NULL, NULL, 0)
     , ('42001', 'RICOH', 'Pro 8100', '', '否', '', '', '卡纸: 内纸盘/输出纸盘', NULL, NULL, NULL, 0)
     , ('42001', 'RICOH', 'MP 7502', '', '否', '', '', '卡纸: 内纸盘/输出纸盘', NULL, NULL, NULL, 0)
     , ('42001', 'RICOH', '', '送纸错误：内部/输出', '否', '送纸错误：内部/输出', '', '', NULL, NULL, NULL, 0)

     , ('42001', 'RICOH', '', 'Paper Misfeed: Internal Path', '否', '卡纸：内部纸盘', '', '', NULL, NULL, NULL, 0)
     , ('42001', 'RICOH', 'Pro C5200', '', '否', '', '', '卡纸: 内纸盘/输出纸盘', NULL, NULL, NULL, 0)
     , ('42001', 'RICOH', 'Pro 8200', '', '否', '', '', '卡纸: 内纸盘/输出纸盘', NULL, NULL, NULL, 0)
     , ('42001', 'RICOH', 'Pro C9200', '', '否', '', '', '卡纸: 内纸盘/输出纸盘', NULL, NULL, NULL, 0)
     , ('42004', 'RICOH', 'MP 7001', '', '否', '', '', '卡纸: 输出纸盘', NULL, NULL, NULL, 0)
     , ('42004', 'RICOH', '', 'Paper Misfeed: Output Tray', '否', '卡纸：出纸纸盘', '', '', NULL, NULL, NULL, 0)
     , ('42004', 'RICOH', 'Pro C5200', 'Paper Misfeed: Output Tray', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('42004', 'RICOH', 'MP 7502', '', '否', '', '', '卡纸: 输出纸盘', NULL, NULL, NULL, 0)
     , ('42005', 'RICOH', 'Pro C5200', '', '否', '', '', '卡纸：文件制成机', NULL, NULL, NULL, 0)
     , ('42005', 'RICOH', 'Pro 8100', '', '否', '', '', '卡纸：文件制成机', NULL, NULL, NULL, 0)

     , ('42005', 'RICOH', '', 'Paper Misfeed: Finisher', '否', '送纸错误：文件制成机', '', '', NULL, NULL, NULL, 0)
     , ('42005', 'RICOH', 'Pro C9100', 'Paper Misfeed: Finisher', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('42005', 'RICOH', 'Pro 8200', '', '否', '', '', '卡纸：文件制成机', NULL, NULL, NULL, 0)
     , ('42005', 'RICOH', 'Pro C9200', '送纸错误：文件制成机', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('42005', 'RICOH', '', '送纸错误：文件制成机', '否', '送纸错误：文件制成机', '', '', NULL, NULL, NULL, 0)
     , ('42008', 'RICOH', 'Pro C5200', '', '否', '', '', '卡纸：输入纸盘', NULL, NULL, NULL, 0)
     , ('42009', 'RICOH', 'Pro 8100', '', '否', '', '', '卡纸：双面单元', NULL, NULL, NULL, 0)
     , ('42009', 'RICOH', 'Pro C9100', 'Paper Misfeed: Duplex Unit', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('42009', 'RICOH', '', '送纸错误：双面打印单元', '否', '送纸错误：双面打印单元', '', '', NULL, NULL, NULL, 0)
     , ('42009', 'RICOH', 'Pro 8200', '', '否', '', '', '卡纸：双面单元', NULL, NULL, NULL, 0)

     , ('42009', 'RICOH', '', 'Paper Misfeed: Duplex Unit', '否', '卡纸：双面单元', '', '', NULL, NULL, NULL, 0)
     , ('42009', 'RICOH', 'Pro C9200', '送纸错误：双面打印单元', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('42019', 'RICOH', 'MP 7001', '', '否', '', '', '卡纸: 双面进纸单元', NULL, NULL, NULL, 0)
     , ('42019', 'RICOH', '', '送纸错误：双面进纸单元', '否', '送纸错误：双面进纸单元', '', '', NULL, NULL, NULL, 0)
     , ('45004', 'RICOH', '', 'Mismatch: Paper Size and Type', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('45004', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('45007', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('45013', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('45026', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('45054', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)

     , ('45061', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('45063', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('45099', 'RICOH', '', 'Mismatch: Paper Size and Type', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('45099', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('46111', 'RICOH', '', '不符：纸张类型', '是', '不符：纸张类型', '', '', NULL, NULL, NULL, 0)
     , ('46122', 'RICOH', '', '不符：纸张类型', '是', '不符：纸张类型', '', '', NULL, NULL, NULL, 0)
     , ('46130', 'RICOH', '', '不符：纸张类型', '是', '不符：纸张类型', '', '', NULL, NULL, NULL, 0)
     , ('46162', 'RICOH', '', '不符：纸张类型', '是', '不符：纸张类型', '', '', NULL, NULL, NULL, 0)
     , ('46169', 'RICOH', '', '不符：纸张类型', '是', '不符：纸张类型', '', '', NULL, NULL, NULL, 0)
     , ('46183', 'RICOH', '', '不符：纸张类型', '是', '不符：纸张类型', '', '', NULL, NULL, NULL, 0)

     , ('46569', 'RICOH', 'Pro C9100', 'Mismatch: Paper Type', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('47107', 'RICOH', '', '不符：纸张尺寸', '是', '不符：纸张尺寸', '', '', NULL, NULL, NULL, 0)
     , ('47154', 'RICOH', '', '不符：纸张尺寸', '是', '不符：纸张尺寸', '', '', NULL, NULL, NULL, 0)
     , ('47179', 'RICOH', '', '不符：纸张尺寸', '是', '不符：纸张尺寸', '', '', NULL, NULL, NULL, 0)
     , ('47199', 'RICOH', '', '不符：纸张尺寸', '是', '不符：纸张尺寸', '', '', NULL, NULL, NULL, 0)
     , ('47304', 'RICOH', '', '不符：纸张尺寸', '是', '不符：纸张尺寸', '', '', NULL, NULL, NULL, 0)
     , ('47399', 'RICOH', 'Pro C9100', 'Mismatch: Paper Size', '否', '', '', '', NULL, NULL, NULL, 0)
     , ('48154', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('48199', 'RICOH', '', '不符：纸张尺寸和类型', '是', '不符：纸张尺寸和类型', '', '', NULL, NULL, NULL, 0)
     , ('48599', 'RICOH', 'Pro C9100', 'Mismatch: Paper Size and Type', '否', '', '', '', NULL, NULL, NULL, 0)

     , ('49200', 'RICOH', '', '未检测到：输入纸盘', '是', '未检测到：输入纸盘', '', '', NULL, NULL, NULL, 0)
     , ('49204', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('49254', 'RICOH', '', '没有纸张：选择纸盘', '是', '没有纸张：选择纸盘', '', '', NULL, NULL, NULL, 0)
     , ('003-974', 'FUJI XEROX', 'Printer-D95',
        'Scanning of the current original is completed. Scan your next original. Printing may be stopped.', '否', '',
        '003-974', '', NULL, NULL, NULL, 0)
     , ('003-974', 'FUJI XEROX', 'C75P-J75P',
        'Scanning of the current original is completed. Scan your next original. Printing may be stopped.', '否', '',
        '003-974', '', NULL, NULL, NULL, 0)
     , ('003-974', 'FUJI XEROX', 'ApeosPort-V',
        'Scanning of the current original is completed. Scan your next original. Printing may be stopped.', '否', '',
        '003-974', '', NULL, NULL, NULL, 0)
     , ('005-122', 'FUJI XEROX', 'C75P-J75P',
        'There is a paper jam in the Document Feeder. Clear the paper jam using the instructions provided on the Local User Interface. Scanning has stopped.',
        '否', '', '005-122', '', NULL, NULL, NULL, 0)
     , ('005-141', 'FUJI XEROX', 'Printer-D95',
        'There is a paper jam in the Document Feeder. Clear the paper jam using the instructions provided on the Local User Interface. Scanning has stopped.',
        '否', '', '005-141', '', NULL, NULL, NULL, 0)
     , ('005-305', 'FUJI XEROX', 'C75P-J75P',
        'The Document Feeder Top Cover is open. Close the Cover. Scanning has stopped.', '否', '', '005-305', '', NULL,
        NULL, NULL, 0)
     , ('010-420', 'FUJI XEROX', 'C75P-J75P',
        'The Fusing Unit needs to be replaced soon. Order a new Fusing Unit but do not replace until you are instructed to. Printing can continue.',
        '否', '', '010-420', '', NULL, NULL, NULL, 0)

     , ('010-420', 'FUJI XEROX', 'Pro 8100',
        'The Fusing Unit needs to be replaced soon. Order a new Fusing Unit but do not replace until you are instructed to. Printing can continue.',
        '否', '', '010-420', '', NULL, NULL, NULL, 0)
     , ('010-421', 'FUJI XEROX', 'Pro 8100',
        'The Fusing Unit needs to be replaced. Replace the Fusing Unit now. Printing can continue.', '否', '', '010-421',
        '', NULL, NULL, NULL, 0)
     , ('010-421', 'FUJI XEROX', 'C75P-J75P',
        'The Fusing Unit needs to be replaced. Replace the Fusing Unit now. Printing can continue.', '否', '', '010-421',
        '', NULL, NULL, NULL, 0)
     , ('012-100', 'FUJI XEROX', 'Printer-D95',
        'There is a paper jam near the Finisher Decurler. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '012-100', '', NULL, NULL, NULL, 0)
     , ('012-103', 'FUJI XEROX', 'Printer-D95',
        'There is a paper jam near the Hole Punch module. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '012-103', '', NULL, NULL, NULL, 0)
     , ('012-151', 'FUJI XEROX', 'Printer-D95',
        'There is a paper jam in the Finisher. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '012-151', '', NULL, NULL, NULL, 0)
     , ('012-159', 'FUJI XEROX', 'Printer-D95',
        'There is a paper jam near the Finisher Transport Cover. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '012-159', '', NULL, NULL, NULL, 0)
     , ('012-228', 'FUJI XEROX', 'Printer-D95',
        'Finisher Booklet End Guide Sensor fault. Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '012-228', '', NULL, NULL, NULL, 0)
     , ('012-239', 'FUJI XEROX', 'Printer-D95',
        'Motion detection sensor fault in the Stacker. Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '012-239', '', NULL, NULL, NULL, 0)
     , ('012-255', 'FUJI XEROX', 'Printer-D95',
        'Tray 8 (Inserter) fault. Call your System Administrator. Printing can continue.', '否', '', '012-255', '', NULL,
        NULL, NULL, 0)

     , ('012-302', 'FUJI XEROX', 'Printer-D95',
        'The Finisher Front Door is open. Close the Door. Printing has stopped.', '否', '', '012-302', '', NULL, NULL,
        NULL, 0)
     , ('012-304', 'FUJI XEROX', 'Printer-D95', 'The Finisher Left Door is open. Close the Door. Printing has stopped.',
        '否', '', '012-304', '', NULL, NULL, NULL, 0)
     , ('012-401', 'FUJI XEROX', 'Printer-D95',
        'The Finisher Staple Cartridge is out of staples or not properly seated. Replace the Finisher Staple Cartridge or refill staples. Printing can continue.',
        '否', '', '012-401', '', NULL, NULL, NULL, 0)
     , ('012-403', 'FUJI XEROX', 'C75P-J75P',
        'The Finisher Front Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-403', '', NULL, NULL, NULL, 0)
     , ('012-403', 'FUJI XEROX', 'Printer-D95',
        'The Finisher Front Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-403', '', NULL, NULL, NULL, 0)
     , ('012-403', 'FUJI XEROX', 'Pro 8100',
        'The Finisher Front Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-403', '', NULL, NULL, NULL, 0)
     , ('012-404', 'FUJI XEROX', 'C75P-J75P',
        'The Finisher Rear Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-404', '', NULL, NULL, NULL, 0)
     , ('012-404', 'FUJI XEROX', 'Printer-D95',
        'The Finisher Rear Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-404', '', NULL, NULL, NULL, 0)
     , ('012-454', 'FUJI XEROX', 'C75P-J75P',
        'The Finisher Tray is full. Unload paper from the Finisher Tray. Printing can continue.', '否', '', '012-454',
        '', NULL, NULL, NULL, 0)
     , ('012-454', 'FUJI XEROX', 'Printer-D95',
        'The Finisher Tray is full. Unload paper from the Finisher Tray. Printing can continue.', '否', '', '012-454',
        '', NULL, NULL, NULL, 0)

     , ('013-451', 'FUJI XEROX', 'Printer-D95', 'Interposer is empty. Add paper to Interposer. Printing can continue.',
        '否', '', '013-451', '', NULL, NULL, NULL, 0)
     , ('013-451', 'FUJI XEROX', '4110 Printer', 'Interposer is empty. Add paper to Interposer. Printing can continue.',
        '否', '', '013-451', '', NULL, NULL, NULL, 0)
     , ('013-451', 'FUJI XEROX', 'C75P-J75P', 'Interposer is empty. Add paper to Interposer. Printing can continue.',
        '否', '', '013-451', '', NULL, NULL, NULL, 0)
     , ('016-242', 'FUJI XEROX', 'Xerox 770DCP',
        'System GMT Clock Fail Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '016-242', '', NULL, NULL, NULL, 0)
     , ('016-242', 'FUJI XEROX', 'C550-C560',
        'System GMT Clock Fail Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '016-242', '', NULL, NULL, NULL, 0)
     , ('016-242', 'FUJI XEROX', 'ApeosPort-IV系C7780',
        'System GMT Clock Fail Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '016-242', '', NULL, NULL, NULL, 0)
     , ('016-424', 'FUJI XEROX', 'Matt',
        'The machine is in Low Power Mode. No user intervention is required. Printing will start when a job is received or a user initiates a job at the machine.',
        '否', '', '016-424', '', NULL, NULL, NULL, 0)
     , ('016-425', 'FUJI XEROX', 'Matt',
        'The machine is in Sleep Moded. No user intervention is required. Printing will start when a job is received or a user initiates a job at the machine.',
        '否', '', '016-425', '', NULL, NULL, NULL, 0)
     , ('024-946', 'FUJI XEROX', 'C75P-J75P',
        'Paper Tray 1 is open. Close Tray 1 to print the current job. Printing has stopped for the current job.', '否',
        '', '024-946', '', NULL, NULL, NULL, 0)
     , ('024-946', 'FUJI XEROX', 'Printer-D95',
        'Paper Tray 1 is open. Close Tray 1 to print the current job. Printing has stopped for the current job.', '否',
        '', '024-946', '', NULL, NULL, NULL, 0)

     , ('024-949', 'FUJI XEROX', 'ApeosPort-V',
        'Paper Tray 4 is open. Close Tray 4 to print the current job. Printing has stopped for the current job.', '否',
        '', '024-949', '', NULL, NULL, NULL, 0)
     , ('024-950', 'FUJI XEROX', 'C75P-J75P',
        'Paper Tray 1 is empty. Add paper to Tray 1. Printing has stopped for the current job.', '否', '', '024-950', '',
        NULL, NULL, NULL, 0)
     , ('024-950', 'FUJI XEROX', 'Matt',
        'Paper Tray 1 is empty. Add paper to Tray 1. Printing has stopped for the current job.', '否', '', '024-950', '',
        NULL, NULL, NULL, 0)
     , ('024-951', 'FUJI XEROX', 'C75P-J75P',
        'Paper Tray 2 is empty. Add paper to Tray 2. Printing has stopped for the current job.', '否', '', '024-951', '',
        NULL, NULL, NULL, 0)
     , ('024-952', 'FUJI XEROX', 'C75P-J75P',
        'Paper Tray 3 is empty. Add paper to Tray 3. Printing has stopped for the current job.', '否', '', '024-952', '',
        NULL, NULL, NULL, 0)
     , ('024-954', 'FUJI XEROX', 'ApeosPort-V',
        'Paper Tray 5 is empty. Add paper to Tray 5. Printing has stopped for the current job.', '否', '', '024-954', '',
        NULL, NULL, NULL, 0)
     , ('024-955', 'FUJI XEROX', 'C75P-J75P',
        'Tray 6 is empty. Add paper to Tray 6 using the instructions provided on the Local User Interface. Printing has stopped for the current job.',
        '否', '', '024-955', '', NULL, NULL, NULL, 0)
     , ('024-956', 'FUJI XEROX', 'C75P-J75P',
        'Paper Tray 7 is empty. Add paper to Tray 7 using the instructions provided on the Local User Interface. Printing has stopped for the current job.',
        '否', '', '024-956', '', NULL, NULL, NULL, 0)
     , ('024-965', 'FUJI XEROX', 'Printer-D95',
        'A job is held in the Job Queue because the correct paper is not loaded. Load the correct paper. Printing has stopped for the current job.',
        '否', '', '024-965', '', NULL, NULL, NULL, 0)
     , ('024-966', 'FUJI XEROX', 'Printer-D95',
        'Out of paper. Load the correct paper to complete the current job. Printing has stopped for the current job.',
        '否', '', '024-966', '', NULL, NULL, NULL, 0)

     , ('024-966', 'FUJI XEROX', '4110 Printer',
        'The media required by the current job is now empty. User intervention is required to load the appropriate media for the current job. Printing has stopped for the current job.',
        '否', '', '024-966', '', NULL, NULL, NULL, 0)
     , ('024-966', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'Out of paper. Load the correct paper to complete the current job. Printing has stopped for the current job.',
        '否', '', '024-966', '', NULL, NULL, NULL, 0)
     , ('024-970', 'FUJI XEROX', 'C75P-J75P',
        'Tray 6 which is required by the current job is open. Close Tray 6. Printing has stopped for the current job.',
        '否', '', '024-970', '', NULL, NULL, NULL, 0)
     , ('024-971', 'FUJI XEROX', 'C75P-J75P',
        'Tray 7 (High Capacity Feeder) is not in position. Make sure Tray 7 (High Capacity Feeder) is in position. Printing has stopped for the current job.',
        '否', '', '024-971', '', NULL, NULL, NULL, 0)
     , ('024-980', 'FUJI XEROX', 'Printer-D95',
        'The Finisher Tray is full. Unload paper from the Right Middle Tray to continue. Printing has stopped for the current job.',
        '否', '', '024-980', '', NULL, NULL, NULL, 0)
     , ('027-400', 'FUJI XEROX', 'ApeosPort-V',
        'The machine has been taken offline. Press the machine online button at the local UI or wait until the setting is finished. Printing is disabled.',
        '否', '', '027-400', '', NULL, NULL, NULL, 0)
     , ('027-400', 'FUJI XEROX', 'C75P-J75P',
        'The machine has been taken offline. Press the machine online button at the local UI or wait until the setting is finished. Printing is disabled.',
        '否', '', '027-400', '', NULL, NULL, NULL, 0)
     , ('042-326', 'FUJI XEROX', 'C75P-J75P',
        'Belt hardware fault. Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing is disabled.',
        '否', '', '042-326', '', NULL, NULL, NULL, 0)
     , ('062-300', 'FUJI XEROX', '4110 Printer',
        'The Document Glass Cover is open. Close the Cover. Scanning has stopped.', '否', '', '062-300', '', NULL, NULL,
        NULL, 0)
     , ('062-300', 'FUJI XEROX', 'C75P-J75P',
        'The Document Glass Cover is open. Close the Cover. Scanning has stopped.', '否', '', '062-300', '', NULL, NULL,
        NULL, 0)

     , ('062-300', 'FUJI XEROX', 'ApeosPort-V',
        'The Document Glass Cover is open. Close the Cover. Scanning has stopped.', '否', '', '062-300', '', NULL, NULL,
        NULL, 0)
     , ('062-300', 'FUJI XEROX', 'Matt', 'The Document Glass Cover is open. Close the Cover. Scanning has stopped.',
        '否', '', '062-300', '', NULL, NULL, NULL, 0)
     , ('062-300', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'The Document Glass Cover is open. Close the Cover. Scanning has stopped.', '否', '', '062-300', '', NULL, NULL,
        NULL, 0)
     , ('062-300', 'FUJI XEROX', 'Printer-D95',
        'The Document Glass Cover is open. Close the Cover. Scanning has stopped.', '否', '', '062-300', '', NULL, NULL,
        NULL, 0)
     , ('071-100', 'FUJI XEROX', 'Printer-D95',
        'Paper jam while feeding paper from Tray 1. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '071-100', '', NULL, NULL, NULL, 0)
     , ('071-101', 'FUJI XEROX', 'Printer-D95',
        'There is a paper jam near Tray 1. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '071-101', '', NULL, NULL, NULL, 0)
     , ('071-101', 'FUJI XEROX', 'C75P-J75P',
        'There is a paper jam near Tray 1. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '071-101', '', NULL, NULL, NULL, 0)
     , ('071-450', 'FUJI XEROX', 'C75P-J75P', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否',
        '', '071-450', '', NULL, NULL, NULL, 0)
     , ('071-450', 'FUJI XEROX', 'Matt', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-450', '', NULL, NULL, NULL, 0)
     , ('071-450', 'FUJI XEROX', 'Pro 8100', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否',
        '', '071-450', '', NULL, NULL, NULL, 0)

     , ('071-450', 'FUJI XEROX', 'Printer-D95', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.',
        '否', '', '071-450', '', NULL, NULL, NULL, 0)
     , ('071-450', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否', '', '071-450', '', NULL, NULL, NULL,
        0)
     , ('071-450', 'FUJI XEROX', 'ApeosPort-V', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.',
        '否', '', '071-450', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', 'ApeosPort-IV系C7780', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.',
        '否', '', '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', 'Xerox 770DCP', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否',
        '', '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', 'C550-C560', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', 'C754系', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', 'Printer-D95', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', 'DocuCentre-V系C5575', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.',
        '否', '', '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', '4110 Printer', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否',
        '', '071-451', '', NULL, NULL, NULL, 0)

     , ('071-451', 'FUJI XEROX', 'Matt', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', 'C75P-J75P', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'FUJI XEROX', 'ApeosPort-V', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-452', 'FUJI XEROX', '4110 Printer', 'Tray 1 is open. Close Tray 1. Printing can continue.', '否', '',
        '071-452', '', NULL, NULL, NULL, 0)
     , ('071-452', 'FUJI XEROX', 'Printer-D95', 'Tray 1 is open. Close Tray 1. Printing can continue.', '否', '',
        '071-452', '', NULL, NULL, NULL, 0)
     , ('071-452', 'FUJI XEROX', 'DocuCentre-V系C5575', 'Tray 1 is open. Close Tray 1. Printing can continue.', '否', '',
        '071-452', '', NULL, NULL, NULL, 0)
     , ('071-452', 'FUJI XEROX', 'C75P-J75P', 'Tray 1 is open. Close Tray 1. Printing can continue.', '否', '',
        '071-452', '', NULL, NULL, NULL, 0)
     , ('072-102', 'FUJI XEROX', 'C75P-J75P',
        'There is a paper jam near Tray 3. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '072-102', '', NULL, NULL, NULL, 0)
     , ('072-450', 'FUJI XEROX', 'Pro 8100', 'Tray 2 is Near empty. Add paper to Tray 2. Printing can continue.', '否',
        '', '072-450', '', NULL, NULL, NULL, 0)
     , ('072-450', 'FUJI XEROX', 'C75P-J75P', 'Tray 2 is Near empty. Add paper to Tray 2. Printing can continue.', '否',
        '', '072-450', '', NULL, NULL, NULL, 0)

     , ('072-450', 'FUJI XEROX', '4110 Printer',
        'Tray 2 is Near empty. User intervention is required to add paper to Tray 2. Printing can continue.', '否', '',
        '072-450', '', NULL, NULL, NULL, 0)
     , ('072-451', 'FUJI XEROX', 'Printer-D95', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'FUJI XEROX', '4110 Printer', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否',
        '', '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'FUJI XEROX', 'Xerox 770DCP', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否',
        '', '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'FUJI XEROX', 'ApeosPort-V', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'FUJI XEROX', 'ApeosPort-IV系C7780', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.',
        '否', '', '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'FUJI XEROX', 'Pro 8100', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'FUJI XEROX', 'C75P-J75P', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-452', 'FUJI XEROX', 'C75P-J75P', 'Tray 2 is open. Close Tray 2. Printing can continue.', '否', '',
        '072-452', '', NULL, NULL, NULL, 0)
     , ('073-100', 'FUJI XEROX', '4110 Printer',
        'A paper jam has been detected while the machine was feeding from Tray 3. User intervention is required to clear paper jam condition (instructions provided at the UI). Printing has stopped.',
        '否', '', '073-100', '', NULL, NULL, NULL, 0)

     , ('073-101', 'FUJI XEROX', 'ApeosPort-V',
        'Paper Jam near Paper Tray 3 and the Cover on the left side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '073-101', '', NULL, NULL, NULL, 0)
     , ('073-450', 'FUJI XEROX', 'Pro 8100', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否',
        '', '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'FUJI XEROX', 'ApeosPort-V', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.',
        '否', '', '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'FUJI XEROX', 'Matt', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'FUJI XEROX', 'Printer-D95', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.',
        '否', '', '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'FUJI XEROX', '4110 Printer', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.',
        '否', '', '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'FUJI XEROX', 'C75P-J75P', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否',
        '', '073-450', '', NULL, NULL, NULL, 0)
     , ('073-451', 'FUJI XEROX', 'Matt', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'FUJI XEROX', 'ApeosPort-V', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'FUJI XEROX', 'DocuCentre-V系C5575', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.',
        '否', '', '073-451', '', NULL, NULL, NULL, 0)

     , ('073-451', 'FUJI XEROX', '4110 Printer', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否',
        '', '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'FUJI XEROX', 'Printer-D95', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'FUJI XEROX', 'C550-C560', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'FUJI XEROX', 'C75P-J75P', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'FUJI XEROX', 'Pro 8100', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-452', 'FUJI XEROX', 'ApeosPort-V', 'Tray 3 is open. Close Tray 3. Printing can continue.', '否', '',
        '073-452', '', NULL, NULL, NULL, 0)
     , ('073-452', 'FUJI XEROX', '4110 Printer',
        'Tray 3 is open. User intervention is required to close Tray 3. Printing can continue.', '否', '', '073-452', '',
        NULL, NULL, NULL, 0)
     , ('073-452', 'FUJI XEROX', 'C75P-J75P', 'Tray 3 is open. Close Tray 3. Printing can continue.', '否', '',
        '073-452', '', NULL, NULL, NULL, 0)
     , ('074-450', 'FUJI XEROX', 'C550-C560', 'Tray 4 is Near empty. Add paper to Tray 4. Printing can continue.', '否',
        '', '074-450', '', NULL, NULL, NULL, 0)
     , ('074-450', 'FUJI XEROX', 'ApeosPort-V', 'Tray 4 is Near empty. Add paper to Tray 4. Printing can continue.',
        '否', '', '074-450', '', NULL, NULL, NULL, 0)

     , ('074-450', 'FUJI XEROX', 'Printer-D95', 'Tray 4 is Near empty. Add paper to Tray 4. Printing can continue.',
        '否', '', '074-450', '', NULL, NULL, NULL, 0)
     , ('074-451', 'FUJI XEROX', 'C754系', 'Tray 4 is empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-451', '', NULL, NULL, NULL, 0)
     , ('074-451', 'FUJI XEROX', 'ApeosPort-V', 'Tray 4 is empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-451', '', NULL, NULL, NULL, 0)
     , ('074-451', 'FUJI XEROX', 'Printer-D95', 'Tray 4 is empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-451', '', NULL, NULL, NULL, 0)
     , ('074-451', 'FUJI XEROX', '4110 Printer', 'Tray 4 is empty. Add paper to Tray 4. Printing can continue.', '否',
        '', '074-451', '', NULL, NULL, NULL, 0)
     , ('074-452', 'FUJI XEROX', '4110 Printer', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '',
        '074-452', '', NULL, NULL, NULL, 0)
     , ('074-452', 'FUJI XEROX', 'Printer-D95', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '',
        '074-452', '', NULL, NULL, NULL, 0)
     , ('074-452', 'FUJI XEROX', 'Xerox 770DCP', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '',
        '074-452', '', NULL, NULL, NULL, 0)
     , ('074-452', 'FUJI XEROX', 'ApeosPort-V', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '',
        '074-452', '', NULL, NULL, NULL, 0)
     , ('074-452', 'FUJI XEROX', 'ApeosPort-IV系C7780', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '',
        '074-452', '', NULL, NULL, NULL, 0)

     , ('075-451', 'FUJI XEROX', 'ApeosPort-IV系C7780', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.',
        '否', '', '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', 'C550-C560', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', 'ApeosPort-V', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', 'C754系', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', 'C75P-J75P', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', 'Xerox 770DCP', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否',
        '', '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', 'DocuCentre-V系C5575', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.',
        '否', '', '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', 'Matt', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', 'Pro 8100', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'FUJI XEROX', '4110 Printer',
        'Tray 5 is empty. User intervention is required to add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)

     , ('077-101', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'There is a paper jam within the Cover on the left side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-101', '', NULL, NULL, NULL, 0)
     , ('077-106', 'FUJI XEROX', 'ApeosPort-V',
        'There is a paper jam near the Fuser and Cover A on the left side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-106', '', NULL, NULL, NULL, 0)
     , ('077-106', 'FUJI XEROX', '4110 Printer', 'Paper Jam', '否', '', '077-106', '', NULL, NULL, NULL, 0)
     , ('077-107', 'FUJI XEROX', 'Xerox 770DCP',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-107', '', NULL, NULL, NULL, 0)
     , ('077-107', 'FUJI XEROX', '4110 Printer', 'Paper Jam', '否', '', '077-107', '', NULL, NULL, NULL, 0)
     , ('077-107', 'FUJI XEROX', 'ApeosPort-V',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-107', '', NULL, NULL, NULL, 0)
     , ('077-109', 'FUJI XEROX', 'C75P-J75P',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-109', '', NULL, NULL, NULL, 0)
     , ('077-109', 'FUJI XEROX', '4110 Printer',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-109', '', NULL, NULL, NULL, 0)
     , ('077-111', 'FUJI XEROX', 'Printer-D95',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-111', '', NULL, NULL, NULL, 0)
     , ('077-111', 'FUJI XEROX', 'ApeosPort-V',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-111', '', NULL, NULL, NULL, 0)

     , ('077-115', 'FUJI XEROX', 'Xerox 770DCP',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-115', '', NULL, NULL, NULL, 0)
     , ('077-132', 'FUJI XEROX', 'C75P-J75P',
        'There is a paper jam near the Right Cover. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-132', '', NULL, NULL, NULL, 0)
     , ('077-142', 'FUJI XEROX', '4110 Printer',
        'There is a paper jam in the Machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-142', '', NULL, NULL, NULL, 0)
     , ('077-300', 'FUJI XEROX', 'Pro 8100', 'The Front Cover is open. Close the Cover. Printing has stopped.', '否', '',
        '077-300', '', NULL, NULL, NULL, 0)
     , ('077-300', 'FUJI XEROX', '4110 Printer', 'Interlock Open', '否', '', '077-300', '', NULL, NULL, NULL, 0)
     , ('077-300', 'FUJI XEROX', 'ApeosPort-V', 'The Front Cover is open. Close the Cover. Printing has stopped.', '否',
        '', '077-300', '', NULL, NULL, NULL, 0)
     , ('077-300', 'FUJI XEROX', 'C75P-J75P', 'The Front Cover is open. Close the Cover. Printing has stopped.', '否',
        '', '077-300', '', NULL, NULL, NULL, 0)
     , ('077-300', 'FUJI XEROX', 'Printer-D95', 'The Front Cover is open. Close the Cover. Printing has stopped.', '否',
        '', '077-300', '', NULL, NULL, NULL, 0)
     , ('077-301', 'FUJI XEROX', 'C75P-J75P',
        'The Cover on the left side of the machine is open. Close the Cover. Printing has stopped.', '否', '', '077-301',
        '', NULL, NULL, NULL, 0)
     , ('077-301', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'The Cover on the left side of the machine is open. Close the Cover. Printing has stopped.', '否', '', '077-301',
        '', NULL, NULL, NULL, 0)

     , ('077-302', 'FUJI XEROX', 'ApeosPort-V',
        'The Cover on the right side of the machine is open. Close the Cover. Printing has stopped.', '否', '',
        '077-302', '', NULL, NULL, NULL, 0)
     , ('077-303', 'FUJI XEROX', 'Pro 8100', 'The PH Drawer is open. Close the PH Drawer. Printing has stopped.', '否',
        '', '077-303', '', NULL, NULL, NULL, 0)
     , ('077-303', 'FUJI XEROX', 'C75P-J75P', 'The PH Drawer is open. Close the PH Drawer. Printing has stopped.', '否',
        '', '077-303', '', NULL, NULL, NULL, 0)
     , ('077-303', 'FUJI XEROX', 'ApeosPort-V', 'The PH Drawer is open. Close the PH Drawer. Printing has stopped.',
        '否', '', '077-303', '', NULL, NULL, NULL, 0)
     , ('077-306', 'FUJI XEROX', 'Printer-D95',
        'The Marking Drawer is open. Close the Marking Drawer. Printing has stopped.', '否', '', '077-306', '', NULL,
        NULL, NULL, 0)
     , ('077-306', 'FUJI XEROX', '4110 Printer', 'Interlock Open', '否', '', '077-306', '', NULL, NULL, NULL, 0)
     , ('077-307', 'FUJI XEROX', '4110 Printer', 'Interlock Open', '否', '', '077-307', '', NULL, NULL, NULL, 0)
     , ('077-307', 'FUJI XEROX', 'Printer-D95',
        'The Duplex Module Cover on the left side of the machine is open. Close the Cover. Printing has stopped.', '否',
        '', '077-307', '', NULL, NULL, NULL, 0)
     , ('077-311', 'FUJI XEROX', '4110 Printer', 'Marker Fatal Error', '否', '', '077-311', '', NULL, NULL, NULL, 0)
     , ('077-909', 'FUJI XEROX', 'Pro 8100',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-909', '', NULL, NULL, NULL, 0)

     , ('077-909', 'FUJI XEROX', 'C75P-J75P',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-909', '', NULL, NULL, NULL, 0)
     , ('077-909', 'FUJI XEROX', 'Printer-D95',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-909', '', NULL, NULL, NULL, 0)
     , ('077-909', 'FUJI XEROX', '4110 Printer', 'Paper Jam', '否', '', '077-909', '', NULL, NULL, NULL, 0)
     , ('077-968', 'FUJI XEROX', 'C75P-J75P',
        'The paper type required by the current job is not available. Load the paper type required for the current job. Printing has stopped for the current job.',
        '否', '', '077-968', '', NULL, NULL, NULL, 0)
     , ('078-213', 'FUJI XEROX', '4110 Printer',
        'Tray 5 / Bypass Tray fault. Call your System Administrator to fix Tray 5. Printing can continue.', '否', '',
        '078-213', '', NULL, NULL, NULL, 0)
     , ('078-300', 'FUJI XEROX', '4110 Printer',
        'The High Capacity Feeder Transport Cover is open. Close the Cover. Printing has stopped.', '否', '', '078-300',
        '', NULL, NULL, NULL, 0)
     , ('078-302', 'FUJI XEROX', 'C75P-J75P',
        'The Tray 5 / Bypass Tray cover is open. Close the Cover. Printing has stopped.', '否', '', '078-302', '', NULL,
        NULL, NULL, 0)
     , ('078-302', 'FUJI XEROX', '4110 Printer',
        'The Tray 5 / Bypass Tray cover is open. Close the Cover. Printing has stopped.', '否', '', '078-302', '', NULL,
        NULL, NULL, 0)
     , ('078-303', 'FUJI XEROX', 'C75P-J75P',
        'The High Capacity Feeder Front Cover is open. Close the Cover. Printing has stopped.', '否', '', '078-303', '',
        NULL, NULL, NULL, 0)
     , ('078-450', 'FUJI XEROX', 'C75P-J75P',
        'Tray 6 is out of paper soon. Add paper to Tray 6. Printing can continue.', '否', '', '078-450', '', NULL, NULL,
        NULL, 0)

     , ('078-450', 'FUJI XEROX', 'Pro 8100', 'Tray 6 is out of paper soon. Add paper to Tray 6. Printing can continue.',
        '否', '', '078-450', '', NULL, NULL, NULL, 0)
     , ('078-450', 'FUJI XEROX', 'Matt', 'Tray 6 is out of paper soon. Add paper to Tray 6. Printing can continue.',
        '否', '', '078-450', '', NULL, NULL, NULL, 0)
     , ('078-451', 'FUJI XEROX', 'C75P-J75P', 'Tray 6 is empty. Add paper to Tray 6. Printing can continue.', '否', '',
        '078-451', '', NULL, NULL, NULL, 0)
     , ('078-451', 'FUJI XEROX', '4110 Printer',
        'High Capacity Feeder1 is empty. Add paper to High Capacity Feeder1. Printing can continue.', '否', '',
        '078-451', '', NULL, NULL, NULL, 0)
     , ('078-460', 'FUJI XEROX', '4110 Printer',
        'High Capacity Feeder1 is open. Close the High Capacity Feeder1. Printing can continue.', '否', '', '078-460',
        '', NULL, NULL, NULL, 0)
     , ('078-460', 'FUJI XEROX', 'C75P-J75P', 'Tray 6 is open. Close Tray 6. Printing can continue.', '否', '',
        '078-460', '', NULL, NULL, NULL, 0)
     , ('079-450', 'FUJI XEROX', 'C75P-J75P',
        'Tray 7 is out of paper soon. Add paper to Tray 7. Printing can continue.', '否', '', '079-450', '', NULL, NULL,
        NULL, 0)
     , ('079-450', 'FUJI XEROX', 'Pro 8100', 'Tray 7 is out of paper soon. Add paper to Tray 7. Printing can continue.',
        '否', '', '079-450', '', NULL, NULL, NULL, 0)
     , ('079-451', 'FUJI XEROX', '4110 Printer',
        'High Capacity Feeder2 is empty. Add paper to High Capacity Feeder2. Printing can continue.', '否', '',
        '079-451', '', NULL, NULL, NULL, 0)
     , ('079-451', 'FUJI XEROX', 'C75P-J75P', 'Tray 7 is empty. Add paper to Tray 7. Printing can continue.', '否', '',
        '079-451', '', NULL, NULL, NULL, 0)

     , ('079-460', 'FUJI XEROX', '4110 Printer',
        'High Capacity Feeder2 is open. Close the High Capacity Feeder2. Printing can continue.', '否', '', '079-460',
        '', NULL, NULL, NULL, 0)
     , ('079-460', 'FUJI XEROX', 'C75P-J75P', 'Tray 7 is open. Close Tray 7. Printing can continue.', '否', '',
        '079-460', '', NULL, NULL, NULL, 0)
     , ('091-400', 'FUJI XEROX', 'ApeosPort-IV系C7780',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'FUJI XEROX', 'C550-C560',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'FUJI XEROX', 'ApeosPort-V',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'FUJI XEROX', 'Matt',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'FUJI XEROX', 'Printer-D95',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'FUJI XEROX', 'C75P-J75P',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'FUJI XEROX', 'Xerox 770DCP',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-404', 'FUJI XEROX', 'ApeosPort-V',
        'The Charge Corotron unit needs to be replaced. Replace the Charge Corotron unit. Printing can continue.', '否',
        '', '091-404', '', NULL, NULL, NULL, 0)

     , ('091-404', 'FUJI XEROX', 'C754系',
        'The Charge Corotron unit needs to be replaced. Replace the Charge Corotron unit. Printing can continue.', '否',
        '', '091-404', '', NULL, NULL, NULL, 0)
     , ('091-912', 'FUJI XEROX', '4110 Printer',
        'The Drum Cartridge may not be properly installed. Reinsert the Drum Cartridge in order to verify that it is properly installed. Printing has stopped.',
        '否', '', '091-912', '', NULL, NULL, NULL, 0)
     , ('091-912', 'FUJI XEROX', 'Printer-D95',
        'The Drum Cartridge may not be properly installed. Reinsert the Drum Cartridge in order to verify that it is properly installed. Printing has stopped.',
        '否', '', '091-912', '', NULL, NULL, NULL, 0)
     , ('093-300', 'FUJI XEROX', 'C75P-J75P',
        'The Marking Drawer is open. Close the Marking Drawer. Printing has stopped.', '否', '', '093-300', '', NULL,
        NULL, NULL, 0)
     , ('093-400', 'FUJI XEROX', 'Printer-D95',
        'The Black Toner [K] Cartridge needs to be replaced soon. Order a new Black Toner [K] Cartridge but do not replace until you are instructed to. Printing can continue.',
        '否', '', '093-400', '', NULL, NULL, NULL, 0)
     , ('093-406', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'The Black Toner [K] Cartridge needs to be replaced soon. Order a new Black Toner [K] Cartridge. Printing can continue.',
        '否', '', '093-406', '', NULL, NULL, NULL, 0)
     , ('093-407', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'The Yellow Toner [Y] Cartridge needs to be replaced soon. Order a new Yellow Toner [Y] Cartridge. Printing can continue.',
        '否', '', '093-407', '', NULL, NULL, NULL, 0)
     , ('093-408', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'The Magenta Toner [M] Cartridge needs to be replaced soon. Order a new Magenta Toner [M] Cartridge but do not replace until you are instructed to. Printing can continue.',
        '否', '', '093-408', '', NULL, NULL, NULL, 0)
     , ('093-409', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'The Cyan Toner [C] is low. Cartridge needs to be replaced soon. Order a new Cyan Toner [C] Cartridge. Printing can continue.',
        '否', '', '093-409', '', NULL, NULL, NULL, 0)
     , ('093-422', 'FUJI XEROX', 'C550-C560',
        'Black Toner [K1] Cartridge is empty. Black Toner [K2] Cartridge needs to be replaced soon. Replace the Black Toner [K1] Cartridge. Printing can continue.',
        '否', '', '093-422', '', NULL, NULL, NULL, 0)

     , ('093-422', 'FUJI XEROX', 'C75P-J75P',
        'Black Toner [K1] Cartridge is empty. Black Toner [K2] Cartridge needs to be replaced soon. Replace the Black Toner [K1] Cartridge. Printing can continue.',
        '否', '', '093-422', '', NULL, NULL, NULL, 0)
     , ('094-421', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'The Transfer Belt Cleaner has reached end of life. Call your System Administrator to replace the Transfer Belt Cleaner. Printing can continue.',
        '否', '', '094-421', '', NULL, NULL, NULL, 0)
     , ('094-422', 'FUJI XEROX', 'DocuCentre-V系C5575',
        'The Second Bias Transfer Roll has reached end of life. Call your System Administrator to replace the Second Bias Transfer Roll. Printing can continue.',
        '否', '', '094-422', '', NULL, NULL, NULL, 0)
     , ('Paper', 'FUJI XEROX', '4110 Printer', 'Low', '否', '', 'Paper', '', NULL, NULL, NULL, 0)
     , ('Tray', 'FUJI XEROX', '4110 Printer', 'Missing', '否', '', 'Tray', '', NULL, NULL, NULL, 0)
     , ('003-974', 'Xerox', 'Printer-D95',
        'Scanning of the current original is completed. Scan your next original. Printing may be stopped.', '否', '',
        '003-974', '', NULL, NULL, NULL, 0)
     , ('003-974', 'Xerox', 'C75P-J75P',
        'Scanning of the current original is completed. Scan your next original. Printing may be stopped.', '否', '',
        '003-974', '', NULL, NULL, NULL, 0)
     , ('003-974', 'Xerox', 'ApeosPort-V',
        'Scanning of the current original is completed. Scan your next original. Printing may be stopped.', '否', '',
        '003-974', '', NULL, NULL, NULL, 0)
     , ('005-122', 'Xerox', 'C75P-J75P',
        'There is a paper jam in the Document Feeder. Clear the paper jam using the instructions provided on the Local User Interface. Scanning has stopped.',
        '否', '', '005-122', '', NULL, NULL, NULL, 0)
     , ('005-141', 'Xerox', 'Printer-D95',
        'There is a paper jam in the Document Feeder. Clear the paper jam using the instructions provided on the Local User Interface. Scanning has stopped.',
        '否', '', '005-141', '', NULL, NULL, NULL, 0)

     , ('005-305', 'Xerox', 'C75P-J75P',
        'The Document Feeder Top Cover is open. Close the Cover. Scanning has stopped.', '否', '', '005-305', '', NULL,
        NULL, NULL, 0)
     , ('010-420', 'Xerox', 'C75P-J75P',
        'The Fusing Unit needs to be replaced soon. Order a new Fusing Unit but do not replace until you are instructed to. Printing can continue.',
        '否', '', '010-420', '', NULL, NULL, NULL, 0)
     , ('010-420', 'Xerox', 'Pro 8100',
        'The Fusing Unit needs to be replaced soon. Order a new Fusing Unit but do not replace until you are instructed to. Printing can continue.',
        '否', '', '010-420', '', NULL, NULL, NULL, 0)
     , ('010-421', 'Xerox', 'Pro 8100',
        'The Fusing Unit needs to be replaced. Replace the Fusing Unit now. Printing can continue.', '否', '', '010-421',
        '', NULL, NULL, NULL, 0)
     , ('010-421', 'Xerox', 'C75P-J75P',
        'The Fusing Unit needs to be replaced. Replace the Fusing Unit now. Printing can continue.', '否', '', '010-421',
        '', NULL, NULL, NULL, 0)
     , ('012-100', 'Xerox', 'Printer-D95',
        'There is a paper jam near the Finisher Decurler. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '012-100', '', NULL, NULL, NULL, 0)
     , ('012-103', 'Xerox', 'Printer-D95',
        'There is a paper jam near the Hole Punch module. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '012-103', '', NULL, NULL, NULL, 0)
     , ('012-151', 'Xerox', 'Printer-D95',
        'There is a paper jam in the Finisher. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '012-151', '', NULL, NULL, NULL, 0)
     , ('012-159', 'Xerox', 'Printer-D95',
        'There is a paper jam near the Finisher Transport Cover. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '012-159', '', NULL, NULL, NULL, 0)
     , ('012-228', 'Xerox', 'Printer-D95',
        'Finisher Booklet End Guide Sensor fault. Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '012-228', '', NULL, NULL, NULL, 0)

     , ('012-239', 'Xerox', 'Printer-D95',
        'Motion detection sensor fault in the Stacker. Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '012-239', '', NULL, NULL, NULL, 0)
     , ('012-255', 'Xerox', 'Printer-D95',
        'Tray 8 (Inserter) fault. Call your System Administrator. Printing can continue.', '否', '', '012-255', '', NULL,
        NULL, NULL, 0)
     , ('012-302', 'Xerox', 'Printer-D95', 'The Finisher Front Door is open. Close the Door. Printing has stopped.',
        '否', '', '012-302', '', NULL, NULL, NULL, 0)
     , ('012-304', 'Xerox', 'Printer-D95', 'The Finisher Left Door is open. Close the Door. Printing has stopped.', '否',
        '', '012-304', '', NULL, NULL, NULL, 0)
     , ('012-401', 'Xerox', 'Printer-D95',
        'The Finisher Staple Cartridge is out of staples or not properly seated. Replace the Finisher Staple Cartridge or refill staples. Printing can continue.',
        '否', '', '012-401', '', NULL, NULL, NULL, 0)
     , ('012-403', 'Xerox', 'C75P-J75P',
        'The Finisher Front Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-403', '', NULL, NULL, NULL, 0)
     , ('012-403', 'Xerox', 'Printer-D95',
        'The Finisher Front Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-403', '', NULL, NULL, NULL, 0)
     , ('012-403', 'Xerox', 'Pro 8100',
        'The Finisher Front Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-403', '', NULL, NULL, NULL, 0)
     , ('012-404', 'Xerox', 'C75P-J75P',
        'The Finisher Rear Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-404', '', NULL, NULL, NULL, 0)
     , ('012-404', 'Xerox', 'Printer-D95',
        'The Finisher Rear Booklet Staple Cartridge is out of staples or not properly seated. Replace the Cartridge. Printing can continue.',
        '否', '', '012-404', '', NULL, NULL, NULL, 0)

     , ('012-454', 'Xerox', 'C75P-J75P',
        'The Finisher Tray is full. Unload paper from the Finisher Tray. Printing can continue.', '否', '', '012-454',
        '', NULL, NULL, NULL, 0)
     , ('012-454', 'Xerox', 'Printer-D95',
        'The Finisher Tray is full. Unload paper from the Finisher Tray. Printing can continue.', '否', '', '012-454',
        '', NULL, NULL, NULL, 0)
     , ('013-451', 'Xerox', 'Printer-D95', 'Interposer is empty. Add paper to Interposer. Printing can continue.', '否',
        '', '013-451', '', NULL, NULL, NULL, 0)
     , ('013-451', 'Xerox', '4110 Printer', 'Interposer is empty. Add paper to Interposer. Printing can continue.', '否',
        '', '013-451', '', NULL, NULL, NULL, 0)
     , ('013-451', 'Xerox', 'C75P-J75P', 'Interposer is empty. Add paper to Interposer. Printing can continue.', '否',
        '', '013-451', '', NULL, NULL, NULL, 0)
     , ('016-242', 'Xerox', 'Xerox 770DCP',
        'System GMT Clock Fail Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '016-242', '', NULL, NULL, NULL, 0)
     , ('016-242', 'Xerox', 'C550-C560',
        'System GMT Clock Fail Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '016-242', '', NULL, NULL, NULL, 0)
     , ('016-242', 'Xerox', 'ApeosPort-IV系C7780',
        'System GMT Clock Fail Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing can continue.',
        '否', '', '016-242', '', NULL, NULL, NULL, 0)
     , ('016-424', 'Xerox', 'Matt',
        'The machine is in Low Power Mode. No user intervention is required. Printing will start when a job is received or a user initiates a job at the machine.',
        '否', '', '016-424', '', NULL, NULL, NULL, 0)
     , ('016-425', 'Xerox', 'Matt',
        'The machine is in Sleep Moded. No user intervention is required. Printing will start when a job is received or a user initiates a job at the machine.',
        '否', '', '016-425', '', NULL, NULL, NULL, 0)

     , ('024-946', 'Xerox', 'C75P-J75P',
        'Paper Tray 1 is open. Close Tray 1 to print the current job. Printing has stopped for the current job.', '否',
        '', '024-946', '', NULL, NULL, NULL, 0)
     , ('024-946', 'Xerox', 'Printer-D95',
        'Paper Tray 1 is open. Close Tray 1 to print the current job. Printing has stopped for the current job.', '否',
        '', '024-946', '', NULL, NULL, NULL, 0)
     , ('024-949', 'Xerox', 'ApeosPort-V',
        'Paper Tray 4 is open. Close Tray 4 to print the current job. Printing has stopped for the current job.', '否',
        '', '024-949', '', NULL, NULL, NULL, 0)
     , ('024-950', 'Xerox', 'C75P-J75P',
        'Paper Tray 1 is empty. Add paper to Tray 1. Printing has stopped for the current job.', '否', '', '024-950', '',
        NULL, NULL, NULL, 0)
     , ('024-950', 'Xerox', 'Matt',
        'Paper Tray 1 is empty. Add paper to Tray 1. Printing has stopped for the current job.', '否', '', '024-950', '',
        NULL, NULL, NULL, 0)
     , ('024-951', 'Xerox', 'C75P-J75P',
        'Paper Tray 2 is empty. Add paper to Tray 2. Printing has stopped for the current job.', '否', '', '024-951', '',
        NULL, NULL, NULL, 0)
     , ('024-952', 'Xerox', 'C75P-J75P',
        'Paper Tray 3 is empty. Add paper to Tray 3. Printing has stopped for the current job.', '否', '', '024-952', '',
        NULL, NULL, NULL, 0)
     , ('024-954', 'Xerox', 'ApeosPort-V',
        'Paper Tray 5 is empty. Add paper to Tray 5. Printing has stopped for the current job.', '否', '', '024-954', '',
        NULL, NULL, NULL, 0)
     , ('024-955', 'Xerox', 'C75P-J75P',
        'Tray 6 is empty. Add paper to Tray 6 using the instructions provided on the Local User Interface. Printing has stopped for the current job.',
        '否', '', '024-955', '', NULL, NULL, NULL, 0)
     , ('024-956', 'Xerox', 'C75P-J75P',
        'Paper Tray 7 is empty. Add paper to Tray 7 using the instructions provided on the Local User Interface. Printing has stopped for the current job.',
        '否', '', '024-956', '', NULL, NULL, NULL, 0)

     , ('024-965', 'Xerox', 'Printer-D95',
        'A job is held in the Job Queue because the correct paper is not loaded. Load the correct paper. Printing has stopped for the current job.',
        '否', '', '024-965', '', NULL, NULL, NULL, 0)
     , ('024-966', 'Xerox', 'Printer-D95',
        'Out of paper. Load the correct paper to complete the current job. Printing has stopped for the current job.',
        '否', '', '024-966', '', NULL, NULL, NULL, 0)
     , ('024-966', 'Xerox', '4110 Printer',
        'The media required by the current job is now empty. User intervention is required to load the appropriate media for the current job. Printing has stopped for the current job.',
        '否', '', '024-966', '', NULL, NULL, NULL, 0)
     , ('024-966', 'Xerox', 'DocuCentre-V系C5575',
        'Out of paper. Load the correct paper to complete the current job. Printing has stopped for the current job.',
        '否', '', '024-966', '', NULL, NULL, NULL, 0)
     , ('024-970', 'Xerox', 'C75P-J75P',
        'Tray 6 which is required by the current job is open. Close Tray 6. Printing has stopped for the current job.',
        '否', '', '024-970', '', NULL, NULL, NULL, 0)
     , ('024-971', 'Xerox', 'C75P-J75P',
        'Tray 7 (High Capacity Feeder) is not in position. Make sure Tray 7 (High Capacity Feeder) is in position. Printing has stopped for the current job.',
        '否', '', '024-971', '', NULL, NULL, NULL, 0)
     , ('024-980', 'Xerox', 'Printer-D95',
        'The Finisher Tray is full. Unload paper from the Right Middle Tray to continue. Printing has stopped for the current job.',
        '否', '', '024-980', '', NULL, NULL, NULL, 0)
     , ('027-400', 'Xerox', 'ApeosPort-V',
        'The machine has been taken offline. Press the machine online button at the local UI or wait until the setting is finished. Printing is disabled.',
        '否', '', '027-400', '', NULL, NULL, NULL, 0)
     , ('027-400', 'Xerox', 'C75P-J75P',
        'The machine has been taken offline. Press the machine online button at the local UI or wait until the setting is finished. Printing is disabled.',
        '否', '', '027-400', '', NULL, NULL, NULL, 0)
     , ('042-326', 'Xerox', 'C75P-J75P',
        'Belt hardware fault. Power off the machine, wait for the Control Panel to turn off. Then power the machine back on. If this fault persists, call your System Administrator. Printing is disabled.',
        '否', '', '042-326', '', NULL, NULL, NULL, 0)

     , ('062-300', 'Xerox', '4110 Printer', 'The Document Glass Cover is open. Close the Cover. Scanning has stopped.',
        '否', '', '062-300', '', NULL, NULL, NULL, 0)
     , ('062-300', 'Xerox', 'C75P-J75P', 'The Document Glass Cover is open. Close the Cover. Scanning has stopped.',
        '否', '', '062-300', '', NULL, NULL, NULL, 0)
     , ('062-300', 'Xerox', 'ApeosPort-V', 'The Document Glass Cover is open. Close the Cover. Scanning has stopped.',
        '否', '', '062-300', '', NULL, NULL, NULL, 0)
     , ('062-300', 'Xerox', 'Matt', 'The Document Glass Cover is open. Close the Cover. Scanning has stopped.', '否', '',
        '062-300', '', NULL, NULL, NULL, 0)
     , ('062-300', 'Xerox', 'DocuCentre-V系C5575',
        'The Document Glass Cover is open. Close the Cover. Scanning has stopped.', '否', '', '062-300', '', NULL, NULL,
        NULL, 0)
     , ('062-300', 'Xerox', 'Printer-D95', 'The Document Glass Cover is open. Close the Cover. Scanning has stopped.',
        '否', '', '062-300', '', NULL, NULL, NULL, 0)
     , ('071-100', 'Xerox', 'Printer-D95',
        'Paper jam while feeding paper from Tray 1. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '071-100', '', NULL, NULL, NULL, 0)
     , ('071-101', 'Xerox', 'Printer-D95',
        'There is a paper jam near Tray 1. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '071-101', '', NULL, NULL, NULL, 0)
     , ('071-101', 'Xerox', 'C75P-J75P',
        'There is a paper jam near Tray 1. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '071-101', '', NULL, NULL, NULL, 0)
     , ('071-450', 'Xerox', 'C75P-J75P', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-450', '', NULL, NULL, NULL, 0)
     , ('071-450', 'Xerox', 'Matt', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-450', '', NULL, NULL, NULL, 0)
     , ('071-450', 'Xerox', 'Pro 8100', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-450', '', NULL, NULL, NULL, 0)
     , ('071-450', 'Xerox', 'Printer-D95', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-450', '', NULL, NULL, NULL, 0)
     , ('071-450', 'Xerox', 'DocuCentre-V系C5575', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.',
        '否', '', '071-450', '', NULL, NULL, NULL, 0)
     , ('071-450', 'Xerox', 'ApeosPort-V', 'Tray 1 is Near empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-450', '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'ApeosPort-IV系C7780', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否',
        '', '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'Xerox 770DCP', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'C550-C560', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'C754系', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '', '071-451',
        '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'Printer-D95', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'DocuCentre-V系C5575', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否',
        '', '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', '4110 Printer', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'Matt', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '', '071-451',
        '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'C75P-J75P', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-451', 'Xerox', 'ApeosPort-V', 'Tray 1 is empty. Add paper to Tray 1. Printing can continue.', '否', '',
        '071-451', '', NULL, NULL, NULL, 0)
     , ('071-452', 'Xerox', '4110 Printer', 'Tray 1 is open. Close Tray 1. Printing can continue.', '否', '', '071-452',
        '', NULL, NULL, NULL, 0)
     , ('071-452', 'Xerox', 'Printer-D95', 'Tray 1 is open. Close Tray 1. Printing can continue.', '否', '', '071-452',
        '', NULL, NULL, NULL, 0)
     , ('071-452', 'Xerox', 'DocuCentre-V系C5575', 'Tray 1 is open. Close Tray 1. Printing can continue.', '否', '',
        '071-452', '', NULL, NULL, NULL, 0)
     , ('071-452', 'Xerox', 'C75P-J75P', 'Tray 1 is open. Close Tray 1. Printing can continue.', '否', '', '071-452', '',
        NULL, NULL, NULL, 0)
     , ('072-102', 'Xerox', 'C75P-J75P',
        'There is a paper jam near Tray 3. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '072-102', '', NULL, NULL, NULL, 0)
     , ('072-450', 'Xerox', 'Pro 8100', 'Tray 2 is Near empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-450', '', NULL, NULL, NULL, 0)
     , ('072-450', 'Xerox', 'C75P-J75P', 'Tray 2 is Near empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-450', '', NULL, NULL, NULL, 0)
     , ('072-450', 'Xerox', '4110 Printer',
        'Tray 2 is Near empty. User intervention is required to add paper to Tray 2. Printing can continue.', '否', '',
        '072-450', '', NULL, NULL, NULL, 0)
     , ('072-451', 'Xerox', 'Printer-D95', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'Xerox', '4110 Printer', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'Xerox', 'Xerox 770DCP', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'Xerox', 'ApeosPort-V', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'Xerox', 'ApeosPort-IV系C7780', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否',
        '', '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'Xerox', 'Pro 8100', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-451', 'Xerox', 'C75P-J75P', 'Tray 2 is empty. Add paper to Tray 2. Printing can continue.', '否', '',
        '072-451', '', NULL, NULL, NULL, 0)
     , ('072-452', 'Xerox', 'C75P-J75P', 'Tray 2 is open. Close Tray 2. Printing can continue.', '否', '', '072-452', '',
        NULL, NULL, NULL, 0)
     , ('073-100', 'Xerox', '4110 Printer',
        'A paper jam has been detected while the machine was feeding from Tray 3. User intervention is required to clear paper jam condition (instructions provided at the UI). Printing has stopped.',
        '否', '', '073-100', '', NULL, NULL, NULL, 0)
     , ('073-101', 'Xerox', 'ApeosPort-V',
        'Paper Jam near Paper Tray 3 and the Cover on the left side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '073-101', '', NULL, NULL, NULL, 0)
     , ('073-450', 'Xerox', 'Pro 8100', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'Xerox', 'ApeosPort-V', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'Xerox', 'Matt', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'Xerox', 'Printer-D95', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'Xerox', '4110 Printer', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否',
        '', '073-450', '', NULL, NULL, NULL, 0)
     , ('073-450', 'Xerox', 'C75P-J75P', 'Tray 3 is Near empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-450', '', NULL, NULL, NULL, 0)
     , ('073-451', 'Xerox', 'Matt', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '', '073-451',
        '', NULL, NULL, NULL, 0)
     , ('073-451', 'Xerox', 'ApeosPort-V', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'Xerox', 'DocuCentre-V系C5575', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否',
        '', '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'Xerox', '4110 Printer', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'Xerox', 'Printer-D95', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'Xerox', 'C550-C560', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'Xerox', 'C75P-J75P', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-451', 'Xerox', 'Pro 8100', 'Tray 3 is empty. Add paper to Tray 3. Printing can continue.', '否', '',
        '073-451', '', NULL, NULL, NULL, 0)
     , ('073-452', 'Xerox', 'ApeosPort-V', 'Tray 3 is open. Close Tray 3. Printing can continue.', '否', '', '073-452',
        '', NULL, NULL, NULL, 0)
     , ('073-452', 'Xerox', '4110 Printer',
        'Tray 3 is open. User intervention is required to close Tray 3. Printing can continue.', '否', '', '073-452', '',
        NULL, NULL, NULL, 0)
     , ('073-452', 'Xerox', 'C75P-J75P', 'Tray 3 is open. Close Tray 3. Printing can continue.', '否', '', '073-452', '',
        NULL, NULL, NULL, 0)
     , ('074-450', 'Xerox', 'C550-C560', 'Tray 4 is Near empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-450', '', NULL, NULL, NULL, 0)
     , ('074-450', 'Xerox', 'ApeosPort-V', 'Tray 4 is Near empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-450', '', NULL, NULL, NULL, 0)
     , ('074-450', 'Xerox', 'Printer-D95', 'Tray 4 is Near empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-450', '', NULL, NULL, NULL, 0)
     , ('074-451', 'Xerox', 'C754系', 'Tray 4 is empty. Add paper to Tray 4. Printing can continue.', '否', '', '074-451',
        '', NULL, NULL, NULL, 0)
     , ('074-451', 'Xerox', 'ApeosPort-V', 'Tray 4 is empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-451', '', NULL, NULL, NULL, 0)
     , ('074-451', 'Xerox', 'Printer-D95', 'Tray 4 is empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-451', '', NULL, NULL, NULL, 0)
     , ('074-451', 'Xerox', '4110 Printer', 'Tray 4 is empty. Add paper to Tray 4. Printing can continue.', '否', '',
        '074-451', '', NULL, NULL, NULL, 0)
     , ('074-452', 'Xerox', '4110 Printer', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '', '074-452',
        '', NULL, NULL, NULL, 0)
     , ('074-452', 'Xerox', 'Printer-D95', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '', '074-452',
        '', NULL, NULL, NULL, 0)
     , ('074-452', 'Xerox', 'Xerox 770DCP', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '', '074-452',
        '', NULL, NULL, NULL, 0)
     , ('074-452', 'Xerox', 'ApeosPort-V', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '', '074-452',
        '', NULL, NULL, NULL, 0)
     , ('074-452', 'Xerox', 'ApeosPort-IV系C7780', 'Tray 4 is open. Close Tray 4. Printing can continue.', '否', '',
        '074-452', '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'ApeosPort-IV系C7780', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否',
        '', '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'C550-C560', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'ApeosPort-V', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'C754系', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '', '075-451',
        '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'C75P-J75P', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'Xerox 770DCP', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'DocuCentre-V系C5575', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否',
        '', '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'Matt', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '', '075-451',
        '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', 'Pro 8100', 'Tray 5 is empty. Add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('075-451', 'Xerox', '4110 Printer',
        'Tray 5 is empty. User intervention is required to add paper to Tray 5. Printing can continue.', '否', '',
        '075-451', '', NULL, NULL, NULL, 0)
     , ('077-101', 'Xerox', 'DocuCentre-V系C5575',
        'There is a paper jam within the Cover on the left side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-101', '', NULL, NULL, NULL, 0)
     , ('077-106', 'Xerox', 'ApeosPort-V',
        'There is a paper jam near the Fuser and Cover A on the left side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-106', '', NULL, NULL, NULL, 0)
     , ('077-106', 'Xerox', '4110 Printer', 'Paper Jam', '否', '', '077-106', '', NULL, NULL, NULL, 0)
     , ('077-107', 'Xerox', 'Xerox 770DCP',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-107', '', NULL, NULL, NULL, 0)
     , ('077-107', 'Xerox', '4110 Printer', 'Paper Jam', '否', '', '077-107', '', NULL, NULL, NULL, 0)
     , ('077-107', 'Xerox', 'ApeosPort-V',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-107', '', NULL, NULL, NULL, 0)
     , ('077-109', 'Xerox', 'C75P-J75P',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-109', '', NULL, NULL, NULL, 0)
     , ('077-109', 'Xerox', '4110 Printer',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-109', '', NULL, NULL, NULL, 0)
     , ('077-111', 'Xerox', 'Printer-D95',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-111', '', NULL, NULL, NULL, 0)
     , ('077-111', 'Xerox', 'ApeosPort-V',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-111', '', NULL, NULL, NULL, 0)
     , ('077-115', 'Xerox', 'Xerox 770DCP',
        'There is a paper jam near the Cover on the right side of the machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-115', '', NULL, NULL, NULL, 0)
     , ('077-132', 'Xerox', 'C75P-J75P',
        'There is a paper jam near the Right Cover. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-132', '', NULL, NULL, NULL, 0)
     , ('077-142', 'Xerox', '4110 Printer',
        'There is a paper jam in the Machine. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-142', '', NULL, NULL, NULL, 0)
     , ('077-300', 'Xerox', 'Pro 8100', 'The Front Cover is open. Close the Cover. Printing has stopped.', '否', '',
        '077-300', '', NULL, NULL, NULL, 0)
     , ('077-300', 'Xerox', '4110 Printer', 'Interlock Open', '否', '', '077-300', '', NULL, NULL, NULL, 0)
     , ('077-300', 'Xerox', 'ApeosPort-V', 'The Front Cover is open. Close the Cover. Printing has stopped.', '否', '',
        '077-300', '', NULL, NULL, NULL, 0)
     , ('077-300', 'Xerox', 'C75P-J75P', 'The Front Cover is open. Close the Cover. Printing has stopped.', '否', '',
        '077-300', '', NULL, NULL, NULL, 0)
     , ('077-300', 'Xerox', 'Printer-D95', 'The Front Cover is open. Close the Cover. Printing has stopped.', '否', '',
        '077-300', '', NULL, NULL, NULL, 0)
     , ('077-301', 'Xerox', 'C75P-J75P',
        'The Cover on the left side of the machine is open. Close the Cover. Printing has stopped.', '否', '', '077-301',
        '', NULL, NULL, NULL, 0)
     , ('077-301', 'Xerox', 'DocuCentre-V系C5575',
        'The Cover on the left side of the machine is open. Close the Cover. Printing has stopped.', '否', '', '077-301',
        '', NULL, NULL, NULL, 0)
     , ('077-302', 'Xerox', 'ApeosPort-V',
        'The Cover on the right side of the machine is open. Close the Cover. Printing has stopped.', '否', '',
        '077-302', '', NULL, NULL, NULL, 0)
     , ('077-303', 'Xerox', 'Pro 8100', 'The PH Drawer is open. Close the PH Drawer. Printing has stopped.', '否', '',
        '077-303', '', NULL, NULL, NULL, 0)
     , ('077-303', 'Xerox', 'C75P-J75P', 'The PH Drawer is open. Close the PH Drawer. Printing has stopped.', '否', '',
        '077-303', '', NULL, NULL, NULL, 0)
     , ('077-303', 'Xerox', 'ApeosPort-V', 'The PH Drawer is open. Close the PH Drawer. Printing has stopped.', '否', '',
        '077-303', '', NULL, NULL, NULL, 0)
     , ('077-306', 'Xerox', 'Printer-D95',
        'The Marking Drawer is open. Close the Marking Drawer. Printing has stopped.', '否', '', '077-306', '', NULL,
        NULL, NULL, 0)
     , ('077-306', 'Xerox', '4110 Printer', 'Interlock Open', '否', '', '077-306', '', NULL, NULL, NULL, 0)
     , ('077-307', 'Xerox', '4110 Printer', 'Interlock Open', '否', '', '077-307', '', NULL, NULL, NULL, 0)
     , ('077-307', 'Xerox', 'Printer-D95',
        'The Duplex Module Cover on the left side of the machine is open. Close the Cover. Printing has stopped.', '否',
        '', '077-307', '', NULL, NULL, NULL, 0)
     , ('077-311', 'Xerox', '4110 Printer', 'Marker Fatal Error', '否', '', '077-311', '', NULL, NULL, NULL, 0)
     , ('077-909', 'Xerox', 'Pro 8100',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-909', '', NULL, NULL, NULL, 0)
     , ('077-909', 'Xerox', 'C75P-J75P',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-909', '', NULL, NULL, NULL, 0)
     , ('077-909', 'Xerox', 'Printer-D95',
        'There is a paper jam. Clear the paper jam using the instructions provided on the Local User Interface. Printing has stopped.',
        '否', '', '077-909', '', NULL, NULL, NULL, 0)
     , ('077-909', 'Xerox', '4110 Printer', 'Paper Jam', '否', '', '077-909', '', NULL, NULL, NULL, 0)
     , ('077-968', 'Xerox', 'C75P-J75P',
        'The paper type required by the current job is not available. Load the paper type required for the current job. Printing has stopped for the current job.',
        '否', '', '077-968', '', NULL, NULL, NULL, 0)
     , ('078-213', 'Xerox', '4110 Printer',
        'Tray 5 / Bypass Tray fault. Call your System Administrator to fix Tray 5. Printing can continue.', '否', '',
        '078-213', '', NULL, NULL, NULL, 0)
     , ('078-300', 'Xerox', '4110 Printer',
        'The High Capacity Feeder Transport Cover is open. Close the Cover. Printing has stopped.', '否', '', '078-300',
        '', NULL, NULL, NULL, 0)
     , ('078-302', 'Xerox', 'C75P-J75P',
        'The Tray 5 / Bypass Tray cover is open. Close the Cover. Printing has stopped.', '否', '', '078-302', '', NULL,
        NULL, NULL, 0)
     , ('078-302', 'Xerox', '4110 Printer',
        'The Tray 5 / Bypass Tray cover is open. Close the Cover. Printing has stopped.', '否', '', '078-302', '', NULL,
        NULL, NULL, 0)
     , ('078-303', 'Xerox', 'C75P-J75P',
        'The High Capacity Feeder Front Cover is open. Close the Cover. Printing has stopped.', '否', '', '078-303', '',
        NULL, NULL, NULL, 0)
     , ('078-450', 'Xerox', 'C75P-J75P', 'Tray 6 is out of paper soon. Add paper to Tray 6. Printing can continue.',
        '否', '', '078-450', '', NULL, NULL, NULL, 0)
     , ('078-450', 'Xerox', 'Pro 8100', 'Tray 6 is out of paper soon. Add paper to Tray 6. Printing can continue.', '否',
        '', '078-450', '', NULL, NULL, NULL, 0)
     , ('078-450', 'Xerox', 'Matt', 'Tray 6 is out of paper soon. Add paper to Tray 6. Printing can continue.', '否', '',
        '078-450', '', NULL, NULL, NULL, 0)
     , ('078-451', 'Xerox', 'C75P-J75P', 'Tray 6 is empty. Add paper to Tray 6. Printing can continue.', '否', '',
        '078-451', '', NULL, NULL, NULL, 0)
     , ('078-451', 'Xerox', '4110 Printer',
        'High Capacity Feeder1 is empty. Add paper to High Capacity Feeder1. Printing can continue.', '否', '',
        '078-451', '', NULL, NULL, NULL, 0)
     , ('078-460', 'Xerox', '4110 Printer',
        'High Capacity Feeder1 is open. Close the High Capacity Feeder1. Printing can continue.', '否', '', '078-460',
        '', NULL, NULL, NULL, 0)
     , ('078-460', 'Xerox', 'C75P-J75P', 'Tray 6 is open. Close Tray 6. Printing can continue.', '否', '', '078-460', '',
        NULL, NULL, NULL, 0)
     , ('079-450', 'Xerox', 'C75P-J75P', 'Tray 7 is out of paper soon. Add paper to Tray 7. Printing can continue.',
        '否', '', '079-450', '', NULL, NULL, NULL, 0)
     , ('079-450', 'Xerox', 'Pro 8100', 'Tray 7 is out of paper soon. Add paper to Tray 7. Printing can continue.', '否',
        '', '079-450', '', NULL, NULL, NULL, 0)
     , ('079-451', 'Xerox', '4110 Printer',
        'High Capacity Feeder2 is empty. Add paper to High Capacity Feeder2. Printing can continue.', '否', '',
        '079-451', '', NULL, NULL, NULL, 0)
     , ('079-451', 'Xerox', 'C75P-J75P', 'Tray 7 is empty. Add paper to Tray 7. Printing can continue.', '否', '',
        '079-451', '', NULL, NULL, NULL, 0)
     , ('079-460', 'Xerox', '4110 Printer',
        'High Capacity Feeder2 is open. Close the High Capacity Feeder2. Printing can continue.', '否', '', '079-460',
        '', NULL, NULL, NULL, 0)
     , ('079-460', 'Xerox', 'C75P-J75P', 'Tray 7 is open. Close Tray 7. Printing can continue.', '否', '', '079-460', '',
        NULL, NULL, NULL, 0)
     , ('091-400', 'Xerox', 'ApeosPort-IV系C7780',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'Xerox', 'C550-C560',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'Xerox', 'ApeosPort-V',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'Xerox', 'Matt',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'Xerox', 'Printer-D95',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'Xerox', 'C75P-J75P',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-400', 'Xerox', 'Xerox 770DCP',
        'The Waste Toner Container is nearly full. Order a new Waste Toner Container. Printing can continue.', '否', '',
        '091-400', '', NULL, NULL, NULL, 0)
     , ('091-404', 'Xerox', 'ApeosPort-V',
        'The Charge Corotron unit needs to be replaced. Replace the Charge Corotron unit. Printing can continue.', '否',
        '', '091-404', '', NULL, NULL, NULL, 0)
     , ('091-404', 'Xerox', 'C754系',
        'The Charge Corotron unit needs to be replaced. Replace the Charge Corotron unit. Printing can continue.', '否',
        '', '091-404', '', NULL, NULL, NULL, 0)
     , ('091-912', 'Xerox', '4110 Printer',
        'The Drum Cartridge may not be properly installed. Reinsert the Drum Cartridge in order to verify that it is properly installed. Printing has stopped.',
        '否', '', '091-912', '', NULL, NULL, NULL, 0)
     , ('091-912', 'Xerox', 'Printer-D95',
        'The Drum Cartridge may not be properly installed. Reinsert the Drum Cartridge in order to verify that it is properly installed. Printing has stopped.',
        '否', '', '091-912', '', NULL, NULL, NULL, 0)
     , ('093-300', 'Xerox', 'C75P-J75P', 'The Marking Drawer is open. Close the Marking Drawer. Printing has stopped.',
        '否', '', '093-300', '', NULL, NULL, NULL, 0)
     , ('093-400', 'Xerox', 'Printer-D95',
        'The Black Toner [K] Cartridge needs to be replaced soon. Order a new Black Toner [K] Cartridge but do not replace until you are instructed to. Printing can continue.',
        '否', '', '093-400', '', NULL, NULL, NULL, 0)
     , ('093-406', 'Xerox', 'DocuCentre-V系C5575',
        'The Black Toner [K] Cartridge needs to be replaced soon. Order a new Black Toner [K] Cartridge. Printing can continue.',
        '否', '', '093-406', '', NULL, NULL, NULL, 0)
     , ('093-407', 'Xerox', 'DocuCentre-V系C5575',
        'The Yellow Toner [Y] Cartridge needs to be replaced soon. Order a new Yellow Toner [Y] Cartridge. Printing can continue.',
        '否', '', '093-407', '', NULL, NULL, NULL, 0)
     , ('093-408', 'Xerox', 'DocuCentre-V系C5575',
        'The Magenta Toner [M] Cartridge needs to be replaced soon. Order a new Magenta Toner [M] Cartridge but do not replace until you are instructed to. Printing can continue.',
        '否', '', '093-408', '', NULL, NULL, NULL, 0)
     , ('093-409', 'Xerox', 'DocuCentre-V系C5575',
        'The Cyan Toner [C] is low. Cartridge needs to be replaced soon. Order a new Cyan Toner [C] Cartridge. Printing can continue.',
        '否', '', '093-409', '', NULL, NULL, NULL, 0)
     , ('093-422', 'Xerox', 'C550-C560',
        'Black Toner [K1] Cartridge is empty. Black Toner [K2] Cartridge needs to be replaced soon. Replace the Black Toner [K1] Cartridge. Printing can continue.',
        '否', '', '093-422', '', NULL, NULL, NULL, 0)
     , ('093-422', 'Xerox', 'C75P-J75P',
        'Black Toner [K1] Cartridge is empty. Black Toner [K2] Cartridge needs to be replaced soon. Replace the Black Toner [K1] Cartridge. Printing can continue.',
        '否', '', '093-422', '', NULL, NULL, NULL, 0)
     , ('094-421', 'Xerox', 'DocuCentre-V系C5575',
        'The Transfer Belt Cleaner has reached end of life. Call your System Administrator to replace the Transfer Belt Cleaner. Printing can continue.',
        '否', '', '094-421', '', NULL, NULL, NULL, 0)
     , ('094-422', 'Xerox', 'DocuCentre-V系C5575',
        'The Second Bias Transfer Roll has reached end of life. Call your System Administrator to replace the Second Bias Transfer Roll. Printing can continue.',
        '否', '', '094-422', '', NULL, NULL, NULL, 0)
     , ('Paper', 'Xerox', '4110 Printer', 'Low', '否', '', 'Paper', '', NULL, NULL, NULL, 0)
     , ('Tray', 'Xerox', '4110 Printer', 'Missing', '否', '', 'Tray', '', NULL, NULL, NULL, 0);


DROP TABLE IF EXISTS `b_iot_oid_config`;

CREATE TABLE `b_iot_oid_config`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `oid`        varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Oid',
    `oid_name`   varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Oid名称',
    `brand`      varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品牌',
    `mode`       varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '型号',
    `sort`       bigint(20) DEFAULT NULL COMMENT '排序值',
    `created_at` datetime                         DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime                         DEFAULT NULL COMMENT '更新时间',
    `deleted`    tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-oid配置表';

INSERT INTO b_iot_oid_config (oid, oid_name, brand, mode, sort, created_at, updated_at, deleted)
VALUES ('.1.3.6.1.2.1.43.8.2.1.14.1.1', 'Brand', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.367.3.2.1.1.1.7.0', 'Brand', 'All', '', 2, NULL, NULL, 0)
     , ('1.3.6.1.4.1.18334.1.1.1.5.3.1.0', 'Brand', 'All', '', 3, NULL, NULL, 0)
     , ('1.3.6.1.2.1.43.5.1.1.16.1', 'Mode', 'All', '', 1, NULL, NULL, 0)
     , ('1.3.6.1.4.1.367.3.2.1.1.1.1.0', 'Mode', 'RICOH', 'All', 2, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.1.1.0', 'Mode', 'KONICA MINOLTA', 'All', 3, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.43.5.1.1.17.1', 'MachineSn', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.367.3.2.1.2.1.4.0', 'MachineSn', 'RICOH', 'All', 2, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.43.13.4.1.4.1.1', 'Speed', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.1.2.0', 'DevOid', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.43.10.2.1.4.1.1', 'Counter_blackMachine', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.18334.1.1.1.5.7.2.1.1.0', 'Counter_blackMachine', 'KONICA MINOLTA', 'All', 2, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.367.3.2.1.2.19.5.1.6.*', 'Counter_content', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.253.8.53.13.2.1.8.1.20.*', 'Counter_content', 'FUJI XEROX', 'All', 2, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.367.3.2.1.2.19.5.1.9.*', 'Counter_num', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.253.8.53.13.2.1.6.1.20.*', 'Counter_num', 'FUJI XEROX', 'All', 2, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.43.11.1.1.6.1.*', 'Powder_Bottle', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.43.11.1.1.9.1.*', 'Powder_Num', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.43.11.1.1.8.1.*', 'Powder_Total', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.43.18.1.1.3.*', 'MachineState', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.43.18.1.1.8.1.*', 'Exception', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.367.3.2.1.2.23.4.2.1.8.3.*', 'Exception', 'RICOH', 'All', 2, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.25.3.2.1.3.1', 'BrandAndMode', 'All', '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.2.1.1.1.0', 'BrandAndMode', 'All', '', 2, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.18334.1.1.1.5.7.2.2.1.5.1.1;.1.3.6.1.4.1.18334.1.1.1.5.7.2.2.1.5.1.2', 'KM_counter_black', 'All',
        '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.18334.1.1.1.5.7.2.2.1.5.2.1;.1.3.6.1.4.1.18334.1.1.1.5.7.2.2.1.5.2.2', 'KM_counter_color', 'All',
        '', 1, NULL, NULL, 0)
     , ('.1.3.6.1.4.1.18334.1.1.2.1.5.7.1.1.1.12.1', 'BrandAndMode', 'KONICA MINOLTA', '', 3, NULL, NULL, 0);



DROP TABLE IF EXISTS `b_iot_powder`;

CREATE TABLE `b_iot_powder`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id`      bigint(20) DEFAULT NULL COMMENT '客户id',
    `device_group_id`  bigint(20) DEFAULT NULL COMMENT '设备组id',
    `toneybottle_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '粉瓶',
    `toney_num`        bigint(20) DEFAULT NULL COMMENT '粉量',
    `counter`          bigint(20) DEFAULT NULL COMMENT '计数器',
    `report_time`      datetime                         DEFAULT NULL COMMENT '上报时间',
    `created_at`       datetime                         DEFAULT NULL COMMENT '创建时间',
    `updated_at`       datetime                         DEFAULT NULL COMMENT '更新时间',
    `deleted`          tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-粉量上报';

INSERT INTO b_iot_powder (customer_id, device_group_id, toneybottle_name, toney_num, counter, report_time, created_at,
                          updated_at, deleted)
VALUES (1730132451806642178, 1734884199908798465, 'pink', 2121, 500, '2024-01-02 20:49:00', '2024-01-12 20:51:48',
        '2024-01-12 20:51:48', 0)
     , (1730132451806642178, 1734884199908798465, '黑色碳粉', 80, 280549, '2024-01-15 16:50:18', '2024-01-15 16:50:18',
        '2024-01-15 16:50:18', 0)
     , (1730132451806642178, 1734884199908798465, '废弃碳粉', 100, 69848, '2024-01-15 16:50:18', '2024-01-15 16:50:18',
        '2024-01-15 16:50:18', 0)
     , (1730132451806642178, 1734884199908798465, '青色碳粉', -2, 69848, '2024-01-15 16:50:18', '2024-01-15 16:50:18',
        '2024-01-15 16:50:18', 0)
     , (1730132451806642178, 1734884199908798465, '品红色碳粉', 60, 69848, '2024-01-15 16:50:18', '2024-01-15 16:50:18',
        '2024-01-15 16:50:18', 0)
     , (1730132451806642178, 1734884199908798465, '黄色碳粉', 70, 69848, '2024-01-15 16:50:18', '2024-01-15 16:50:18',
        '2024-01-15 16:50:18', 0)
     , (1730132451806642178, 1746850696201805826, '黑色碳粉', 80, 280549, '2024-01-17 16:54:44', '2024-01-17 16:54:44',
        '2024-01-17 16:54:44', 0)
     , (1730132451806642178, 1746850696201805826, '废弃碳粉', 100, 69848, '2024-01-17 16:54:44', '2024-01-17 16:54:44',
        '2024-01-17 16:54:44', 0)
     , (1730132451806642178, 1746850696201805826, '青色碳粉', -2, 69848, '2024-01-17 16:54:44', '2024-01-17 16:54:44',
        '2024-01-17 16:54:44', 0)
     , (1730132451806642178, 1746850696201805826, '品红色碳粉', 60, 69848, '2024-01-17 16:54:44', '2024-01-17 16:54:44',
        '2024-01-17 16:54:44', 0)
     , (1730132451806642178, 1746850696201805826, '黄色碳粉', 70, 69848, '2024-01-17 16:54:44', '2024-01-17 16:54:45',
        '2024-01-17 16:54:45', 0)
     , (1730132451806642178, 1746850696201805826, '黑色碳粉', 80, 280549, '2024-01-17 20:06:30', '2024-01-17 20:06:30',
        '2024-01-17 20:06:30', 0)
     , (1730132451806642178, 1746850696201805826, '废弃碳粉', 100, 69848, '2024-01-17 20:06:30', '2024-01-17 20:06:30',
        '2024-01-17 20:06:30', 0)
     , (1730132451806642178, 1746850696201805826, '青色碳粉', -2, 69848, '2024-01-17 20:06:30', '2024-01-17 20:06:30',
        '2024-01-17 20:06:30', 0)
     , (1730132451806642178, 1746850696201805826, '品红色碳粉', 60, 69848, '2024-01-17 20:06:30', '2024-01-17 20:06:30',
        '2024-01-17 20:06:30', 0)
     , (1730132451806642178, 1746850696201805826, '黄色碳粉', 70, 69848, '2024-01-17 20:06:30', '2024-01-17 20:06:30',
        '2024-01-17 20:06:30', 0)
     , (1730132451806642178, 1746850696201805826, '黑色碳粉', 80, 280549, '2024-01-18 12:06:03', '2024-01-18 12:06:03',
        '2024-01-18 12:06:03', 0)
     , (1730132451806642178, 1746850696201805826, '废弃碳粉', 100, 69848, '2024-01-18 12:06:04', '2024-01-18 12:06:03',
        '2024-01-18 12:06:03', 0)
     , (1730132451806642178, 1746850696201805826, '青色碳粉', -2, 69848, '2024-01-18 12:06:04', '2024-01-18 12:06:03',
        '2024-01-18 12:06:03', 0)
     , (1730132451806642178, 1746850696201805826, '品红色碳粉', 60, 69848, '2024-01-18 12:06:04', '2024-01-18 12:06:03',
        '2024-01-18 12:06:03', 0)
     , (1730132451806642178, 1746850696201805826, '黄色碳粉', 70, 69848, '2024-01-18 12:06:04', '2024-01-18 12:06:03',
        '2024-01-18 12:06:03', 0)
     , (1730132451806642178, 1746850696201805826, '黑色碳粉', 80, 280549, '2024-01-18 14:06:03', '2024-01-18 14:06:04',
        '2024-01-18 14:06:04', 0)
     , (1730132451806642178, 1746850696201805826, '废弃碳粉', 100, 69848, '2024-01-18 14:06:04', '2024-01-18 14:06:04',
        '2024-01-18 14:06:04', 0)
     , (1730132451806642178, 1746850696201805826, '青色碳粉', -2, 69848, '2024-01-18 14:06:04', '2024-01-18 14:06:04',
        '2024-01-18 14:06:04', 0)
     , (1730132451806642178, 1746850696201805826, '品红色碳粉', 60, 69848, '2024-01-18 14:06:04', '2024-01-18 14:06:04',
        '2024-01-18 14:06:04', 0)
     , (1730132451806642178, 1746850696201805826, '黄色碳粉', 70, 69848, '2024-01-18 14:06:04', '2024-01-18 14:06:04',
        '2024-01-18 14:06:04', 0)
     , (1730132451806642178, 1746850696201805826, '黑色碳粉', 80, 280549, '2024-01-19 13:47:08', '2024-01-19 13:47:09',
        '2024-01-19 13:47:09', 0)
     , (1730132451806642178, 1746850696201805826, '废弃碳粉', 100, 69848, '2024-01-19 13:47:08', '2024-01-19 13:47:09',
        '2024-01-19 13:47:09', 0)
     , (1730132451806642178, 1746850696201805826, '青色碳粉', -2, 69848, '2024-01-19 13:47:08', '2024-01-19 13:47:09',
        '2024-01-19 13:47:09', 0)
     , (1730132451806642178, 1746850696201805826, '品红色碳粉', 60, 69848, '2024-01-19 13:47:08', '2024-01-19 13:47:09',
        '2024-01-19 13:47:09', 0)
     , (1730132451806642178, 1746850696201805826, '黄色碳粉', 70, 69848, '2024-01-19 13:47:08', '2024-01-19 13:47:09',
        '2024-01-19 13:47:09', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-19 20:38:40', '2024-01-19 20:38:45',
        '2024-01-19 20:38:45', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-19 20:38:41', '2024-01-19 20:38:45',
        '2024-01-19 20:38:45', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-19 20:38:41', '2024-01-19 20:38:45',
        '2024-01-19 20:38:45', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-19 20:38:41', '2024-01-19 20:38:45',
        '2024-01-19 20:38:45', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-19 20:38:41', '2024-01-19 20:38:45',
        '2024-01-19 20:38:45', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-19 20:57:32', '2024-01-19 20:57:36',
        '2024-01-19 20:57:36', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-19 20:57:32', '2024-01-19 20:57:37',
        '2024-01-19 20:57:37', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-19 20:57:32', '2024-01-19 20:57:37',
        '2024-01-19 20:57:37', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-19 20:57:32', '2024-01-19 20:57:37',
        '2024-01-19 20:57:37', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-19 20:57:32', '2024-01-19 20:57:37',
        '2024-01-19 20:57:37', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-19 21:04:25', '2024-01-19 21:04:30',
        '2024-01-19 21:04:30', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-19 21:04:25', '2024-01-19 21:04:30',
        '2024-01-19 21:04:30', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-19 21:04:26', '2024-01-19 21:04:30',
        '2024-01-19 21:04:30', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-19 21:04:26', '2024-01-19 21:04:30',
        '2024-01-19 21:04:30', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-19 21:04:26', '2024-01-19 21:04:30',
        '2024-01-19 21:04:30', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-19 23:05:15', '2024-01-19 23:05:20',
        '2024-01-19 23:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-19 23:05:16', '2024-01-19 23:05:20',
        '2024-01-19 23:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-19 23:05:16', '2024-01-19 23:05:20',
        '2024-01-19 23:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-19 23:05:16', '2024-01-19 23:05:21',
        '2024-01-19 23:05:21', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-19 23:05:16', '2024-01-19 23:05:21',
        '2024-01-19 23:05:21', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 01:05:15', '2024-01-20 01:05:20',
        '2024-01-20 01:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 01:05:16', '2024-01-20 01:05:20',
        '2024-01-20 01:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 01:05:16', '2024-01-20 01:05:20',
        '2024-01-20 01:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 01:05:16', '2024-01-20 01:05:21',
        '2024-01-20 01:05:21', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 01:05:16', '2024-01-20 01:05:21',
        '2024-01-20 01:05:21', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 03:05:15', '2024-01-20 03:05:20',
        '2024-01-20 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 03:05:16', '2024-01-20 03:05:20',
        '2024-01-20 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 03:05:16', '2024-01-20 03:05:20',
        '2024-01-20 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 03:05:16', '2024-01-20 03:05:20',
        '2024-01-20 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 03:05:16', '2024-01-20 03:05:21',
        '2024-01-20 03:05:21', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 05:05:15', '2024-01-20 05:05:20',
        '2024-01-20 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 05:05:16', '2024-01-20 05:05:20',
        '2024-01-20 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 05:05:16', '2024-01-20 05:05:20',
        '2024-01-20 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 05:05:16', '2024-01-20 05:05:20',
        '2024-01-20 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 05:05:16', '2024-01-20 05:05:20',
        '2024-01-20 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 07:05:16', '2024-01-20 07:05:20',
        '2024-01-20 07:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 07:05:16', '2024-01-20 07:05:20',
        '2024-01-20 07:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 07:05:16', '2024-01-20 07:05:20',
        '2024-01-20 07:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 07:05:16', '2024-01-20 07:05:20',
        '2024-01-20 07:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 07:05:16', '2024-01-20 07:05:20',
        '2024-01-20 07:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 09:05:16', '2024-01-20 09:05:20',
        '2024-01-20 09:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 09:05:16', '2024-01-20 09:05:20',
        '2024-01-20 09:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 09:05:16', '2024-01-20 09:05:20',
        '2024-01-20 09:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 09:05:16', '2024-01-20 09:05:20',
        '2024-01-20 09:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 09:05:16', '2024-01-20 09:05:20',
        '2024-01-20 09:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 11:05:16', '2024-01-20 11:05:20',
        '2024-01-20 11:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 11:05:16', '2024-01-20 11:05:20',
        '2024-01-20 11:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 11:05:16', '2024-01-20 11:05:20',
        '2024-01-20 11:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 11:05:16', '2024-01-20 11:05:20',
        '2024-01-20 11:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 11:05:16', '2024-01-20 11:05:20',
        '2024-01-20 11:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 13:05:19', '2024-01-20 13:05:20',
        '2024-01-20 13:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 13:05:19', '2024-01-20 13:05:20',
        '2024-01-20 13:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 13:05:20', '2024-01-20 13:05:20',
        '2024-01-20 13:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 13:05:20', '2024-01-20 13:05:20',
        '2024-01-20 13:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 13:05:20', '2024-01-20 13:05:20',
        '2024-01-20 13:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 15:05:19', '2024-01-20 15:05:20',
        '2024-01-20 15:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 15:05:20', '2024-01-20 15:05:20',
        '2024-01-20 15:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 15:05:20', '2024-01-20 15:05:20',
        '2024-01-20 15:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 15:05:20', '2024-01-20 15:05:20',
        '2024-01-20 15:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 15:05:20', '2024-01-20 15:05:20',
        '2024-01-20 15:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 17:05:19', '2024-01-20 17:05:20',
        '2024-01-20 17:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 17:05:20', '2024-01-20 17:05:20',
        '2024-01-20 17:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 17:05:20', '2024-01-20 17:05:20',
        '2024-01-20 17:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 17:05:20', '2024-01-20 17:05:20',
        '2024-01-20 17:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 17:05:20', '2024-01-20 17:05:20',
        '2024-01-20 17:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 19:05:19', '2024-01-20 19:05:20',
        '2024-01-20 19:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 19:05:20', '2024-01-20 19:05:20',
        '2024-01-20 19:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 19:05:20', '2024-01-20 19:05:20',
        '2024-01-20 19:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 19:05:20', '2024-01-20 19:05:20',
        '2024-01-20 19:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 19:05:20', '2024-01-20 19:05:20',
        '2024-01-20 19:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 21:05:19', '2024-01-20 21:05:23',
        '2024-01-20 21:05:23', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 21:05:23', '2024-01-20 21:05:23',
        '2024-01-20 21:05:23', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 21:05:23', '2024-01-20 21:05:23',
        '2024-01-20 21:05:23', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 21:05:23', '2024-01-20 21:05:23',
        '2024-01-20 21:05:23', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 21:05:23', '2024-01-20 21:05:23',
        '2024-01-20 21:05:23', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-20 23:05:19', '2024-01-20 23:05:20',
        '2024-01-20 23:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-20 23:05:20', '2024-01-20 23:05:20',
        '2024-01-20 23:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-20 23:05:20', '2024-01-20 23:05:20',
        '2024-01-20 23:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-20 23:05:20', '2024-01-20 23:05:20',
        '2024-01-20 23:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-20 23:05:20', '2024-01-20 23:05:20',
        '2024-01-20 23:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 01:05:19', '2024-01-21 01:05:20',
        '2024-01-21 01:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 01:05:20', '2024-01-21 01:05:20',
        '2024-01-21 01:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 01:05:20', '2024-01-21 01:05:20',
        '2024-01-21 01:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 01:05:20', '2024-01-21 01:05:20',
        '2024-01-21 01:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 01:05:20', '2024-01-21 01:05:20',
        '2024-01-21 01:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 03:05:19', '2024-01-21 03:05:20',
        '2024-01-21 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 03:05:20', '2024-01-21 03:05:20',
        '2024-01-21 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 03:05:20', '2024-01-21 03:05:20',
        '2024-01-21 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 03:05:20', '2024-01-21 03:05:20',
        '2024-01-21 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 03:05:20', '2024-01-21 03:05:20',
        '2024-01-21 03:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 05:05:19', '2024-01-21 05:05:19',
        '2024-01-21 05:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 05:05:20', '2024-01-21 05:05:20',
        '2024-01-21 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 05:05:20', '2024-01-21 05:05:20',
        '2024-01-21 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 05:05:20', '2024-01-21 05:05:20',
        '2024-01-21 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 05:05:20', '2024-01-21 05:05:20',
        '2024-01-21 05:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 07:05:20', '2024-01-21 07:05:19',
        '2024-01-21 07:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 07:05:20', '2024-01-21 07:05:19',
        '2024-01-21 07:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 07:05:20', '2024-01-21 07:05:20',
        '2024-01-21 07:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 07:05:20', '2024-01-21 07:05:20',
        '2024-01-21 07:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 07:05:20', '2024-01-21 07:05:20',
        '2024-01-21 07:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 09:05:20', '2024-01-21 09:05:19',
        '2024-01-21 09:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 09:05:20', '2024-01-21 09:05:19',
        '2024-01-21 09:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 09:05:20', '2024-01-21 09:05:19',
        '2024-01-21 09:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 09:05:20', '2024-01-21 09:05:20',
        '2024-01-21 09:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 09:05:20', '2024-01-21 09:05:20',
        '2024-01-21 09:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 11:05:20', '2024-01-21 11:05:19',
        '2024-01-21 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 11:05:20', '2024-01-21 11:05:19',
        '2024-01-21 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 11:05:20', '2024-01-21 11:05:19',
        '2024-01-21 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 11:05:20', '2024-01-21 11:05:19',
        '2024-01-21 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 11:05:20', '2024-01-21 11:05:20',
        '2024-01-21 11:05:20', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 13:05:20', '2024-01-21 13:05:19',
        '2024-01-21 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 13:05:20', '2024-01-21 13:05:19',
        '2024-01-21 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 13:05:20', '2024-01-21 13:05:19',
        '2024-01-21 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 13:05:20', '2024-01-21 13:05:19',
        '2024-01-21 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 13:05:20', '2024-01-21 13:05:19',
        '2024-01-21 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 15:05:20', '2024-01-21 15:05:19',
        '2024-01-21 15:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 15:05:20', '2024-01-21 15:05:19',
        '2024-01-21 15:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 15:05:20', '2024-01-21 15:05:19',
        '2024-01-21 15:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 15:05:20', '2024-01-21 15:05:19',
        '2024-01-21 15:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 15:05:20', '2024-01-21 15:05:19',
        '2024-01-21 15:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 17:05:20', '2024-01-21 17:05:19',
        '2024-01-21 17:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 17:05:20', '2024-01-21 17:05:19',
        '2024-01-21 17:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 17:05:20', '2024-01-21 17:05:19',
        '2024-01-21 17:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 17:05:20', '2024-01-21 17:05:19',
        '2024-01-21 17:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 17:05:20', '2024-01-21 17:05:19',
        '2024-01-21 17:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 19:05:20', '2024-01-21 19:05:19',
        '2024-01-21 19:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 19:05:20', '2024-01-21 19:05:19',
        '2024-01-21 19:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 19:05:20', '2024-01-21 19:05:19',
        '2024-01-21 19:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 19:05:20', '2024-01-21 19:05:19',
        '2024-01-21 19:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 19:05:20', '2024-01-21 19:05:19',
        '2024-01-21 19:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 21:05:20', '2024-01-21 21:05:19',
        '2024-01-21 21:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 21:05:20', '2024-01-21 21:05:19',
        '2024-01-21 21:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 21:05:20', '2024-01-21 21:05:19',
        '2024-01-21 21:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 21:05:20', '2024-01-21 21:05:19',
        '2024-01-21 21:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 21:05:20', '2024-01-21 21:05:19',
        '2024-01-21 21:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-21 23:05:20', '2024-01-21 23:05:19',
        '2024-01-21 23:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-21 23:05:20', '2024-01-21 23:05:19',
        '2024-01-21 23:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-21 23:05:20', '2024-01-21 23:05:19',
        '2024-01-21 23:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-21 23:05:20', '2024-01-21 23:05:19',
        '2024-01-21 23:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-21 23:05:20', '2024-01-21 23:05:19',
        '2024-01-21 23:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-22 01:05:20', '2024-01-22 01:05:19',
        '2024-01-22 01:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-22 01:05:20', '2024-01-22 01:05:19',
        '2024-01-22 01:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-22 01:05:20', '2024-01-22 01:05:19',
        '2024-01-22 01:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-22 01:05:20', '2024-01-22 01:05:19',
        '2024-01-22 01:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-22 01:05:20', '2024-01-22 01:05:19',
        '2024-01-22 01:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-22 03:05:20', '2024-01-22 03:05:19',
        '2024-01-22 03:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-22 03:05:20', '2024-01-22 03:05:19',
        '2024-01-22 03:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-22 03:05:20', '2024-01-22 03:05:19',
        '2024-01-22 03:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-22 03:05:20', '2024-01-22 03:05:19',
        '2024-01-22 03:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-22 03:05:20', '2024-01-22 03:05:19',
        '2024-01-22 03:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-22 05:05:20', '2024-01-22 05:05:19',
        '2024-01-22 05:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-22 05:05:20', '2024-01-22 05:05:19',
        '2024-01-22 05:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-22 05:05:20', '2024-01-22 05:05:19',
        '2024-01-22 05:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-22 05:05:20', '2024-01-22 05:05:19',
        '2024-01-22 05:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-22 05:05:20', '2024-01-22 05:05:19',
        '2024-01-22 05:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-22 07:05:20', '2024-01-22 07:05:19',
        '2024-01-22 07:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-22 07:05:20', '2024-01-22 07:05:19',
        '2024-01-22 07:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-22 07:05:20', '2024-01-22 07:05:19',
        '2024-01-22 07:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-22 07:05:20', '2024-01-22 07:05:19',
        '2024-01-22 07:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-22 07:05:20', '2024-01-22 07:05:19',
        '2024-01-22 07:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-22 09:05:20', '2024-01-22 09:05:19',
        '2024-01-22 09:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-22 09:05:20', '2024-01-22 09:05:19',
        '2024-01-22 09:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-22 09:05:20', '2024-01-22 09:05:19',
        '2024-01-22 09:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-22 09:05:20', '2024-01-22 09:05:19',
        '2024-01-22 09:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-22 09:05:20', '2024-01-22 09:05:19',
        '2024-01-22 09:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-22 11:05:20', '2024-01-22 11:05:19',
        '2024-01-22 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-22 11:05:20', '2024-01-22 11:05:19',
        '2024-01-22 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-22 11:05:20', '2024-01-22 11:05:19',
        '2024-01-22 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-22 11:05:20', '2024-01-22 11:05:19',
        '2024-01-22 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-22 11:05:20', '2024-01-22 11:05:19',
        '2024-01-22 11:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黑色碳粉', 80, 280549, '2024-01-22 13:05:18', '2024-01-22 13:05:19',
        '2024-01-22 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '废弃碳粉', 100, 69848, '2024-01-22 13:05:18', '2024-01-22 13:05:19',
        '2024-01-22 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '青色碳粉', -2, 69848, '2024-01-22 13:05:18', '2024-01-22 13:05:19',
        '2024-01-22 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '品红色碳粉', 60, 69848, '2024-01-22 13:05:18', '2024-01-22 13:05:19',
        '2024-01-22 13:05:19', 0)
     , (1732762057314492417, 1748232034767130625, '黄色碳粉', 70, 69848, '2024-01-22 13:05:18', '2024-01-22 13:05:19',
        '2024-01-22 13:05:19', 0);


DROP TABLE IF EXISTS `b_iot_state`;

CREATE TABLE `b_iot_state`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id`         bigint(20) DEFAULT NULL COMMENT '客户id',
    `device_group_id`     bigint(20) DEFAULT NULL COMMENT '设备组id',
    `machine_state_code`  bigint(20) DEFAULT NULL COMMENT '设备状态',
    `sc_code`             varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '故障代码',
    `jam_describe`        varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '卡纸描述',
    `state_describe`      varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态描述',
    `num_code`            varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数字代码',
    `report_time`         datetime                         DEFAULT NULL COMMENT '上报时间',
    `black_white_counter` bigint(20) DEFAULT NULL COMMENT '第1色计数器（K）',
    `cyan_counter`        bigint(20) DEFAULT NULL COMMENT '第2色计数器（C）',
    `magenta_counter`     bigint(20) DEFAULT NULL COMMENT '第3色计数器（M）',
    `yellow_counter`      bigint(20) DEFAULT NULL COMMENT '第4色计数器（Y）',
    `fifth_counter`       bigint(20) DEFAULT NULL COMMENT '第5色计数器',
    `sixth_counter`       bigint(20) DEFAULT NULL COMMENT '第6色计数器',
    `seventh_counter`     bigint(20) DEFAULT NULL COMMENT '第7色计数器',
    `eightth_counter`     bigint(20) DEFAULT NULL COMMENT '第8色计数器',
    `ninth_counter`       bigint(20) DEFAULT NULL COMMENT '第9色计数器',
    `tenth_counter`       bigint(20) DEFAULT NULL COMMENT '第10色计数器',
    `eleventh_counter`    bigint(20) DEFAULT NULL COMMENT '第11色计数器',
    `twelfth_counter`     bigint(20) DEFAULT NULL COMMENT '第12色计数器',
    `thirteenth_counter`  bigint(20) DEFAULT NULL COMMENT '第13色计数器',
    `fourteenth_counter`  bigint(20) DEFAULT NULL COMMENT '第14色计数器',
    `fifteenth_counter`   bigint(20) DEFAULT NULL COMMENT '第15色计数器',
    `sixteenth_counter`   bigint(20) DEFAULT NULL COMMENT '第16色计数器',
    `seventeenth_counter` bigint(20) DEFAULT NULL COMMENT '第17色计数器',
    `eightteenth_counter` bigint(20) DEFAULT NULL COMMENT '第18色计数器',
    `created_at`          datetime                         DEFAULT NULL COMMENT '创建时间',
    `updated_at`          datetime                         DEFAULT NULL COMMENT '更新时间',
    `deleted`             tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-状态上报';


INSERT INTO b_iot_state (customer_id, device_group_id, machine_state_code, sc_code, jam_describe, state_describe,
                         num_code, report_time, black_white_counter, cyan_counter, magenta_counter, yellow_counter,
                         fifth_counter, sixth_counter, seventh_counter, eightth_counter, ninth_counter, tenth_counter,
                         eleventh_counter, twelfth_counter, thirteenth_counter, fourteenth_counter, fifteenth_counter,
                         sixteenth_counter, seventeenth_counter, eightteenth_counter, created_at, updated_at, deleted)
VALUES (1730132451806642178, 1746850696201805826, 5, '', '卡纸：输入纸盘 ', '', '42000', '2024-01-15 16:50:17', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-15 16:50:18', '2024-01-15 16:50:18', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '', '独立供应商碳粉 ', '30136', '2024-01-15 16:50:17', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-15 16:50:18', '2024-01-15 16:50:18', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '', '盖打开：ADF ', '240101', '2024-01-15 16:50:17', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-15 16:50:18', '2024-01-15 16:50:18', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '卡纸：输入纸盘 ', '', '42000', '2024-01-17 16:54:43', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-17 16:54:44', '2024-01-17 16:54:44', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '', '独立供应商碳粉 ', '30136', '2024-01-17 16:54:43', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-17 16:54:44', '2024-01-17 16:54:44', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '', '盖打开：ADF ', '240101', '2024-01-17 16:54:43', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-17 16:54:44', '2024-01-17 16:54:44', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '卡纸：输入纸盘 ', '', '42000', '2024-01-17 20:06:29', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-17 20:06:30', '2024-01-17 20:06:30', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '', '独立供应商碳粉 ', '30136', '2024-01-17 20:06:29', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-17 20:06:30', '2024-01-17 20:06:30', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '', '盖打开：ADF ', '240101', '2024-01-17 20:06:29', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-17 20:06:30', '2024-01-17 20:06:30', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '卡纸：输入纸盘 ', '', '42000', '2024-01-19 13:47:08', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 13:47:08', '2024-01-19 13:47:08', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '', '独立供应商碳粉 ', '30136', '2024-01-19 13:47:08', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 13:47:08', '2024-01-19 13:47:08', 0)
     , (1730132451806642178, 1746850696201805826, 5, '', '', '盖打开：ADF ', '240101', '2024-01-19 13:47:08', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 13:47:08', '2024-01-19 13:47:08', 0)
     , (1732762057314492417, 1748232034767130625, 5, '', '卡纸：输入纸盘 ', '', '42000', '2024-01-19 20:38:40', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 20:38:45', '2024-01-19 20:38:45', 0)
     , (1732762057314492417, 1748232034767130625, 5, '', '', '独立供应商碳粉 ', '30136', '2024-01-19 20:38:40', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 20:38:45', '2024-01-19 20:38:45', 0)
     , (1732762057314492417, 1748232034767130625, 5, '', '', '盖打开：ADF ', '240101', '2024-01-19 20:38:40', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 20:38:45', '2024-01-19 20:38:45', 0)
     , (1732762057314492417, 1748232034767130625, 5, '', '卡纸：输入纸盘 ', '', '42000', '2024-01-19 20:57:32', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 20:57:36', '2024-01-19 20:57:36', 0)
     , (1732762057314492417, 1748232034767130625, 5, '', '', '独立供应商碳粉 ', '30136', '2024-01-19 20:57:32', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 20:57:36', '2024-01-19 20:57:36', 0)
     , (1732762057314492417, 1748232034767130625, 5, '', '', '盖打开：ADF ', '240101', '2024-01-19 20:57:32', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 20:57:36', '2024-01-19 20:57:36', 0)
     , (1732762057314492417, 1748232034767130625, 5, '', '卡纸：输入纸盘 ', '', '42000', '2024-01-19 21:04:25', 280549, 69848,
        69848, 69848, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,
        '2024-01-19 21:04:30', '2024-01-19 21:04:30', 0)
;


DROP TABLE IF EXISTS `b_iot_time_config`;

CREATE TABLE `b_iot_time_config`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id`            bigint(20) DEFAULT NULL COMMENT '客户id',
    `upload_counter_time`    varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上报计数器时间（分钟）',
    `upload_exception_time`  varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上报异常时间（分钟）',
    `upload_power_time`      varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '上报粉量时间（分钟）',
    `upload_machineMsg_time` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '查询基本信息时间（分钟）',
    `check_service_time`     varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '检查更新（分钟）',
    `created_at`             datetime                         DEFAULT NULL COMMENT '创建时间',
    `updated_at`             datetime                         DEFAULT NULL COMMENT '更新时间',
    `deleted`                tinyint(1) DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='物联网-定时器时间配置';