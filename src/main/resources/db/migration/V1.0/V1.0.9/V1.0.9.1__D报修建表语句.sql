CREATE TABLE tb_engineer_info
(
    `id`              bigint primary key COMMENT 'id',
    `user_id`         bigint COMMENT '登录表ID',
    `photo`           VARCHAR(150) COMMENT '头像',
    `age`             tinyint COMMENT '年龄',
    `introduce`       VARCHAR(255) COMMENT '工程师介绍',
    `work_his`        VARCHAR(255) COMMENT '从业经历',
    `year_of_service` tinyint COMMENT '从业年限',
    `region_code`     int(8) COMMENT '所属区域',
    `detail_address`  VARCHAR(255) COMMENT '详细地址',
    `created_at`      DATETIME COMMENT '创建时间',
    `updated_at`      DATETIME COMMENT '更新时间',
    `deleted`         tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin  COMMENT = '维修工程师基本信息表';

CREATE TABLE tb_engineer_skill
(
    `id`          BIGINT PRIMARY KEY COMMENT 'id',
    `engineer_id` BIGINT COMMENT '工程师ID',
    `product_id`  BIGINT COMMENT '机型',
    `skill_exp`   VARCHAR(50) COMMENT '熟练度',
    `created_at`  DATETIME COMMENT '创建时间',
    `updated_at`  DATETIME COMMENT '更新时间',
    `deleted`     tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT = '工程师维修能力表';

CREATE TABLE tb_work_order
(
    `id`                   bigint primary key COMMENT 'id',
    `code`                 VARCHAR(50) COMMENT '编码',
    `product_id`           bigint COMMENT '关联产品树id',
    `device_group_id`      bigint COMMENT '登记设备组的ID',
    `customer_id`          bigint COMMENT '客户ID',
    `customer_staff_id`    bigint COMMENT '报修人ID',
    `status`               VARCHAR(50) COMMENT '工单状态',
    `error_code`           VARCHAR(50) COMMENT '故障码',
    `exc_report_time`      DATETIME COMMENT '上报时间',
    `exc_desc`             VARCHAR(255) COMMENT '问题描述',
    `exc_pics`             json COMMENT '故障图片',
    `expect_arrive_time`   datetime COMMENT '期望到店时间',
    `order_receive_time`   datetime COMMENT '接单时间',
    `prospect_arrive_time` datetime COMMENT '预计到到时间',
    `departure_time`       datetime COMMENT '工程师出发时间',
    `actual_arrive_time`   datetime COMMENT '实际到达时间',
    `repair_pay`           int COMMENT '维修费用',
    `visit_pay`            int COMMENT '上门费用',
    `discount_amount`      int COMMENT '折扣费用',
    `additional_pay`       int COMMENT '追加费用',
    `total_amount`            int COMMENT '总支付',
    `is_assign_engineer`   tinyint(1) COMMENT '是否指定工程师',
    `engineer_id`          bigint COMMENT '工程师ID',
    `black_white_count`    bigint COMMENT '黑白计数器',
    `color_count`          bigint COMMENT '彩色计数器',
    `fifth_count`          bigint COMMENT '第五计数器',
    `created_at`           DATETIME COMMENT '创建时间',
    `updated_at`           DATETIME COMMENT '更新时间',
    `deleted`              tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT = '工单信息表';


CREATE TABLE tb_repair_report
(
    `id`                bigint primary key COMMENT '',
    `work_order_id`     bigint COMMENT '工单ID',
    `work_order_code`   VARCHAR(50) COMMENT '工单编码',
    `device_group_id`   bigint COMMENT '登记设备组的ID',
    `customer_id`       bigint COMMENT '客户ID',
    `engineer_id`       bigint COMMENT '工程师ID',
    `exc_type`          VARCHAR(50) COMMENT '问题类型',
    `reason_type`       VARCHAR(50) COMMENT '原因分类',
    `resolve_type`      VARCHAR(50) COMMENT '处理类型',
    `exc_unit`          VARCHAR(50) COMMENT '故障单元',
    `black_white_count` bigint COMMENT '黑白计数器',
    `color_count`       bigint COMMENT '彩色计数器',
    `fifth_count`       bigint COMMENT '第五计数器',
    `exc_desc`          VARCHAR(255) COMMENT '原因描述',
    `resolve_desc`      text COMMENT '解决措施',
    `resolve_process`   VARCHAR(255) COMMENT '处理过程',
    `finish_images`     json COMMENT '维修结果图片',
    `finish_time`       DATETIME COMMENT '维修完成时间',
    `created_at`        DATETIME COMMENT '创建时间',
    `updated_at`        DATETIME COMMENT '更新时间',
    `deleted`           tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT = '维修报告';

CREATE TABLE tb_appeal
(
    `id`              bigint primary key COMMENT '',
    `code`            VARCHAR(50) COMMENT '申诉单编号',
    `status`          VARCHAR(50) COMMENT '申诉状态',
    `work_order_id`   bigint COMMENT '工单ID',
    `work_order_code` VARCHAR(50) COMMENT '工单号',
    `customer_id`     bigint COMMENT '客户ID',
    `desc`            VARCHAR(255) COMMENT '描述',
    `pic_urls`        json COMMENT '图片',
    `created_at`      DATETIME COMMENT '创建时间',
    `updated_at`      DATETIME COMMENT '更新时间',
    `deleted`         tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT = '申诉表;';

CREATE TABLE tb_repair_process
(
    `id`              bigint primary key  COMMENT '',
    `work_order_id`   bigint COMMENT '工单ID',
    `work_order_code` VARCHAR(50) COMMENT '工单编码',
    `current_node`    VARCHAR(100) COMMENT '当前进度',
    `created_at`      DATETIME COMMENT '创建时间',
    `updated_at`      DATETIME COMMENT '更新时间',
    `deleted`         tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT = '维修进度表;';

CREATE TABLE tb_replace_order
(
    `id`                bigint primary key COMMENT '',
    `code`              VARCHAR(255) COMMENT '',
    `device_group_id`   bigint COMMENT '登记机器表的ID',
    `customer_id`       bigint COMMENT '客户id',
    `customer_staff_id` bigint COMMENT '报修人id',
    `replace_type`      tinyint(1) COMMENT '自修/上门',
    `work_order_id`     bigint COMMENT '工单ID',
    `work_order_code`   VARCHAR(50) COMMENT '工单编号',
    `engineer_id`       bigint COMMENT '工程师ID',
    `created_at`        DATETIME COMMENT '创建时间',
    `updated_at`        DATETIME COMMENT '更新时间',
    `deleted`           tinyint(1) COMMENT '是否删除'
) COMMENT = '设备换件单;';

CREATE TABLE tb_replace_detail
(
    `id`                 bigint primary key COMMENT '',
    `replace_order_id`   bigint COMMENT '换件单ID',
    `replace_order_code` VARCHAR(50) COMMENT '换件单编码',
    `sku_source`         VARCHAR(50) COMMENT '配件来源',
    `sku_info`           TEXT COMMENT '商品快照',
    `num`                INT COMMENT '数量',
    `is_pm`              tinyint(1) COMMENT '是否是PM件',
    `created_at`         DATETIME COMMENT '创建时间',
    `updated_at`         DATETIME COMMENT '更新时间',
    `deleted`            tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin  COMMENT = '设备换件明细;';


CREATE TABLE tb_item_store
(
    `id`         bigint NOT NULL COMMENT '',
    `user_id`    bigint COMMENT '工程师/客户ID',
    `user_type`  VARCHAR(50) COMMENT '用户类型，工程师/客户',
    `sku_source` VARCHAR(50) COMMENT '来源；商城购入，其他渠道购入',
    `sku_info`   TEXT COMMENT 'sku快照',
    `num`        INT COMMENT '数量',
    `created_at` DATETIME COMMENT '创建时间',
    `updated_at` DATETIME COMMENT '更新时间',
    `deleted`    tinyint(1) COMMENT '是否删除',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin  COMMENT = '耗材零件仓库表;';

CREATE TABLE tb_apply_order
(
    `id`            bigint primary key COMMENT '',
    `code`          VARCHAR(50) COMMENT '领料单编号',
    `engineer_id`   bigint COMMENT '工程师ID',
    `work_order_id` bigint COMMENT '工单ID',
    `status`        VARCHAR(50) COMMENT '状态',
    `created_at`    DATETIME COMMENT '创建时间',
    `updated_at`    DATETIME COMMENT '更新时间',
    `deleted`       tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin  COMMENT = '领料单;';

CREATE TABLE tb_apply_detail
(
    `id`               bigint primary key COMMENT '',
    `code`             VARCHAR(50) COMMENT '明细编号',
    `apply_order_id`   bigint COMMENT '领料单ID',
    `apply_order_code` VARCHAR(50) COMMENT '领料单编码',
    `sku_info`         TEXT COMMENT '商品快照',
    `num`              INT COMMENT '',
    `created_at`       DATETIME COMMENT '创建时间',
    `updated_at`       DATETIME COMMENT '更新时间',
    `deleted`          tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin  COMMENT = '领料单明细;';

CREATE TABLE tb_item_store_log
(
    `id`            bigint primary key COMMENT '',
    `item_store_id` bigint COMMENT '耗材仓库表ID',
    `user_id`       bigint COMMENT '操作人Id',
    `user_type`     VARCHAR(50) COMMENT '工程师｜图文店老板',
    `operate_type`  VARCHAR(50) COMMENT '操作类型，领入｜使用',
    `created_at`    DATETIME COMMENT '创建时间',
    `updated_at`    DATETIME COMMENT '更新时间',
    `deleted`       tinyint(1) COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin  COMMENT = '耗材仓库日志表;';