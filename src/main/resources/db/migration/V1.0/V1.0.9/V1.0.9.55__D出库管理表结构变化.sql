ALTER TABLE b_storage_out_warehouse MODIFY COLUMN out_warehouse_number int(11) NULL COMMENT '应出库(出库单总量)';
ALTER TABLE b_storage_out_warehouse ADD auidt_out_warehouse_number int(11) NULL COMMENT '出库数量（审核出库数量）';
ALTER TABLE b_storage_out_warehouse CHANGE auidt_out_warehouse_number auidt_out_warehouse_number int(11) NULL COMMENT '出库数量（审核出库数量）' AFTER out_warehouse_number;

RENAME TABLE b_storage_out_warehouse_goods TO b_storage_out_warehouse_goods_back;


RENAME TABLE b_storage_out_warehouse_item TO b_storage_out_warehouse_goods;

ALTER TABLE b_storage_out_warehouse_goods CHANGE item_num out_warehouse_number int(10) NULL COMMENT '购买数量(应出库量)';
ALTER TABLE b_storage_out_warehouse_goods ADD auidt_out_warehouse_number int(10) NULL COMMENT '已出库量（已审核出库数量）';
ALTER TABLE b_storage_out_warehouse_goods CHANGE auidt_out_warehouse_number auidt_out_warehouse_number int(10) NULL COMMENT '已出库量（已审核出库数量）' AFTER out_warehouse_number;

