ALTER TABLE `b_storage_invoice_item`
    ADD COLUMN `expected_number` INT COMMENT '发货数量' AFTER `associated_order_number`;

UPDATE `b_storage_invoice_item` `bsii`
SET `bsii`.`expected_number` = (SELECT SUM(`expected_number`)
                                FROM `b_storage_invoice_item_detail` `bsiid`
                                WHERE `bsiid`.`invoice_item_id` = `bsii`.`id`)
WHERE `bsii`.`expected_number` IS NULL;
