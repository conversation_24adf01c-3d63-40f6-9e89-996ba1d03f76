create table tb_reverse_order_detail
(
    id bigint not null primary key ,
    code varchar(50) null comment '编号',
    reverse_order_id bigint comment '售后单id',
    reverse_order_code bigint comment '售后单号',
    trade_order_id bigint comment '主订单id',
    trade_order_num bigint comment '订单编号',
    trade_order_detail_id bigint comment '订单明细id',
    trade_order_detail_num bigint comment '订单明细编号',
    reverse_type varchar(50) null comment '售后类型',
    item_id bigint comment  '商品id',
    itemName varchar(256) null comment '商品名称',
    sale_sku_id bigint comment  '销售SKUid',
    item_num int not null comment '购买数量',
    inv_sku_id bigint comment  '库存SKU id',
    actual_unit_price bigint comment '成交单价',
    pay_amount bigint comment '应付金额',
    discountAmount bigint comment '优惠金额',
    refund_amount bigint comment '退款金额',
    refund_unit_price bigint comment '退款单价',
    reverse_item_num int not null comment '退款退货数量',
    actual_refund_amount bigint comment '实退总额',
    refund_status varchar(50) null comment '退款状态',
    return_status varchar(50) null comment '退货状态',
    refund_order_id bigint null comment '退款单Id',
    refund_order_code varchar(50) null comment '退款单编码',
    return_order_id bigint null comment '退货单ID'
) comment '售后明细';