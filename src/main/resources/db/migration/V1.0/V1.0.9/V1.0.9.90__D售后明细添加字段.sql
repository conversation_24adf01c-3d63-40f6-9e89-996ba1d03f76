alter table b_reverse_order
    add column code varchar(50) comment '售后单编号' after id;
alter table b_reverse_order
    add column pic_url json comment '申请图片' after reason;

alter table b_reverse_order
    add column refund_amount bigint comment '应退金额' after apply_time;
alter table b_reverse_order
    add column actual_refund_amount bigint comment '实退总额'  after refund_amount;
alter table b_reverse_order
    add column refund_shipping_fee bigint comment '实退运费' after actual_refund_amount;

alter table b_reverse_order add column process_status varchar(100) default 'PENDING_AUDIT'
    comment '售后单状态' after reverse_type;

alter table b_reverse_refund_order add column reverse_order_code varchar(50) comment '售后单号' after id;

alter table b_reverse_refund_order add column  trade_order_num varchar(50) comment '订单号' after trade_order_id;



alter table b_reverse_return_order add column reverse_order_code varchar(50) comment '售后单编号' after reverse_order_id;
alter table b_reverse_return_order add column code varchar(50) comment '售后单编号' after id;


create table tb_return_detail_order(
                                       id bigint not null primary key ,
                                       code varchar(50) null comment '编号',
                                       return_order_id bigint comment '退货单ID',
                                       return_order_code varchar(50) comment '退货单编号',
                                       reverse_order_id bigint comment '退售后单ID',
                                       reverse_order_code varchar(50) comment '售后单号',
                                       trade_order_id bigint comment '主订单id',
                                       trade_order_num varchar(50) comment '订单编号',
                                       trade_order_detail_id bigint comment '订单明细',
                                       trade_order_detail_num varchar(50) comment '订单明细编号',
                                       item_id                bigint       null comment '商品id',
                                       itemName               varchar(256) null comment '商品名称',
                                       sale_sku_id            bigint       null comment '销售SKUid',
                                       inv_sku_id             bigint       null comment '库存SKU id',
                                       reverse_item_num        int          not null comment '退回数量',
                                       created_at      datetime             null comment '创建时间',
                                       updated_at      datetime             null comment '更新时间',
                                       deleted         tinyint(1) default 0 null
);

alter table b_reverse_return_order add column tradeOrderCode varchar(50) comment '交易订单编号' after trade_order_id;