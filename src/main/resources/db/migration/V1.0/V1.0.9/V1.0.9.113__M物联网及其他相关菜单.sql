-- 更新工程师端小程序菜单 移动端功能排序乘10 便于调整功能顺序
UPDATE `st_resource`
SET `id`   = 6000,
    `sort` = 0
WHERE `id` = 2080;
UPDATE `st_resource`
SET `id`   = 6001,
    `sort` = 10
WHERE `id` = 2090;
UPDATE `st_resource`
SET `id`   = 6002,
    `sort` = 20
WHERE `id` = 2100;
UPDATE `st_resource`
SET `id`   = 6003,
    `sort` = 30
WHERE `id` = 2110;
UPDATE `st_resource`
SET `id`   = 6004,
    `sort` = 40
WHERE `id` = 2120;
UPDATE `st_resource`
SET `id`   = 6005,
    `sort` = 50
WHERE `id` = 2130;

-- 更新权限
DELETE
FROM `st_role_resource`
WHERE `resource_id` IN (2080, 2090, 2100, 2110, 2120, 2130)
  AND `role_id` = 1002;


REPLACE INTO `st_resource` (`id`, `parent_id`, `label`, `value`, `type`, `permit`, `icon`, `is_enable`, `sort`,
                            `is_build_in`, `terminal`)
VALUES (2080, 0, '物联网', '/IOT', 'directory', NULL, 'icon-connection', 1, 8, 1, 'pc'),
       (208001, 2080, '状态上报', '/status', 'menu', NULL, NULL, 1, 1, 1, 'pc'),
       (208002, 2080, '粉量上报', '/powder', 'menu', NULL, NULL, 1, 2, 1, 'pc'),
       (208003, 2080, '计数器上报', '/counter', 'menu', NULL, NULL, 1, 3, 1, 'pc'),
       (208004, 2080, '软件信息表', '/software', 'menu', NULL, NULL, 1, 4, 1, 'pc'),
       (208005, 2080, 'oid配置表', '/oid', 'menu', NULL, NULL, 1, 5, 0, 'pc'),
       (208006, 2080, '取值配置接口', '/allocation', 'menu', NULL, NULL, 1, 6, 1, 'pc'),
       (208007, 2080, '数字代码记录', '/code', 'menu', NULL, NULL, 1, 7, 1, 'pc'),
       (208008, 2080, '异常日志', '/abnormal', 'menu', NULL, NULL, 1, 8, 1, 'pc'),
       (202006, 2020, '发货单', '/sendgoods', 'menu', NULL, NULL, 1, 5, 1, 'pc'),
       (207005, 2070, '领料单', '/receive', 'menu', NULL, NULL, 1, 5, 1, 'pc'),
       (207006, 2070, '退料单', '/reback', 'menu', NULL, NULL, 1, 6, 1, 'pc'),
       (6006, 0, '退料', '/returnApply', 'menu', NULL, NULL, 1, 60, 1, 'staff-mini-program');

-- 添加管理员权限
REPLACE INTO `st_role_resource` (`role_id`, `resource_id`)
VALUES (1001, 2080),
       (1001, 208001),
       (1001, 208002),
       (1001, 208003),
       (1001, 208004),
       (1001, 208005),
       (1001, 208006),
       (1001, 208007),
       (1001, 208008),
       (1001, 202006),
       (1001, 207005),
       (1001, 207006);

-- 添加工程师员权限
REPLACE INTO `st_role_resource` (`role_id`, `resource_id`)
VALUES (1002, 6000),
       (1002, 6001),
       (1002, 6002),
       (1002, 6003),
       (1002, 6004),
       (1002, 6005),
       (1002, 6006);
