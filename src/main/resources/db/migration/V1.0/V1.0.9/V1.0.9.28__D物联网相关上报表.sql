CREATE TABLE `b_iot_counter`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `machine_number`      varchar(255) NULL COMMENT '机器编号',
    `customer_id`         bigint(20) NULL COMMENT '客户id',
    `device_group_id`     bigint(20) NULL COMMENT '设备组id',
    `report_time`         datetime(0) NULL COMMENT '上报时间',
    `black_white_counter` bigint(20) NULL COMMENT '第1色计数器（K）',
    `cyan_counter`        bigint(20) NULL COMMENT '第2色计数器（C）',
    `magenta_counter`     bigint(20) NULL COMMENT '第3色计数器（M）',
    `yellow_counter`      bigint(20) NULL COMMENT '第4色计数器（Y）',
    `fifth_counter`       bigint(20) NULL COMMENT '第5色计数器',
    `sixth_counter`       bigint(20) NULL COMMENT '第6色计数器',
    `seventh_counter`     bigint(20) NULL COMMENT '第7色计数器',
    `eightth_counter`     bigint(20) NULL COMMENT '第8色计数器',
    `ninth_counter`       bigint(20) NULL COMMENT '第9色计数器',
    `tenth_counter`       bigint(20) NULL COMMENT '第10色计数器',
    `eleventh_counter`    bigint(20) NULL COMMENT '第11色计数器',
    `twelfth_counter`     bigint(20) NULL COMMENT '第12色计数器',
    `thirteenth_counter`  bigint(20) NULL COMMENT '第13色计数器',
    `fourteenth_counter`  bigint(20) NULL COMMENT '第14色计数器',
    `fifteenth_counter`   bigint(20) NULL COMMENT '第15色计数器',
    `sixteenth_counter`   bigint(20) NULL COMMENT '第16色计数器',
    `seventeenth_counter` bigint(20) NULL COMMENT '第17色计数器',
    `eightteenth_counter` bigint(20) NULL COMMENT '第18色计数器',
    `created_at`          datetime(0) NULL COMMENT '创建时间',
    `updated_at`          datetime(0) NULL COMMENT '更新时间',
    `deleted`             tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-计数器上报';


CREATE TABLE `b_iot_state`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `machine_number`      varchar(255) NULL COMMENT '机器编号',
    `customer_id`         bigint(20) NULL COMMENT '客户id',
    `device_group_id`     bigint(20) NULL COMMENT '设备组id',
    `machine_state_code`  bigint(20) NULL COMMENT '设备状态',
    `sc_code`             varchar(255) NULL COMMENT '故障代码',
    `jam_describe`        varchar(255) NULL COMMENT '卡纸描述',
    `state_describe`      varchar(255) NULL COMMENT '状态描述',
    `num_code`            varchar(255) NULL COMMENT '数字代码',
    `report_time`         datetime(0) NULL COMMENT '上报时间',
    `black_white_counter` bigint(20) NULL COMMENT '第1色计数器（K）',
    `cyan_counter`        bigint(20) NULL COMMENT '第2色计数器（C）',
    `magenta_counter`     bigint(20) NULL COMMENT '第3色计数器（M）',
    `yellow_counter`      bigint(20) NULL COMMENT '第4色计数器（Y）',
    `fifth_counter`       bigint(20) NULL COMMENT '第5色计数器',
    `sixth_counter`       bigint(20) NULL COMMENT '第6色计数器',
    `seventh_counter`     bigint(20) NULL COMMENT '第7色计数器',
    `eightth_counter`     bigint(20) NULL COMMENT '第8色计数器',
    `ninth_counter`       bigint(20) NULL COMMENT '第9色计数器',
    `tenth_counter`       bigint(20) NULL COMMENT '第10色计数器',
    `eleventh_counter`    bigint(20) NULL COMMENT '第11色计数器',
    `twelfth_counter`     bigint(20) NULL COMMENT '第12色计数器',
    `thirteenth_counter`  bigint(20) NULL COMMENT '第13色计数器',
    `fourteenth_counter`  bigint(20) NULL COMMENT '第14色计数器',
    `fifteenth_counter`   bigint(20) NULL COMMENT '第15色计数器',
    `sixteenth_counter`   bigint(20) NULL COMMENT '第16色计数器',
    `seventeenth_counter` bigint(20) NULL COMMENT '第17色计数器',
    `eightteenth_counter` bigint(20) NULL COMMENT '第18色计数器',
    `created_at`          datetime(0) NULL COMMENT '创建时间',
    `updated_at`          datetime(0) NULL COMMENT '更新时间',
    `deleted`             tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-状态上报';


CREATE TABLE `b_iot_powder`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `machine_number`   varchar(255) NULL COMMENT '机器编号',
    `customer_id`      bigint(20) NULL COMMENT '客户id',
    `device_group_id`  bigint(20) NULL COMMENT '设备组id',
    `toneybottle_name` varchar(255) NULL COMMENT '粉瓶',
    `toney_num`        bigint(20) NULL COMMENT '粉量',
    `counter`          bigint(20) NULL COMMENT '计数器',
    `report_time`      datetime(0) NULL COMMENT '上报时间',
    `created_at`       datetime(0) NULL COMMENT '创建时间',
    `updated_at`       datetime(0) NULL COMMENT '更新时间',
    `deleted`          tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-粉量上报';


CREATE TABLE `b_iot_numeric_code`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `machine_number`  varchar(255) NULL COMMENT '机器编号',
    `customer_id`     bigint(20) NULL COMMENT '客户id',
    `device_group_id` bigint(20) NULL COMMENT '设备组id',
    `num_code`        varchar(255) NULL COMMENT '数字代码',
    `brand`           varchar(255) NULL COMMENT '品牌',
    `series`          varchar(255) NULL COMMENT '系列',
    `num_data`        varchar(255) NULL COMMENT '抓取的数据',
    `state`           varchar(255) NULL COMMENT '过滤状态',
    `data_describe`   varchar(255) NULL COMMENT '中文描述',
    `fault_code`      varchar(255) NULL COMMENT '故障代码',
    `paper_code`      varchar(255) NULL COMMENT '卡纸代码',
    `fault_describe`  varchar(255) NULL COMMENT '故障手册中的描述',
    `created_at`      datetime(0) NULL COMMENT '创建时间',
    `updated_at`      datetime(0) NULL COMMENT '更新时间',
    `deleted`         tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-数字代码记录';


CREATE TABLE `b_iot_oid_config`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `oid`        varchar(255) NULL COMMENT 'Oid',
    `oid_name`   varchar(255) NULL COMMENT 'Oid名称',
    `brand`      varchar(255) NULL COMMENT '品牌',
    `mode`       varchar(255) NULL COMMENT '型号',
    `sort`       bigint(20) NULL COMMENT '排序值',
    `created_at` datetime(0) NULL COMMENT '创建时间',
    `updated_at` datetime(0) NULL COMMENT '更新时间',
    `deleted`    tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-oid配置表';


CREATE TABLE `b_iot_configure`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `brand`      varchar(255) NULL COMMENT '品牌',
    `item`       varchar(255) NULL COMMENT '项',
    `value`      varchar(255) NULL COMMENT '匹配值',
    `way`        varchar(255) NULL COMMENT '方式（精确、模糊）',
    `remark`     varchar(255) NULL COMMENT '备注',
    `created_at` datetime(0) NULL COMMENT '创建时间',
    `updated_at` datetime(0) NULL COMMENT '更新时间',
    `deleted`    tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-取值配置';


CREATE TABLE `b_iot_exception_log`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id` bigint(20) NULL COMMENT '客户id',
    `ip_address`  varchar(255) NULL COMMENT 'IP地址',
    `port`        varchar(255) NULL COMMENT '端口',
    `describe`    varchar(255) NULL COMMENT '异常描述',
    `brand`       varchar(255) NULL COMMENT '品牌',
    `mode`        varchar(255) NULL COMMENT '机型',
    `created_at`  datetime(0) NULL COMMENT '创建时间',
    `updated_at`  datetime(0) NULL COMMENT '更新时间',
    `deleted`     tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-异常日志';