DROP TABLE IF EXISTS `b_waybill`;
CREATE TABLE `b_waybill`
(
    `id`                      BIGINT(20) PRIMARY KEY COMMENT '运货单id',
    `message_id`              VARCHAR(36) COMMENT 'api消息id',
    `provider`                VARCHAR(16) COMMENT '物流商',
    `provider_waybill_number` VARCHAR(64) COMMENT '物流商运单号',
    `sender_name`             VARCHAR(128) COMMENT '发货人姓名',
    `sender_company`          VARCHAR(128) COMMENT '发货人公司',
    `sender_mobile`           VARCHAR(64) COMMENT '发货人电话',
    `sender_address`          VARCHAR(255) COMMENT '发货人地址',
    `sender_address_code`     INT(11) COMMENT '发货人地址地区编码',
    `sender_latitude`         DECIMAL(12, 8) COMMENT '发货人纬度',
    `sender_longitude`        DECIMAL(12, 8) COMMENT '发货人经度',
    `receiver_name`           VARCHAR(128) COMMENT '收货人姓名',
    `receiver_company`        VARCHAR(128) COMMENT '收货人公司',
    `receiver_mobile`         VARCHAR(64) COMMENT '收货人电话',
    `receiver_address`        VARCHAR(255) COMMENT '收货人地址',
    `receiver_address_code`   INT(11) COMMENT '收货人地址地区编码',
    `receiver_latitude`       DECIMAL(12, 8) COMMENT '收货人纬度',
    `receiver_longitude`      DECIMAL(12, 8) COMMENT '收货人经度',
    `amount`                  BIGINT(20) COMMENT '下单估算费用',
    `status`                  VARCHAR(16) COMMENT '己方运货单状态',
    `extra_data`              JSON COMMENT '额外数据',
    `created_at`              DATETIME(5) COMMENT '运单创建时间',
    `started_at`              DATETIME(5) COMMENT '运单开始时间',
    `finished_at`             DATETIME(5) COMMENT '运单结束时间'
) COMMENT '运单';

DROP TABLE IF EXISTS `b_waybill_trace`;
CREATE TABLE `b_waybill_trace`
(
    `id`               BIGINT(20) PRIMARY KEY COMMENT '轨迹id',
    `waybill_id`       BIGINT(20) NOT NULL COMMENT '运单id',
    `status`           VARCHAR(16) COMMENT '运单状态',
    `provider_status`  VARCHAR(32) COMMENT '物流商运单状态',
    `operation_remark` VARCHAR(255) COMMENT '轨迹变更描述',
    `operated_at`      DATETIME(5) COMMENT '物流商轨迹变更时间',
    `created_at`       DATETIME(5) COMMENT '轨迹创建时间',
    KEY (`waybill_id`, `operated_at`)
) COMMENT '运单轨迹';
