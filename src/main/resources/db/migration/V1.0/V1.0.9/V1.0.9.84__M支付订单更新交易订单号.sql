UPDATE `b_pay_order` `bpo`
SET `bpo`.`trade_order_number` = CASE `bpo`.`trade_order_origin`
                                     WHEN 'REPAIR_ORDER'
                                         THEN (SELECT `code` FROM `tb_work_order` WHERE `id` = `bpo`.`trade_order_id`)
                                     ELSE
                                         (SELECT `order_num` FROM `tb_trade_order` WHERE `id` = `bpo`.`trade_order_id`)
                                 END
WHERE `bpo`.`trade_order_number` IS NULL
