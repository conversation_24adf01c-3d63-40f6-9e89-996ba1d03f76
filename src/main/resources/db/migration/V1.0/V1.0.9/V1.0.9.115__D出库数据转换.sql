-- 主表
replace into b_storage_out_warehouse
(out_warehouse_id, out_type, warehouse_id, main_waybill, secondly_waybill, shop_waybill, logistics_type, remarks, out_status, out_warehouse_number, auidt_out_warehouse_number, created_at, updated_at, deleted)
select
    t1.out_warehouse_id, t1.out_type, t1.warehouse_id, t1.main_waybill, t1.secondly_waybill,
    t1.shop_waybill, null as logistics_type , t1.remarks, t1.out_status, t1.out_warehouse_number,
    sum(t2.item_num ) as auidt_out_warehouse_number ,t1.created_at, t1.updated_at, t1.deleted
from  b_storage_out_warehouse_back t1
left join b_storage_out_warehouse_item_back t2 on t1.out_warehouse_id  = t2.out_warehouse_id
group by out_warehouse_id ;


replace into b_storage_out_warehouse_goods
(out_warehouse_id, warehouse_id, out_warehouse_number, auidt_out_warehouse_number, inventory_id, operator_id, created_at, updated_at, deleted)
select
    a1.out_warehouse_id, a1.warehouse_id,
    ifnull(( select t1.out_warehouse_number from b_storage_out_warehouse_goods_back t1 where a2.code = t1.code and a1.out_warehouse_id = t1.out_warehouse_id group by t1.code ),0) as out_warehouse_number ,
    a1.item_num as auidt_out_warehouse_number , a1.inventory_id, null as operator_id,
    a1.created_at,  a1.updated_at,  a1.deleted
from b_storage_out_warehouse_item_back a1
join b_storage_inventory a2 on a1.inventory_id  = a2.id ;




