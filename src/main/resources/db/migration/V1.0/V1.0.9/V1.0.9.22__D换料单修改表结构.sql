alter table tb_item_store
    add column oem_number varchar(50) null comment 'oem 编号' after user_type,
    add column item_id  bigint null comment '商品id' after oem_number,
    add column item_name  varchar(256) null comment '商品名称' after item_id,
    add column sale_sku_id  bigint null comment 'sku id' after item_name;


alter table tb_replace_detail drop column sku_source;
alter table tb_replace_detail
    add column item_store_id bigint comment '耗材仓库ID' after replace_order_code,
    add column item_id  bigint null comment '商品id' after item_store_id,
    add column item_name  varchar(256) null comment '商品名称' after item_id,
    add column sale_sku_id  bigint null comment 'sku id' after item_name;