ALTER TABLE b_storage_out_warehouse RENAME TO b_storage_out_warehouse_back;
ALTER TABLE b_storage_out_warehouse_goods RENAME TO b_storage_out_warehouse_item_back;

CREATE TABLE `b_storage_out_warehouse` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `out_warehouse_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '[CKID（出库）+年份后两位+月份+日期+六位序列数字（不重复）)]',
    `out_type` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出库类型（枚举）',
    `warehouse_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出库仓库id',
    `main_waybill` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联物流主单号',
    `secondly_waybill` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联物流子单号',
    `shop_waybill` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '关联商城订单号',
    `logistics_type` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '配送方式',
    `remarks` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注信息',
    `out_status` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出库状态（枚举）',
    `out_warehouse_number` int(11) DEFAULT NULL COMMENT '应出库(出库单总量)',
    `auidt_out_warehouse_number` int(11) DEFAULT '0' COMMENT '出库数量（审核出库数量）',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `out_warehouse_id` (`out_warehouse_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='出库单';

CREATE TABLE `b_storage_out_warehouse_goods` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `out_warehouse_id` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '出库单编号[[CKID（出库）+年份后两位+月份+日期+六位序列数字（不重复）)]]',
    `warehouse_id` bigint(20) DEFAULT NULL COMMENT '仓库id',
    `trade_order_id` bigint(20) DEFAULT NULL COMMENT '订单id',
    `trade_order_detail_id` bigint(20) DEFAULT NULL COMMENT '订单详情id',
    `trade_order_code` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '订单编号[XSDD（销售订单）+客户ID的后三位+年份后两位+月日时分秒+3位序列数字（不重复）]',
    `item_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商品名称',
    `out_warehouse_number` int(10) DEFAULT NULL COMMENT '购买数量(应出库量)',
    `auidt_out_warehouse_number` int(10) DEFAULT '0' COMMENT '已出库量（已审核出库数量）',
    `inventory_id` bigint(20) DEFAULT NULL COMMENT '库品id(库存SKU id)',
    `operator_id` int(10) DEFAULT NULL COMMENT '操作人ID',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `out_warehouse_id` (`out_warehouse_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='出库单明细';