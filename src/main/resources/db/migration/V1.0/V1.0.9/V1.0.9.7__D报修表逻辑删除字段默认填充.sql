alter table `tb_engineer_info`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_work_order`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_repair_report`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_appeal`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_repair_process`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_replace_order`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_replace_detail`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_item_store`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_apply_order`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_apply_detail`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_item_store_log`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_repair_price`
    modify column `deleted` tinyint(1) DEFAULT '0';
alter table `tb_visit_price`
    modify column `deleted` tinyint(1) DEFAULT '0';