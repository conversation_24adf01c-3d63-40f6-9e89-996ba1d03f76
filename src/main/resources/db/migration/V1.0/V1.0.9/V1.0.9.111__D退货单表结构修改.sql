
update b_reverse_order set code = reverse_order_id where reverse_order_id is not null ;
update b_reverse_order set trade_order_num = seq_id where seq_id is not null ;
update b_reverse_order set reverse_type = 'REFUND_MONEY' where reverse_type = 2701;
update b_reverse_order set reverse_type = 'RETURN_MONEY_GOODS' where reverse_type = 2702;
update b_reverse_order set audit_status = 'APPROVE' where audit_status = '2804';
update b_reverse_order set audit_status = null where audit_status = '2805';
update b_reverse_order set process_status = 'SUCCESS' where audit_status = '2804' and created_at<'2024-01-27 11:18:13';
update b_reverse_order set process_status = 'CLOSED' where audit_status = '2805' and created_at<'2024-01-27 11:18:13';
update b_reverse_order a ,tb_trade_order b
set a.refund_amount = b.actual_goods_amount,
    actual_refund_amount = b.paid_amount ,
    refund_shipping_fee = b.shipping_fee,
    a.shipping_fee = b.shipping_fee
where a.trade_order_id = b.id
  and a.process_status = 'SUCCESS';
update b_reverse_order a ,tb_trade_order b
set a.refund_amount = b.actual_goods_amount,
    a.shipping_fee = b.shipping_fee
where a.trade_order_id = b.id
  and a.process_status = 'CLOSED';



update b_reverse_return_order set reverse_order_code = reverse_order_id;
update b_reverse_return_order set reverse_order_id =null;
alter table b_reverse_return_order modify column reverse_order_id bigint comment '售后单ID';
update b_reverse_return_order set trade_order_num = seq_id;

update b_reverse_refund_order set reverse_order_code = reverse_order_id;
update b_reverse_refund_order set reverse_order_id =null;
alter table b_reverse_refund_order modify column reverse_order_id bigint comment '售后单ID';
update b_reverse_refund_order set trade_order_num = seq_id;

