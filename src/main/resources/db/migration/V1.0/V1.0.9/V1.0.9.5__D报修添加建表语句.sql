create table tb_repair_price
(
    id             bigint primary key,
    product_id     bigint not null comment '机型ID',
    visit_price    bigint comment '基础上门费用',
    normal_price   bigint comment '普通维修价格',
    discount_price bigint comment '客户端折扣价格',
    vip_price      bigint comment 'vip折扣价格',
    audit_status   varchar(50) comment '审核状态',
    audit_by       bigint comment '审核人',
    audit_time     datetime comment '审核时间',
    status        tinyint(1) default 1 comment '数据状态;0:禁用，1:启用',
    `created_at`   DATETIME COMMENT '创建时间',
    `updated_at`   DATETIME COMMENT '更新时间',
    `deleted`      tinyint(1) COMMENT '是否删除'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin  COMMENT = '维修报价表';

create table tb_visit_price
(
    id            bigint primary key,
    start_region  int(8) comment '出发城市',
    arrive_region int(8) comment '到达城市',
    price         bigint comment '远程上门费',
    audit_by      bigint comment '审核人',
    audit_time    datetime comment '审核时间',
    status        tinyint(1) default 1 comment '数据状态;0:禁用，1:启用',
    `created_at`  DATETIME COMMENT '创建时间',
    `updated_at`  DATETIME COMMENT '更新时间',
    `deleted`     tinyint(1) COMMENT '是否删除'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin  COMMENT = '远程费价格表';