create table tb_apply_return
(
    id            bigint               not null
        primary key,
    code          varchar(50)          null comment '退料单编号',
    engineer_id   bigint               null comment '工程师ID',
    status        varchar(50)          null comment '状态',
    created_at    datetime             null comment '创建时间',
    updated_at    datetime             null comment '更新时间',
    deleted       tinyint(1) default 0 null
)
    comment '退料单;';

create table tb_apply_return_detail
(
    id               bigint               not null
        primary key,
    code             varchar(50)          null comment '明细编号',
    apply_return_id   bigint               null comment '退料单ID',
    apply_return_code varchar(50)          null comment '退料编码',
    item_store_id    bigint               null comment '耗材仓库ID',
    num              int                  null,
    created_at       datetime             null comment '创建时间',
    updated_at       datetime             null comment '更新时间',
    deleted          tinyint(1) default 0 null
)
    comment '退料单明细;';