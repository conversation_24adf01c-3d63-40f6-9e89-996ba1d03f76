CREATE TABLE `b_iot_time_config`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `customer_id` bigint(20) NULL COMMENT '客户id',
    `upload_counter_time` varchar(255) NULL COMMENT '上报计数器时间（分钟）',
    `upload_exception_time` varchar(255) NULL COMMENT '上报异常时间（分钟）',
    `upload_power_time` varchar(255) NULL COMMENT '上报粉量时间（分钟）',
    `upload_machineMsg_time` varchar(255) NULL COMMENT '查询基本信息时间（分钟）',
    `check_service_time` varchar(255) NULL COMMENT '检查更新（分钟）',
    `created_at` datetime(0) NULL COMMENT '创建时间',
    `updated_at` datetime(0) NULL COMMENT '更新时间',
    `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-定时器时间配置';

CREATE TABLE `b_iot_install_software`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `version_number` varchar(255) NULL COMMENT '版本号',
    `upload_time`  datetime(0) NULL COMMENT '上传时间',
    `install_file` json DEFAULT NULL COMMENT '安装文件',
    `version_affiliation` varchar(255) NULL COMMENT '版本所属',
    `created_at` datetime(0) NULL COMMENT '创建时间',
    `updated_at` datetime(0) NULL COMMENT '更新时间',
    `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) COMMENT = '物联网-软件信息';