alter table tb_repair_report
    add column exc_desc_pics json comment '故障描述图片' after exc_desc,
    add column resolve_desc_pics json comment '解决措施图片' after resolve_desc;
alter table tb_repair_report drop column finish_images,drop column resolve_desc,drop column fifth_count;

INSERT INTO `st_dict`(`id`, `code`, `description`, `is_build_in`) VALUES (26000, '6000', '现象分类', 0);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2600001, 26000, '6001', 0, '报代码', 1, '报代码', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2600002, 26000, '6002', 0, '零件故障', 2, '零件故障', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2600003, 26000, '6003', 0, '卡纸', 3, '卡纸', 1);


INSERT INTO `st_dict`(`id`, `code`, `description`, `is_build_in`) VALUES (27000, '7000', '原因分类', 0);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2700001, 27000, '7001', 0, '机器卫生', 1, '机器卫生', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2700002, 27000, '7002', 0, 'PM到期', 2, 'PM到期', 1);

INSERT INTO `st_dict`(`id`, `code`, `description`, `is_build_in`) VALUES (28000, '8000', '处理类型', 0);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2800001, 28000, '8001', 0, '清洁', 1, '清洁', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2800002, 28000, '8002', 0, '更换PM件', 2, '更换PM件', 1);

INSERT INTO `st_dict`(`id`, `code`, `description`, `is_build_in`) VALUES (29000, '9000', '故障组件', 0);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2900001, 29000, '9001', 0, '鼓单元', 1, '鼓单元', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2900002, 29000, '9002', 0, '定影单元', 2, '定影单元', 1);