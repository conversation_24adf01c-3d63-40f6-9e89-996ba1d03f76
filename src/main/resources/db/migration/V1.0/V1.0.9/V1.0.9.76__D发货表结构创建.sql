CREATE TABLE `b_advance_invoice`
(
    `id`                  bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `outbound_order_code` varchar(255) DEFAULT NULL COMMENT '出库单号',
    `logistics_provider`  varchar(16)  DEFAULT NULL COMMENT '物流商',
    `out_type`            varchar(100) DEFAULT NULL COMMENT '出库类型',
    `sender`              json         DEFAULT NULL COMMENT '发货人',
    `receiver`            json         DEFAULT NULL COMMENT '收货人',
    `total_number`        int(10)      DEFAULT NULL COMMENT '总数量',
    `expected_number`     int(10)      DEFAULT '0' COMMENT '应发货数量',
    `collecting_number`   int(10)      DEFAULT '0' COMMENT '待揽收',
    `shipped_number`      int(10)      DEFAULT '0' COMMENT '已发货数量',
    `canceled_number`     int(10)      DEFAULT '0' COMMENT '取消发货数量',
    `created_at`          datetime     DEFAULT NULL COMMENT '创建时间',
    `updated_at`          datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`             tinyint(1)   DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    key (outbound_order_code)
) COMMENT ='预发货单';

CREATE TABLE `b_advance_invoice_detail`
(
    `id`                 bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `advance_invoice_id` bigint(11)   DEFAULT NULL COMMENT '预发货单id',
    `article_code`       varchar(255) DEFAULT NULL COMMENT '物品编号',
    `total_number`       int(10)      DEFAULT NULL COMMENT '总数量',
    `expected_number`    int(10)      DEFAULT '0' COMMENT '应发货数量',
    `collecting_number`  int(10)      DEFAULT '0' COMMENT '待揽收',
    `shipped_number`     int(10)      DEFAULT '0' COMMENT '已发货数量',
    `canceled_number`    int(10)      DEFAULT '0' COMMENT '取消发货数量',
    `created_at`         datetime     DEFAULT NULL COMMENT '创建时间',
    `updated_at`         datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`            tinyint(1)   DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    key (advance_invoice_id, article_code)
) COMMENT ='预发货明细';

CREATE TABLE `b_storage_invoice`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `waybill_id`               varchar(255) DEFAULT NULL COMMENT '发货单id',
    `logistics_provider`       varchar(255) DEFAULT NULL COMMENT '物流商',
    `logistics_waybill_number` varchar(255) DEFAULT NULL COMMENT '物流商运单号',
    `sender`                   json         DEFAULT NULL COMMENT '发货人信息',
    `receiver`                 json         DEFAULT NULL COMMENT '收货人信息',
    `status`                   varchar(255) DEFAULT NULL COMMENT '发货单状态',
    `created_by`               json         DEFAULT NULL COMMENT '创建人',
    `is_uploaded`              tinyint(1)   DEFAULT '0' COMMENT '是否上传微信发货信息',
    `created_at`               datetime     DEFAULT NULL COMMENT '创建时间',
    `updated_at`               datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`                  tinyint(1)   DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    PRIMARY KEY (`id`),
    KEY (waybill_id)
) COMMENT ='发货单-主表';

CREATE TABLE `b_storage_invoice_item`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `invoice_id`              bigint(20)   DEFAULT NULL COMMENT '发货单id',
    `outbound_order_number`   varchar(255) DEFAULT NULL COMMENT '出库单号',
    `outbound_type`           varchar(255) DEFAULT NULL COMMENT '出库方式',
    `associated_order_number` varchar(255) DEFAULT NULL COMMENT '出库单关联单号',
    `created_at`              datetime     DEFAULT NULL COMMENT '创建时间',
    `updated_at`              datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`                 tinyint(1)   DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    PRIMARY KEY (`id`),
    KEY (invoice_id)
) COMMENT ='发货子单';

CREATE TABLE `b_storage_invoice_item_detail`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `invoice_item_id` bigint(20)   DEFAULT NULL COMMENT '发货子单id',
    `article_code`    varchar(100) DEFAULT NULL COMMENT '物品编码',
    `expected_number` int(10)      DEFAULT NULL COMMENT '应发货数量',
    `storage_flow_id` bigint(20)   DEFAULT NULL COMMENT '出库单流水id',
    `created_at`      datetime     DEFAULT NULL COMMENT '创建时间',
    `updated_at`      datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`         tinyint(1)   DEFAULT '0' COMMENT '是否删除  0未删除  1删除',
    PRIMARY KEY (`id`),
    KEY (invoice_item_id)
) COMMENT ='发货子单明细';