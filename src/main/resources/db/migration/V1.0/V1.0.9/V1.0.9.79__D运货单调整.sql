ALTER TABLE `b_waybill`
    DROP COLUMN `sender_name`,
    DROP COLUMN `sender_company`,
    DROP COLUMN `sender_mobile`,
    DROP COLUMN `sender_address`,
    DROP COLUMN `sender_address_code`,
    DROP COLUMN `sender_latitude`,
    DROP COLUMN `sender_longitude`,
    DROP COLUMN `receiver_name`,
    DROP COLUMN `receiver_company`,
    DROP COLUMN `receiver_mobile`,
    DROP COLUMN `receiver_address`,
    DROP COLUMN `receiver_address_code`,
    DROP COLUMN `receiver_latitude`,
    DROP COLUMN `receiver_longitude`,
    ADD COLUMN `sender`   JSON COMMENT '发货人信息' AFTER `provider_waybill_number`,
    ADD COLUMN `receiver` JSON COMMENT '收货人信息' AFTER `sender`;
