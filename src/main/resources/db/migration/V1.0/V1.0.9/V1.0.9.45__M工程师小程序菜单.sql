-- 内置工程师角色（不可删除）
UPDATE `st_role` SET `is_build_in`=1 WHERE id=1002;
REPLACE
    INTO `st_resource` (`id`, `parent_id`, `label`, `value`, `type`, `permit`, `icon`, `is_enable`, `sort`,
                        `is_build_in`, `terminal`)
VALUES (2080, 0, '待接工单', '/pendingOrder', 'menu', NULL, NULL, 1, 8, 1, 'staff-mini-program'),
       (2090, 0, '我的工单', '/myWorkOrder', 'menu', NULL, NULL, 1, 9, 1, 'staff-mini-program'),
       (2100, 0, '申诉单', '/appealOrder', 'menu', NULL, NULL, 1, 10, 1, 'staff-mini-program'),
       (2110, 0, '耗材仓库', '/wareStore', 'menu', NULL, NULL, 1, 11, 1, 'staff-mini-program'),
       (2120, 0, '申领耗材', '/wareApply', 'menu', NULL, NULL, 1, 12, 1, 'staff-mini-program'),
       (2130, 0, '知识库', '/engLearn', 'menu', NULL, NULL, 1, 13, 1, 'staff-mini-program');
-- 添加工程师员权限
REPLACE
    INTO `st_role_resource` (`role_id`, `resource_id`)
VALUES (1002, 2080),
       (1002, 2090),
       (1002, 2100),
       (1002, 2110),
       (1002, 2120),
       (1002, 2130);