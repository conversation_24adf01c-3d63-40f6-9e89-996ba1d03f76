create table tb_offer_price
(
    id         bigint                               not null comment 'id' primary key,
    code       varchar(50)                          not null comment '报价单号',
    name       varchar(50)                          not null comment '报价单名称',
    product_id bigint                               null comment '机型id',
    create_by  bigint                               null comment '创建人',
    update_by  bigint                               null comment '更新人',
    create_at  timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    update_at  timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted    tinyint(1) default 0                 null comment '是否删除'
) comment '报价单表';
create table tb_offer_price_detail
(
    id               bigint                               not null comment 'id' primary key,
    offer_price_id   bigint comment '报价单ID',
    offer_price_code varchar(50)                          not null comment '报价单号',
    sale_sku_id bigint comment '销售商品ID',
    create_at        timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    update_at        timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted          tinyint(1) default 0                 null comment '是否删除'
)comment '报价单明细表';
