-- 同步字段-入库状态
update b_storage_in_warehouse set `in_status` = 'drk' where `in_status`= '2601';
update b_storage_in_warehouse set `in_status` = 'yrk' where `in_status` = '2602';

-- 同步字段-入库类型
update b_storage_in_warehouse set `in_type` = 'originally' where `in_type`= '2501';
update b_storage_in_warehouse set `in_type` = 'return_goods' where `in_type` = '2502';
update b_storage_in_warehouse set `in_type` = 'purchase' where `in_type` = '2503';

-- 同步出入库流水-出入库类型
update b_storage_warehouse_flow set `type` = 'originally' where `type` = '2501';
update b_storage_warehouse_flow set `type` = 'return_goods' where `type` = '2502';
update b_storage_warehouse_flow set `type` = 'purchase' where `type` = '2503';
update b_storage_warehouse_flow set `type` = 'shopping_mall' where `type` = '3101';

-- 同步库品批次表-入库类型
update b_storage_inventory_batch set `in_warehouse_type` = 'originally' where `in_warehouse_type` = '2501';
update b_storage_inventory_batch set `in_warehouse_type` = 'return_goods' where `in_warehouse_type` = '2502';
update b_storage_inventory_batch set `in_warehouse_type` = 'purchase' where `in_warehouse_type` = '2503';


-- 同步字段-出库状态
update b_storage_out_warehouse set `out_status` = 'dck' where `out_status`= '2401';
update b_storage_out_warehouse set `out_status` = 'yck' where `out_status` = '2402';
update b_storage_out_warehouse set `out_status` = 'gb' where `out_status` = '2403';

-- 同步字段-出库类型
update b_storage_out_warehouse set `out_type` = 'shopping_mall' where `out_type`= '3101';
-- 删掉初始化使用状态字典
delete from st_dict_item where id in(2310001,2240001,2240002,2250001,2250002,2250003,2260001,2260002);
delete from st_dict where id in (22600,22500,23100,22400);