ALTER TABLE `b_storage_in_warehouse`
    MODIFY COLUMN `in_type` varchar (20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入库类型（枚举）' AFTER `in_warehouse_id`,
    MODIFY COLUMN `in_status` varchar (20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入库状态（枚举）' AFTER `remarks`;

ALTER TABLE `b_storage_warehouse_flow`
    MODIFY COLUMN `type` varchar (20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出入库类型' AFTER `time`;

ALTER TABLE `b_storage_inventory_batch`
    MODIFY COLUMN `in_warehouse_type` varchar (20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '入库类型' AFTER `price`;

ALTER TABLE `b_storage_out_warehouse`
    MODIFY COLUMN `out_type` varchar (20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出库类型（枚举）' AFTER `out_warehouse_id`,
MODIFY COLUMN `out_status` varchar (20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '出库状态（枚举）' AFTER `remarks`;
