INSERT INTO `st_dict`(`id`, `code`, `description`, `is_build_in`) VALUES (23300, '3300', '更换频次', 0);
INSERT INTO `st_dict`(`id`, `code`, `description`, `is_build_in`) VALUES (23400, '3400', '备件等级', 0);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2330001, 23300, '3301', 0, '常用', 1, '常用', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2330002, 23300, '3302', 0, '极少', 2, '极少', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2330003, 23300, '3303', 0, '偶尔', 3, '偶尔', 1);

INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2340001, 23400, '3401', 0, '不备', 1, '不备', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2340002, 23400, '3402', 0, '大量备', 2, '大量备', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2340003, 23400, '3403', 0, '小量备', 3, '小量备', 1);
INSERT INTO `st_dict_item`(`id`, `dict_id`, `value`, `parent_id`, `label`, `sort`, `description`, `is_enable`)
VALUES (2340004, 23400, '3304', 0, '少量备', 4, '少量备', 1);

update `b_product_part_bom` set rep_frequency=3301 where rep_frequency='常用';
update `b_product_part_bom` set rep_frequency=3302 where rep_frequency='极少';
update `b_product_part_bom` set rep_frequency=3303 where rep_frequency='偶尔';

update `b_product_part_bom` set rep_frequency=3401 where rep_frequency='不备';
update `b_product_part_bom` set rep_frequency=3402 where rep_frequency='大量备';
update `b_product_part_bom` set rep_frequency=3403 where rep_frequency='小量备';
update `b_product_part_bom` set rep_frequency=3404 where rep_frequency='少量备';
