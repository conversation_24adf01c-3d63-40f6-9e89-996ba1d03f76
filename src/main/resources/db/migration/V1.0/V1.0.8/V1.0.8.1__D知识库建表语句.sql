CREATE TABLE `b_knowledge_use_help` (
                                        `id` bigint(20) NOT NULL COMMENT 'id',
                                        `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '视频名称',
                                        `url` json DEFAULT NULL COMMENT '视频链接',
                                        `sort` int(11) DEFAULT NULL COMMENT '视频排序',
                                        `created_at` datetime DEFAULT NULL COMMENT '创建时间',
                                        `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
                                        `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='使用帮助';
CREATE TABLE `b_knowledge_hot_words` (
                                         `id` bigint(20) NOT NULL COMMENT 'id',
                                         `name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '热词',
                                         `created_at` datetime DEFAULT NULL COMMENT '创建时间',
                                         `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
                                         `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='热词';
CREATE TABLE `b_knowledge_base_info` (
                                         `id` bigint(20) NOT NULL COMMENT 'id',
                                         `product_list` json DEFAULT NULL COMMENT '适用机型',
                                         `type` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '知识库类型（字典项码）',
                                         `code_explain` text COLLATE utf8mb4_bin COMMENT '代码解释',
                                         `title` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '标题',
                                         `tags` text COLLATE utf8mb4_bin COMMENT '标签',
                                         `created_at` datetime DEFAULT NULL COMMENT '创建时间',
                                         `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
                                         `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
                                         PRIMARY KEY (`id`),
                                         FULLTEXT KEY `idx_full_tags` (`tags`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='知识库-基础信息';
CREATE TABLE `b_knowledge_repair_case` (
                                           `id` bigint(20) NOT NULL COMMENT 'id',
                                           `base_id` bigint(20) NOT NULL COMMENT '知识库id',
                                           `title` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '案例标题',
                                           `fault_desc` text COLLATE utf8mb4_bin COMMENT '故障描述',
                                           `solution_measures` text COLLATE utf8mb4_bin COMMENT '解决措施',
                                           `install_video` json DEFAULT NULL COMMENT '拆装视频',
                                           `relate_video` json DEFAULT NULL COMMENT '相关视频',
                                           `created_at` varchar(0) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建时间',
                                           `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
                                           `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='维修案例';
CREATE TABLE `b_knowledge_repair_case_item` (
                                                `repair_case_id` bigint(20) NOT NULL COMMENT '维修案例id',
                                                `item_id` bigint(20) NOT NULL COMMENT '商品id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='维修案例商品';