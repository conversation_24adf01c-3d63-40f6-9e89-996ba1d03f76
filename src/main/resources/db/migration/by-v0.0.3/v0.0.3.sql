
DROP TABLE b_customer_page_view_event;

CREATE TABLE `tb_self_repair_report` (
     `id` bigint(20) NOT NULL,
     `device_group_id` bigint(20) DEFAULT NULL COMMENT '登记设备组的ID',
     `customer_id` bigint(20) DEFAULT NULL COMMENT '客户ID',
     `customer_staff_id` bigint(20) DEFAULT NULL COMMENT '员工ID',
     `black_white_count` bigint(20) DEFAULT NULL COMMENT '黑白计数器',
     `color_count` bigint(20) DEFAULT NULL COMMENT '彩色计数器',
     `print_count` bigint(20) DEFAULT NULL COMMENT '上次维修后到目前的印量',
     `exc_desc` text COLLATE utf8mb4_bin COMMENT '原因描述',
     `created_at` datetime DEFAULT NULL COMMENT '创建时间',
     `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
     `deleted` tinyint(1) DEFAULT '0',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='客户自我维修报告';

CREATE TABLE `tb_self_repair_report_replace_detail` (
    `id` bigint(20) NOT NULL,
    `self_repair_report_id` bigint(20) DEFAULT NULL COMMENT '自修记录ID',
    `item_store_id` bigint(20) DEFAULT NULL COMMENT '耗材仓库ID',
    `item_id` bigint(20) DEFAULT NULL COMMENT '商品id',
    `item_name` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商品名称',
    `sale_sku_id` bigint(20) DEFAULT NULL COMMENT 'sku id',
    `sale_unit_price` bigint(20) DEFAULT NULL COMMENT '商品销售单价',
    `sku_info` text COLLATE utf8mb4_bin COMMENT '商品快照',
    `num` int(11) DEFAULT NULL COMMENT '数量',
    `is_pm` tinyint(1) DEFAULT NULL COMMENT '是否是PM件',
    `part_id` bigint(20) DEFAULT NULL COMMENT '零件id',
    `location` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '零件位置',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='客户自我维修设备换件明细;';


ALTER TABLE tb_item_store_log  ADD COLUMN  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '变更备注';
ALTER TABLE tb_item_store_log  ADD COLUMN  `alter_before` int(11) DEFAULT NULL COMMENT '变更前数量';
ALTER TABLE tb_item_store_log  ADD COLUMN  `alter_end` int(11) DEFAULT NULL COMMENT '变更后数量';


ALTER TABLE tb_sale_sku  ADD COLUMN  `bom_id` bigint(20) DEFAULT NULL COMMENT '所属BOMID',