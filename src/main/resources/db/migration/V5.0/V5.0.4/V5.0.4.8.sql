create table b_purchase_payment_detail
(
    payment_id bigint not null comment '付款单id',
    manufacter_order_id    bigint not null comment '供应商订单id',
    primary key (payment_id, manufacter_order_id)
)
    comment '采购付款单明细表' collate = utf8mb4_bin
                          row_format = DYNAMIC;

insert into b_purchase_payment_detail
select t.id,t1.id
from b_purchase_payment t  join b_manufacturer_order  t1 on t1.code=t.manufacter_order_codes;