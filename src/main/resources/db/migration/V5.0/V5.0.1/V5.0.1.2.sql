alter table b_iot_print_count
    add pic_urls json null comment '图片' after last_id;

alter table tb_activity_award_grant
    add award_name varchar(32) null comment '奖品名称' after award_type;

alter table tb_activity_award_grant
    add sku_id bigint(20) null comment 'skuid' after award_name;

alter table tb_activity_award_grant
    add article_code varchar(32) null comment '物品编码' after sku_id;

alter table tb_activity_award_grant
    add price decimal(8,2) null comment '奖品价值' after article_code;

create table tb_activity_involved
(
    id                bigint        not null
        primary key,
    activity_id       bigint        not null comment '活动ID',
    situation_id       bigint        not null comment '分享id',
    customer_id       bigint        not null comment '客户ID',
    click_num         int default 0 null comment '点击次数',
    created_at        datetime(3)   not null comment '创建时间'
)
    comment '活动参与表';

alter table tb_activity_situation
    drop column click_num;

alter table tb_activity_situation
    drop column collect_num;

