alter table b_customer_ticket
    add limit_type varchar(20) null comment '限制类型' after use_code;

alter table b_customer_ticket
    add limit_info json null comment '使用限制' after limit_type;

alter table b_customer_ticket
    add min_amount int default 0 null comment '最低消费' after limit_info;


alter table tb_activity_award
    add limit_type varchar(20) null comment '限制类型' after price;

alter table tb_activity_award
    add limit_info json null comment '使用限制' after limit_type;

alter table tb_activity_award
    add min_amount int default 0 null comment '最低消费' after limit_info;


alter table tb_activity_award_grant
    add limit_type varchar(20) null comment '限制类型' after price;

alter table tb_activity_award_grant
    add limit_info json null comment '使用限制' after limit_type;

alter table tb_activity_award_grant
    add min_amount int default 0 null comment '最低消费' after limit_info;
