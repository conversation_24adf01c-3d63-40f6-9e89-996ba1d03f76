alter table tb_install_order
    add audit_by bigint null comment '审核人' after five_colours_counter;

alter table tb_install_order
    add audit_at datetime null comment '审核时间' after audit_by;

alter table tb_install_order
    add created_by bigint null comment '创建人' after created_at;

create table tb_exchange_info
(
    id              bigint      not null
        primary key,
    install_code  varchar(32) null comment '安装单号',
    customer_id     bigint      not null comment '客户id',
    device_group_id     bigint      not null comment '设备组id',
    host_type  varchar(32) null comment '主机类型',
    machine_code  varchar(32) null comment '原机器编码',
    new_machine_code  varchar(32) null comment '新机器编码',
    created_at      datetime    null comment '创建时间'
)
    comment '客户换机关联表' collate = utf8mb4_bin
                               row_format = DYNAMIC;

