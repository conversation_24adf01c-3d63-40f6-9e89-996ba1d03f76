alter table b_customer_contract
    add return_time datetime null comment '退机时间' after return_reason;

create table b_machine_inout_flow
(
    id          bigint auto_increment comment 'id '
        primary key,
    in_out_type int                  null comment '类型:  1入库  2出库',
    flow_code   varchar(32)          null comment '出入库单号',
    machine_num varchar(32)          null comment '机器编号',
    price      int      default 0           null comment '价格',
    time        datetime             null comment '出入库时间',
    source_type  varchar(20)          null comment '出入库类型',
    source_code varchar(20)          null comment '关联单号',
    created_by  bigint               null comment '创建人id',
    created_at  datetime             null comment '创建时间'
)
    comment '机器出入库流水' collate = utf8mb4_bin
                             row_format = DYNAMIC;

create index code
    on b_machine_inout_flow (machine_num);

