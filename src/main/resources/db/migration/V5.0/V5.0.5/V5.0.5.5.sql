drop table if exists b_machine_purchase_return;

alter table b_machine_purchase_detail
    add cancel_num int null default 0 comment '取消数量' after receive_num;
update b_machine_purchase_detail set cancel_num = 0 where cancel_num is null;

create table b_machine_return
(
    id          bigint auto_increment comment 'id '
        primary key,
    code varchar(32)      not        null comment '单号',
    purchase_code   varchar(32)          null comment '采购单号',
    type   varchar(32)          null comment '退货类型',
    manufacturer_id      bigint             null comment '供应商id',
    total_amount  int          null comment '总金额',
    refund_amount  int          null comment '退款金额',
    refund_type  varchar(32)          null comment '退款方式',
    status varchar(20)          null comment '退货状态',
    remark varchar(255)          null comment '退货原因',
    audit_by bigint(20) null comment '审核人',
    audit_at datetime(3) null comment '审核时间',
    pay_time datetime(3) null comment '退款时间',
    created_by  bigint               null comment '创建人id',
    created_at  datetime             null comment '创建时间'
)
    comment '机器采购退货' collate = utf8mb4_bin
                             row_format = DYNAMIC;

create table b_machine_return_detail
(
    id          bigint auto_increment comment 'id '
        primary key,
    return_code varchar(32)      not        null comment '退款单号',
    purchase_code   varchar(32)          null comment '采购单号',
    purchase_detail_id   bigint(20)          null comment '采购明细id',
    host_type   varchar(32)          null comment '主机类型',
    product_id      bigint             null comment '机器型号id',
    machine_num varchar(32)          null comment '机器编号',
    total_amount  int          null comment '总金额',
    refund_amount  int          null comment '退款金额',
    status varchar(20)          null comment '退货状态',
    delivery_time  datetime             null comment '发货时间',
    created_by  bigint               null comment '创建人id',
    created_at  datetime             null comment '创建时间'
)
    comment '机器退货明细' collate = utf8mb4_bin
                             row_format = DYNAMIC;
