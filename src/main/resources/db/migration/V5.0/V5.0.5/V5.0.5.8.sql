create table b_iot_oid_setting
(
    id         bigint auto_increment comment 'id'
        primary key,
    config_type        int         null comment '配置类型',
    str_type        varchar(128)         null comment '字符类型',
    match_data        varchar(128)         null comment '配对数据',
    main_oid        varchar(128)         null comment 'oid1',
    second_oid   varchar(128)         null comment 'Oid2',
    deal_type        int         null comment '处理类型',
    oid_name   varchar(255)         null comment 'Oid名称',
    key_name      varchar(255)         null comment '字名称',
    value_type       varchar(255)         null comment '值类型',
    sort       bigint               null comment '排序值',
    description       varchar(255)               null comment '描述',
    created_at datetime             null comment '创建时间',
    updated_at datetime             null comment '更新时间',
    deleted    tinyint(1) default 0 null comment '是否删除'
)
    comment '物联网-oid配置表' collate = utf8mb4_bin
                               row_format = DYNAMIC;