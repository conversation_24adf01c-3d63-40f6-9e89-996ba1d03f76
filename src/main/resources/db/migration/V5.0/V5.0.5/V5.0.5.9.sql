drop table if exists tb_expense_item;
drop table if exists tb_expense;
create table tb_expense
(
    id            bigint               not null comment 'id'
        primary key,
    code          varchar(32)          null comment '报销单号',
    apply_amount  int        default 0 null comment '申请报销金额',
    actual_amount int        default 0 null comment '实际报销总额',
    status        varchar(32)          null comment '状态',
    audit_at      datetime             null comment '审核时间',
    audit_by      bigint               null comment '审核人',
    created_by    bigint               null comment '创建人',
    created_at    datetime             null comment '创建时间',
    updated_at    datetime             null comment '更新时间',
    deleted       tinyint(1) default 0 null comment '是否删除'
)
    comment '报销单主表' collate = utf8mb4_bin
                         row_format = DYNAMIC;
create table tb_expense_item
(
    id           bigint        not null comment 'id'
        primary key,
    expense_code varchar(32)   null comment '报销单号',
    customer_id  bigint        null comment '客户id',
    apply_amount int default 0 null comment '申请报销金额',
    reason       varchar(255)  null comment '申请原因',
    voucher_pic  json          null comment '凭证图片',
    occur_at     date          null comment '发生时间',
    created_at   datetime      null comment '创建时间',
    updated_at   datetime      null comment '更新时间'
)
    comment '报销单明细表' collate = utf8mb4_bin
                           row_format = DYNAMIC;

alter table tb_expense
    add remark varchar(255) null comment '备注' after audit_by;



alter table tb_expense_item
    add finance_subject varchar(32) null comment '账务科目' after apply_amount;

alter table tb_expense_item
    add depart_id bigint null comment '部门' after finance_subject;

