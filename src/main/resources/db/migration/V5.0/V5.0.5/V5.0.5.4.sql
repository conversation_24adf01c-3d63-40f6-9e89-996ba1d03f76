alter table tb_item_store_log
    add flow_id bigint null comment '发货id' after batch_code;

update tb_item_store_log t ,tb_item_store t1
set t.remark = t1.article_code
where t.item_store_id = t1.id;

update tb_item_store_log t ,(
    select distinct t4.id,t4.code,t2.shop_waybill,t4.batch_code batchCode,t4.number num
    from b_storage_out_warehouse t2
             join b_storage_out_warehouse_goods t1 on t1.out_warehouse_id = t2.out_warehouse_id
             join b_storage_warehouse_flow t4 on t4.flow_id=t2.out_warehouse_id  and t4.code = t1.article_code) t1
set t.flow_id = t1.id
where t.remark = t1.code and t.operate_code = t1.shop_waybill and t.batch_code = t1.batchCode and t.num = t1.num;

update tb_item_store_log set remark =null where remark is not null;
