alter table tb_replace_detail add locations json null comment '更换位置' after location;
update   tb_replace_detail set locations = JSON_ARRAY(location) where location is not null;
alter table tb_replace_detail  drop column location;
alter table tb_replace_detail   change locations location json null comment '零件更换位置';


alter table tb_self_repair_report_replace_detail add locations json null comment '更换位置' after location;
update   tb_self_repair_report_replace_detail set locations = JSON_ARRAY(location) where location is not null;
alter table tb_self_repair_report_replace_detail  drop column location;
alter table tb_self_repair_report_replace_detail   change locations location json null comment '零件更换位置';

alter table tb_machine_repair_replace
    modify location json null comment '零件位置';

alter table tb_component_repair_replace
    modify location json null comment '零件位置';

