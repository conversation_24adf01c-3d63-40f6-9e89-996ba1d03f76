alter table b_customer_contract_serve
    add prepayment int default 0 null comment '预付款' after deposit_amount;

update b_customer_contract_serve set prepayment = 0 where prepayment is null;

create table tb_component_repair
(
    id                  bigint               not null
        primary key,
    code                varchar(32)          not null comment '维修单号',
    engineer_id         bigint               not null comment '工程师ID',
    article_code         varchar(32)          not null comment '物品编码',
    part_id          bigint               not null comment '零件ID',
    is_store      tinyint        null comment '是否入库',
    amount              int        default 0 null comment '耗材金额',
    pic_urls            json                 null comment '图片',
    description         text                 null comment '描述',
    status              varchar(32)          null comment '状态',
    completed_at        datetime             null comment '完成时间',
    audit_by            bigint               null comment '审核人',
    audit_name          varchar(32)          null comment '审核人',
    audit_at            datetime             null comment '审核时间',
    created_at          datetime             null comment '创建时间',
    updated_at          datetime             null comment '更新时间',
    deleted             tinyint(1) default 0 null
)
    comment '组件维修' row_format = DYNAMIC;

create table tb_component_repair_replace
(
    id              bigint               not null
        primary key,
    repair_id       bigint               null comment '维修id',
    item_store_id   bigint               null comment '耗材仓库ID',
    item_id         bigint               null comment '商品id',
    item_name       varchar(256)         null comment '商品名称',
    sale_sku_id     bigint               null comment 'sku id',
    sale_unit_price bigint               null comment '商品销售单价',
    sku_info        text                 null comment '商品快照',
    num             int                  null comment '数量',
    is_pm           tinyint(1)           null comment '是否是PM件',
    part_id         bigint               null comment '零件id',
    location        varchar(50)          null comment '零件位置',
    amount          bigint               null comment '金额',
    batch_code      varchar(64)          null comment '批次号',
    created_at      datetime             null comment '创建时间',
    updated_at      datetime             null comment '更新时间',
    deleted         tinyint(1) default 0 null
)
    comment '组件维修换件明细' row_format = DYNAMIC;

