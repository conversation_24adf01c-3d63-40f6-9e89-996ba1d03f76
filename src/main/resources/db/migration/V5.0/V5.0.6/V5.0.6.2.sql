create table tb_work_evaluate
(
    id            bigint               not null comment 'id'
        primary key,
    work_order_code          varchar(32)          null comment '工单号',
    professional  int        default 5 null comment '专业能力',
    service int        default 5 null comment '服务态度',
    content        varchar(255)          null comment '评价内容',
    created_by    bigint               null comment '创建人',
    created_at    datetime             null comment '创建时间',
    deleted       tinyint(1) default 0 null comment '是否删除'
)
    comment '工单评价' collate = utf8mb4_bin row_format = DYNAMIC;


alter table tb_work_order
    add is_evaluated tinyint(1) default 0 null comment '是否评价' after cancel_status;

update tb_work_order set is_evaluated = 0 where is_evaluated is null;