create table r_finance_collection
(
    id               bigint comment 'id' primary key,
    year_months       varchar(32)   null comment '年月',
    customer_id      bigint        null comment '客户id',
    period_amount    int default 0 null comment '期初余额',
    curr_amount      int default 0 null comment '开单金额',
    settle_amount    int default 0 null comment '本单结算',
    terminal_amount  int default 0 null comment '期末余额',
    material_amount  int default 0 null comment '耗材费用',
    machine_amount   int default 0 null comment '机器费用',
    operation_amount int default 0 null comment '抄表费用',
    repair_amount    int default 0 null comment '维修费用',
    created_at       datetime      null comment '创建时间'
)
    comment '应收款汇总' collate = utf8mb4_bin row_format = DYNAMIC;

create table r_finance_payment
(
    id               bigint comment 'id' primary key,
    year_months       varchar(32)   null comment '年月',
    manufacturer_id      bigint        null comment '供应商id',
    period_amount    int default 0 null comment '期初余额',
    curr_amount      int default 0 null comment '开单金额',
    settle_amount    int default 0 null comment '本单结算',
    terminal_amount  int default 0 null comment '期末余额',
    material_amount  int default 0 null comment '耗材采购',
    material_pre_amount int default 0 null comment '耗材预付',
    machine_amount   int default 0 null comment '机器采购',
    machine_pre_amount    int default 0 null comment '机器预付',
    created_at       datetime      null comment '创建时间'
)
    comment '应付款汇总' collate = utf8mb4_bin row_format = DYNAMIC;
