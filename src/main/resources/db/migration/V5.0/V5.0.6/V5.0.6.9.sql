create table b_machine_purchase_pay
(
    id          bigint auto_increment comment 'id '
        primary key,
    purchase_code varchar(32)           null comment '采购单号',
    manufacturer_id   bigint(20)   null comment '供应商',
    amount       int default 0 null comment '付款金额',
    pay_vouchers        json      null comment '付款凭证',
    remark varchar(255)   null comment '备注',
    created_by  bigint        null comment '创建人id',
    created_at  datetime      null comment '创建时间'
)
    comment '机器采购付款' collate = utf8mb4_bin
                           row_format = DYNAMIC;
create index code
    on b_machine_purchase_pay (purchase_code);

insert into b_machine_purchase_pay (id, purchase_code, manufacturer_id, amount, pay_vouchers, remark, created_by,
                                    created_at)
select id+1,purchase_code,manufacturer_id,paid_amount,pay_vouchers,remark,updated_by,pay_time
from b_machine_purchase where paid_amount>0;