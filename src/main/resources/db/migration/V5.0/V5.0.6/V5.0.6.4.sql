create table b_iot_bind_log
(
    id                 bigint  comment 'id'  primary key,
    customer_id        bigint               null comment '客户id',
    err_type               int         null comment '错误类型',
    customer_name               varchar(255)         null comment '客户名称',
    config_type             int         null comment 'OID配置类型',
    ip_address         varchar(255)         null comment 'IP地址',
    port               varchar(255)         null comment '端口',
    exception_describe varchar(1000)        null comment '异常描述',
    brand              varchar(255)         null comment '品牌',
    model               varchar(255)         null comment '机型',
    error_code         varchar(255)         null comment '自定义错误代码',
    machine_no varchar(1000)        null comment '设备编号',
    device_name              varchar(255)         null comment '设备名称',
    record_model               varchar(255)         null comment '机型',
    error_time        datetime             null comment '上报时间',
    created_at         datetime             null comment '创建时间',
    updated_at         datetime             null comment '更新时间'
)
    comment '物联网-绑定日志' collate = utf8mb4_bin
                              row_format = DYNAMIC;


