UPDATE
    `b_customer_device_group` `t`
        LEFT JOIN (SELECT `t1`.*
                   FROM `b_iot_counter` `t1`
                   WHERE `t1`.`id` = (SELECT MIN(`t2`.`id`)
                                      FROM `b_iot_counter` `t2`
                                      WHERE `t1`.`device_group_id` = `t2`.`device_group_id`)) `b` ON
        `t`.`id` = `b`.`device_group_id`
SET `t`.`enable_statistics`   = `b`.`id` IS NOT NULL,
    `t`.`black_white_counter` = `b`.`black_white_counter`,
    `t`.`color_counter`       = `b`.`cyan_counter`
WHERE `t`.`black_white_counter` IS NULL
   OR `t`.`color_counter` IS NULL
