CREATE TABLE IF NOT EXISTS `b_customer_device_bind`
(
    `id`              BIGINT(20) NOT NULL AUTO_INCREMENT,
    `customer_id`     BIGINT(20)   DEFAULT NULL COMMENT '客户id',
    `device_group_id` BIGINT(20)   DEFAULT NULL COMMENT '客户下的设备id',
    `mac_code`        VARCHAR(255) DEFAULT NULL COMMENT '局域网查出的机器编号',
    `brand`           VARCHAR(100) DEFAULT NULL COMMENT '品牌',
    `mode`            VARCHAR(100) DEFAULT NULL COMMENT '机器型号',
    `machine_sn`      VARCHAR(100) DEFAULT NULL COMMENT '机器序列号',
    `bind_time`       DATETIME     DEFAULT NULL COMMENT '绑定时间',
    `created_at`      DATETIME     DEFAULT NULL COMMENT '创建时间',
    `updated_at`      DATETIME     DEFAULT NULL COMMENT '更新时间',
    `deleted`         TINYINT(1)   DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) AUTO_INCREMENT = 1 COMMENT ='客户--设备绑定';
