-- 临时存储过程做数据迁移
DROP PROCEDURE IF EXISTS `temp_procedure`;

DELIMITER $$

CREATE PROCEDURE `temp_procedure`
(
)
BEGIN
    DECLARE `v_done` INT DEFAULT 0;
    DECLARE `v_id` BIGINT;
    DECLARE `v_product_ids` JSON;
    DECLARE `v_serial_ids` JSON;
    DECLARE `v_cursor` CURSOR FOR SELECT `id`, `product_list` FROM `b_knowledge_base_info`;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET `v_done` = TRUE;

    OPEN `v_cursor`;
    REPEAT
        FETCH `v_cursor` INTO `v_id`, `v_product_ids`;
        IF `v_done` <> 1 THEN
            SELECT JSON_ARRAYAGG(`t`.`id`)
            FROM (SELECT DISTINCT `b2`.`id`
                  FROM `b_product_tree` `b1`
                           INNER JOIN `b_product_tree` `b2`
                                      ON `b1`.`parent_id` = `b2`.`id` AND
                                         JSON_CONTAINS(`v_product_ids`, CAST(`b1`.`id` AS JSON))) `t`
            INTO `v_serial_ids`;

            UPDATE `b_knowledge_base_info` SET `product_serials` = `v_serial_ids` WHERE `id` = `v_id`;
        END IF;
    UNTIL `v_done` END REPEAT;
END $$
DELIMITER ;

CALL `temp_procedure`();

DROP PROCEDURE `temp_procedure`;
