package com.hightop.benyin.product;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.product.api.dto.query.ProductPartPageQueryDto;
import com.hightop.benyin.product.application.service.ProductPartService;
import com.hightop.benyin.product.domain.service.ProductPartAssociationDomainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Description: 配置-产品树、设备、零件服务等测试
 * @Author: xhg
 * @Date: 2023/10/25 10:02
 */
public class ProductServiceTest extends BaseTest {
    @Autowired
    ProductPartService productPartService;
    @Autowired
    ProductPartAssociationDomainService productPartAssociationDomainService;
    @Test
    public void t1() {
        ProductPartPageQueryDto dto=new ProductPartPageQueryDto();
        dto.setPageNumber(1);
        dto.setPageNumber(10);
        System.out.println(json(productPartService.page(dto)));
    }

    @Test
    public void t2() {
        List<Long> list = productPartAssociationDomainService.getAdapterPart(1730159309239589866L);
        List<Long> lis2 = productPartAssociationDomainService.getAdapterPart(1730159309512217248L);

        System.out.println(list);
        System.out.println(lis2);
    }
}
