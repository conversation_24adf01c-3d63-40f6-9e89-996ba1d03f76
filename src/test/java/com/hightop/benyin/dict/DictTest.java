package com.hightop.benyin.dict;

import com.hightop.benyin.BaseTest;
import com.hightop.magina.standard.code.dictionary.dict.Dict;
import com.hightop.magina.standard.code.dictionary.dict.DictDomainService;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description: 字典数据测试
 * @Author: xhg
 * @Date: 2023/10/25 10:02
 */
public class DictTest extends BaseTest {
    @Autowired
    DictDomainService dictDomainService;
    @Autowired
    DictItemDomainService dictItemDomainService;

    @Test
    public void appendDictItem() {
        // 字典编码
        String dictCode = "300";
        // 字典项名称(value、sort根据已有项最大值累加)
        List<String> it = Arrays.asList(
            "公检法", "军队", "事业单位", "医院",
            "政府", "集团公司", "学校", "印刷厂",
            "银行", "制造业"
        );
        Dict dict = dictDomainService.getNonNullByCode(dictCode);
        List<DictItem> existItems = dictItemDomainService.lambdaQuery()
            .eq(DictItem::getDictId, dict.getId())
            .orderByDesc(DictItem::getSort)
            .list();
        AtomicInteger count = new AtomicInteger(existItems.get(0).getSort());
        existItems.sort(Comparator.comparing(DictItem::getValue).reversed());
        AtomicInteger maxValue = new AtomicInteger(Integer.parseInt(existItems.get(0).getValue()));
        List<DictItem> items = new ArrayList<>();
        it.stream().forEach(i -> {
            maxValue.getAndIncrement();
            count.getAndIncrement();
            items.add(new DictItem()
                .setDictId(dict.getId())
                .setDescription(i)
                .setValue(String.valueOf(maxValue.get()))
                .setLabel(i)
                .setSort(count.get())
            );
        });
        dictItemDomainService.saveBatch(items);
    }

    @Test
    public void addNewDict() {
        // 字典编码
        AtomicInteger dictCode = new AtomicInteger(2500);
        // 字典描述
        String desc = "选配件类型";
        // 字典项名称(value根据字典编码累加、sort由1累加)
        List<String> it = Arrays.asList(
            "分页器","服务器"
        );
        AtomicInteger count = new AtomicInteger(1);
        Dict dict = new Dict().setCode(String.valueOf(dictCode.get())).setDescription(desc);
        dictDomainService.save(dict);
        List<DictItem> items = new ArrayList<>();
        it.stream().forEach(i -> {
            dictCode.getAndIncrement();
            items.add(new DictItem()
                .setDictId(dict.getId())
                .setDescription(i)
                .setValue(String.valueOf(dictCode.get()))
                .setLabel(i)
                .setSort(count.get())
            );
            count.getAndIncrement();
        });
        dictItemDomainService.saveBatch(items);
    }
}
