package com.hightop.benyin.payment.domain.service.impl;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.payment.domain.service.RefundDomainService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * {@link IcbcRefundDomainServiceImpl}
 * <AUTHOR>
 * @date 2023/11/17 15:12
 */
public class IcbcRefundDomainServiceImplTest extends BaseTest {
    @Resource(type = IcbcRefundDomainServiceImpl.class)
    RefundDomainService refundDomainService;

    @Test
    public void refund() {
        this.refundDomainService.refund(1727866779478343681L, 2L);
    }
}
