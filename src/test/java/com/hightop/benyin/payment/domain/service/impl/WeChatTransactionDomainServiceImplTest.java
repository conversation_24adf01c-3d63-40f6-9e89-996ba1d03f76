package com.hightop.benyin.payment.domain.service.impl;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.payment.domain.service.TransactionDomainService;
import com.hightop.benyin.payment.infrastructure.enums.TradeOrderOrigin;
import com.hightop.fario.base.util.UuidUtils;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * {@link WeChatTransactionDomainServiceImpl}
 * <AUTHOR>
 * @date 2023/12/06 14:54
 */
public class WeChatTransactionDomainServiceImplTest extends BaseTest {
    @Resource(type = WeChatTransactionDomainServiceImpl.class)
    TransactionDomainService transactionDomainService;

    @Test
    public void order() {
        this.transactionDomainService.order(
            "okXsd5appO7z18eiQQPqRUFRCpdg", UuidUtils.full(), TradeOrderOrigin.SALES_ORDER, 23423432L,1L, "测试商品",null,0L);
    }

    @Test
    public void query() {
        this.transactionDomainService.query(1732998501647798273L);
    }
}
