package com.hightop.benyin.payment.domain.service.impl;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.payment.domain.service.RefundDomainService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * {@link WeChatRefundDomainServiceImpl}
 * <AUTHOR>
 * @date 2023/12/06 19:30
 */
public class WeChatRefundDomainServiceImplTest extends BaseTest {
    @Resource(type = WeChatRefundDomainServiceImpl.class)
    RefundDomainService refundDomainService;

    @Test
    public void test() {
        this.refundDomainService.refund(1733023141959163906L, 2L);
    }
}
