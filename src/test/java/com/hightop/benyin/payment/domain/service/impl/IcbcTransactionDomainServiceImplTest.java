package com.hightop.benyin.payment.domain.service.impl;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.payment.domain.service.TransactionDomainService;
import com.hightop.benyin.payment.infrastructure.enums.TradeOrderOrigin;
import com.hightop.fario.base.util.UuidUtils;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * {@link IcbcTransactionDomainServiceImpl}
 * <AUTHOR>
 * @date 2023/10/26 15:24
 */
public class IcbcTransactionDomainServiceImplTest extends BaseTest {
    @Resource(type = IcbcRefundDomainServiceImpl.class)
    TransactionDomainService transactionDomainService;

    @Test
    public void order() {
        this.transactionDomainService.order(
            "okXsd5appO7z18eiQQPqRUFRCpdg", UuidUtils.full(), TradeOrderOrigin.SALES_ORDER, 2324L,1L, "测试商品",null,0L);
    }

    @Test
    public void isOrderPaid() {
        this.transactionDomainService.isOrderPaid(1727866779478343681L);
    }

    @Test
    public void closeOrder() {
        this.transactionDomainService.closeOrder(1726877880572461059L);
    }
}
