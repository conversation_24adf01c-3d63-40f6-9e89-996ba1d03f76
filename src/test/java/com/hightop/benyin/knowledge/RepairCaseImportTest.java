package com.hightop.benyin.knowledge;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.handle.Tools;
import com.hightop.benyin.knowledge.domain.service.KnowledgeBaseInfoDomainService;
import com.hightop.benyin.knowledge.domain.service.KnowledgeRepairCaseDomainService;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeBaseInfo;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeRepairCase;
import com.hightop.benyin.knowledge.service.CaseTempDomainService;
import com.hightop.benyin.knowledge.service.ErrorInfo;
import com.hightop.benyin.knowledge.service.ErrorInfoDomainService;
import com.hightop.benyin.knowledge.ut.CaseTemp;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.util.UuidUtils;
import com.hightop.fario.common.jackson.JacksonUtils;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/13 11:01
 */
public class RepairCaseImportTest extends BaseTest {

    /**
     * COS云地址前缀
     */
    private static final String COS_URL_PREFIX = "https://benyin-1315885374.cos.ap-chengdu.myqcloud.com/prod/knowledge/";
    /**
     * 历史数据本印存储访问前缀(html标签)
     */
    private static final String OLD_HTML_PREFIX = "https://work.benyin.ltd";
    /**
     * 历史数据本印存储访问前缀（非html标签）
     */
    private static final String OLD_NOT_HTML_PREFIX = "https://work.benyin.ltd/seeyon/fileUpload.do?method=showRTE&fileId=";
    /**
     * 本印系统最新token
     */
    private static final String NEWEST_TOKEN = "ts=1705114416396; JSESSIONID=09E8D1107B090F9EF30AEF36F41152CA; login_locale=zh_CN; avatarImageUrl=-6663106077244308170; loginPageURL=";

    @Autowired
    CosUpload cosUpload;

    @Autowired
    ProductTreeDomainService productTreeDomainService;

    @Autowired
    KnowledgeBaseInfoDomainService knowledgeBaseInfoDomainService;
    @Autowired
    KnowledgeRepairCaseDomainService knowledgeRepairCaseDomainService;

    @Autowired
    CaseTempDomainService caseTempDomainService;

    @Autowired
    DictItemDomainService dictItemDomainService;

    @Autowired
    ErrorInfoDomainService errorInfoDomainService;

    private static final ConcurrentHashMap<String,List<String>> STASH_MAP = new ConcurrentHashMap();

    private ExecutorService executorService = new ThreadPoolExecutor(30, 50, 30, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(200), new ThreadPoolExecutor.CallerRunsPolicy());

    private String productTreeName = "产品树";
    private String seriesName = "系列";
    /**
     * 维修案例
     */
    @Test
    public void test2()  {
        String fileName = "/Users/<USER>/Downloads/knowledge/卡纸报代码案例汇总.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        params.setStartSheetIndex(0);
        params.setSheetNum(14);
        List<KnowledgeTemplateExcel> data = ExcelImportUtil.importExcel(new File(fileName), KnowledgeTemplateExcel.class, params);
        for (KnowledgeTemplateExcel k : data) {
            if (StringUtils.isBlank(k.getCode())||StringUtils.isBlank(k.getId())) {
                continue;
            }
            doTask(k);
        }
    }

    private void doTask(KnowledgeTemplateExcel k ){
        // 目前只有产品树、品牌
        ProductTree productTree = null;
        Set<String> modelIds = new HashSet<>();
        String range = k.getRange();
        if (productTreeName.equals(range)) {
            productTree = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getName, k.getTree())
                    .eq(ProductTree::getLastLevel, 0)
                    .one();

        } else if (seriesName.equals(range)) {
            String serials = k.getSeries();
            if (StringUtils.isBlank(serials)) {
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setCode(k.getCode());
                errorInfo.setType("系列为空");
                errorInfo.setRemark("moduleId:"+k.getId()+" 产品树："+k.getTree());
                errorInfoDomainService.save(errorInfo);
                return;
            }
            String[] serial = serials.split(",");
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, s)
                        .eq(ProductTree::getLastLevel, 0)
                        .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getParentId, xl.getId())
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelIds.addAll(models.stream().map(ProductTree::getId).map(String::valueOf)
                                .collect(Collectors.toSet()));
                    }

                }
            }
        }

        if (null != productTree) {
            List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                    .like(ProductTree::getFullIdPath, productTree.getId())
                    .eq(ProductTree::getLastLevel, 1)
                    .list();
            modelIds = models.stream().map(ProductTree::getId).map(String::valueOf).collect(Collectors.toSet());
        }
        if (CollectionUtils.isNotEmpty(modelIds)) {
            StringBuffer sql = new StringBuffer();
            modelIds.stream().forEach(e -> sql.append(" OR JSON_CONTAINS(product_list,'" + e + "')"));
            // 查询故障代码与适用范围所匹配的机型，给基础信息添加案例
            List<KnowledgeBaseInfo> baseInfo = this.knowledgeBaseInfoDomainService.lambdaQuery()
                    .eq(KnowledgeBaseInfo::getTitle, k.getCode())
                    .last("AND (" + sql.substring(3) + ")")
                    .list();
            setCase(baseInfo, k);
        }

    }


    /**
     * 设置维修案例
     *
     * @param baseInfoList
     * @param kte
     */
    private void setCase(List<KnowledgeBaseInfo> baseInfoList, KnowledgeTemplateExcel kte) {
        if (CollectionUtils.isNotEmpty(baseInfoList)) {
            String tags = "";
            String k1 = Optional.ofNullable(kte.getKeyword1()).orElse("").trim();
            String k2 = Optional.ofNullable(kte.getKeyword2()).orElse("").trim();
            String k3 = Optional.ofNullable(kte.getKeyword3()).orElse("").trim();
            String k4 = Optional.ofNullable(kte.getKeyword4()).orElse("").trim();
            if (StringUtils.isNotBlank(k1)) {
                tags += k1 + ",";
            }
            if (StringUtils.isNotBlank(k2)) {
                tags += k2 + ",";
            }
            if (StringUtils.isNotBlank(k3)) {
                tags += k3 + ",";
            }
            if (StringUtils.isNotBlank(k4)) {
                tags += k4 + ",";
            }
            if (StringUtils.isNotBlank(tags)) {
                tags = tags.substring(0, tags.length() - 1);
            }
            for (KnowledgeBaseInfo baseInfo : baseInfoList) {
                if (kte.getId() == null) {
                    continue;
                }
                String[] idArray = kte.getId().trim().split("\n");
                for (String moduleId : idArray) {
                    List<KnowledgeRepairCase> cases = readCase(moduleId, baseInfo.getId());
                    if (CollectionUtils.isNotEmpty(cases)) {
                        this.knowledgeRepairCaseDomainService.saveBatch(cases);
                        this.knowledgeBaseInfoDomainService.lambdaUpdate()
                                .set(KnowledgeBaseInfo::getHistoryId, kte.getId())
                                .set(KnowledgeBaseInfo::getTags, tags)
                                .eq(KnowledgeBaseInfo::getId, baseInfo.getId())
                                .update();
                    }else {
                        ErrorInfo errorInfo = new ErrorInfo();
                        errorInfo.setCode(String.valueOf(baseInfo.getId()));
                        errorInfo.setType("no case");
                        errorInfo.setRemark("moduleId:"+moduleId+" 产品树："+kte.getTree());
                        errorInfoDomainService.save(errorInfo);
                    }
                }

            }
        }
    }


    /**
     * 读取案例
     *
     * @param moduleId
     * @param baseId
     * @return
     */
    public List<KnowledgeRepairCase> readCase(String moduleId, Long baseId) {
        LocalDateTime now = LocalDateTime.now();
        List<KnowledgeRepairCase> cases = new ArrayList<>();
        List<CaseTemp> caseTempList = caseTempDomainService.list(new LambdaQueryWrapper<CaseTemp>().eq(CaseTemp::getHistoryId, moduleId));
        if (CollectionUtils.isEmpty(caseTempList)) {
            return Lists.newArrayList();
        }
        for (CaseTemp caseTemp : caseTempList) {
            // 有多少组数据就有多少案例
            KnowledgeRepairCase repairCase = new KnowledgeRepairCase();
            repairCase.setBaseId(baseId);
            // 标题=现象描述
            repairCase.setTitle(caseTemp.getTitle());

            // 故障描述=现象描述+描述图片
            repairCase.setFaultDesc(caseTemp.getFaultDesc());
            // 解决措施=原因+处理过程+解决措施（按顺序）
            repairCase.setSolutionMeasures(caseTemp.getSolutionMeasures());
            repairCase.setCreatedAt(now);
            repairCase.setUpdatedAt(now);
            repairCase.setDeleted(0);
            repairCase.setRemark("代码案例");
            cases.add(repairCase);
        }
        return cases;
    }

    /**
     * 替换图片
     *
     * @param html
     * @return
     */
    private String replaceImg(String html) {
        Document doc = Jsoup.parse(html);
        Elements img = doc.getElementsByTag("img");
        img.stream().iterator().forEachRemaining(i -> {
            String src = i.attr("src");
            String cosUrl = parseImg(OLD_HTML_PREFIX + src);
            // 替换src
            i.attr("src", cosUrl);
        });
        return doc.outerHtml();
    }

    /**
     * 读取响应
     *
     * @param moduleId
     * @return
     */
    private String requestBody(String moduleId) {
        Map<String, String> map = new HashMap<>(4);
        map.put("rightId", "7027768636809955559.6655775903610913817");
        // 数据id
        map.put("moduleId", moduleId);
        map.put("moduleType", "42");
        map.put("operateType", "2");
        HttpHeaders headers = new HttpHeaders();
        headers.set("Cookie", NEWEST_TOKEN);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(JacksonUtils.serialize(map), headers);
        ResponseEntity<String> response = new RestTemplate()
                .exchange("https://work.benyin.ltd/seeyon/rest/cap4/form/createOrEdit",
                        HttpMethod.POST, request, String.class);
        return response.getBody();
    }


    private List<String> parseImg(List<String> imgList) {
        List<String> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(imgList)) {
            imgList.stream().forEach(e -> {
                // 根据url读取文件流上传到cos
                String fileName = UuidUtils.full() + ".png";
                try {
                    cosUpload.upload(fileName, getNetUrlInputStream(e));
                } catch (IOException ex) {
                    ex.printStackTrace();
                } catch (Exception exception) {
                    exception.printStackTrace();
                }
                list.add(COS_URL_PREFIX + fileName);
            });
        }
        return list;
    }

    private String parseImg(String imgUrl) {
        // 根据url读取文件流上传到cos
        String fileName = UuidUtils.full() + ".png";
        try {
            cosUpload.upload(fileName, getNetUrlInputStream(imgUrl));
        } catch (IOException ex) {
            ex.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return COS_URL_PREFIX + fileName;
    }

    private InputStream getNetUrlInputStream(String url) throws IOException {
        URL videoUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) videoUrl.openConnection();
        return connection.getInputStream();
    }


    private static Map<String, String> fieldVal(JsonNode fieldInfo) {
        Map<String, String> map = new HashMap<>(16);
        fieldInfo.elements().forEachRemaining(i -> {
            map.put(i.get("display").textValue(), i.get("name").textValue());
        });
        return map;
    }

}
