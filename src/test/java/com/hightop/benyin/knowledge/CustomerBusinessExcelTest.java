package com.hightop.benyin.knowledge;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.hightop.benyin.Application;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.customer.application.service.CustomerBusinessService;
import com.hightop.benyin.customer.application.service.CustomerService;
import com.hightop.benyin.customer.domain.service.CustomerBusinessDomainService;
import com.hightop.benyin.customer.domain.service.CustomerCallRecordDomainService;
import com.hightop.benyin.customer.domain.service.CustomerDomainService;
import com.hightop.benyin.customer.domain.service.CustomerStaffDomainService;
import com.hightop.benyin.customer.infrastructure.entity.Customer;
import com.hightop.benyin.customer.infrastructure.entity.CustomerBusiness;
import com.hightop.benyin.customer.infrastructure.entity.CustomerCallRecord;
import com.hightop.benyin.customer.infrastructure.entity.CustomerStaff;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.ums.user.basic.UserBasic;
import com.hightop.magina.standard.ums.user.basic.UserBasicDomainService;
import com.hightop.magina.standard.ums.user.personal.UserService;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户-商务信息实体
 * <AUTHOR>
 * @date 2023-10-24 11:03:17
 */
//public class CustomerBusinessExcelTest{
public class CustomerBusinessExcelTest extends BaseTest {
//    public static void main(String[] args) {
//        String fileName = "C:\\qq.xlsx";
//        ImportParams params = new ImportParams();
//        // 读取开始行。默认第一行
//        params.setStartRows(0);
//        // 读取结束行
//        params.setReadRows(10000);
//        params.setStartSheetIndex(1);
////        params.setSheetNum(18);
//        List<CustomerBusinessExcel> data = ExcelImportUtil.importExcel(new File(fileName), CustomerBusinessExcel.class, params);
//        System.out.println("数量条数：" + data.size());
//        if (data != null){
//            data.stream().forEach(it ->{
//
//            });
//        }
//        System.out.println(JSONUtil.toJsonStr(data));
//    }

    @Autowired
    private  CustomerDomainService customerService;
    @Autowired
    private  CustomerBusinessDomainService customerBusinessService;
    @Autowired
    private CustomerCallRecordDomainService customerCallRecordDomainService;
    @Autowired
    private UserBasicDomainService userBasicDomainService;
    @Autowired
    private CustomerStaffDomainService customerStaffDomainService;

    @Test
    public void test1(){
        String fileName = "C:\\qq.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        params.setStartSheetIndex(1);
//        params.setSheetNum(18);
        List<CustomerBusinessExcel> data = ExcelImportUtil.importExcel(new File(fileName), CustomerBusinessExcel.class, params);
        System.out.println("数量条数：" + data.size());
        if (data != null){
            data.stream().forEach(it ->{
                String bank = it.getBank();
                Customer byId = customerService.lambdaQuery().eq(Customer::getSeqId,bank).one();
                if (byId != null){
                    CustomerBusiness one = customerBusinessService.lambdaQuery().eq(CustomerBusiness::getCustomerId, byId.getId()).one();
                    if (one == null){
                        CustomerBusiness customerBusiness = new CustomerBusiness();
                        customerBusiness.setCustomerId(byId.getId());
                        customerBusiness.setBusinessman(it.getBusinessman());
                        customerBusiness.setSalesman(it.getSalesman());
                        customerBusinessService.save(customerBusiness);
                    } else {
                        one.setBusinessman(it.getBusinessman());
                        one.setBusinessman(it.getSalesman());
                        customerBusinessService.updateById(one);
                    }
                }
            });
        }
        System.out.println(JSONUtil.toJsonStr(data));
    }


    @Test
    public void test12(){
        String fileName = "C:\\qq.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        params.setStartSheetIndex(2);
//        params.setSheetNum(18);
        List<CustomerCallRecordExcel> data = ExcelImportUtil.importExcel(new File(fileName), CustomerCallRecordExcel.class, params);
        System.out.println("数量条数：" + data.size());
        if (data != null){
            data.stream().forEach(it ->{
                  String reachShopRole = it.getReachShopRole();
                      if ("店长".equals(reachShopRole)){
                          it.setReachShopRole("501");
                      }
                    if ("报修员".equals(reachShopRole)){
                        it.setReachShopRole("504");
                    }
                    if ("老板".equals(reachShopRole)){
                        it.setReachShopRole("505");
                    }
                    if ("老板娘".equals(reachShopRole)){
                        it.setReachShopRole("505");
                    }
                    if ("财务".equals(reachShopRole)){
                        it.setReachShopRole("502");
                    }
                    if ("员工".equals(reachShopRole)){
                        it.setReachShopRole("503");
                    }


                    String callState = it.getCallState();
                    if ("简单沟通".equals(callState)){
                        it.setCallState("3710");
                    }
                    if ("继续沟通".equals(callState)){
                        it.setCallState("3720");
                    }
                    if ("需要上门".equals(callState)){
                        it.setCallState("3730");
                    }
                    if ("可以转化".equals(callState)){
                        it.setCallState("3740");
                    }
                    if ("开始合作".equals(callState)){
                        it.setCallState("3750");
                    }
                    if ("深度合作".equals(callState)){
                        it.setCallState("3760");
                    }
                    if ("完成绑定".equals(callState)){
                        it.setCallState("3770");
                    }
                  Customer byId = customerService.lambdaQuery().eq(Customer::getSeqId,it.getCustomerId()).one();
                  UserBasic eq = userBasicDomainService.lambdaQuery().eq(UserBasic::getName, it.getOperatName()).one();

                    CustomerCallRecord customerCallRecord = new CustomerCallRecord();
                    if (byId != null){
                        customerCallRecord.setCustomerId(byId.getId());
                    }
                    customerCallRecord.setReachShopName(it.getReachShopName());
                    DictItemEntry shopRole = new DictItemEntry();
                    shopRole.setValue(it.getReachShopRole());
                    customerCallRecord.setReachShopRole(shopRole);
                    customerCallRecord.setReachShopTel(it.getReachShopTel());
                    customerCallRecord.setRemark(it.getRemark());
                    customerCallRecord.setNextNoticeRemark(it.getNextNoticeRemark());
                    if (StringUtils.isNotBlank(it.getOperatName())){
                        customerCallRecord.setOperatName(it.getOperatName());
                        customerCallRecord.setOperatId(eq.getId());
                    }
                    customerCallRecord.setOperatName(it.getOperatName());
                    DictItemEntry dictItemEntry = new DictItemEntry();
                    dictItemEntry.setValue(it.getCallState());
                    customerCallRecord.setCallGoal(dictItemEntry);
                    customerCallRecord.setCreateTime(it.getCreateTime());
                    customerCallRecord.setDeleted(0);

                    customerCallRecordDomainService.save(customerCallRecord);
            });
        }
        System.out.println(JSONUtil.toJsonStr(data));
    }

    @Test
    public void test13(){
        String fileName = "C:\\qq2.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        params.setStartSheetIndex(3);
//        params.setSheetNum(18);
        List<CustomerStaffExcel> data = ExcelImportUtil.importExcel(new File(fileName), CustomerStaffExcel.class, params);
        System.out.println("数量条数：" + data.size());
        if (data != null){
            data.stream().forEach(it ->{

                String vxGroupName = it.getVxGroupName();
                if ("四川2群".equals(vxGroupName)){
                    it.setVxGroupName("2610");
                }
                if ("四川3群".equals(vxGroupName)){
                    it.setVxGroupName("2620");
                }
                if ("四川4群".equals(vxGroupName)){
                    it.setVxGroupName("2630");
                }
                if ("四川5群".equals(vxGroupName)){
                    it.setVxGroupName("2660");
                }
                if ("VIP群".equals(vxGroupName)){
                    it.setVxGroupName("2670");
                }
                if ("未入群".equals(vxGroupName)){
                    it.setVxGroupName("2680");
                }
                if ("重庆1群".equals(vxGroupName)){
                    it.setVxGroupName("2650");
                }
                if ("四川1群".equals(vxGroupName)){
                    it.setVxGroupName("2699");
                }
                Customer byId = customerService.lambdaQuery().eq(Customer::getSeqId,it.getCustomerIdNo()).one();
                if (byId != null){
                    CustomerStaff one = customerStaffDomainService.lambdaQuery().eq(CustomerStaff::getCustomerId, byId.getId()).eq(CustomerStaff::getTel, it.getTel()).one();
                    if (one != null){
                        one.setCustomerId(byId.getId());
                        one.setVxId(it.getVxId());
                        one.setVxNikeName(it.getVxNikeName());
                        DictItemEntry dictItemEntry = new DictItemEntry();
                        dictItemEntry.setValue(it.getVxGroupName());
                        one.setVxGroupName(dictItemEntry);
                      customerStaffDomainService.updateById(one);
                    }
                }
            });
        }
        System.out.println(JSONUtil.toJsonStr(data));
    }
}
