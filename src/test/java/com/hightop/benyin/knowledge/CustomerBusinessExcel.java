package com.hightop.benyin.knowledge;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hightop.benyin.share.infrastructure.type.CosObject;
import com.hightop.benyin.share.infrastructure.type.CosObjectList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 客户-商务信息实体
 * <AUTHOR>
 * @date 2023-10-24 11:03:17
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerBusinessExcel {
//    @Excel(name = "新版本大主题（大标题+机型构成唯一值，见左边第一列）",orderNum = "0")
//    Long customerId;
    @Excel(name = "客户编号",orderNum = "0")
    String bank;
    @Excel(name = "销售人员",orderNum = "1")
    String businessman;
    @Excel(name = "工程师",orderNum = "2")
    String salesman;

}
