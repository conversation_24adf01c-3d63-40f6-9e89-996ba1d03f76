package com.hightop.benyin.knowledge.code;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.handle.Tools;
import com.hightop.benyin.knowledge.CosUpload;
import com.hightop.benyin.knowledge.domain.service.KnowledgeBaseInfoDomainService;
import com.hightop.benyin.knowledge.domain.service.KnowledgeRepairCaseDomainService;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeBaseInfo;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.fario.base.Trio;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 把cos上传改成相同案例只上传一次，现在是会重复上传
 *
 * @Description: 知识库test
 * @Author: xhg
 * @Date: 2023/12/25 17:40
 */
public class LgKnowledgeTest extends BaseTest {

    @Autowired
    ProductTreeDomainService productTreeDomainService;

    @Autowired
    KnowledgeBaseInfoDomainService knowledgeBaseInfoDomainService;
    @Autowired
    KnowledgeRepairCaseDomainService knowledgeRepairCaseDomainService;

    @Autowired
    DictItemDomainService dictItemDomainService;



    /**
     * 理光系列
     * 1、先读机器代码，生成机型+知识库类型+标题+代码解释
     *
     * @param
     * @return:
     * @Author: xhg
     * @Date: 2023/12/27 16:57
     */
    @Test
    public void excCodeSeriesImport() {
        String fileName = "/Users/<USER>/Downloads/import/理光主流机型故障代码汇总完整版20240111.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("MP 6054系列,MP 3554系列", 0, 600),
                Trio.of("MP 6055系列,MP 3055系列", 1, 583),
                Trio.of("IM 8000系列", 2, 683),
                Trio.of("MP 7001系列", 3, 227),
//                Trio.of("MP 5500", 4, 193),
                Trio.of("MP 7502系列", 5, 361),
                Trio.of("MP 7503系列", 6, 571),
//                Trio.of("MP C2503 C2003", 7, 576),
                Trio.of("MP C6003系列,MP C3503系列", 8, 647),
//                Trio.of("Pro 1107EX,Pro 1357EX", 9, 512),
                Trio.of("Pro 8100系列", 10, 1195),
//                Trio.of("Pro 8200系列", 11, 992),
//                Trio.of("Pro 8300系列", 12, 1263),
//                Trio.of("Pro C5100系列", 13, 898),
//                Trio.of("Pro C5200系列", 14, 1033),
//                Trio.of("Pro C5300系列", 15, 1145),
//                Trio.of("Pro C7100系列", 16, 1606),
//                Trio.of("Pro C7200系列", 17, 1402),
//                Trio.of("Pro C9100系列", 18, 1600),
                Trio.of("Pro C9200系列", 19, 1617)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, s)
                        .eq(ProductTree::getLastLevel, 0)
                        .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getParentId, xl.getId())
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("SC编号"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                            .eq(DictItem::getDictId, 23600L)
                            .eq(DictItem::getLabel, m.get("等级"))
                            .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"SC编号".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("理光系列纬度-代码");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }


    /**
     * 理光机型纬度
     */
    @Test
    public void excCodeProductImport() {
        String fileName = "/Users/<USER>/Downloads/import/理光主流机型故障代码汇总完整版20240111.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("MP 5500", 4, 193),
                Trio.of("MP C2503,C2003", 7, 576),
                Trio.of("Pro 1107EX,Pro 1357EX", 9, 512)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getName, s)
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("SC编号"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                            .eq(DictItem::getDictId, 23600L)
                            .eq(DictItem::getLabel, m.get("等级"))
                            .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"SC编号".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("理光机型纬度-代码");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }



    private List<Trio<String, Integer, Integer>> getData2(){
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("MP 6054系列,MP 3554系列", 0, 141),
                Trio.of("MP 6055系列,MP 3055系列", 1, 170),
                Trio.of("IM 8000系列", 2, 220),
                Trio.of("MP 7001系列", 3,140),
//                Trio.of("MP 5500", 4, 104),
                Trio.of("MP 7502系列", 5, 113),
                Trio.of("MP 7503系列", 6, 199),
//                Trio.of("MP C2503 C2003", 7, 62),
                Trio.of("MP C6003系列,MP C3503系列", 8, 130),
//                Trio.of("Pro 1107EX Pro 1357EX", 9, 120),
                Trio.of("Pro 8100系列", 10, 379),
                Trio.of("Pro 8200系列", 11, 379),
                Trio.of("Pro 8300系列", 12, 491),
                Trio.of("Pro C5100系列", 13, 100),
                Trio.of("Pro C5200系列", 14, 235),
                Trio.of("Pro C5300系列", 15, 439),
                Trio.of("Pro C7100系列", 16, 411),
                Trio.of("Pro C7200系列", 17, 422),
                Trio.of("Pro C9100系列", 18, 375),
                Trio.of("Pro C9200系列", 19, 399)


        );
        return sheets;
    }
    /**
     * @return void
     * 理光卡纸 -系列纬度
    */
    @Test
    public void lockPaperSeriesImport() {
        String fileName = "/Users/<USER>/Downloads/import/理光主流机型卡纸代码汇总完整版20240111.xlsx";
        List<Trio<String, Integer, Integer>> sheets = getData2();
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, s)
                        .eq(ProductTree::getLastLevel, 0)
                        .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getParentId, xl.getId())
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("卡纸代码"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                            .eq(DictItem::getDictId, 23600L)
                            .eq(DictItem::getLabel, m.get("等级"))
                            .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"卡纸代码".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501;卡纸：3502
                    k.setType(new DictItemEntry().setValue("3502"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("理光系列纬度-卡纸");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }

    /**
     * 理光卡纸 -机型纬度
     * @param
     * @return: {@link Long}
     * @Author: derek
     * @Date: 2023/12/6 21:42
     */
    @Test
    public void lockPaperProductImport() {
        String fileName = "/Users/<USER>/Downloads/import/理光主流机型卡纸代码汇总完整版20240111.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("MP 5500", 4, 104),
                Trio.of("MP C2503,C2003", 7, 62),
                Trio.of("Pro 1107EX,Pro 1357EX", 9, 120)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getName, s)
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("卡纸代码"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                            .eq(DictItem::getDictId, 23600L)
                            .eq(DictItem::getLabel, m.get("等级"))
                            .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"卡纸代码".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501;卡纸：3502
                    k.setType(new DictItemEntry().setValue("3502"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("理光机型纬度-卡纸");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }

}
