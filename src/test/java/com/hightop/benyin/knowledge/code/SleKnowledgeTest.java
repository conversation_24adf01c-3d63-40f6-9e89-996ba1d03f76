package com.hightop.benyin.knowledge.code;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.handle.Tools;
import com.hightop.benyin.knowledge.CosUpload;
import com.hightop.benyin.knowledge.domain.service.KnowledgeBaseInfoDomainService;
import com.hightop.benyin.knowledge.domain.service.KnowledgeRepairCaseDomainService;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeBaseInfo;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.fario.base.Trio;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 把cos上传改成相同案例只上传一次，现在是会重复上传
 *
 * @Description: 知识库test
 * @Author: xhg
 * @Date: 2023/12/25 17:40
 */
public class SleKnowledgeTest extends BaseTest {

    @Autowired
    CosUpload cosUpload;

    @Autowired
    ProductTreeDomainService productTreeDomainService;

    @Autowired
    KnowledgeBaseInfoDomainService knowledgeBaseInfoDomainService;
    @Autowired
    KnowledgeRepairCaseDomainService knowledgeRepairCaseDomainService;

    @Autowired
    DictItemDomainService dictItemDomainService;



    /**
     * 施乐系列
     * 1、先读机器代码，生成机型+知识库类型+标题+代码解释
     *
     * @param
     * @return:
     * @Author: xhg
     * @Date: 2023/12/27 16:57
     */
    @Test
    public void excCodeSeriesImport() {
        String fileName = "/Users/<USER>/Downloads/import/施乐主流机型故障代码汇总完整版20240111.xls";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("AP/DC-IV系C7780", 0, 2039),
                Trio.of("AP/DC-V系C7780", 1, 2039),
//                Trio.of("Printer4110系", 2, 2077),
//                Trio.of("Stanford系B9",3 ,2428 ),
//                Trio.of("Printer系D95", 4, 2206),
//                Trio.of("DC900系", 5, 2113),
//                Trio.of("Xerox系C700",6 , 2246),
//                Trio.of("Matt系V180", 7, 3258),
//                Trio.of("Phin系V3100", 8,3309 ),
//                Trio.of("Color系C75", 9,2345 ),
//                Trio.of("Color系C560", 10, 1429),
//                Trio.of("Color系C70", 11, 2486),
//                Trio.of("AP/DC-IV系C5575", 12, 2248),
//                Trio.of("AP/DC-V系C5575", 13, 2248),
//                Trio.of("AP/DC-IV系C5570", 14, 2255),
                Trio.of("AP/DC-V系C5570", 15, 2255)

        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, s)
                        .eq(ProductTree::getLastLevel, 0)
                        .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getParentId, xl.getId())
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("代码"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                            .eq(DictItem::getDictId, 23600L)
                            .eq(DictItem::getLabel, m.get("等级"))
                            .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"代码".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("施乐系列纬度-代码");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }




    /**
     * 施乐卡纸 -系列纬度
     */
    @Test
    public void lockPaperSeriesImport() {
        String fileName = "/Users/<USER>/Downloads/import/施乐主流机型卡纸代码汇总完整版20240111.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("AP/DC-IV系C7780", 0, 81),
                Trio.of("AP/DC-V系C7780", 1, 81),
                Trio.of("Printer4110系", 2, 133),
                Trio.of("Stanford系B9",3 ,81 ),
                Trio.of("Printer系D95", 4, 83),
                Trio.of("DC900系", 5, 161),
                Trio.of("Xerox系C700",6 , 111),
                Trio.of("Matt系V180", 7, 119),
                Trio.of("Phin系V3100", 8,137 ),
                Trio.of("Color系C75", 9,127 ),
                Trio.of("Color系C560", 10, 97),
                Trio.of("Color系C70", 11, 106),
                Trio.of("AP/DC-IV系C5575", 12, 65),
                Trio.of("AP/DC-V系C5575", 13, 65),
                Trio.of("AP/DC-IV系C5570", 14, 2303),
                Trio.of("AP/DC-V系C5570", 15, 2255)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, s)
                        .eq(ProductTree::getLastLevel, 0)
                        .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getParentId, xl.getId())
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("代码"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                            .eq(DictItem::getDictId, 23600L)
                            .eq(DictItem::getLabel, m.get("等级"))
                            .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"代码".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501;卡纸：3502
                    k.setType(new DictItemEntry().setValue("3502"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("施乐系列纬度-卡纸");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }




}
