package com.hightop.benyin.knowledge.code;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.handle.Tools;
import com.hightop.benyin.knowledge.CosUpload;
import com.hightop.benyin.knowledge.domain.service.KnowledgeBaseInfoDomainService;
import com.hightop.benyin.knowledge.domain.service.KnowledgeRepairCaseDomainService;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeBaseInfo;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.fario.base.Trio;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @Description: 知识库test
 * @Author: xhg
 * @Date: 2023/12/25 17:40
 */
public class KmKnowledgeTest extends BaseTest {

    @Autowired
    CosUpload cosUpload;

    @Autowired
    ProductTreeDomainService productTreeDomainService;

    @Autowired
    KnowledgeBaseInfoDomainService knowledgeBaseInfoDomainService;
    @Autowired
    KnowledgeRepairCaseDomainService knowledgeRepairCaseDomainService;

    @Autowired
    DictItemDomainService dictItemDomainService;



    /**
     * 科美系列纬度
     * 1、先读机器代码，生成机型+知识库类型+标题+代码解释
     *
     * @param
     * @return:
     * @Author: xhg
     * @Date: 2023/12/27 16:57
     */
    @Test
    public void excCodeSeriesImport() {
        String fileName = "/Users/<USER>/Downloads/import/柯美主流机型故障代码20240120.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("BH Press C6100系列", 0, 855),
                Trio.of("AP 6136系列", 1, 1047),
                Trio.of("BH Press C2070系列", 2,655 ),
                Trio.of("BH Press C3070系列", 3, 892),
                Trio.of("AP C7100系列", 4, 1636),
//                Trio.of("AP 2100", 5, 486),
                Trio.of("BH 754系列", 6, 239),
                Trio.of("BH 754e系列", 7, 255),
                Trio.of("BH C754e系列", 8, 271),
                Trio.of("BH Press C1070系列", 9, 618),
//                Trio.of(" BH C7000,BH C6000", 10,551 ),
                Trio.of("BH 1250系列", 11, 751),
//                Trio.of("BH 1100", 12, 305),
                Trio.of("BH 958系列", 13, 289),
                Trio.of("BH C658系列", 14, 327),
                Trio.of("BH Press C1100系列", 15, 739),
                Trio.of("BH 1200系列", 16, 715),
                Trio.of("BH C554系列", 17, 279),
                Trio.of("BH C554e系列", 18,285 ),
                Trio.of("BH C754系列", 19, 283),
                Trio.of("BH Press C4070系列", 20, 1247)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, s)
                        .eq(ProductTree::getLastLevel, 0)
                        .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getParentId, xl.getId())
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("故障代码"));
                    // 没有等级
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (StringUtils.isBlank(mv)) {
                            mv = "";
                        }
                        if (!"故障代码".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("科美系列纬度-代码");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }


    /**
     * 科美机型纬度
     */
    @Test
    public void excCodeProductImport() {
        String fileName = "/Users/<USER>/Downloads/import/柯美主流机型故障代码20240120.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("AP 2100", 5, 486),
                Trio.of(" BH C7000,BH C6000", 10,551 ),
                Trio.of("BH 1100", 12, 305)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                // 系列下的机型
                List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, s)
                        .list();
                if (CollectionUtils.isNotEmpty(models)) {
                    modelList.addAll(models.stream().map(ProductTree::getId)
                            .collect(Collectors.toSet()));
                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("故障代码"));
                    // 没有等级
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (StringUtils.isBlank(mv)) {
                            mv = "";
                        }
                        if (!"故障代码".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("科美系列机型-代码");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }


    private List<Trio<String, Integer, Integer>> getData() {
         List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("BH Press C6100系列", 0, 482),
                Trio.of("AP 6136系列", 1, 630),
                Trio.of("BH Press C2070系列", 2,394 ),
                Trio.of("BH Press C3070系列", 3, 613),
                Trio.of("AP C7100系列", 4, 714),
                //  Trio.of("AP 2100", 5, 265),
                Trio.of("BH 754系列", 6, 91),
                Trio.of("BH 754e系列", 7, 94),
                Trio.of("BH C754e系列", 8, 94),
                Trio.of("BH Press C1070系列", 9, 373),
                //  Trio.of(" BH C7000,BH C6000", 10,292 ),
                Trio.of("BH 1250系列", 11, 492),
                //Trio.of("BH 1100", 12, 216),
                Trio.of("BH 958系列", 13, 82),
                //  Trio.of("BH C368,BH C308,BH C258", 14, 74),
                //  Trio.of("BH C658,BH C558,BH C458", 15, 84),
                Trio.of("BH Press C1100系列", 16, 440),
                Trio.of("BH 1200系列", 17, 398),
                Trio.of("BH C554系列", 18, 83),
                Trio.of("BH C554e系列", 19,92 ),
                Trio.of("BH C754系列", 20, 90),
                Trio.of("BH Press C4070系列", 21, 676)
        );
        return sheets;
    }
    /**
     * 科美卡纸 -系列纬度
     */
    @Test
    public void lockPaperSeriesImport() {
        String fileName = "/Users/<USER>/Downloads/import/柯美主流机型卡纸代码汇总完整版20240111.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = getData();
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getName, s)
                        .eq(ProductTree::getLastLevel, 0)
                        .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getParentId, xl.getId())
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("卡纸代码"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                            .eq(DictItem::getDictId, 23600L)
                            .eq(DictItem::getLabel, m.get("等级"))
                            .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"卡纸代码".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501;卡纸：3502
                    k.setType(new DictItemEntry().setValue("3502"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("科美系列纬度-卡纸");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }


    /**
     * 理光卡纸 -机型纬度
     */
    @Test
    public void lockPaperProductImport() {
        String fileName = "/Users/<USER>/Downloads/import/柯美主流机型卡纸代码汇总完整版20240111.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
                Trio.of("AP 2100", 5, 265),
                Trio.of("BH C7000,BH C6000", 10,292 ),
                Trio.of("BH 1100", 12, 216),
                Trio.of("BH C368,BH C308,BH C258", 14, 74),
                Trio.of("BH C658,BH C558,BH C458", 15, 84)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                            .eq(ProductTree::getName, s)
                            .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                                .collect(Collectors.toSet()));
                    }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                    fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("卡纸代码"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                            .eq(DictItem::getDictId, 23600L)
                            .eq(DictItem::getLabel, m.get("等级"))
                            .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"卡纸代码".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                    mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501;卡纸：3502
                    k.setType(new DictItemEntry().setValue("3502"));
                    k.setIsEnable(Boolean.FALSE);
                    k.setRemark("科美机型纬度-卡纸");
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }

}
