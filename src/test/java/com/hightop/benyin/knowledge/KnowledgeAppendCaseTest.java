package com.hightop.benyin.knowledge;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.fasterxml.jackson.databind.JsonNode;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.knowledge.domain.service.KnowledgeBaseInfoDomainService;
import com.hightop.benyin.knowledge.domain.service.KnowledgeRepairCaseDomainService;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeBaseInfo;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeRepairCase;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.util.UuidUtils;
import com.hightop.fario.common.jackson.JacksonUtils;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.apache.poi.ss.usermodel.Workbook;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.hightop.benyin.knowledge.CommonUtils.*;

/**
 * @Description: 知识库追加维修案例test
 * @Author: xhg
 * @Date: 2023/12/25 17:40
 */
public class KnowledgeAppendCaseTest extends BaseTest {
    /**
     * COS云地址前缀
     */
    private static final String COS_URL_PREFIX = "https://benyin-1315885374.cos.ap-chengdu.myqcloud.com/prod/knowledge/";
    /**
     * 历史数据本印存储访问前缀(html标签)
     */
    private static final String OLD_HTML_PREFIX = "https://work.benyin.ltd";
    /**
     * 历史数据本印存储访问前缀（非html标签）
     */
    private static final String OLD_NOT_HTML_PREFIX = "https://work.benyin.ltd/seeyon/fileUpload.do?method=showRTE&fileId=";
    /**
     * 本印系统最新token
     */
    private static final String NEWEST_TOKEN = "ts=1705549305658; JSESSIONID=EBAD2D2D36BE95BD29B665D12ADFD0B0; login_locale=zh_CN; avatarImageUrl=4886330481952901351; loginPageURL=";

    Map<String, List<KnowledgeRepairCase>> cache = new ConcurrentHashMap<>();
    @Autowired
    CosUpload cosUpload;

    @Autowired
    ProductTreeDomainService productTreeDomainService;

    @Autowired
    KnowledgeBaseInfoDomainService knowledgeBaseInfoDomainService;
    @Autowired
    KnowledgeRepairCaseDomainService knowledgeRepairCaseDomainService;

    @Autowired
    DictItemDomainService dictItemDomainService;

    public static void main(String[] args) {
        String fileName = "C:\\Users\\<USER>\\Desktop\\知识库卡纸报代码知识库案例汇总.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        params.setStartSheetIndex(0);
        params.setSheetNum(18);
        List<KnowledgeTemplateExcel> data = ExcelImportUtil.importExcel(new File(fileName), KnowledgeTemplateExcel.class, params);
        System.out.println("数量条数：" + data.size());
        // 执行前校验知识库类型是否正确；是否只有产品树、系列；校验产品树和系列能否在正式环境查到

        System.out.println(data.stream().map(KnowledgeTemplateExcel::getType).collect(Collectors.toSet()));
        System.out.println(data.stream().map(KnowledgeTemplateExcel::getRange).collect(Collectors.toSet()));
        System.out.println(data.stream().map(KnowledgeTemplateExcel::getSeries).collect(Collectors.toSet()));
        System.out.println(data.stream().map(KnowledgeTemplateExcel::getTree).collect(Collectors.toSet()));
    }


    /**
     * 根据案例补充维修案例（故障描述+解决措施）
     * 根据线上已有数据来匹配（代码+机型）添加，没有匹配到的打印出来
     * 同步知识库基础表，清空案例表，导入
     *
     * @Author: xhg
     * @Date: 2024/1/17 16:21
     */
    @Test
    public void appendCase() throws IOException {
        LocalDateTime now = LocalDateTime.now();
        List<KnowledgeTemplateExcel> data =getData();
        List<KnowledgeTemplateExcel> notFound = new ArrayList<>();
        int size = data.size();
        int ic=0;
        for (KnowledgeTemplateExcel k : data) {
            String range = k.getRange();
            String tree = k.getTree();
            String serial = k.getSeries();
            // 找到对应的机型
            Set<String> modelIds = getModel(tree, serial, range);
            StringBuffer sql = new StringBuffer();
            modelIds.stream().forEach(e -> sql.append(" OR JSON_CONTAINS(product_list,'" + e + "')"));
            // 查询故障代码与适用范围所匹配的机型，给基础信息添加案例
            List<KnowledgeBaseInfo> baseInfo = this.knowledgeBaseInfoDomainService.lambdaQuery()
                .eq(KnowledgeBaseInfo::getTitle, k.getCode())
                .last("AND (" + sql.substring(3) + ")")
                .list();
            if (CollectionUtils.isEmpty(baseInfo)) {
                // 没有找到对应的知识库
                notFound.add(k);
            } else {
                if (StringUtils.isBlank(k.getId())){
                    notFound.add(k);
                    ic++;
                    continue;
                }
                Long bs = this.knowledgeRepairCaseDomainService.lambdaQuery()
                    .in(KnowledgeRepairCase::getBaseId, baseInfo.stream()
                        .map(KnowledgeBaseInfo::getId).collect(Collectors.toSet()))
                    .count();
                // 导入过的就不要导了
                if (bs > 0) {
                    ic++;
                    continue;
                }
                // 缓存案例
                String[] ids = k.getId().split("\n");
                List<KnowledgeRepairCase> cases = new ArrayList<>();
                for (String i : ids) {
                    List<KnowledgeRepairCase> exist = cache.get(i);
                    if (CollectionUtils.isEmpty(exist)) {
                        readCaseWordContent(i);
                        List<KnowledgeRepairCase> ci = cache.get(i);
                        if (CollectionUtils.isNotEmpty(ci)) {
                            cases.addAll(ci);
                        }
                    } else {
                        cases.addAll(cases);
                    }
                }
                if (cases.size() > 0) {
                    // 给匹配上的知识库添加案例
                    for (KnowledgeBaseInfo kbi : baseInfo) {
                        List<KnowledgeRepairCase> add = new ArrayList<>();
                        for (KnowledgeRepairCase krc : cases) {
                            KnowledgeRepairCase c = new KnowledgeRepairCase();
                            c.setBaseId(kbi.getId());
                            c.setDeleted(0);
                            c.setCreatedAt(now);
                            c.setUpdatedAt(now);
                            c.setFaultDesc(krc.getFaultDesc());
                            c.setTitle(krc.getTitle());
                            c.setSolutionMeasures(krc.getSolutionMeasures());
                            add.add(c);
                        }
                        this.knowledgeRepairCaseDomainService.saveBatch(add);
                    }
                }
            }
            ic++;
            System.out.println("总计："+size+"已完成:"+ic);
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), KnowledgeTemplateExcel.class, notFound);
        //将工作簿的数据写到指定位置生成表格 web开发可直接将将流输出给前端
        FileOutputStream fileOutputStream = new FileOutputStream(
            "C:\\Users\\<USER>\\Desktop\\没有找到对应知识库.xlsx");
        workbook.write(fileOutputStream);
        fileOutputStream.close();
        workbook.close();
    }


    private List<KnowledgeTemplateExcel> getData(){
        String fileName = "C:\\Users\\<USER>\\Desktop\\知识库卡纸报代码知识库案例汇总.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        params.setStartSheetIndex(0);
        params.setSheetNum(18);
        return ExcelImportUtil.importExcel(new File(fileName), KnowledgeTemplateExcel.class, params);
    }
    /**
     * 根据产品树、系列查询机型
     *
     * @param treeName
     * @param serialName
     * @param range
     * @return: {@link Set< Long>}
     * @Author: xhg
     * @Date: 2024/1/17 17:59
     */
    private Set<String> getModel(String treeName, String serialName, String range) {
        Set<String> set = new HashSet<>();
        if (Objects.equals(TREE, range)) {
            String[] tn = treeName.split(",\n");
            for (String t : tn) {
                ProductTree tree = this.productTreeDomainService.lambdaQuery()
                    .select(ProductTree::getId)
                    .eq(ProductTree::getName, t)
                    .eq(ProductTree::getLastLevel, 0)
                    .one();
                List<ProductTree> serial = this.productTreeDomainService.lambdaQuery()
                    .select(ProductTree::getId)
                    .eq(ProductTree::getParentId, tree.getId())
                    .eq(ProductTree::getLastLevel, 0)
                    .list();
                serial.stream().forEach(s -> {
                    set.addAll(this.productTreeDomainService.lambdaQuery()
                        .select(ProductTree::getId)
                        .eq(ProductTree::getParentId, s.getId())
                        .eq(ProductTree::getLastLevel, 1)
                        .list().stream().map(ProductTree::getId)
                        .map(String::valueOf)
                        .collect(Collectors.toList()));
                });
            }
        } else if (Objects.equals(SERIAL, range)) {
            String[] sn = serialName.split(",\n");
            for (String s : sn) {
                ProductTree serial = this.productTreeDomainService.lambdaQuery()
                    .select(ProductTree::getId)
                    .eq(ProductTree::getName, s)
                    .eq(ProductTree::getLastLevel, 0)
                    .one();
                set.addAll(this.productTreeDomainService.lambdaQuery()
                    .select(ProductTree::getId)
                    .eq(ProductTree::getParentId, serial.getId())
                    .eq(ProductTree::getLastLevel, 1)
                    .list().stream().map(ProductTree::getId)
                    .map(String::valueOf)
                    .collect(Collectors.toList()));
            }
        }
        return set;
    }

    @Test
    public void check() {
        String fileName = "C:\\Users\\<USER>\\Desktop\\知识库卡纸报代码知识库案例汇总.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        params.setStartSheetIndex(0);
        params.setSheetNum(18);
        List<KnowledgeTemplateExcel> data = ExcelImportUtil.importExcel(new File(fileName), KnowledgeTemplateExcel.class, params);
        Set<String> t = data.stream().filter(f -> Objects.equals(TREE, f.getRange())).map(KnowledgeTemplateExcel::getTree).collect(Collectors.toSet());
        Set<String> s = data.stream()
            .filter(f -> Objects.equals(SERIAL, f.getRange()))
            .map(KnowledgeTemplateExcel::getSeries)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        List<String> msg = new ArrayList<>();
        t.stream().forEach(e -> {
            ProductTree product = this.productTreeDomainService.lambdaQuery()
                .eq(ProductTree::getName, e)
                .eq(ProductTree::getLastLevel, 0)
                .one();
            if (Objects.isNull(product)) {
                msg.add("没有找到产品树：" + e);
            }
        });
        s.stream().forEach(e -> {
            String[] res = e.split(",\n");
            for (String str : res) {
                ProductTree product = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getName, str)
                    .eq(ProductTree::getLastLevel, 0)
                    .one();
                if (Objects.isNull(product)) {
                    msg.add("没有找到系列：" + str);
                }
            }
        });
        System.out.println(msg);
    }


    private String replaceImg(String html) {
        Document doc = Jsoup.parse(html);
        Elements img = doc.getElementsByTag("img");
        img.stream().iterator().forEachRemaining(i -> {
            String src = i.attr("src");
            String cosUrl = null;
            try {
                cosUrl = parseImg(OLD_HTML_PREFIX + src);
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 替换src
            i.attr("src", cosUrl);
        });
        return doc.outerHtml();
    }

    private String requestBody(String moduleId) {
        Map<String, String> map = new HashMap<>(4);
        map.put("rightId", "7027768636809955559.6655775903610913817");
        // 数据id
        map.put("moduleId", moduleId);
        map.put("moduleType", "42");
        map.put("operateType", "2");
        HttpHeaders headers = new HttpHeaders();
        headers.set("Cookie", NEWEST_TOKEN);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(JacksonUtils.serialize(map), headers);
        ResponseEntity<String> response = new RestTemplate()
            .exchange("https://work.benyin.ltd/seeyon/rest/cap4/form/createOrEdit",
                HttpMethod.POST, request, String.class);
        return response.getBody();
    }

    /**
     * 查找案例内容
     *
     * @param moduleId
     * @return:
     * @Author: xhg
     * @Date: 2024/1/17 19:16
     */
    public void readCaseWordContent(String moduleId) {
        if (StringUtils.isBlank(moduleId)) {
            return;
        }
        List<KnowledgeRepairCase> cases = new ArrayList<>();
        String resultBody = requestBody(moduleId);
        JsonNode jsonNode = JacksonUtils.deserialize(resultBody, JsonNode.class);
        JsonNode data = jsonNode.get("data");
        if (null != data && !data.isNull()) {
            JsonNode data2 = data.get("data");
            if (null != data2 && !data2.isNull()) {
                JsonNode tableInfo = data2.get("tableInfo");
                JsonNode formson = tableInfo.get("formson");
                String word = "文档库";
                formson.iterator().forEachRemaining(i -> {
                    JsonNode display = i.get("display");
                    // 只读取文档库
                    if (word.equals(display.textValue())) {
                        Map<String, String> fieldInfo = fieldVal(i.get("fieldInfo"));
                        JsonNode items = i.get("pageData").get("items");
                        items.iterator().forEachRemaining(it -> {
                            // 有多少组数据就有多少案例
                            KnowledgeRepairCase krc = new KnowledgeRepairCase();
                            String desc = it.get(fieldInfo.get("文档库-现象描述")).get("value").textValue();
                            // 标题=现象描述
                            krc.setTitle(desc);
                            JsonNode images = it.get(fieldInfo.get("文档库-描述图片"));
                            JsonNode attachmentInfos = images.get("attachmentInfo").get("attachmentInfos");
                            List<String> imgList = new ArrayList<>();
                            if (!attachmentInfos.isNull()) {
                                attachmentInfos.iterator().forEachRemaining(a -> {
                                    // 文件id
                                    String fileUrl = a.get("fileUrl").textValue();
                                    String img = OLD_NOT_HTML_PREFIX + fileUrl + "&type=image";
                                    imgList.add(img);
                                });
                            }
                            // 追加多个图片
                            StringBuffer faultDesc = new StringBuffer("<p>" + desc + "</p></br>");
                            List<String> cosUrl = parseImg(imgList);
                            if (cosUrl.size() > 0) {
                                cosUrl.stream().forEach(e ->
                                    faultDesc.append("<img src=\"" + e + "\" width=\"100%\"/></br>"));
                            }
                            // 故障描述=现象描述+描述图片
                            krc.setFaultDesc(faultDesc.toString());
                            // 文本
                            String reason = it.get(fieldInfo.get("文档库-原因")).get("value").textValue();
                            // 富文本html标签
                            String process = it.get(fieldInfo.get("文档库-处理过程")).get("value").textValue();
                            // 文本
                            String measure = it.get(fieldInfo.get("文档库-解决措施")).get("value").textValue();
                            // 解决措施=原因+处理过程+解决措施（按顺序）
                            krc.setSolutionMeasures("<p>" + reason + "</p></br>" +
                                // 解析html标签，替换掉里面的img的src到cos-url
                                replaceImg(process) + "</br>" +
                                "<p>" + measure + "</p></br>");
                            cases.add(krc);
                        });
                    }
                });
                cache.put(moduleId, cases);
            }
        }
    }

    private List<String> parseImg(List<String> imgList) {
        List<String> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(imgList)) {
            imgList.stream().forEach(e -> {
                InputStream in = getNetUrlInputStream(e);
                if (null != in) {
                    // 根据url读取文件流上传到cos
                    String fileName = UuidUtils.full() + ".png";
                    try {
                        cosUpload.upload(fileName, in);
                    } catch (Exception exception) {
                        exception.printStackTrace();
                    }
                    list.add(COS_URL_PREFIX + fileName);
                }
            });
        }
        return list;
    }

    private String parseImg(String imgUrl) throws Exception {
        InputStream in = getNetUrlInputStream(imgUrl);
        if (null != in) {
            // 根据url读取文件流上传到cos
            String fileName = UuidUtils.full() + ".png";
            cosUpload.upload(fileName, in);
            return COS_URL_PREFIX + fileName;
        }
        return "";
    }


    private static Map<String, String> fieldVal(JsonNode fieldInfo) {
        Map<String, String> map = new HashMap<>(16);
        fieldInfo.elements().forEachRemaining(i -> {
            map.put(i.get("display").textValue(), i.get("name").textValue());
        });
        return map;
    }
}
