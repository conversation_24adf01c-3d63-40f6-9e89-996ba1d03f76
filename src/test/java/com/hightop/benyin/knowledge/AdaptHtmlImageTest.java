package com.hightop.benyin.knowledge;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.knowledge.domain.service.KnowledgeRepairCaseDomainService;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.util.codec.UrlCodec;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 维修案例中图片自适应
 * <AUTHOR>
 * @date 2024/02/02 12:31
 */
public class AdaptHtmlImageTest extends BaseTest {
    @Autowired
    KnowledgeRepairCaseDomainService knowledgeRepairCaseDomainService;
    private static final String WIDTH = "width", HEIGHT = "height";
    private static final int LEN = 2;

    @Test
    public void adapt() throws IOException {
        StringBuilder sb = new StringBuilder("SET CHARACTER SET utf8mb4;").append(StringConstants.BR);
        this.knowledgeRepairCaseDomainService.list()
            .forEach(it -> {
                Document document = Jsoup.parse(it.getSolutionMeasures());
                document.getElementsByTag("img")
                    .forEach(img -> {
                        img.attr(WIDTH, "100%");
                        img.removeAttr(HEIGHT);

                        // 解析style
                        String style = img.attr("style");
                        if (StringUtils.isEmpty(style)) {
                            return;
                        }
                        String newStyle =
                            Arrays.stream(style.split(StringConstants.SEMICOLON))
                                .map(v -> {
                                    String[] split = v.split(StringConstants.COLON);
                                    String key;
                                    if (split.length < LEN || Objects.equals(WIDTH, key = split[0].trim())
                                        || Objects.equals(HEIGHT, key)) {
                                        return null;
                                    }
                                    return String.format("%s: %s", key, split[1].trim());
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(StringConstants.COLON));
                        if (StringUtils.isEmpty(style)) {
                            img.removeAttr(style);
                            return;
                        }

                        img.attr("style", newStyle);
                    });
                sb.append(
                    String.format(
                        "UPDATE `b_knowledge_repair_case` set `solution_measures` = '%s' WHERE `id` = %d;",
                        document.body()
                            .html()
                            .replaceAll(
                                StringConstants.SINGLE_QUOTE,
                                StringConstants.SINGLE_QUOTE + StringConstants.SINGLE_QUOTE
                            ),
                        it.getId()
                    )
                );
                sb.append(StringConstants.BR);
            });


        try (OutputStream os = Files.newOutputStream(
            Paths.get(UrlCodec.decode(AdaptHtmlImageTest.class.getResource("/").getFile().substring(1) + "adapt.sql")),
            StandardOpenOption.CREATE,
            StandardOpenOption.TRUNCATE_EXISTING
        )
        ) {
            os.write(sb.toString().getBytes(StandardCharsets.UTF_8));
        }
    }
}
