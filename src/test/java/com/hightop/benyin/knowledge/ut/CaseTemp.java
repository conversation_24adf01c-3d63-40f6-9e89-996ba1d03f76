package com.hightop.benyin.knowledge.ut;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 维修案例实体
 * <AUTHOR>
 * @date 2023-12-21 15:24:26
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("b_case_temp")
@ApiModel
public class CaseTemp {
    @TableId("history_id")
    @ApiModelProperty("历史数据id")
    String historyId;

    @TableField("title")
    @ApiModelProperty("案例标题")
    String title;

    @TableField("fault_desc")
    @ApiModelProperty("故障描述")
    String faultDesc;

    @TableField("solution_measures")
    @ApiModelProperty("解决措施")
    String solutionMeasures;

    @TableField("remark")
    @ApiModelProperty("备足")
    String remark;

}
