package com.hightop.benyin.knowledge.ut;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.knowledge.KnowledgeTemplateExcel;
import com.hightop.benyin.knowledge.domain.service.KnowledgeBaseInfoDomainService;
import com.hightop.benyin.knowledge.domain.service.KnowledgeRepairCaseDomainService;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeBaseInfo;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeRepairCase;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.dictionary.dict.Dict;
import com.hightop.magina.standard.code.dictionary.dict.DictDomainService;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.hightop.benyin.knowledge.CommonUtils.SERIAL;
import static com.hightop.benyin.knowledge.CommonUtils.TREE;

/**
 * @Description: 知识库效果案例test(不设置代码解释)
 * @Author: xhg
 * @Date: 2023/12/25 17:40
 */
public class EffectTest extends BaseTest {
    @Autowired
    ProductTreeDomainService productTreeDomainService;

    @Autowired
    KnowledgeBaseInfoDomainService knowledgeBaseInfoDomainService;
    @Autowired
    KnowledgeRepairCaseDomainService knowledgeRepairCaseDomainService;

    @Autowired
    DictItemDomainService dictItemDomainService;
    @Autowired
    DictDomainService dictDomainService;

    private List<KnowledgeTemplateExcel> getData() {
        String fileName = "C:\\Users\\<USER>\\Desktop\\案例库效果类汇总new.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        // 读取excel的sheet（填写文件sheet个数）
        params.setSheetNum(26);
        return ExcelImportUtil.importExcel(new File(fileName), KnowledgeTemplateExcel.class, params);

    }

    /**
     * 效果类、卡纸知识库导入：添加知识库基础信息+维修案例
     *
     * @Author: xhg
     * @Date: 2024/1/17 16:21
     */
    @Test
    public void importData() throws IOException {
        LocalDateTime now = LocalDateTime.now();
        List<KnowledgeTemplateExcel> data = getData();
        List<KnowledgeTemplateExcel> notFound = new ArrayList<>();
        for (KnowledgeTemplateExcel krc : data) {
            String range = krc.getRange();
            String tree = krc.getTree();
            String serial = krc.getSeries();
            String typeCode = this.getDictItemValue("3500", krc.getType());
            // 没有找到知识库类型就不导入
            if (StringUtils.isNotBlank(typeCode)) {
                // 找到对应的机型
                Set<String> modelIds = getModel(tree, serial, range);
                Long baseId = IdWorker.getId();
                KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                k.setId(baseId);
                k.setProductList(modelIds.stream().map(Long::valueOf).collect(Collectors.toSet()));
                k.setTitle(krc.getCode());
                k.setOperatorId(101L);
                k.setCreatedAt(now);
                k.setUpdatedAt(now);
                k.setDeleted(0);
                k.setType(new DictItemEntry().setValue(typeCode));
                k.setIsEnable(Boolean.TRUE);
                k.setTags(keywordParse(krc));
                this.knowledgeBaseInfoDomainService.save(k);
                // 导入维修案例
                if (StringUtils.isNotBlank(krc.getId())) {
                    Set<String> ids = Arrays.stream(krc.getId().split("\n")).collect(Collectors.toSet());
                    List<CaseTemp> cases = new ArrayList<>();
                    if (cases.size() > 0) {
                        // 给匹配上的知识库添加案例
                        List<KnowledgeRepairCase> add = new ArrayList<>();
                        for (CaseTemp ct : cases) {
                            KnowledgeRepairCase c = new KnowledgeRepairCase();
                            c.setBaseId(baseId);
                            c.setDeleted(0);
                            c.setCreatedAt(now);
                            c.setUpdatedAt(now);
                            c.setFaultDesc(ct.getFaultDesc());
                            c.setTitle(ct.getTitle());
                            c.setSolutionMeasures(ct.getSolutionMeasures());
                            add.add(c);
                        }
                        this.knowledgeRepairCaseDomainService.saveBatch(add);
                    }
                }
            } else {
                notFound.add(krc);
            }
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), KnowledgeTemplateExcel.class, notFound);
        //将工作簿的数据写到指定位置生成表格 web开发可直接将将流输出给前端
        FileOutputStream fileOutputStream = new FileOutputStream(
            "C:\\Users\\<USER>\\Desktop\\没有找到对应知识库2.xlsx");
        workbook.write(fileOutputStream);
        fileOutputStream.close();
        workbook.close();
    }

    private String keywordParse(KnowledgeTemplateExcel krc) {
        String tags = "";
        String k1 = Optional.ofNullable(krc.getKeyword1()).orElse("").trim();
        String k2 = Optional.ofNullable(krc.getKeyword2()).orElse("").trim();
        String k3 = Optional.ofNullable(krc.getKeyword3()).orElse("").trim();
        String k4 = Optional.ofNullable(krc.getKeyword4()).orElse("").trim();
        if (StringUtils.isNotBlank(k1)) {
            tags += k1 + ",";
        }
        if (StringUtils.isNotBlank(k2)) {
            tags += k2 + ",";
        }
        if (StringUtils.isNotBlank(k3)) {
            tags += k3 + ",";
        }
        if (StringUtils.isNotBlank(k4)) {
            tags += k4 + ",";
        }
        if (StringUtils.isNotBlank(tags)) {
            tags = tags.substring(0, tags.length() - 1);
        }
        return tags;
    }


    private String getDictItemValue(String dictCode, String itemLabel) {
        Dict d = dictDomainService.getByCode(dictCode);
        DictItem i = this.dictItemDomainService.lambdaQuery().eq(DictItem::getLabel, itemLabel)
            .eq(DictItem::getDictId, d.getId()).one();
        return Optional.ofNullable(i).map(DictItem::getValue).orElse(null);
    }

    /**
     * 根据产品树、系列查询机型
     *
     * @param treeName
     * @param serialName
     * @param range
     * @return: {@link Set< Long>}
     * @Author: xhg
     * @Date: 2024/1/17 17:59
     */
    private Set<String> getModel(String treeName, String serialName, String range) {
        Set<String> set = new HashSet<>();
        if (Objects.equals(TREE, range)) {
            String[] tn = treeName.split(",\n");
            for (String t : tn) {
                ProductTree tree = this.productTreeDomainService.lambdaQuery()
                    .select(ProductTree::getId)
                    .eq(ProductTree::getName, t)
                    .eq(ProductTree::getLastLevel, 0)
                    .one();
                List<ProductTree> serial = this.productTreeDomainService.lambdaQuery()
                    .select(ProductTree::getId)
                    .eq(ProductTree::getParentId, tree.getId())
                    .eq(ProductTree::getLastLevel, 0)
                    .list();
                serial.stream().forEach(s -> {
                    set.addAll(this.productTreeDomainService.lambdaQuery()
                        .select(ProductTree::getId)
                        .eq(ProductTree::getParentId, s.getId())
                        .eq(ProductTree::getLastLevel, 1)
                        .list().stream().map(ProductTree::getId)
                        .map(String::valueOf)
                        .collect(Collectors.toList()));
                });
            }
        } else if (Objects.equals(SERIAL, range)) {
            String[] sn = serialName.split(",\n");
            for (String s : sn) {
                ProductTree serial = this.productTreeDomainService.lambdaQuery()
                    .select(ProductTree::getId)
                    .eq(ProductTree::getName, s)
                    .eq(ProductTree::getLastLevel, 0)
                    .one();
                set.addAll(this.productTreeDomainService.lambdaQuery()
                    .select(ProductTree::getId)
                    .eq(ProductTree::getParentId, serial.getId())
                    .eq(ProductTree::getLastLevel, 1)
                    .list().stream().map(ProductTree::getId)
                    .map(String::valueOf)
                    .collect(Collectors.toList()));
            }
        }
        return set;
    }
}
