package com.hightop.benyin.knowledge.ut;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.knowledge.CosUpload;
import com.hightop.benyin.knowledge.KnowledgeTemplateExcel;
import com.hightop.benyin.knowledge.service.CaseTempDomainService;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.util.UuidUtils;
import com.hightop.fario.common.jackson.JacksonUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static com.hightop.benyin.knowledge.CommonUtils.getNetUrlInputStream;
import static com.hightop.benyin.knowledge.ut.CommonConstants.*;

/**
 * @Description: 缓存历史维修案例
 * @Author: xhg
 * @Date: 2023/12/25 17:40
 */
public class CacheHistory extends BaseTest {
    @Autowired
    CosUpload cosUpload;
    @Autowired
    CaseTempDomainService caseTempDomainService;


    /**
     * 缓存案例到数据库
     *
     * @return:
     * @Author: xhg
     * @Date: 2024/1/19 17:45
     */
    @Test
    public void parseCaseToDb() {
        String fileName = "/Users/<USER>/Downloads/help/fix.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        // 读取excel的sheet（填写文件sheet个数）
        params.setSheetNum(1);
        List<KnowledgeTemplateExcel> data = ExcelImportUtil.importExcel(new File(fileName), KnowledgeTemplateExcel.class, params);
        Set<String> idSet = data.stream()
            .filter(f -> StringUtils.isNotBlank(f.getId()))
            .map(KnowledgeTemplateExcel::getId).collect(Collectors.toSet());
        Integer index = 1;
        for (String ids : idSet) {
            System.out.println("当前第"+index+"条数据");
            index++;
            String[] id = ids.split("\n");
            for (String i : id) {
                List<CaseTemp> existList = caseTempDomainService.list(new LambdaQueryWrapper<CaseTemp>().eq(CaseTemp::getHistoryId, i));
                if (CollectionUtils.isNotEmpty(existList)) {
                    continue;
                }else {
                    try {
                        this.readCaseWordContent(i);
                    } catch (Exception e) {
                        CaseTemp caseTemp = new CaseTemp();
                        caseTemp.setHistoryId(i);
                        caseTemp.setRemark("异常");
                        caseTemp.setRemark(e.getStackTrace().toString());
                        caseTempDomainService.save(caseTemp);
                        e.printStackTrace();
                    }
                }

            }
        }
    }
    @Test
    public void run() {
        readCaseWordContent("-2048205279616873190");
    }

    /**
     * 从老系统爬取维修案例并入库
     *
     * @param moduleId
     * @return:
     * @Author: xhg
     * @Date: 2024/1/17 19:16
     */
    public void readCaseWordContent(String moduleId) {
        if (StringUtils.isBlank(moduleId)) {
            return;
        }
        List<CaseTemp> cases = new ArrayList<>();
        String resultBody = requestBody(moduleId);
        JsonNode jsonNode = JacksonUtils.deserialize(resultBody, JsonNode.class);
        JsonNode data = jsonNode.get("data");
        if (null != data && !data.isNull()) {
            JsonNode data2 = data.get("data");
            if (null != data2 && !data2.isNull()) {
                JsonNode tableInfo = data2.get("tableInfo");
                JsonNode formson = tableInfo.get("formson");
                String word = "文档库";
                formson.iterator().forEachRemaining(i -> {
                    JsonNode display = i.get("display");
                    // 只读取文档库
                    if (word.equals(display.textValue())) {
                        Map<String, String> fieldInfo = fieldVal(i.get("fieldInfo"));
                        JsonNode items = i.get("pageData").get("items");
                        items.iterator().forEachRemaining(it -> {
                            // 有多少组数据就有多少案例
                            CaseTemp krc = new CaseTemp();
                            krc.setHistoryId(moduleId);
                            String desc = it.get(fieldInfo.get("文档库-现象描述")).get("value").textValue();
                            // 标题=现象描述
                            krc.setTitle(desc);
                            JsonNode images = it.get(fieldInfo.get("文档库-描述图片"));
                            JsonNode attachmentInfos = images.get("attachmentInfo").get("attachmentInfos");
                            List<String> imgList = new ArrayList<>();
                            if (!attachmentInfos.isNull()) {
                                attachmentInfos.iterator().forEachRemaining(a -> {
                                    // 文件id
                                    String fileUrl = a.get("fileUrl").textValue();
                                    String img = OLD_NOT_HTML_PREFIX + fileUrl + "&type=image";
                                    imgList.add(img);
                                });
                            }
                            // 追加多个图片
                            StringBuffer faultDesc = new StringBuffer("<p>" + desc + "</p></br>");
                            List<String> cosUrl = parseImg(imgList);
                            if (cosUrl.size() > 0) {
                                for (String url : cosUrl) {
                                    if (StringUtils.isBlank(url)) {
                                        krc.setRemark("图片缺失");
                                    }
                                    faultDesc.append("<img src=\"" + url + "\" width=\"100%\"/></br>");
                                }

                            }
                            // 故障描述=现象描述+描述图片
                            krc.setFaultDesc(faultDesc.toString());
                            // 文本
                            String reason = it.get(fieldInfo.get("文档库-原因")).get("value").textValue();
                            // 富文本html标签
                            String process = it.get(fieldInfo.get("文档库-处理过程")).get("value").textValue();
                            // 文本
                            String measure = it.get(fieldInfo.get("文档库-解决措施")).get("value").textValue();
                            // 解决措施=原因+处理过程+解决措施（按顺序）
                            krc.setSolutionMeasures("<p>" + reason + "</p></br>" +
                                // 解析html标签，替换掉里面的img的src到cos-url
                                replaceImg(process,krc) + "</br>" +
                                "<p>" + measure + "</p></br>");
                            cases.add(krc);
                        });
                    }
                });
                if (cases.size() > 0) {
                    caseTempDomainService.saveBatch(cases);
                }
            }
        }
    }

    private List<String> parseImg(List<String> imgList) {
        List<String> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(imgList)) {
            imgList.stream().forEach(e -> {
                InputStream in = getNetUrlInputStream(e);
                if (null != in) {
                    // 根据url读取文件流上传到cos
                    String fileName = UuidUtils.full() + ".png";
                    try {
                        cosUpload.upload(fileName, in);
                    }catch (Exception exception){
                        fileName = "";
                    }finally {
                        if (null != in) {
                            try {
                                in.close();
                            } catch (IOException ioException) {
                                ioException.printStackTrace();
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(fileName)) {
                        list.add(COS_URL_PREFIX + fileName);
                    }else {
                        list.add("");
                    }

                }
            });
        }
        return list;
    }

    private String replaceImg(String html,CaseTemp caseTemp) {
        Document doc = Jsoup.parse(html);
        Elements img = doc.getElementsByTag("img");
        img.stream().iterator().forEachRemaining(i -> {
            String src = i.attr("src");
            String cosUrl = parseImg(OLD_HTML_PREFIX + src);
            if (StringUtils.isBlank(cosUrl)) {
                caseTemp.setRemark("图片缺失");
            }
            // 替换src
            i.attr("src", cosUrl);
        });
        return doc.outerHtml();
    }

    private String parseImg(String imgUrl) {
        InputStream in = getNetUrlInputStream(imgUrl);
        if (null != in) {
            // 根据url读取文件流上传到cos
            String fileName = UuidUtils.full() + ".png";
            try {
                cosUpload.upload(fileName, in);
            } catch (Exception e) {
                e.printStackTrace();
                return "";
            }finally {
                if (null != in) {
                    try {
                        in.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            return COS_URL_PREFIX + fileName;
        }
        return "";
    }

    private String requestBody(String moduleId) {
        Map<String, String> map = new HashMap<>(4);
        map.put("rightId", "7027768636809955559.6655775903610913817");
        // 数据id
        map.put("moduleId", moduleId);
        map.put("moduleType", "42");
        map.put("operateType", "2");
        HttpHeaders headers = new HttpHeaders();
        headers.set("Cookie", NEWEST_TOKEN);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(JacksonUtils.serialize(map), headers);
        ResponseEntity<String> response = new RestTemplate()
            .exchange("https://work.benyin.ltd/seeyon/rest/cap4/form/createOrEdit",
                HttpMethod.POST, request, String.class);
        return response.getBody();
    }

    private static Map<String, String> fieldVal(JsonNode fieldInfo) {
        Map<String, String> map = new HashMap<>(16);
        fieldInfo.elements().forEachRemaining(i -> {
            map.put(i.get("display").textValue(), i.get("name").textValue());
        });
        return map;
    }
}
