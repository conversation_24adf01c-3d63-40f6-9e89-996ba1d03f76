package com.hightop.benyin.knowledge.service;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * 维修案例实体
 * <AUTHOR>
 * @date 2023-12-21 15:24:26
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName("tb_error_info")
@ApiModel
public class ErrorInfo {
    @TableId("code")
    @ApiModelProperty("历史数据id")
    String code;

    @TableField("type")
    @ApiModelProperty("类型")
    String type;

    @TableField("remark")
    @ApiModelProperty("备足")
    String remark;

}
