package com.hightop.benyin.knowledge;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 知识库结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class KnowledgeTemplateExcel {
    @Excel(name = "新版本大主题（大标题+机型构成唯一值，见左边第一列）",orderNum = "0")
    String code;
    @Excel(name = "从老系统中导入案例数据所用ID",orderNum = "2")
    String id;
    @Excel(name = "适用范围",orderNum = "4")
    String range;
    @Excel(name = "新版产品树",orderNum = "7")
    String tree;
    @Excel(name = "新版系列",orderNum = "9")
    String series;
    @Excel(name = "知识库类型",orderNum = "11")
    String type;
    @Excel(name = "关键字1",orderNum = "15")
    String keyword1;
    @Excel(name = "关键字2",orderNum = "16")
    String keyword2;
    @Excel(name = "关键字3",orderNum = "17")
    String keyword3;
    @Excel(name = "关键字4",orderNum = "18")
    String keyword4;
}
