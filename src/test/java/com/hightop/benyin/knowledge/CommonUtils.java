package com.hightop.benyin.knowledge;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @Description: tool
 * @Author: xhg
 * @Date: 2024/1/18 16:22
 */
public class CommonUtils {
    public static final String TREE = "产品树";
    public static final String SERIAL = "系列";

    public static InputStream getNetUrlInputStream(String url) {
        try {
            URL videoUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) videoUrl.openConnection();
            return connection.getInputStream();
        } catch (Exception e) {
            System.out.println("未识别url：" + url);
        }
        return null;
    }
}
