package com.hightop.benyin.knowledge;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 客户-商务信息实体
 * <AUTHOR>
 * @date 2023-10-24 11:03:17
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerCallRecordExcel {
    @Excel(name = "客户编号",orderNum = "0")
    String customerId ;
    @Excel(name = "联系人姓名",orderNum = "4")
    String reachShopName ;
    @Excel(name = "拜访角色",orderNum = "5")
    String reachShopRole ;
    @Excel(name = "店员电话",orderNum = "6")
    String reachShopTel ;
    @Excel(name = "拜访内容",orderNum = "7")
    String remark ;
    @Excel(name = "下次注意事项",orderNum = "8")
    String nextNoticeRemark ;
    @Excel(name = "拜访人员",orderNum = "10")
    String operatName ;
    @Excel(name = "拜访时间",orderNum = "11")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    Date createTime ;
    @Excel(name = "操作员",orderNum = "12")
    String optUserName ;
    @Excel(name = "跟进状态",orderNum = "13")
    String callState ;
}
