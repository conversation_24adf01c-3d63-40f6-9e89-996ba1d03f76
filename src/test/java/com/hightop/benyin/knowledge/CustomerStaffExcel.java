package com.hightop.benyin.knowledge;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 客户-员工信息实体
 *
 * <AUTHOR>
 * @date 2023-10-24 11:03:17
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerStaffExcel {
    @Excel(name = "客户编号",orderNum = "0")
    String customerIdNo;
    @Excel(name = "手机号码",orderNum = "7")
    String tel;
    @Excel(name = "微信昵称",orderNum = "2")
    String vxNikeName;
    @Excel(name = "微信ID",orderNum = "3")
    String vxId;
    @Excel(name = "答疑群",orderNum = "4")
    String vxGroupName;
}
