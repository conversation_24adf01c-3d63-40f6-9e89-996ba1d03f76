package com.hightop.benyin.knowledge;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.handle.Tools;
import com.hightop.benyin.knowledge.domain.service.KnowledgeBaseInfoDomainService;
import com.hightop.benyin.knowledge.domain.service.KnowledgeRepairCaseDomainService;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeBaseInfo;
import com.hightop.benyin.knowledge.infrastructure.entity.KnowledgeRepairCase;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.fario.base.Trio;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.util.UuidUtils;
import com.hightop.fario.common.jackson.JacksonUtils;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 把cos上传改成相同案例只上传一次，现在是会重复上传
 *
 * @Description: 知识库test
 * @Author: xhg
 * @Date: 2023/12/25 17:40
 */
public class KnowledgeTest extends BaseTest {
    /**
     * COS云地址前缀
     */
    private static final String COS_URL_PREFIX = "https://benyin-1315885374.cos.ap-chengdu.myqcloud.com/prod/knowledge/";
    /**
     * 历史数据本印存储访问前缀(html标签)
     */
    private static final String OLD_HTML_PREFIX = "https://work.benyin.ltd";
    /**
     * 历史数据本印存储访问前缀（非html标签）
     */
    private static final String OLD_NOT_HTML_PREFIX = "https://work.benyin.ltd/seeyon/fileUpload.do?method=showRTE&fileId=";
    /**
     * 本印系统最新token
     */
    private static final String NEWEST_TOKEN = "s=1705114416396; JSESSIONID=09E8D1107B090F9EF30AEF36F41152CA; login_locale=zh_CN; avatarImageUrl=-6663106077244308170; loginPageURL=";

    @Autowired
    CosUpload cosUpload;

    @Autowired
    ProductTreeDomainService productTreeDomainService;

    @Autowired
    KnowledgeBaseInfoDomainService knowledgeBaseInfoDomainService;
    @Autowired
    KnowledgeRepairCaseDomainService knowledgeRepairCaseDomainService;

    @Autowired
    DictItemDomainService dictItemDomainService;


    /**
     * 维修案例
     */
    @Test
    public void test2() {
        String fileName = "C:\\Users\\<USER>\\Desktop\\知识库第一次导入(1).xlsx";
        List<KnowledgeTemplateExcel> data = Tools.parseExcel(fileName, 0, 872, KnowledgeTemplateExcel.class);
        for (KnowledgeTemplateExcel k : data) {
            // 目前只有产品树、品牌
            ProductTree productTree = null;
            String range = k.getRange();
            if ("产品树".equals(range)) {
                productTree = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getName, k.getTree())
                    .eq(ProductTree::getLastLevel, 0)
                    .one();

            }
            if (null != productTree) {
                List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                    .like(ProductTree::getFullIdPath, productTree.getId())
                    .eq(ProductTree::getLastLevel, 1)
                    .list();
                Set<String> modelIds = models.stream().map(ProductTree::getId).map(String::valueOf).collect(Collectors.toSet());
                StringBuffer sql = new StringBuffer();
                modelIds.stream().forEach(e -> sql.append(" OR JSON_CONTAINS(product_list,'" + e + "')"));
                // 查询故障代码与适用范围所匹配的机型，给基础信息添加案例
                List<KnowledgeBaseInfo> baseInfo = this.knowledgeBaseInfoDomainService.lambdaQuery()
                    .eq(KnowledgeBaseInfo::getTitle, k.getCode())
                    .last("AND (" + sql.substring(3) + ")")
                    .list();
//                setCase(baseInfo, k);
            }
        }
    }



    /**
     * 理光数据读取到db
     * 1、先读机器代码，生成机型+知识库类型+标题+代码解释
     *
     * @param
     * @return:
     * @Author: xhg
     * @Date: 2023/12/27 16:57
     **/

    private List<Trio<String, Integer, Integer>> getExcelData() {

        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
            Trio.of("MP 6054系列,MP 3554系列", 0, 600),
            Trio.of("MP 6055系列,MP 3055系列", 1, 583),
            Trio.of("IM 8000系列", 2, 683),
            Trio.of("MP 7001系列", 3, 227),
            Trio.of("MP 5500", 4, 193),
            Trio.of("MP 7502系列", 5, 361),
            Trio.of("MP 7503系列", 6, 571),
            Trio.of("MP C2503 C2003", 7, 576),
            Trio.of("MP C6003系列,MP C3503系列", 8, 647),
            Trio.of("Pro 1107EX,Pro 1357EX", 9, 512),
            Trio.of("Pro 8100系列", 10, 1195),
            Trio.of("Pro 8200系列", 11, 992),
            Trio.of("Pro 8300系列", 12, 1263),
            Trio.of("Pro C5100系列", 13, 898),
            Trio.of("Pro C5200系列", 14, 1033),
            Trio.of("Pro C5300系列", 15, 1145),
            Trio.of("Pro C7100系列", 16, 1606),
            Trio.of("Pro C7200系列", 17, 1402),
            Trio.of("Pro C9100系列", 18, 1600),
            Trio.of("Pro C9200系列", 19, 1617)
        );
        return sheets;
    }

    @Test
    public void lgImport() {
        String fileName = "/Users/<USER>/Downloads/import/理光主流机型故障代码汇总完整版20240111.xlsx";
        List<Trio<String, Integer, Integer>> sheets = getExcelData();
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getName, s)
                    .eq(ProductTree::getLastLevel, 0)
                    .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getParentId, xl.getId())
                        .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                            .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("SC编号"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                        .eq(DictItem::getDictId, 23600L)
                        .eq(DictItem::getLabel, m.get("等级"))
                        .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"SC编号".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }

    /**
     * 理光文件导入
     *
     * @return
     */
    public void lgLockImport() {
        String fileName = "/Users/<USER>/Downloads/import/理光主流机型故障代码汇总完整版20240111.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
            Trio.of("MP 6054系列 MP 3554系列", 0, 141)

        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getName, s)
                    .eq(ProductTree::getLastLevel, 0)
                    .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getParentId, xl.getId())
                        .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                            .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("SC编号"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                        .eq(DictItem::getDictId, 23600L)
                        .eq(DictItem::getLabel, m.get("等级"))
                        .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"SC编号".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    info.add(k);
                }
            }
            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }

    /**
     * 柯美机器代码导入
     *
     * @param
     * @return:
     * @Author: xhg
     * @Date: 2023/12/27 17:32
     */
    @Test
    public void kmImport() {
        String fileName = "C:\\Users\\<USER>\\Desktop\\柯美机器代码第一批导入.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
            Trio.of("BH C658系列", 0, 327),
            Trio.of("BH Press C1100系列", 1, 739),
            Trio.of("BH 1200系列", 2, 715),
            Trio.of("AP 6136系列", 3, 1047),
            Trio.of("BH Press C1070系列", 4, 618),
            Trio.of("BH 1250系列", 5, 751)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String serials = sheet.getFirst();
            String[] serial = serials.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : serial) {
                ProductTree xl = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getName, s)
                    .eq(ProductTree::getLastLevel, 0)
                    .one();
                if (null != xl) {
                    // 系列下的机型
                    List<ProductTree> models = this.productTreeDomainService.lambdaQuery()
                        .eq(ProductTree::getParentId, xl.getId())
                        .list();
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.addAll(models.stream().map(ProductTree::getId)
                            .collect(Collectors.toSet()));
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("故障代码"));
                    // 没有等级
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"故障代码".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    info.add(k);
                }
            }
//            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }

    /**
     * 柯美只含机型的数据
     *
     * @param
     * @return:
     * @Author: xhg
     * @Date: 2023/12/27 18:21
     */
    @Test
    public void kmModelImport() {
        String fileName = "C:\\Users\\<USER>\\Desktop\\柯美-机型机器代码第一批导入.xlsx";
        // 根据excel读取:sheet名称+sheet下标+读取行数
        List<Trio<String, Integer, Integer>> sheets = Lists.newArrayList(
            Trio.of("bizhub PRO 1100", 0, 305),
            Trio.of("bizhub C454e,bizhub C554e", 1, 285),
            Trio.of("bizhub C554,bizhub C454", 2, 279),
            Trio.of("bizhub 758,bizhub 808,bizhub 958,bizhub PRO 958", 3, 289),
            Trio.of("Bizhub Press C6000,Bizhub Press C7000", 4, 551),
            Trio.of("bizhub C654e,bizhub C754e", 5, 271),
            Trio.of("bizhub 654,bizhub 754", 6, 239),
            Trio.of("AccurioPrint 2100", 7, 486),
            Trio.of("bizhub 654e,bizhub 754e", 8, 255),
            Trio.of("bizhub C654,bizhub C754", 9, 284)
        );
        LocalDateTime now = LocalDateTime.now();
        for (Trio<String, Integer, Integer> sheet : sheets) {
            List<KnowledgeBaseInfo> info = new ArrayList<>();
            String models = sheet.getFirst();
            String[] model = models.split(",");
            Set<Long> modelList = new HashSet<>();
            for (String s : model) {
                ProductTree jx = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getFullName, s)
                    .eq(ProductTree::getLastLevel, 1)
                    .one();
                if (null != jx) {
                    if (CollectionUtils.isNotEmpty(models)) {
                        modelList.add(jx.getId());
                    }

                }
            }
            List<Map<String, String>> lg = Tools.multiSheetParse(
                fileName, sheet.getSecond(), sheet.getThird());
            if (CollectionUtils.isNotEmpty(lg)) {
                for (Map<String, String> m : lg) {
                    Long baseId = IdWorker.getId();
                    KnowledgeBaseInfo k = new KnowledgeBaseInfo();
                    k.setId(baseId);
                    k.setProductList(modelList);
                    k.setTitle(m.get("故障代码"));
                    DictItem di = dictItemDomainService.lambdaQuery()
                        .eq(DictItem::getDictId, 23600L)
                        .eq(DictItem::getLabel, m.get("等级"))
                        .one();
                    if (null != di) {
                        k.setLevel(new DictItemEntry().setValue(di.getValue()));
                    }
                    StringBuffer sb = new StringBuffer();
                    m.remove("excelRowNum");
                    m.forEach((mk, mv) -> {
                        if (!"故障代码".equals(mk) && !"等级".equals(mk)) {
                            sb.append(String.format("<p><b>%s：</b></p><p>&nbsp; &nbsp;%s</p><br/>",
                                mk, mv));
                        }
                    });
                    // 富文本
                    k.setCodeExplain(sb.toString());
                    k.setOperatorId(101L);
                    k.setCreatedAt(now);
                    k.setUpdatedAt(now);
                    k.setDeleted(0);
                    // 默认为报代码：3501
                    k.setType(new DictItemEntry().setValue("3501"));
                    k.setIsEnable(Boolean.FALSE);
                    info.add(k);
                }
            }
//            this.knowledgeBaseInfoDomainService.saveBatch(info);
        }
    }

//    private String replaceImg(String html) {
//        Document doc = Jsoup.parse(html);
//        Elements img = doc.getElementsByTag("img");
//        img.stream().iterator().forEachRemaining(i -> {
//            String src = i.attr("src");
//            String cosUrl = parseImg(OLD_HTML_PREFIX + src);
//            // 替换src
//            i.attr("src", cosUrl);
//        });
//        return doc.outerHtml();
//    }

    private String requestBody(String moduleId) {
        Map<String, String> map = new HashMap<>(4);
        map.put("rightId", "7027768636809955559.6655775903610913817");
        // 数据id
        map.put("moduleId", moduleId);
        map.put("moduleType", "42");
        map.put("operateType", "2");
        HttpHeaders headers = new HttpHeaders();
        headers.set("Cookie", NEWEST_TOKEN);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(JacksonUtils.serialize(map), headers);
        ResponseEntity<String> response = new RestTemplate()
            .exchange("https://work.benyin.ltd/seeyon/rest/cap4/form/createOrEdit",
                HttpMethod.POST, request, String.class);
        return response.getBody();
    }

//    public List<KnowledgeRepairCase> readCase(String moduleId, Long baseId) {
//        LocalDateTime now = LocalDateTime.now();
//        List<KnowledgeRepairCase> cases = new ArrayList<>();
//        String resultBody = requestBody(moduleId);
//        JsonNode jsonNode = JacksonUtils.deserialize(resultBody, JsonNode.class);
//        JsonNode tableInfo = jsonNode.get("data").get("data").get("tableInfo");
//        JsonNode formson = tableInfo.get("formson");
//        String word = "文档库";
//        formson.iterator().forEachRemaining(i -> {
//            JsonNode display = i.get("display");
//            // 只读取文档库
//            if (word.equals(display.textValue())) {
//                Map<String, String> fieldInfo = fieldVal(i.get("fieldInfo"));
//                JsonNode items = i.get("pageData").get("items");
//                items.iterator().forEachRemaining(it -> {
//                    // 有多少组数据就有多少案例
//                    KnowledgeRepairCase krc = new KnowledgeRepairCase();
//                    String desc = it.get(fieldInfo.get("文档库-现象描述")).get("value").textValue();
//                    krc.setBaseId(baseId);
//                    // 标题=现象描述
//                    krc.setTitle(desc);
//                    JsonNode images = it.get(fieldInfo.get("文档库-描述图片"));
//                    JsonNode attachmentInfos = images.get("attachmentInfo").get("attachmentInfos");
//                    List<String> imgList = new ArrayList<>();
//                    if (!attachmentInfos.isNull()) {
//                        attachmentInfos.iterator().forEachRemaining(a -> {
//                            // 文件id
//                            String fileUrl = a.get("fileUrl").textValue();
//                            String img = OLD_NOT_HTML_PREFIX + fileUrl + "&type=image";
//                            imgList.add(img);
//                        });
//                    }
//                    // 追加多个图片
//                    StringBuffer faultDesc = new StringBuffer("<p>" + desc + "</p></br>");
//                    List<String> cosUrl = parseImg(imgList);
//                    if (cosUrl.size() > 0) {
//                        cosUrl.stream().forEach(e ->
//                            faultDesc.append("<img src=\"" + e + "\" width=\"100%\"/></br>"));
//                    }
//                    // 故障描述=现象描述+描述图片
//                    krc.setFaultDesc(faultDesc.toString());
//                    // 文本
//                    String reason = it.get(fieldInfo.get("文档库-原因")).get("value").textValue();
//                    // 富文本html标签
//                    String process = it.get(fieldInfo.get("文档库-处理过程")).get("value").textValue();
//                    // 文本
//                    String measure = it.get(fieldInfo.get("文档库-解决措施")).get("value").textValue();
//                    // 解决措施=原因+处理过程+解决措施（按顺序）
//                    krc.setSolutionMeasures("<p>" + reason + "</p></br>" +
//                        // 解析html标签，替换掉里面的img的src到cos-url
//                        replaceImg(process) + "</br>" +
//                        "<p>" + measure + "</p></br>");
//                    krc.setCreatedAt(now);
//                    krc.setUpdatedAt(now);
//                    krc.setDeleted(0);
//                    cases.add(krc);
//                });
//            }
//        });
//        return cases;
//    }

//    private List<String> parseImg(List<String> imgList) {
//        List<String> list = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(imgList)) {
//            imgList.stream().forEach(e -> {
//                // 根据url读取文件流上传到cos
//                String fileName = UuidUtils.full() + ".png";
//                try {
//                    cosUpload.upload(fileName, getNetUrlInputStream(e));
//                } catch (IOException ex) {
//                    ex.printStackTrace();
//                }
//                list.add(COS_URL_PREFIX + fileName);
//            });
//        }
//        return list;
//    }

//    private String parseImg(String imgUrl) {
//        // 根据url读取文件流上传到cos
//        String fileName = UuidUtils.full() + ".png";
//        try {
//            cosUpload.upload(fileName, getNetUrlInputStream(imgUrl));
//        } catch (IOException ex) {
//            ex.printStackTrace();
//        }
//        return COS_URL_PREFIX + fileName;
//    }

    private InputStream getNetUrlInputStream(String url) throws IOException {
        URL videoUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) videoUrl.openConnection();
        return connection.getInputStream();
    }


    private static Map<String, String> fieldVal(JsonNode fieldInfo) {
        Map<String, String> map = new HashMap<>(16);
        fieldInfo.elements().forEachRemaining(i -> {
            map.put(i.get("display").textValue(), i.get("name").textValue());
        });
        return map;
    }
}
