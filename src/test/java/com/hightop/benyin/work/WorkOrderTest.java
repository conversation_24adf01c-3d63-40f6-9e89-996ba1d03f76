package com.hightop.benyin.work;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.work.order.application.service.WorkOrderService;
import com.hightop.benyin.work.order.application.vo.CounterVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 工单ut
 * @Author: xhg
 * @Date: 2024/1/31 13:47
 */
public class WorkOrderTest extends BaseTest {
    @Autowired
    WorkOrderService workOrderService;

    @Test
    public void test1(){
        // 获取设备组最新的计数器和印量
        CounterVo counter = workOrderService.getCounter(1751850701056651266L);
        System.out.println(counter);
    }
}
