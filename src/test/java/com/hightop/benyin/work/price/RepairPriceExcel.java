package com.hightop.benyin.work.price;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 维修价格
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RepairPriceExcel {
    @Excel(name = "品牌",orderNum = "0")
    String brand;
    @Excel(name = "产品树",orderNum = "1")
    String tree;
    @Excel(name = "系列",orderNum = "2")
    String serial;
    @Excel(name = "机型全称",orderNum = "3")
    String fullName;
    @Excel(name = "机型",orderNum = "4")
    String model;
    @Excel(name = "机型id",orderNum = "5")
    String modelId;
    @Excel(name = "基础上门费",orderNum = "6")
    String visitPay;
    @Excel(name = "维修诊断费",orderNum = "7")
    String checkPay;
    @Excel(name = "零件更换费",orderNum = "8")
    String replacePay;
    @Excel(name = "登记折扣",orderNum = "9")
    String registerPrice;
    @Excel(name = "客户端折扣",orderNum = "10")
    String discountPrice;
    @Excel(name = "签约折扣",orderNum = "11")
    String signingPrice;
    @Excel(name = "VIP折扣",orderNum = "12")
    String vipPrice;
}
