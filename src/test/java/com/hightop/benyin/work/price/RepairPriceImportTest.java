package com.hightop.benyin.work.price;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.handle.Tools;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.benyin.repair.price.infrastructure.entity.RepairPrice;
import com.hightop.benyin.repair.price.infrastructure.enums.AuditEnum;
import com.hightop.fario.base.constant.ProfileConstants;
import com.hightop.magina.standard.ums.user.bind.UserEntry;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.hightop.benyin.handle.Tools.insertPrefix;

/**
 * @Description: 维修价格导入
 * @Author: xhg
 * @Date: 2024/2/1 10:01
 */
@ActiveProfiles(ProfileConstants.DEV)
public class RepairPriceImportTest extends BaseTest {
    @Autowired
    private ProductTreeDomainService productTreeDomainService;

    @Test
    public void test() {
        List<RepairPrice> list = new ArrayList<>();
        List<RepairPriceExcel> data = Tools.parseExcel(
            "C:\\Users\\<USER>\\Desktop\\机型维修报价(1).xlsx",
            0, 1217, RepairPriceExcel.class);
        Set<String> s = data.stream().map(RepairPriceExcel::getModelId).collect(Collectors.toSet());
        Set<Long> p = productTreeDomainService.lambdaQuery()
            .select(ProductTree::getId)
            .in(ProductTree::getId, s).list().stream().map(ProductTree::getId).collect(Collectors.toSet());

        System.out.println("机型数：" + s.size());
        System.out.println("查询到机型数：" + p.size());
    }

    /**
     * 确保test能查到所有机型
     *
     * @param args
     * @return:
     * @Author: xhg
     * @Date: 2024/2/1 10:59
     */
    public static void main(String[] args) throws IOException {
        List<RepairPrice> list = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        List<RepairPriceExcel> data = Tools.parseExcel(
            "C:\\Users\\<USER>\\Desktop\\机型维修报价(1).xlsx",
            0, 1217, RepairPriceExcel.class);
        Map<String, List<RepairPriceExcel>> group = data.stream().collect(Collectors.groupingBy(
            g -> String.format("%s-%s-%s-%s-%s-%s-%s"
                , g.getVisitPay(), g.getCheckPay(), g.getReplacePay(),
                g.getRegisterPrice(), g.getDiscountPrice(), g.getSigningPrice(), g.getVipPrice()
            )));
        group.forEach((k, v) -> {
            String[] price = k.split("-");
            RepairPrice rp = new RepairPrice();
            rp.setId(IdWorker.getId());
            rp.setProductIdList(v.stream()
                .map(RepairPriceExcel::getModelId)
                .map(Long::valueOf).collect(Collectors.toSet()));
            rp.setVisitPrice(Long.valueOf(price[0]) * 100);
            rp.setCheckPay(Long.valueOf(price[1]) * 100);
            rp.setReplacePay(Long.valueOf(price[2]) * 100);
            rp.setRegisterPrice(Long.valueOf(price[3]) * 100);
            rp.setDiscountPrice(Long.valueOf(price[4]) * 100);
            rp.setSigningPrice(Long.valueOf(price[5]) * 100);
            rp.setVipPrice(Long.valueOf(price[6]) * 100);
            rp.setEditBy(new UserEntry("101"));
            rp.setAuditBy(new UserEntry("101"));
            rp.setAuditStatus(AuditEnum.APPROVE);
            rp.setAuditTime(now);
            rp.setStatus(Boolean.TRUE);
            rp.setUpdatedAt(now);
            rp.setCreatedAt(now);
            rp.setDeleted(0);
            list.add(rp);
        });
        parse(list);
    }

    private static void parse(List<RepairPrice> res) throws IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\维修价格.sql"));
        res.stream().forEach(p -> {
            try {
                bw.write(insertPrefix(RepairPrice.class) +
                    String.join(",",
                        Optional.ofNullable(p.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getProductIdList()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getVipPrice()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getCheckPay()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getReplacePay()).map(String::valueOf).orElse("NULL"),
                        // 已忽略的字段
                        "0",
                        Optional.ofNullable(p.getDiscountPrice()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getRegisterPrice()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSigningPrice()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getVipPrice()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getAuditStatus()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getAuditBy()).map(m -> m.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getEditBy()).map(m -> m.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getAuditTime()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getStatus()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getDeleted()).map(String::valueOf).orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }
}
