package com.hightop.benyin.work.price;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 远程上门价格
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class VisitPriceExcel {
    @Excel(name = "出发地地区码",orderNum = "0")
    String start;
    @Excel(name = "目的地名称",orderNum = "1")
    String endName;
    @Excel(name = "目的地地区码",orderNum = "2")
    String end;
    @Excel(name = "远程误工费",orderNum = "3")
    String price;
}
