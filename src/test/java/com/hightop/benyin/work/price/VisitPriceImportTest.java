package com.hightop.benyin.work.price;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.handle.Tools;
import com.hightop.benyin.repair.price.infrastructure.entity.VisitPrice;
import com.hightop.benyin.repair.price.infrastructure.enums.AuditEnum;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.standard.ums.user.bind.UserEntry;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.hightop.benyin.handle.Tools.insertPrefix;

/**
 * @Description: 远程上门价格导入
 * @Author: xhg
 * @Date: 2024/2/1 10:01
 */
public class VisitPriceImportTest extends BaseTest {

    /**
     *
     * @param args
     * @return:
     * @Author: xhg
     * @Date: 2024/2/1 10:59
     */
    public static void main(String[] args) throws IOException {
        List<VisitPrice> list = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        List<VisitPriceExcel> data = Tools.parseExcel(
            "C:\\Users\\<USER>\\Desktop\\远程上门费.xlsx",
            0, 2887, VisitPriceExcel.class);
        List<VisitPriceExcel> sc = data.stream().filter(f -> StringUtils.isNotBlank(f.getStart())).collect(Collectors.toList());
        sc.stream().forEach(e->{
            VisitPrice vp=new VisitPrice();
            vp.setId(IdWorker.getId());
            vp.setStartRegion(Long.valueOf(e.getStart()));
            vp.setArriveRegion(Long.valueOf(e.getEnd()));
            vp.setPrice(Long.valueOf(e.getPrice()) * 100);
            vp.setEditBy(new UserEntry("101"));
            vp.setAuditBy(new UserEntry("101"));
            vp.setAuditStatus(AuditEnum.APPROVE);
            vp.setAuditTime(now);
            vp.setStatus(Boolean.TRUE);
            vp.setUpdatedAt(now);
            vp.setCreatedAt(now);
            vp.setDeleted(0);
            list.add(vp);
        });
        parse(list);
    }

    private static void parse(List<VisitPrice> res) throws IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter("C:\\Users\\<USER>\\Desktop\\远程上门费.sql"));
        res.stream().forEach(p -> {
            try {
                bw.write(insertPrefix(VisitPrice.class) +
                    String.join(",",
                        Optional.ofNullable(p.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getStartRegion()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getArriveRegion()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getPrice()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getAuditStatus()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getAuditBy()).map(m -> m.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getEditBy()).map(m -> m.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getAuditTime()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getStatus()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getDeleted()).map(String::valueOf).orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }
}
