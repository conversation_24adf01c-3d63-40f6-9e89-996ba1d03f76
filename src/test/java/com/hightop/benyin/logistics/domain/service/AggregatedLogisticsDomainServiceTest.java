package com.hightop.benyin.logistics.domain.service;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.logistics.domain.dto.LogisticsOrderDto;
import com.hightop.benyin.logistics.infrastructure.enums.LogisticsProvider;
import com.hightop.benyin.logistics.infrastructure.model.LogisticsCargo;
import com.hightop.benyin.logistics.infrastructure.model.LogisticsContact;
import com.hightop.benyin.share.infrastructure.type.LbsLocation;
import com.hightop.magina.core.exception.MaginaException;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

/**
 * {@link AggregatedLogisticsDomainService}聚合物流服务测试
 * <AUTHOR>
 * @date 2024/01/02 15:58
 */
public class AggregatedLogisticsDomainServiceTest extends BaseTest {
    @Autowired
    AggregatedLogisticsDomainService aggregatedLogisticsDomainService;

    @Test
    public void estimateJd() {
        LogisticsOrderDto dto = this.get(LogisticsProvider.JING_DONG);

        this.aggregatedLogisticsDomainService.estimate(LogisticsProvider.JING_DONG, dto);
    }

    @Test
    public void orderJd() {
        LogisticsOrderDto dto = this.get(LogisticsProvider.JING_DONG);

        this.aggregatedLogisticsDomainService.order(LogisticsProvider.JING_DONG, dto);
    }

    @Test
    public void estimateSs() {
        LogisticsOrderDto dto = this.get(LogisticsProvider.SHAN_SONG);

        this.aggregatedLogisticsDomainService.estimate(LogisticsProvider.SHAN_SONG, dto);
    }

    @Test
    public void orderSs() {
        LogisticsOrderDto dto = this.get(LogisticsProvider.SHAN_SONG);

        this.aggregatedLogisticsDomainService.order(LogisticsProvider.SHAN_SONG, dto);
    }

    /**
     * 基础数据准备
     * @param provider 物流商
     * @return {@link LogisticsOrderDto}
     */
    protected LogisticsOrderDto get(LogisticsProvider provider) {
        switch (provider) {
            case JING_DONG: {
                return
                    LogisticsOrderDto.builder()
                        .sender(
                            LogisticsContact.builder()
                                .name("本印理想中心").mobile("13388190025")
                                .address("理想中心3栋713")
                                .addressCode(510107)
                                .build()
                        )
                        .receiver(
                            LogisticsContact.builder()
                                .name("瀚涛天图").mobile("13550380832")
                                .address("锦云东三巷1号金融麦田B座107室")
                                .addressCode(510107)
                                .build()
                        )
                        // 货物信息
                        .cargos(
                            Arrays.asList(
                                LogisticsCargo.builder()
                                    .name("一转清洁刮板").weight(195L).length(390L)
                                    .width(100L).height(40L).quantity(2)
                                    .build(),
                                LogisticsCargo.builder()
                                    .name("鼓清洁刮板").weight(187L).length(410L)
                                    .width(70L).height(35L).quantity(1)
                                    .build()
                            )
                        )
                        .build();
            }
            case SHAN_SONG: {
                return
                    LogisticsOrderDto.builder()
                        .sender(
                            LogisticsContact.builder()
                                .name("本印理想中心").mobile("13388190025")
                                .address("理想中心3栋713")
                                .addressCode(510107)
                                .location(LbsLocation.of(30.59542, 104.06303))
                                .build()
                        )
                        .receiver(
                            LogisticsContact.builder()
                                .name("瀚涛天图").mobile("13550380832")
                                .address("锦云东三巷1号金融麦田B座107室")
                                .addressCode(510107)
                                .location(LbsLocation.of(30.58153, 104.076355))
                                .build()
                        )
                        // 货物信息
                        .cargos(
                            Arrays.asList(
                                LogisticsCargo.builder()
                                    .name("一转清洁刮板").weight(195L).length(390L)
                                    .width(100L).height(40L).quantity(2)
                                    .build(),
                                LogisticsCargo.builder()
                                    .name("鼓清洁刮板").weight(187L).length(410L)
                                    .width(70L).height(35L).quantity(1)
                                    .build()
                            )
                        )
                        .build();
            }
            default: {
                throw new MaginaException("不支持的物流类型" + provider.getName());
            }
        }
    }
}
