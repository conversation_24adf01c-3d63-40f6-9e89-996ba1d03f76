package com.hightop.benyin.order;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.customer.domain.service.CustomerDeviceGroupDomainService;
import com.hightop.benyin.customer.domain.service.CustomerDomainService;
import com.hightop.benyin.customer.infrastructure.entity.Customer;
import com.hightop.benyin.customer.infrastructure.entity.CustomerDeviceGroup;
import com.hightop.benyin.item.api.params.ItemSkuUpdateParam;
import com.hightop.benyin.item.application.service.ItemService;
import com.hightop.benyin.order.application.schedule.TradeOrderSchedule;
import com.hightop.benyin.reverse.application.manager.ReverseOrderManager;
import com.hightop.benyin.share.application.service.RegionService;
import com.hightop.benyin.share.domain.service.RegionDomainService;
import com.hightop.benyin.storage.domain.service.StorageOutWarehouseGoodsServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.StorageOutWarehouseGoods;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/29 15:42
 */
@ActiveProfiles("dev")
public class OrderTest extends BaseTest {

    @Autowired
    private TradeOrderSchedule tradeOrderSchedule;

    @Autowired
    private StorageOutWarehouseGoodsServiceDomain storageOutWarehouseGoodsServiceDomain;

    @Autowired
    private ReverseOrderManager reverseOrderManager;
    @Autowired
    ItemService itemService;

    @Autowired
    CustomerDomainService customerDomainService;
    @Autowired
    RegionDomainService regionDomainService;
    @Autowired
    CustomerDeviceGroupDomainService customerDeviceGroupDomainService;

    @Test
    public void test() throws Exception {

        LambdaQueryWrapper<StorageOutWarehouseGoods> outWarehouseGoodsWrapper = new LambdaQueryWrapper<>();
        outWarehouseGoodsWrapper.eq(StorageOutWarehouseGoods::getInventoryId, 180L);
        outWarehouseGoodsWrapper.eq(
                StorageOutWarehouseGoods::getOutWarehouseId, "CKID240130000033");
        List<StorageOutWarehouseGoods> outWarehouseGoodsList = storageOutWarehouseGoodsServiceDomain.list(
                outWarehouseGoodsWrapper);
        StorageOutWarehouseGoods storageOutWarehouseGoods = outWarehouseGoodsList.get(0);
        if (outWarehouseGoodsList.size() > 1) {
            for (int i = 1; i < outWarehouseGoodsList.size(); i++) {
                storageOutWarehouseGoods.setOutWarehouseNumber(storageOutWarehouseGoods.getOutWarehouseNumber()+outWarehouseGoodsList.get(i).getOutWarehouseNumber());
                storageOutWarehouseGoods.setAuidtOutWarehouseNumber(storageOutWarehouseGoods.getAuidtOutWarehouseNumber()+outWarehouseGoodsList.get(i).getAuidtOutWarehouseNumber());
            }

        }

        System.out.println("111");

    }


    @Test
    public void test2() {
        Boolean flag = reverseOrderManager.isAllReverseSuccess(1752669555190165505L, null);
        System.out.println(flag);
    }


    @Test
    public void received() {
        try {
            tradeOrderSchedule.received();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }





}
