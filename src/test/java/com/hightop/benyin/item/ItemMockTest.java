package com.hightop.benyin.item;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.item.domain.service.ItemCategoryDomainService;
import com.hightop.benyin.item.domain.service.ItemCategoryTagsDomainService;
import com.hightop.benyin.item.domain.service.ItemServiceDomain;
import com.hightop.benyin.item.infrastructure.enmu.ItemSaleStatusEnum;
import com.hightop.benyin.item.infrastructure.entity.Item;
import com.hightop.benyin.item.infrastructure.entity.ItemCategory;
import com.hightop.benyin.item.infrastructure.entity.ItemCategoryTags;
import com.hightop.fario.base.Pair;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 商品模拟数据测试
 * @Author: xhg
 * @Date: 2023/10/25 10:02
 */
public class ItemMockTest extends BaseTest {
    @Autowired
    ItemCategoryDomainService itemCategoryDomainService;
    @Autowired
    ItemCategoryTagsDomainService itemCategoryTagsDomainService;
    @Autowired
    ItemServiceDomain itemServiceDomain;
    /**
     * 批量提交量
     */
    private static final int BATCH_SIZE = 800;
    private static final int NUM_10 = 10;
    /**
     * 模拟数据量
     */
    private static final int MOCK_SIZE = 500000;

    @Test
    public void mock() {
        mockData(MOCK_SIZE);
    }

    private char[] splitWords() {
        String words = "的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该冯价严龙飞";
        return words.toCharArray();
    }


    private int randomIndex(int max) {
        return (int) Math.floor(1 + Math.random() * max);
    }

    private String randomName(char[] words) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(words[randomIndex(499)]);
        stringBuffer.append(words[randomIndex(499)]);
        stringBuffer.append(words[randomIndex(499)]);
        stringBuffer.append(words[randomIndex(499)]);
        stringBuffer.append(words[randomIndex(499)]);
        return stringBuffer.toString();
    }

    private HashMap randomTag(List<ItemCategoryTags> tags) {
        // 随机选择几个属性
        int s = randomIndex(tags.size() - 1);
        HashMap map = new HashMap(s);
        for (int i = 0; i < NUM_10; i++) {
            // 单选属性值
            ItemCategoryTags t = tags.get(randomIndex(tags.size() - 1));
            List<String> v = t.getValue();
            map.put(t.getId(), v.get(randomIndex(v.size() - 1)));
            if (map.size()==s){
                break;
            }
        }
        return map;
    }

    private void mockData(int size) {
        List<Long> category = itemCategoryDomainService.list(
            new LambdaQueryWrapper<ItemCategory>()
                .eq(ItemCategory::getDeleted, 0)
                .eq(ItemCategory::getParentId, 0)
                .like(ItemCategory::getName, "测试勿删")
        ).stream().map(ItemCategory::getId).collect(Collectors.toList());
        List<Pair<Long, List<ItemCategoryTags>>> pair = new ArrayList<>();
        category.stream().forEach(e -> {
                pair.add(Pair.of(e, this.itemCategoryTagsDomainService.lambdaQuery()
                    .eq(ItemCategoryTags::getClassifyId, e).list()));
            }
        );
        List<Item> items = new ArrayList<>(BATCH_SIZE);
        char[] words = splitWords();
        // 根据size随机分配分类、分类属性，品牌固定为2
        for (int i = 0; i < size; i++) {
            Item item = new Item();
            item.setName(randomName(words));
            item.setBrandId(2L);
            Pair<Long, List<ItemCategoryTags>> p = pair.get(randomIndex(pair.size() - 1));
            item.setCategoryId(p.getFirst());
            List<ItemCategoryTags> tags = p.getSecond();
            item.setSaleAttrVals(randomTag(tags));
            item.setSaleStatus(ItemSaleStatusEnum.ON_SALE);
            items.add(item);
            if (items.size() >= BATCH_SIZE) {
                try {
                    this.itemServiceDomain.saveBatch(items);
                } finally {
                    items.clear();
                }
            }
        }
        this.itemServiceDomain.saveBatch(items);
    }
}
