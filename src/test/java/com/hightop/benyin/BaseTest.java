package com.hightop.benyin;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hightop.fario.common.jackson.JacksonUtils;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 测试基类
 * <AUTHOR>
 * @date 2023/10/24 09:55
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BaseTest {
    @Autowired
    ObjectMapper objectMapper;

    protected String json(Object o) {
        return JacksonUtils.serialize(this.objectMapper, o);
    }
}
