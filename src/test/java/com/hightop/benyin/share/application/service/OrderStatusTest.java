package com.hightop.benyin.share.application.service;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.order.application.engine.OrderFsmEngine;
import com.hightop.benyin.order.application.event.OrderCreateEvent;
import com.hightop.benyin.order.application.event.OrderPaidEvent;
import com.hightop.benyin.order.domain.service.TradeOrderDomainService;
import com.hightop.benyin.order.infrastructure.entity.TradeOrder;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2023/11/13 18:45
 */
public class OrderStatusTest extends BaseTest {

    @Autowired
    OrderFsmEngine orderFsmEngine;

    @Autowired
    private TradeOrderDomainService tradeOrderDomainService;

    @Test
    public void test() throws Exception {
        OrderCreateEvent orderCreateEvent = new OrderCreateEvent();
        orderFsmEngine.sendEvent(orderCreateEvent);
    }


    @Test
    public void test2() throws Exception {
        OrderPaidEvent orderPaidEvent = new OrderPaidEvent();
        TradeOrder tradeOrder = tradeOrderDomainService.getById(1727929753467121665L);
        orderPaidEvent.setOrderNum(tradeOrder.getOrderNum());
        orderPaidEvent.setPayTime(orderPaidEvent.getPayTime());
        orderPaidEvent.setPayAmount(orderPaidEvent.getPayAmount());
        orderFsmEngine.sendEvent(orderPaidEvent);
    }



}
