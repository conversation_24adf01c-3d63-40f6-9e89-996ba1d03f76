package com.hightop.benyin.share.application.service;

import com.hightop.benyin.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * {@link RegionService}
 * <AUTHOR>
 * @date 2023/10/24 11:01
 */
public class RegionServiceTest extends BaseTest {
    @Autowired
    RegionService regionService;

    @Test
    public void tree() {
        System.out.println(super.json(this.regionService.tree()));
    }

    @Test
    public void async() {
        this.regionService.provinces().forEach(it -> {
            System.out.println(super.json(it));
            if (it.getHasChildren()) {
                System.out.println(super.json(this.regionService.children(it.getCode())));
            }
        });
    }
}
