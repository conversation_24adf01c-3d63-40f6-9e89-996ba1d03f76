package com.hightop.benyin.share.domain.service;

import com.hightop.benyin.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.stream.IntStream;

/**
 * {@link SequenceDomainService}单元测试
 * <AUTHOR>
 * @date 2023/10/24 10:49
 */
public class SequenceDomainServiceTest extends BaseTest {
    @Autowired
    SequenceDomainService sequenceDomainService;

    @Test
    public void sequence() {
        System.out.println(this.sequenceDomainService.nextSequence("test"));
        System.out.println(this.sequenceDomainService.nextSequence("KH"));
        System.out.println(this.sequenceDomainService.nextSequence("KH", 10));
        System.out.println(this.sequenceDomainService.nextSequence("test"));
    }

    @Test
    public void dateSequence() {
        IntStream.range(0, 20).forEach(it -> System.out.println(this.sequenceDomainService.nextDateSequence("date")));
    }

    @Test
    public void datetimeSequence() {
        IntStream.range(0, 1000)
            .forEach(it -> System.out.println(this.sequenceDomainService.nextDatetimeSequence("datetime")));
    }
}
