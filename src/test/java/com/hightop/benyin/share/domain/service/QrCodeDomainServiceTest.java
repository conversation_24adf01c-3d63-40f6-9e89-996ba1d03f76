package com.hightop.benyin.share.domain.service;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.share.application.dto.QrCodeDto;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileInputStream;
import java.io.IOException;

/**
 * {@link QrCodeDomainService}单元测试
 * <AUTHOR>
 * @date 2024/01/10 16:57
 */
public class QrCodeDomainServiceTest extends BaseTest {
    @Autowired
    QrCodeDomainService qrCodeDomainService;

    @Test
    public void qr() {
        System.out.println(
            this.qrCodeDomainService.createQr(QrCodeDto.<String>builder().type("test").data("haha").build())
        );
    }

    @Test
    public void defaultQr() {
        System.out.println(
            this.qrCodeDomainService.createDefaultQr(QrCodeDto.<String>builder().type("default").data("haha").build())
        );
    }

    @Test
    public void logo() throws IOException {
        try (FileInputStream inputStream =
                 new FileInputStream("C:\\Users\\<USER>\\Pictures\\6294ad66e8683970527006ab126e006d.jpg")) {
            System.out.println(
                this.qrCodeDomainService.createQr(
                    QrCodeDto.<String>builder().type("logo").data("heihei").build(),
                    inputStream
                )
            );
        }
    }

    @Test
    public void text() {
        System.out.println(
            this.qrCodeDomainService.createQr(
                QrCodeDto.<String>builder().type("test").data("haha").build(),
                "你好世界"
            )
        );
    }

    @Test
    public void logoAndText() throws IOException {
        try (FileInputStream inputStream =
                 new FileInputStream("C:\\Users\\<USER>\\Pictures\\6294ad66e8683970527006ab126e006d.jpg")) {
            System.out.println(
                this.qrCodeDomainService.createQr(
                    QrCodeDto.<String>builder().type("logo").data("heihei").build(),
                    inputStream,
                    "我是一个粉刷匠，粉刷本领强"
                )
            );
        }
    }
}
