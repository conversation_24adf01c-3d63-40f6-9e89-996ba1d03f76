package com.hightop.benyin.share.domain.service;

import com.alibaba.fastjson.JSON;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.share.infrastructure.enums.CoordinateSystem;
import com.hightop.benyin.share.infrastructure.restful.tencent.lbs.geo.GeoAddress;
import com.hightop.benyin.share.infrastructure.restful.tencent.lbs.geo.GeoCoordinate;
import com.hightop.benyin.share.infrastructure.type.LbsLocation;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

/**
 * {@link LbsGeoDomainService}单元测试
 * <AUTHOR>
 * @date 2024/01/05 11:37
 */
@Slf4j
@ActiveProfiles("dev")
public class LbsGeoDomainServiceTest extends BaseTest {
    @Autowired
    LbsGeoDomainService lbsGeoDomainService;

    @Test
    public void toCoordinate() {
        GeoCoordinate geoCoordinate= this.lbsGeoDomainService.toCoordinate("四川省成都市金牛区群星路170号");
        log.info(JSON.toJSONString(geoCoordinate));
    }
    
    @Test
    public void toAddress(){
        //"latitude": 29.561131, "longitude": 106.530067
        GeoAddress geoAddress= this.lbsGeoDomainService.toAddress(LbsLocation.of(30.689112D, 104.189831D, CoordinateSystem.GCJ_02));
        log.info(JSON.toJSONString(geoAddress));
    }
}
