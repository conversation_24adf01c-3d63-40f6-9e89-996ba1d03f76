package com.hightop.benyin.share.infrastructure.restful;

import com.hightop.benyin.share.infrastructure.restful.aligenie.AligenieInterceptor;
import com.hightop.benyin.share.infrastructure.restful.aligenie.AligenieProperties;
import com.hightop.benyin.share.infrastructure.restful.aligenie.notification.NotificationClient;
import com.hightop.benyin.share.infrastructure.restful.aligenie.notification.NotificationUnicastRequest;
import com.hightop.benyin.share.infrastructure.util.FeignUtils;
import org.junit.Test;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/04/10 10:07
 */
public class AligenieNotificationClientTest {
    AligenieProperties properties = new AligenieProperties();
    NotificationClient notificationClient =
        FeignUtils.json()
            .requestInterceptor(new AligenieInterceptor(properties))
            .target(NotificationClient.class, properties.getUrl());

    {
        properties.setAccessKeyId("4b24d5d9d3e4a8270421daac9da6ad5a");
        properties.setAccessKeySecret("4d2d33bff5e88715ffffdae39050f6dc");
        AligenieProperties.AligenieNotification notification = new AligenieProperties.AligenieNotification();
        notification.setSkillId("104888");
        notification.setMessageTemplateId("6LUaNIBzcMYEHcET");
        notification.setDeviceOpenId("Xl0Veob5bgltEyomHGV3ThAlguecwSdCjw3BAPeRYEHZiyDOxcviWQ==");
        properties.setNotification(notification);
    }

    @Test
    public void test() {
        this.notificationClient.unicast(
            NotificationUnicastRequest.of(
                this.properties.getNotification(),
                Collections.singletonMap("orderName", "销售售后单")
            )
        );
    }
}
