package com.hightop.benyin.statisics;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.statistics.api.dto.RepairReportQuery;
import com.hightop.benyin.statistics.application.service.RepairCountService;
import com.hightop.benyin.statistics.application.service.ReplacePartCountService;
import com.hightop.benyin.work.order.application.service.WorkOrderService;
import com.hightop.benyin.work.order.application.vo.CounterVo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 工单ut
 * @Author: xhg
 * @Date: 2024/1/31 13:47
 */
public class StatisicsTest extends BaseTest {
    @Autowired
    RepairCountService repairCountService;

    @Autowired
    ReplacePartCountService replacePartCountService;

    @Test
    public void initReplaceInfo(){
        replacePartCountService.initReplaceInfo(new RepairReportQuery());
    }

    @Test
    public void initRepairPrintInfo(){
        RepairReportQuery repairReportQuery =  new RepairReportQuery();
        repairReportQuery.setCustomerId(1784839448165416962L);
        repairReportQuery.setDeviceGroupId(1784839677719674882L);
        repairCountService.initRepairPrintInfo(repairReportQuery);
    }

}
