package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 产品树excel结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductTreeExcel {
    @Excel(name = "品牌",orderNum = "0")
    String brand;
    @Excel(name = "产品树",orderNum = "1")
    String product;
    @Excel(name = "新系列",orderNum = "2")
    String serial;
    @Excel(name = "简称",orderNum = "3")
    String shorter;
    @Excel(name = "机型",orderNum = "4")
    String name;
    @Excel(name = "全称",orderNum = "5")
    String fullName;
}
