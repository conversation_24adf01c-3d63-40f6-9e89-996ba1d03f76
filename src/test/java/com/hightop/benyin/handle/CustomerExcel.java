package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 客户excel结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerExcel {
    @Excel(name = "$主表唯一标识",orderNum = "0")
    String id;
    @Excel(name = "客户编号",orderNum = "1")
    String seqId;
    @Excel(name = "客户名称",orderNum = "2")
    String name;
    @Excel(name = "公司详细地址",orderNum = "3")
    String address;
    @Excel(name = "经营状态",orderNum = "8")
    String businessStatus;
    @Excel(name = "法人姓名",orderNum = "9")
    String legalPerson;
    @Excel(name = "法人电话",orderNum = "10")
    String legalPersonTel;
    @Excel(name = "客户属性",orderNum = "11")
    String industryAttr;
    @Excel(name = "客户类型",orderNum = "12")
    String type;
    @Excel(name = "开票类型",orderNum = "13")
    String ticketType;
    @Excel(name = "统一信用代码",orderNum = "14")
    String creditCode;
    @Excel(name = "银行账号",orderNum = "15")
    String bankAccount;
    @Excel(name = "账户名",orderNum = "16")
    String account;
    @Excel(name = "开户银行",orderNum = "17")
    String bank;
    @Excel(name = "销售人员",orderNum = "19")
    String salesman;
    @Excel(name = "拜访时间",orderNum = "20")
    String visitTime;
    @Excel(name = "经度",orderNum = "21")
    String longitude;
    @Excel(name = "纬度",orderNum = "22")
    String latitude;
    @Excel(name = "所属区县",orderNum = "25")
    String regionCode;
    @Excel(name = "商务人员",orderNum = "26")
    String businessman;
    @Excel(name = "财务姓名",orderNum = "27")
    String finance;
    @Excel(name = "财务电话",orderNum = "28")
    String financeTel;
    @Excel(name = "店招名",orderNum = "29")
    String shopRecruitment;
    @Excel(name = "营业执照名称",orderNum = "31")
    String license;
    @Excel(name = "类型",orderNum = "32")
    String status;
    @Excel(name = "签约时间",orderNum = "33")
    String signingTime;
    @Excel(name = "店铺验证码",orderNum = "39")
    String captcha;
    @Excel(name = "创建时间",orderNum = "43")
    String createdAt;
    @Excel(name = "修改时间",orderNum = "44")
    String updatedAt;

}
