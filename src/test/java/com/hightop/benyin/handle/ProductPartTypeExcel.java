package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 零件excel结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductPartTypeExcel {
    @Excel(name = "id",orderNum = "0")
    Long id;
    @Excel(name = "商品小类",orderNum = "3")
    String type;
}
