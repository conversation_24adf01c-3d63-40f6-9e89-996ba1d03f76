package com.hightop.benyin.handle;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.purchase.infrastructure.entity.SupplySource;
import com.hightop.benyin.storage.domain.service.ManufacturerServiceDomain;
import com.hightop.benyin.storage.domain.service.StorageArticleServiceDomain;
import com.hightop.benyin.storage.infrastructure.entity.Manufacturer;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.magina.standard.code.dictionary.dict.DictDomainService;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.hightop.benyin.handle.Tools.*;

/**
 * 数据处理
 *
 * @Author: xhg
 * @Date: 2023/11/28 18:34
 */
public class SupplySourceImportTest extends BaseTest {
    private static final String PATH = "C:\\Users\\<USER>\\Desktop\\benyin-data\\";
    private static final String PROD_SQL = "C:\\Users\\<USER>\\Desktop\\benyin-data\\正式环境sql\\";
    @Autowired
    DictItemDomainService dictItemDomainService;
    @Autowired
    DictDomainService dictDomainService;

    @Autowired
    ManufacturerServiceDomain manufacturerServiceDomain;
    @Autowired
    StorageArticleServiceDomain storageArticleServiceDomain;

    @Test
    public void import2() throws IOException {
        List<SupplySourceExcel> data = parseExcel(PATH + "供应源数据导表.xlsx",
            0, 203, SupplySourceExcel.class);
        LocalDateTime now = LocalDateTime.now();
        List<SupplySource> list = new ArrayList<>();
        data.stream().forEach(e -> {
            StorageArticle article = this.storageArticleServiceDomain.lambdaQuery()
                .eq(StorageArticle::getCode, e.getArticleCode()).one();
            if (null != article) {
                SupplySource s = new SupplySource();
                s.setArticleId(article.getId());
                Manufacturer manufacture = this.manufacturerServiceDomain.lambdaQuery().eq(Manufacturer::getCode, e.getManufacturerCode()).one();
                if (null != manufacture) {
                    s.setManufacturerId(manufacture.getId());
                }
                s.setNum(e.getNum());
                s.setPrice(e.getPrice() * 100);
                s.setApplicantId(101L);
                s.setApplicantName("超级管理员");
                s.setStatus(Objects.equals("启用", e.getStatus()) ? 1 : 0);
                s.setSettlementNumberOne(e.getSettlementNumberOne());
                Optional.ofNullable(e.getSettlementPriceOne()).ifPresent(i -> s.setSettlementPriceOne(i * 100));
                s.setSettlementNumberTwo(e.getSettlementNumberTwo());
                Optional.ofNullable(e.getSettlementPriceTwo()).ifPresent(i -> s.setSettlementPriceTwo(i * 100));
                s.setSettlementNumberThree(e.getSettlementNumberThree());
                Optional.ofNullable(e.getSettlementPriceThree()).ifPresent(i -> s.setSettlementPriceThree(i * 100));
                s.setSettlementNumberFour(e.getSettlementNumberFour());
                Optional.ofNullable(e.getSettlementPriceFour()).ifPresent(i -> s.setSettlementPriceFour(i * 100));
                s.setSettlementNumberFive(e.getSettlementNumberFive());
                Optional.ofNullable(e.getSettlementPriceFive()).ifPresent(i -> s.setSettlementPriceFive(i * 100));
                s.setDeliveryTime(e.getDeliveryTime());
                s.setArrivalTime(e.getArrivalTime());
                s.setValidityStartTime(e.getValidityStartTime());
                s.setValidityEndTime(e.getValidityEndTime());
                s.setCreatedAt(now);
                s.setUpdatedAt(now);
                s.setDeleted(0);
                list.add(s);
            }
        });
        convertDataToSqlFile(list, PROD_SQL + "供应源数据导表test.sql");
    }

    @Test
    public void import1() throws IOException {
        List<SupplySourceExcel> data = parseExcel(PATH + "供应源数据导表.xlsx",
            0, 203, SupplySourceExcel.class);
        LocalDateTime now = LocalDateTime.now();
        List<SupplySource> list = new ArrayList<>();
        data.stream().forEach(e -> {
            StorageArticle article = this.storageArticleServiceDomain.lambdaQuery()
                .eq(StorageArticle::getCode, e.getArticleCode()).one();
            if (null != article) {
                SupplySource s = new SupplySource();
                s.setArticleId(article.getId());
                Manufacturer manufacture = this.manufacturerServiceDomain.lambdaQuery().eq(Manufacturer::getCode, e.getManufacturerCode()).one();
                if (null != manufacture) {
                    s.setManufacturerId(manufacture.getId());
                }
                s.setNum(e.getNum());
                s.setPrice(e.getPrice() * 100);
                s.setApplicantId(101L);
                s.setApplicantName("超级管理员");
                s.setStatus(Objects.equals("启用", e.getStatus()) ? 1 : 0);
                s.setSettlementNumberOne(e.getSettlementNumberOne());
                Optional.ofNullable(e.getSettlementPriceOne()).ifPresent(i -> s.setSettlementPriceOne(i * 100));
                s.setSettlementNumberTwo(e.getSettlementNumberTwo());
                Optional.ofNullable(e.getSettlementPriceTwo()).ifPresent(i -> s.setSettlementPriceTwo(i * 100));
                s.setSettlementNumberThree(e.getSettlementNumberThree());
                Optional.ofNullable(e.getSettlementPriceThree()).ifPresent(i -> s.setSettlementPriceThree(i * 100));
                s.setSettlementNumberFour(e.getSettlementNumberFour());
                Optional.ofNullable(e.getSettlementPriceFour()).ifPresent(i -> s.setSettlementPriceFour(i * 100));
                s.setSettlementNumberFive(e.getSettlementNumberFive());
                Optional.ofNullable(e.getSettlementPriceFive()).ifPresent(i -> s.setSettlementPriceFive(i * 100));
                s.setDeliveryTime(e.getDeliveryTime());
                s.setArrivalTime(e.getArrivalTime());
                s.setValidityStartTime(e.getValidityStartTime());
                s.setValidityEndTime(e.getValidityEndTime());
                s.setCreatedAt(now);
                s.setUpdatedAt(now);
                s.setDeleted(0);
                list.add(s);
            }
        });
        parse(list);
    }

    private void parse(List<SupplySource> list) throws IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter(PROD_SQL + "供应源数据导表.sql"));
        list.stream().forEach(p -> {
            try {
                bw.write(insertNoIdPrefix(SupplySource.class) +
                    String.join(",",
                        Optional.ofNullable(p.getArticleId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getManufacturerId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getNum()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getPrice()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getApplicantId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getApplicantName()).map(m -> m = "\"" + m + "\"").orElse("NULL"),
                        Optional.ofNullable(p.getStatus()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementNumberOne()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementPriceOne()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementNumberTwo()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementPriceTwo()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementNumberThree()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementPriceThree()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementNumberFour()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementPriceFour()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementNumberFive()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getSettlementPriceFive()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getDeliveryTime()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getArrivalTime()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getValidityStartTime()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getValidityEndTime()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getContractFiles()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getDeleted()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }
}
