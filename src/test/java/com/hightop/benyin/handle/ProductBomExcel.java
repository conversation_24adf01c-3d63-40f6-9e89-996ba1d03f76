package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 零件BOM excel结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductBomExcel {
    @Excel(name = "系列",orderNum = "0")
    String serial;
    @Excel(name = "机型",orderNum = "1")
    String model;
    @Excel(name = "零件OEM编号",orderNum = "2")
    String oem;
    @Excel(name = "所属单元",orderNum = "3")
    String unit;
    @Excel(name = "已有数量",orderNum = "4")
    Integer num;
    @Excel(name = "标件",orderNum = "5")
    String standard;
    @Excel(name = "是否PM件",orderNum = "6")
    String isPm;
    @Excel(name = "厂商PM周期",orderNum = "7")
    Long pmCycle;
    @Excel(name = "标准周期",orderNum = "8")
    Long correctedLifespan;
    @Excel(name = "更换频次",orderNum = "9")
    String repFrequency;
    @Excel(name = "备件等级",orderNum = "10")
    String spareLevel;
}
