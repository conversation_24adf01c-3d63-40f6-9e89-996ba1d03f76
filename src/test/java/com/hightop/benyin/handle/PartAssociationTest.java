package com.hightop.benyin.handle;

import com.hightop.benyin.BaseTest;
import com.hightop.benyin.product.domain.service.ProductPartDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductPart;
import com.hightop.benyin.product.infrastructure.entity.ProductPartAssociation;
import com.hightop.fario.base.util.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.hightop.benyin.handle.Tools.insertPrefix;
import static com.hightop.benyin.handle.Tools.parseExcel;

/**
 * 数据处理
 *
 * @Author: xhg
 * @Date: 2023/11/28 18:34
 */
public class PartAssociationTest extends BaseTest {
    private static final String PATH = "C:\\Users\\<USER>\\Desktop\\benyin-data\\";
    private static final String PROD_SQL = "C:\\Users\\<USER>\\Desktop\\benyin-data\\正式环境sql\\";
    @Autowired
    ProductPartDomainService partDomainService;


    @Test
    public void ass() throws IOException {
        List<ProductPartAssExcel> ass = parseExcel(PATH + "零件通用关联表.xlsx",
            0, 258, ProductPartAssExcel.class);
        List<ProductPart> list = this.partDomainService.lambdaQuery().list();
        BufferedWriter bw = new BufferedWriter(new FileWriter(PROD_SQL + "零件通用关联表.sql"));
        parseAssociation(list,ass,bw);
        bw.close();
    }
    /**
     * 从零件list+通用关联表解析通用关联关系
     *
     * @param partList
     * @param bw
     * @return:
     * @Author: xhg
     * @Date: 2023/11/29 16:19
     */
    private static void parseAssociation(List<ProductPart> partList, List<ProductPartAssExcel> ass, BufferedWriter bw) {
        Map<String, Long> pam = partList.stream()
            .collect(Collectors.toMap(ProductPart::getOemNumber, ProductPart::getId));
        List<ProductPartAssociation> associationList = new ArrayList<>();
        Map<String, List<ProductPartAssExcel>> r = ass.stream()
            .filter(f -> StringUtils.isNotBlank(f.getParentOem()))
            .collect(Collectors.groupingBy(ProductPartAssExcel::getParentOem));
        r.forEach((rk, rv) -> {
            rv.stream().filter(f -> StringUtils.isNotBlank(f.getOem())).forEach(rve -> {
                Long parentId = pam.get(rk);
                Optional.ofNullable(parentId).ifPresent(i -> {
                    Long pId = pam.get(rve.getOem());
                    Optional.ofNullable(pId).ifPresent(i2 -> {
                        if (i2.equals(parentId)) {
                            // 顶层
                            associationList.add(new ProductPartAssociation(pId, ProductPartAssociation.TOP));
                        } else {
                            associationList.add(new ProductPartAssociation(pId, parentId));
                        }
                    });
                });
            });
        });
        associationList.stream().forEach(ae -> {
            try {
                bw.write(insertPrefix(ProductPartAssociation.class) +
                    String.join(",",
                        Optional.ofNullable(ae.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getParentId()).map(String::valueOf).orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }
}
