package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 设备excel结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductDeviceExcel {
    @Excel(name = "全称",orderNum = "3")
    String qc;
    @Excel(name = "机型",orderNum = "4")
    String jx;
    @Excel(name = "设备类型",orderNum = "5")
    String type;
    @Excel(name = "主机类型",orderNum = "6")
    String hostType;
    @Excel(name = "色彩类型",orderNum = "7")
    String color;
    @Excel(name = "生产类型",orderNum = "8")
    String produce;
    @Excel(name = "主机原理",orderNum = "9")
    String hostPrinciple;
    @Excel(name = "是否带复印/扫描",orderNum = "10")
    String scan;
    @Excel(name = "寿命印张数",orderNum = "11")
    String lifespan;
    @Excel(name = "寿命年限",orderNum = "12")
    String lifeYear;
    @Excel(name = "电压",orderNum = "13")
    String electric;
    @Excel(name = "黑白打印速度",orderNum = "15")
    String bwSpeed;
    @Excel(name = "彩色打印速度",orderNum = "16")
    String clSpeed;
    @Excel(name = "主机尺寸",orderNum = "17")
    String size;
    @Excel(name = "安装尺寸",orderNum = "18")
    String installationSize;
    @Excel(name = "重量",orderNum = "19")
    String weight;
    @Excel(name = "分辨率",orderNum = "20")
    String ratio;
    @Excel(name = "功率",orderNum = "21")
    String power;
    @Excel(name = "标称尺寸",orderNum = "22")
    String normalSize;
    @Excel(name = "电流",orderNum = "23")
    String current;
}
