package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

/**
 * @Description: 工具类
 * @Author: xhg
 * @Date: 2023/11/29 22:01
 */
public class Tools {
    public static <T> List<T> parseExcel(String fileName, int startRow, int endRow, Class<?> clz) {
        File partPmFile = new File(fileName);
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(startRow);
        // 读取结束行
        params.setReadRows(endRow);
        return ExcelImportUtil.importExcel(partPmFile, clz, params);
    }

    public static <T> List<T> parseExcel(String fileName, int sheetIndex, int startRow, int endRow, Class<?> clz) {
        File partPmFile = new File(fileName);
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(startRow);
        // 读取结束行
        params.setReadRows(endRow);
        params.setStartSheetIndex(sheetIndex);
        return ExcelImportUtil.importExcel(partPmFile, clz, params);
    }

    public static String insertPrefix(Class<?> clz) {
        List<Field> fields = TableInfoHelper.getAllFields(clz);
        String tableName = clz.getDeclaredAnnotation(TableName.class).value();
        List<String> fieldList = new ArrayList<>();
        for (Field f : fields) {
            TableId da = f.getDeclaredAnnotation(TableId.class);
            if (null != da) {
                fieldList.add("`" + f.getDeclaredAnnotation(TableId.class).value() + "`");
            } else {
                String v = f.getDeclaredAnnotation(TableField.class).value();
                if (StringUtils.isNotBlank(v)) {
                    fieldList.add("`" + v + "`");
                }
            }
        }
        return "INSERT INTO " + tableName + " (" + String.join(",", fieldList) + ") VALUES (";
    }

    public static String insertNoIdPrefix(Class<?> clz) {
        List<Field> fields = TableInfoHelper.getAllFields(clz);
        String tableName = clz.getDeclaredAnnotation(TableName.class).value();
        List<String> fieldList = new ArrayList<>();
        for (Field f : fields) {
            if (null != f.getDeclaredAnnotation(TableId.class)) {
                continue;
            }
            String v = f.getDeclaredAnnotation(TableField.class).value();
            if (StringUtils.isNotBlank(v)) {
                fieldList.add("`" + v + "`");
            }
        }
        return "INSERT INTO " + tableName + " (" + String.join(",", fieldList) + ") VALUES (";
    }

    /**
     * 根据数据集生成sql语句
     *
     * @param list
     * @param sqlPath
     * @return:
     * @Author: xhg
     * @Date: 2023/12/12 18:28
     */
    public static <T> void convertDataToSqlFile(List<T> list, String sqlPath) throws IOException {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        BufferedWriter bw = new BufferedWriter(new FileWriter(sqlPath));
        Class<? extends Object> clz = list.get(0).getClass();
        List<Field> fields = TableInfoHelper.getAllFields(clz);
        list.stream().forEach(e -> {
            try {
                List<CharSequence> data = new ArrayList<>();
                for (Field f : fields) {
                    f.setAccessible(true);
                    if (Objects.equals(f.getType().getName(), Long.class.getTypeName())) {
                        data.add(Optional.ofNullable(f.get(e)).map(String::valueOf).orElse("NULL"));
                    } else if (Objects.equals(f.getType().getName(), Integer.class.getTypeName())) {
                        data.add(Optional.ofNullable(f.get(e)).map(String::valueOf).orElse("NULL"));
                    } else {
                        data.add(Optional.ofNullable(f.get(e)).map(String::valueOf).map(m -> m = "\"" + m + "\"").orElse("NULL"));
                    }
                }
                bw.write(insertPrefix(clz) + String.join(",", data) + ");");
                bw.newLine();
                bw.flush();
            } catch (IOException | IllegalAccessException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }

    public static List<Map<String,String>> multiSheetParse(String fileName, int sheetIndex,int readRow){
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(readRow);
        params.setStartSheetIndex(sheetIndex);
        params.setSheetNum(1);
        return ExcelImportUtil.importExcel(new File(fileName), Map.class, params);
    }
    public static void main(String[] args){
        List<Map<String, String>> res =
            multiSheetParse("C:\\Users\\<USER>\\Desktop\\理光机器代码第一批导入.xlsx",
                1, 583);
        System.out.println(res.size());
        System.out.println(res.get(0));
    }
}
