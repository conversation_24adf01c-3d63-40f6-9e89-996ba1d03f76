package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.json.JSONUtil;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.customer.domain.service.CustomerBusinessDomainService;
import com.hightop.benyin.customer.domain.service.CustomerCallRecordDomainService;
import com.hightop.benyin.customer.domain.service.CustomerDomainService;
import com.hightop.benyin.customer.domain.service.CustomerStaffDomainService;
import com.hightop.benyin.customer.infrastructure.entity.Customer;
import com.hightop.benyin.customer.infrastructure.entity.CustomerBusiness;
import com.hightop.benyin.iot.domain.service.IotNumericCodeServiceDomain;
import com.hightop.benyin.iot.infrastructure.entity.IotNumericCode;
import com.hightop.benyin.knowledge.CustomerBusinessExcel;
import com.hightop.magina.standard.ums.user.basic.UserBasicDomainService;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 客户员工结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
public class IotNumericCodeExcelTest extends BaseTest {

    @Autowired
    private IotNumericCodeServiceDomain numericCodeService;

    @Test
    public void test1(){
        String fileName = "C:\\code.xlsx";
        ImportParams params = new ImportParams();
        // 读取开始行。默认第一行
        params.setStartRows(0);
        // 读取结束行
        params.setReadRows(10000);
        params.setStartSheetIndex(0);
//        params.setSheetNum(18);
        List<IotNumericCodeExcel> data = ExcelImportUtil.importExcel(new File(fileName), IotNumericCodeExcel.class, params);
        System.out.println("数量条数：" + data.size());
        if (data != null){
            List<IotNumericCode> list = new ArrayList<IotNumericCode>();
            data.stream().forEach(it ->{
//                String state = it.getState();
//                if ("是".equals(state)){
//                    it.setState("1");
//                } else {
//                    it.setState("0");
//                }
                IotNumericCode item = new IotNumericCode();
                BeanUtils.copyProperties(it,item);
                list.add(item);
            });
            numericCodeService.saveBatch(list);
        }
        System.out.println(JSONUtil.toJsonStr(data));
    }
}
