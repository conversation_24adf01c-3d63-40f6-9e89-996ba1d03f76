package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 零件PM信息excel结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductPartPmExcel {
    @Excel(name = "原厂零件编号",orderNum = "4")
    String oem;
    @Excel(name = "厂商PM周期",orderNum = "12")
    String pmCycle;
    @Excel(name = "运营修正寿命",orderNum = "13")
    String correctedLifespan;
    @Excel(name = "更换频次",orderNum = "14")
    String repFrequency;
    @Excel(name = "备件等级",orderNum = "15")
    String spareLevel;
}
