package com.hightop.benyin.handle;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.yulichang.toolkit.MPJWrappers;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.customer.infrastructure.entity.Customer;
import com.hightop.benyin.customer.infrastructure.entity.CustomerBusiness;
import com.hightop.benyin.customer.infrastructure.entity.CustomerStaff;
import com.hightop.benyin.product.infrastructure.entity.*;
import com.hightop.benyin.share.domain.service.RegionDomainService;
import com.hightop.benyin.share.infrastructure.entity.Region;
import com.hightop.fario.base.constant.StringConstants;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.dictionary.dict.Dict;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.hightop.benyin.handle.Tools.insertPrefix;
import static com.hightop.benyin.handle.Tools.parseExcel;

/**
 * 数据处理
 *
 * @Author: xhg
 * @Date: 2023/11/28 18:34
 */
public class DataHandleTest extends BaseTest {
    private static final String PATH = "C:\\Users\\<USER>\\Desktop\\benyin-data\\";
    @Autowired
    DictItemDomainService dictItemDomainService;
    @Autowired
    RegionDomainService regionDomainService;

    @Test
    public void deviceImport() throws IOException {
        // 产品树和设备需要同时生成，确保机型+全称不会重复
        List<ProductTree> productTree = productImport("品牌产品树系列数据.xlsx",
            0, 1206, ProductTreeExcel.class);
        paresProduct(productTree, "品牌产品树系列数据");
        parseDevice(productTree, "主机设备基础信息.xlsx", 0, 1266);
    }

    @Test
    public void customerImport() throws IOException {
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        List<CustomerExcel> cs = parseExcel(PATH + "客户信息.xlsx", 0, 217, CustomerExcel.class);
        List<CustomerStaffExcel> csf = parseExcel(PATH + "客户信息.xlsx", 1, 0, 236, CustomerStaffExcel.class);
        Map<String, List<CustomerStaffExcel>> csfm = csf.stream()
            .collect(Collectors.groupingBy(CustomerStaffExcel::getId));
        List<Customer> a = new ArrayList<>();
        List<CustomerStaff> b = new ArrayList<>();
        List<CustomerBusiness> c = new ArrayList<>();
        cs.stream().forEach(e -> {
            long cId = IdWorker.getId();
            Customer customer = new Customer();
            BeanUtils.copyProperties(e, customer);
            customer.setId(cId);
            customer.setSource(new DictItemEntry().setValue(Customer.SOURCE_BACKEND));
            customer.setType(new DictItemEntry().setValue(getDictItemValue(Customer.TYPE, e.getType())));
            customer.setStatus(new DictItemEntry().setValue(getDictItemValue(Customer.STATUS, e.getStatus())));
            customer.setBusinessStatus(new DictItemEntry().setValue(getDictItemValue(Customer.STATUS, e.getBusinessStatus())));
            customer.setIndustryAttr(new DictItemEntry().setValue(getDictItemValue(Customer.STATUS, e.getIndustryAttr())));
            customer.setRegionCode(Optional.ofNullable(this.regionDomainService.lambdaQuery()
                .eq(Region::getName, e.getRegionCode()).list().get(0)).map(Region::getCode).orElse(null));
            LocalDateTime createAt = LocalDateTime.parse(e.getCreatedAt(), pattern);
            LocalDateTime updateAt = LocalDateTime.parse(e.getUpdatedAt(), pattern);
            customer.setCreatedAt(createAt);
            customer.setUpdatedAt(updateAt);
            customer.setDeleted(0);
            CustomerBusiness cb = new CustomerBusiness();
            BeanUtils.copyProperties(e, cb);
            cb.setCustomerId(cId);
            cb.setId(IdWorker.getId());
            cb.setCreatedAt(createAt);
            cb.setUpdatedAt(updateAt);

            if (StringUtils.isNotBlank(e.getSigningTime())) {
                cb.setSigningTime(LocalDateTime.parse(e.getSigningTime(), pattern));
            }
            if (StringUtils.isNotBlank(e.getVisitTime())) {
                cb.setVisitTime(LocalDateTime.parse(e.getVisitTime(), pattern));
            }
            c.add(cb);
            List<CustomerStaffExcel> staffExcelList = csfm.get(e.getId());
            if (CollectionUtils.isNotEmpty(staffExcelList)) {
                staffExcelList.stream().forEach(staff -> {
                    CustomerStaff s = new CustomerStaff();
                    s.setId(IdWorker.getId());
                    s.setCustomerId(cId);
                    s.setCreatedAt(createAt);
                    s.setUpdatedAt(createAt);
                    s.setDeleted(0);
                    s.setName(staff.getName());
                    s.setTel(staff.getTel());
                    s.setRole(new DictItemEntry().setValue(getDictItemValue(CustomerStaff.ROLE, staff.getRole())));
                    s.setStatus(true);
                    b.add(s);
                });
            }
            a.add(customer);
        });
        BufferedWriter bw = new BufferedWriter(new FileWriter(PATH + "客户.sql"));
        // 解析数据
        parseCustomer(a, bw);
        parseCustomerStaff(b, bw);
        parseCustomerBusiness(c, bw);
        bw.close();
    }

    private void parseCustomer(List<Customer> a, BufferedWriter bw) {
        a.stream().forEach(ae -> {
            try {
                bw.write(insertPrefix(Customer.class) +
                    String.join(",",
                        Optional.ofNullable(ae.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getSeqId()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getName()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getSource().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getType().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getStatus().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getShopRecruitment()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getBusinessStatus().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getIndustryAttr().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getCaptcha()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getLegalPerson()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getLegalPersonTel()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getGroupId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getLongitude()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getLatitude()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getRegionCode()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getAddress()).map(m -> m = "'" + m + "'").orElse("NULL"),
//                        Optional.ofNullable(ae.getShopRecruitmentImg()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getDeleted()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
    }

    private void parseCustomerStaff(List<CustomerStaff> b, BufferedWriter bw) {
        b.stream().forEach(ae -> {
            try {
                bw.write(insertPrefix(CustomerStaff.class) +
                    String.join(",",
                        Optional.ofNullable(ae.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getCustomerId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getName()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getTel()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getRole().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getStatus()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getDeleted()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
    }

    private void parseCustomerBusiness(List<CustomerBusiness> c, BufferedWriter bw) {
        c.stream().forEach(ae -> {
            try {
                bw.write(insertPrefix(CustomerBusiness.class) +
                    String.join(",",
                        Optional.ofNullable(ae.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getCustomerId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getBank()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getBankAccount()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getAccount()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getTicketType()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getFinance()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getFinanceTel()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getBusinessman()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getSalesman()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getVisitTime()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getSigningTime()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getBankCardImg()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getCreditCode()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getLicense()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getLicenseImg()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
    }

    /**
     * 产品树导入
     *
     * @param
     * @return:
     * @Author: xhg
     * @Date: 2023/11/28 20:23
     */
    private static List<ProductTree> productImport(String excelPath, int startRow, int endRow, Class c) {
        List<ProductTreeExcel> data = parseExcel(PATH + excelPath, startRow, endRow, c);
        LocalDateTime now = LocalDateTime.now();
        List<ProductTree> res = new ArrayList<>();
        // 根据excel数据生成DDL sql语句
        Map<String, List<ProductTreeExcel>> brand = data.stream().filter(f -> StringUtils.isNotBlank(f.getBrand())).collect(Collectors.groupingBy(ProductTreeExcel::getBrand));
        if (CollectionUtils.isNotEmpty(brand)) {
            brand.forEach((k, v) -> {
                long brandId = IdWorker.getId();
                ProductTree p1 = new ProductTree();
                p1.setId(brandId);
                p1.setParentId(ProductTree.TOP);
                p1.setName(k);
                p1.setLastLevel(0);
                p1.setFullIdPath(StringConstants.SLASH + brandId);
                p1.setCreatedAt(now);
                p1.setUpdatedAt(now);
                res.add(p1);
                // 如果产品树为空则跳过
                Map<String, List<ProductTreeExcel>> product = v.stream().filter(f -> StringUtils.isNotBlank(f.getProduct())).collect(Collectors.groupingBy(ProductTreeExcel::getProduct));
                if (CollectionUtils.isNotEmpty(product)) {
                    product.forEach((k2, v2) -> {
                        long productId = IdWorker.getId();
                        ProductTree p2 = new ProductTree();
                        p2.setId(productId);
                        p2.setParentId(brandId);
                        p2.setName(k2);
                        p2.setLastLevel(0);
                        p2.setFullIdPath(StringConstants.SLASH + brandId + StringConstants.SLASH + productId);
                        p2.setCreatedAt(now);
                        p2.setUpdatedAt(now);
                        res.add(p2);
                        Map<String, List<ProductTreeExcel>> serial = v2.stream().filter(f -> StringUtils.isNotBlank(f.getSerial())).collect(Collectors.groupingBy(ProductTreeExcel::getSerial));
                        if (CollectionUtils.isNotEmpty(serial)) {
                            serial.forEach((k3, v3) -> {
                                long serialId = IdWorker.getId();
                                ProductTree p3 = new ProductTree();
                                p3.setId(serialId);
                                p3.setParentId(productId);
                                p3.setName(k3);
                                p3.setLastLevel(0);
                                p3.setFullIdPath(StringConstants.SLASH + brandId +
                                    StringConstants.SLASH + productId +
                                    StringConstants.SLASH + serialId
                                );
                                p3.setCreatedAt(now);
                                p3.setUpdatedAt(now);
                                res.add(p3);
                                // 机型可能存在重复
                                List<ProductTreeExcel> jx = v3.stream().filter(f -> StringUtils.isNotBlank(f.getName())).collect(Collectors.toList());
                                jx.stream().forEach(e -> {
                                    long jxId = IdWorker.getId();
                                    ProductTree p4 = new ProductTree();
                                    p4.setId(jxId);
                                    p4.setParentId(serialId);
                                    p4.setName(e.getName());
                                    p4.setFullName(e.getFullName());
                                    p4.setLastLevel(1);
                                    p4.setFullIdPath(StringConstants.SLASH + brandId +
                                        StringConstants.SLASH + productId +
                                        StringConstants.SLASH + serialId +
                                        StringConstants.SLASH + jxId
                                    );
                                    p4.setCreatedAt(now);
                                    p4.setUpdatedAt(now);
                                    res.add(p4);
                                });
                            });
                        }
                    });
                }
            });
        }
        return res;
    }

    private static void paresProduct(List<ProductTree> res, String sqlName) throws IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter(PATH + sqlName + ".sql"));
        res.stream().forEach(p -> {
            try {
                bw.write(insertPrefix(ProductTree.class) +
                    String.join(",",
                        Optional.ofNullable(p.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getParentId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getName()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getLastLevel()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getFullName()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getFullIdPath()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        bw.close();
    }

    private static List<ProductPart> parsePart(String excelName, int s, int e) {
        List<ProductPartExcel> data = parseExcel(PATH + excelName, s, e, ProductPartExcel.class);
        List<ProductPart> partList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        // 生成b_product_part的sql
        Map<String, List<ProductPartExcel>> oemGroup = data.stream()
            .filter(f -> StringUtils.isNotBlank(f.getOem()))
            .collect(Collectors.groupingBy(ProductPartExcel::getOem));
        if (CollectionUtils.isNotEmpty(oemGroup)) {
            oemGroup.forEach((k, v) -> {
                Long partId = IdWorker.getId();
                ProductPart p = new ProductPart();
                p.setId(partId);
                p.setOemNumber(k);
                ProductPartExcel first = v.get(0);
                p.setCh(first.getCh());
                p.setEn(first.getEn());
                p.setCreatedAt(now);
                p.setUpdatedAt(now);
                p.setDeleted(0);
                partList.add(p);
            });
        }
        return partList;
    }

    public static void partToSql(List<ProductPart> partList, String sqlName) throws IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter(PATH + sqlName + ".sql"));
        // oem大小写去重
        Map<String, List<ProductPart>> op = partList.stream()
            .collect(Collectors.groupingBy(g -> g.getOemNumber().toLowerCase()));

        op.forEach((k, v) -> {
            try {
                ProductPart p = v.get(0);
                bw.write(insertPrefix(ProductPart.class) +
                    String.join(",",
                        Optional.ofNullable(p.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getOemNumber()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getEn()).map(m -> m = "\"" + m + "\"").orElse("NULL"),
                        Optional.ofNullable(p.getCh()).map(m -> m = "\"" + m + "\"").orElse("NULL"),
                        Optional.ofNullable(p.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getDeleted()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }



    private void parseDevice(List<ProductTree> productTree, String excelName, int s, int e) throws IOException {
        Map<String, Long> treeMap = productTree.stream()
            .filter(f -> f.getLastLevel() == 1)
            .collect(Collectors.toMap(ProductTree::getFullName, ProductTree::getId));
        List<ProductDeviceExcel> sb = parseExcel(PATH + excelName, s, e, ProductDeviceExcel.class);
        Map<String, List<ProductDeviceExcel>> jx = sb.stream().collect(Collectors.groupingBy(g -> g.getQc() + g.getJx()));
        List<ProductDevice> dl = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        jx.forEach((k, v) -> {
            ProductDeviceExcel pde = v.get(0);
            Long matchProduct = treeMap.get(pde.getQc());
            if (null != matchProduct) {
                long dId = IdWorker.getId();
                ProductDevice d = new ProductDevice();
                BeanUtils.copyProperties(pde, d);
                d.setProductId(matchProduct);
                d.setType(new DictItemEntry().setValue(getDictItemValue(ProductDevice.TYPE, pde.getType())));
                d.setColor(new DictItemEntry().setValue(getDictItemValue(ProductDevice.COLOR, pde.getColor())));
                d.setProduce(new DictItemEntry().setValue(getDictItemValue(ProductDevice.PRODUCE, pde.getProduce())));
                d.setScan(new DictItemEntry().setValue(getDictItemValue(ProductDevice.SCAN, pde.getScan())));
                d.setHostType(new DictItemEntry().setValue(getDictItemValue(ProductDevice.HOST_TYPE, pde.getHostType())));
                d.setCreatedAt(now);
                d.setUpdatedAt(now);
                d.setDeleted(0);
                d.setId(dId);
                dl.add(d);
            }
        });
        BufferedWriter bw = new BufferedWriter(new FileWriter(PATH + "设备.sql"));
        // 解析数据
        dl.stream().forEach(ae -> {
            try {
                bw.write(insertPrefix(ProductDevice.class) +
                    String.join(",",
                        Optional.ofNullable(ae.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getType().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getColor().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getProduce().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getScan().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getHostType().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getHostPrinciple()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getLifespan()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(ae.getLifeYear()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getBwSpeed()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getClSpeed()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getSize()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getInstallationSize()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getWeight()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getRatio()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getElectric()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getPower()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getNormalSize()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getCurrent()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(ae.getDeleted()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }

    private String getDictItemValue(String dictCode, String itemLabel) {
        return Optional.ofNullable(this.dictItemDomainService.selectJoinOne(
            DictItem.class,
            MPJWrappers.<DictItem>lambdaJoin()
                .selectAll(DictItem.class)
                .innerJoin(Dict.class, on -> on.eq(Dict::getId, DictItem::getDictId)
                    .eq(Dict::getCode, dictCode))
                .eq(DictItem::getLabel, itemLabel))).map(DictItem::getValue).orElse(null);
    }

    public static void main(String[] args) throws IOException {
//        List<ProductPartPMExcel> kmPm = parseExcel(PATH + "柯美PM零件表20231129.xlsx", 0, 490, ProductPartPMExcel.class);
        List<ProductPart> km = parsePart("柯美所有零件20231129.xlsx", 0, 64513);

//        List<ProductPartPMExcel> lgPm = parseExcel(PATH + "理光PM零件表20231128.xlsx", 0, 1190, ProductPartPMExcel.class);
        List<ProductPart> lg = parsePart("理光所有零件20231128.xlsx", 0, 53113);

//        List<ProductPartPMExcel> slPm = parseExcel(PATH + "施乐PM表20231129.xlsx", 0, 734, ProductPartPMExcel.class);
        List<ProductPart> sl = parsePart("施乐所有零件20231129.xlsx", 0, 75958);
//        List<ProductPartAssExcel> ass = parseExcel(PATH + "关联表.xls", 0, 258, ProductPartAssExcel.class);
        List<ProductPart> allPart = new ArrayList<>();
        allPart.addAll(km);
        allPart.addAll(lg);
        allPart.addAll(sl);
        Map<Long, List<ProductPart>> x = allPart.stream().collect(Collectors.groupingBy(ProductPart::getId));
        x.forEach((k, v) -> {
            if (v.size() > 1) {
                System.out.println(v);
            }
        });
        partToSql(allPart, "所有零件");

    }

}
