package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 零件通用关联excel结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductPartAssExcel {
    @Excel(name = "关联oem号",orderNum = "4")
    String parentOem;
    @Excel(name = "oem号",orderNum = "5")
    String oem;
}
