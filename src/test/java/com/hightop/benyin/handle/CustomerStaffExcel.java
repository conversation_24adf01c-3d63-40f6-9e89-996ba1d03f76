package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 客户员工结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomerStaffExcel {
    @Excel(name = "$主表唯一标识",orderNum = "0")
    String id;
    @Excel(name = "姓名",orderNum = "1")
    String name;
    @Excel(name = "手机号",orderNum = "2")
    String tel;
    @Excel(name = "角色",orderNum = "4")
    String role;
}
