package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @Description: 零件excel结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProductPartExcel {
    @Excel(name = "零件编号",orderNum = "4")
    String oem;
    @Excel(name = "零件英文名称",orderNum = "5")
    String en;
    @Excel(name = "零件中文名称",orderNum = "6")
    String ch;
    @Excel(name = "商品小类",orderNum = "9")
    String type;
}
