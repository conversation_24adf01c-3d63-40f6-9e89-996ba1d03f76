package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * @Description: 客户员工结构
 * @Author: xhg
 * @Date: 2023/11/28 20:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IotNumericCodeExcel {
    @Excel(name = "brand",orderNum = "0")
    String brand;
    @Excel(name = "series",orderNum = "1")
    String series;
    @Excel(name = "num_code",orderNum = "21")
    String numCode;
    @Excel(name = "num_data",orderNum = "3")
    String numData;
    @Excel(name = "data_describe",orderNum = "4")
    String dataDescribe;
    @Excel(name = "fault_code",orderNum = "5")
    String faultCode;
    @Excel(name = "paper_code",orderNum = "6")
    String paperCode;
    @Excel(name = "state",orderNum = "7")
    String state;
}
