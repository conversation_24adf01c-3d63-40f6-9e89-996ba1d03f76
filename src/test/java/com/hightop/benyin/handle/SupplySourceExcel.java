package com.hightop.benyin.handle;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

/**
 * 供应源实体
 *
 * <AUTHOR>
 * @date 2023-12-06 14:48:56
 */
@Data
@FieldDefaults(level = AccessLevel.PUBLIC)
public class SupplySourceExcel {
    @Excel(name = "物品编号", orderNum = "0")
    String articleCode;

    @Excel(name = "供应商编号", orderNum = "1")
    String manufacturerCode;

    @Excel(name = "需求数量", orderNum = "2")
    Integer num = 0;

    @Excel(name = "单价", orderNum = "3")
    Long price;

    @Excel(name = "合同开始日期", orderNum = "4")
    LocalDateTime validityStartTime;

    @Excel(name = "合同截止日期", orderNum = "5")
    LocalDateTime validityEndTime;

    @Excel(name = "启用状态", orderNum = "6")
    String status;

    @Excel(name = "发货时间标准", orderNum = "7")
    String deliveryTime;

    @Excel(name = "到货时间标准", orderNum = "8")
    String arrivalTime;
    @Excel(name = "结算量1", orderNum = "9")
    Long settlementNumberOne;

    @Excel(name = "结算价格1", orderNum = "10")
    Long settlementPriceOne;

    @Excel(name = "结算量2", orderNum = "11")
    Long settlementNumberTwo;

    @Excel(name = "结算价格2", orderNum = "12")
    Long settlementPriceTwo;

    @Excel(name = "结算量3", orderNum = "13")
    Long settlementNumberThree;

    @Excel(name = "结算价格3", orderNum = "14")
    Long settlementPriceThree;

    @Excel(name = "结算量4", orderNum = "15")
    Long settlementNumberFour;

    @Excel(name = "结算价格4", orderNum = "16")
    Long settlementPriceFour;

    @Excel(name = "结算量5", orderNum = "17")
    Long settlementNumberFive;

    @Excel(name = "结算价格5", orderNum = "18")
    Long settlementPriceFive;
}
