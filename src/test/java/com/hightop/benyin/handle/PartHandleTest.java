package com.hightop.benyin.handle;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.product.domain.service.ProductPartDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductPart;
import com.hightop.benyin.storage.infrastructure.entity.StorageArticle;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.standard.code.dictionary.dict.Dict;
import com.hightop.magina.standard.code.dictionary.dict.DictDomainService;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.hightop.benyin.handle.Tools.parseExcel;

/**
 * 数据处理
 *
 * @Author: xhg
 * @Date: 2023/11/28 18:34
 */
public class PartHandleTest extends BaseTest {
    private static final String PATH = "C:\\Users\\<USER>\\Desktop\\benyin-data\\";
    private static final String PROD_SQL = "C:\\Users\\<USER>\\Desktop\\benyin-data\\正式环境sql\\";
    @Autowired
    DictItemDomainService dictItemDomainService;
    @Autowired
    DictDomainService dictDomainService;

    @Autowired
    ProductPartDomainService partDomainService;
    @Autowired
    ProductTreeDomainService productTreeDomainService;

    @Test
    public void articleType() throws IOException {
        // 正式环境已有零件更新物品小类信息
        List<ProductPartTypeExcel> types = parseExcel(PATH + "物品表增加小类.xlsx",
            0, 313, ProductPartTypeExcel.class);
        Map<String, String> d = getDictItemValue(StorageArticle.TYPE);
        Map<String, String> m = getMap();
        BufferedWriter bw = new BufferedWriter(new FileWriter(PROD_SQL + "物品更新物品小类.sql"));
        String tableName = StorageArticle.class.getDeclaredAnnotation(TableName.class).value();
        types.stream().forEach(e -> {
            try {
                bw.write("UPDATE `" + tableName + "` set type='" + d.get(correctVal(e.getType(), m)) + "' where id =" + e.getId()+";");
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }

    @Test
    public void type() throws IOException {
        List<ProductPartExcel> km = parseExcel(PATH + "柯美所有零件20231129.xlsx",
            0, 64513, ProductPartExcel.class);
        List<ProductPartExcel> lg = parseExcel(PATH + "理光所有零件20231128.xlsx", 0, 53113, ProductPartExcel.class);
        List<ProductPartExcel> sl = parseExcel(PATH + "施乐所有零件20231129.xlsx", 0, 75958, ProductPartExcel.class);
        List<ProductPartExcel> total = new ArrayList<>();
        total.addAll(km);
        total.addAll(lg);
        total.addAll(sl);
        Map<String, String> d = getDictItemValue(StorageArticle.TYPE);
        Map<String, List<ProductPartExcel>> m = total.stream()
            .filter(f->StringUtils.isNotBlank(f.getOem()))
            .collect(Collectors.groupingBy(ProductPartExcel::getOem));
        parse(m, d);
    }

    private void parse(Map<String, List<ProductPartExcel>> res,
                       Map<String, String> d) throws IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter(PROD_SQL + "零件更新物品小类.sql"));
        String tableName = ProductPart.class.getDeclaredAnnotation(TableName.class).value();
        Map<String, String> m = getMap();
        res.forEach((k, v) -> {
            ProductPartExcel p = v.get(0);
            try {
                String t = d.get(correctVal(p.getType(), m));
                if (StringUtils.isNotBlank(t)) {
                    bw.write("UPDATE `" + tableName + "` set type='" + t + "' where oem_number='" + k + "';");
                    bw.newLine();
                    bw.flush();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }


    /**
     * 根据字典码、字典项描述找到值
     *
     * @param dictCode
     * @return: {@link String}
     * @Author: xhg
     * @Date: 2023/12/7 11:34
     */
    private Map<String, String> getDictItemValue(String dictCode) {
        Dict dict = this.dictDomainService.lambdaQuery().eq(Dict::getCode, dictCode).one();
        List<DictItem> dicItem = this.dictItemDomainService.lambdaQuery()
            .eq(DictItem::getDictId, dict.getId()).list();
        return dicItem.stream().collect(Collectors.toMap(DictItem::getLabel, DictItem::getValue, (k, v) -> k));
    }

    private Map<String, String> getMap() {
        Map<String, String> m = new HashMap<>(16);
        m.put("定影带", "其他");
        m.put("搓纸轮", "其他");
        m.put("毛刷辊", "辊");
        m.put("辅件,密封", "辅件");
        m.put("鼓", "鼓");
        m.put("加热辊/热辊/上辊", "辊");
        m.put("辅件,皮带", "辅件");
        m.put("充电", "充电");
        m.put("辅件,电子元件", "辅件");
        m.put("轴承", "轴承/轴套");
        m.put("刮板", "刮板");
        m.put("齿轮", "齿轮");
        m.put("辅件,传感器", "辅件");
        m.put("灯管", "灯管");
        m.put("压辊/下辊", "辊");
        m.put("电子器件", "电子器件");
        m.put("带子", "带子");
        m.put("分离件", "分离件");
        m.put("辅件,转印带", "辅件");
        m.put("辅件,齿轮", "齿轮");
        m.put("辅件,偏压辊", "辅件");
        m.put("载体", "载体");
        m.put("辅件,热敏电阻", "辅件");
        m.put("辅件,灯管", "灯管");
        m.put("辅件,弹簧", "辅件");
        m.put("钢丝绳", "其他");
        m.put("蜡条", "蜡条");
        m.put("辅件,图像辊", "辅件");
        m.put("轮子", "轮子");
        m.put("辅件,轴套", "辅件");
        m.put("清洁纸", "清洁纸");
        m.put("碳粉", "碳粉墨水");
        m.put("辅件,二转辊", "辅件");
        m.put("轴承/轴套", "轴承/轴套");
        m.put("辅件,螺旋杆", "辅件");
        m.put("组件/单元", "组件/单元");
        m.put("辅件", "辅件");
        m.put("辅件,保险", "辅件");
        m.put("辅件,分离件", "分离件");
        m.put("辊", "辊");
        m.put("辅件,电路板", "电路板");
        return m;
    }

    private String correctVal(String value, Map<String, String> m) {
        return m.get(value);
    }
}
