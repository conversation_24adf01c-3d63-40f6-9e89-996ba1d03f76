package com.hightop.benyin.handle;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.product.domain.service.ProductPartDomainService;
import com.hightop.benyin.product.domain.service.ProductTreeDomainService;
import com.hightop.benyin.product.infrastructure.entity.ProductPart;
import com.hightop.benyin.product.infrastructure.entity.ProductPartBom;
import com.hightop.benyin.product.infrastructure.entity.ProductTree;
import com.hightop.fario.base.util.CollectionUtils;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.magina.standard.code.dictionary.bind.DictItemEntry;
import com.hightop.magina.standard.code.dictionary.dict.Dict;
import com.hightop.magina.standard.code.dictionary.dict.DictDomainService;
import com.hightop.magina.standard.code.dictionary.item.DictItem;
import com.hightop.magina.standard.code.dictionary.item.DictItemDomainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.hightop.benyin.handle.Tools.insertPrefix;
import static com.hightop.benyin.handle.Tools.parseExcel;

/**
 * 数据处理
 *
 * @Author: xhg
 * @Date: 2023/11/28 18:34
 */
public class PartBomImportTest extends BaseTest {
    private static final String PATH = "C:\\Users\\<USER>\\Desktop\\benyin-data\\";
    private static final String PROD_SQL = "C:\\Users\\<USER>\\Desktop\\benyin-data\\正式环境sql\\";
    @Autowired
    DictItemDomainService dictItemDomainService;
    @Autowired
    DictDomainService dictDomainService;

    @Autowired
    ProductPartDomainService partDomainService;
    @Autowired
    ProductTreeDomainService productTreeDomainService;

    public static void main(String[] args) {
        List<ProductBomExcel> bom = parseExcel(PATH + "零件BOM信息(数据单).xlsx",
            0, 193649, ProductBomExcel.class);
        Set<String> serials = bom.stream().map(ProductBomExcel::getUnit).collect(Collectors.toSet());
        System.out.println(String.join("','", serials));
    }

    @Test
    public void bom() throws IOException {
        List<ProductBomExcel> bom = parseExcel(PATH + "零件BOM信息(数据单).xlsx",
            0, 193649, ProductBomExcel.class);
        System.out.println("读取数量：" + bom.size());
        List<ProductPartBom> res = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Map<String, String> d = getDictItemValue(ProductPartBom.UNIT);
        Map<String, String> d2 = getDictItemValue(ProductPartBom.REP_FREQUENCY);
        Map<String, String> d3 = getDictItemValue(ProductPartBom.SPARE_LEVEL);
        Map<String, ProductPart> part = this.partDomainService.lambdaQuery().list()
            .stream().collect(Collectors.toMap(ProductPart::getOemNumber, m -> m));
        Set<String> serials = bom.stream().map(ProductBomExcel::getSerial).collect(Collectors.toSet());
        Set<String> models = bom.stream().map(ProductBomExcel::getModel).collect(Collectors.toSet());
        // 系列下所有的机型
        Map<String, List<Long>> serMap = new HashMap<>(16);
        // 机型
        Map<String, Long> modMap = new HashMap<>(16);
        for (String s : serials) {
            ProductTree se = this.productTreeDomainService.lambdaQuery()
                .eq(ProductTree::getName, s)
                .eq(ProductTree::getLastLevel, 0).one();
            if (Objects.nonNull(se)) {
                List<ProductTree> mm = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getParentId, se.getId())
                    .eq(ProductTree::getLastLevel, 1)
                    .list();
                if (CollectionUtils.isNotEmpty(mm)) {
                    serMap.put(s, mm.stream().map(ProductTree::getId).collect(Collectors.toList()));
                } else {
                    System.out.println("系列" + s + "下没有找到可用机型");
                }
            } else {
                System.out.println("没有查到系列：" + s);
            }
        }
        for (String s : models) {
            List<ProductTree> se = this.productTreeDomainService.lambdaQuery()
                .eq(ProductTree::getName, s)
                .eq(ProductTree::getLastLevel, 1).list();
            if (CollectionUtils.isNotEmpty(se)) {
                modMap.put(s, se.get(0).getId());
            } else {
                // 没有查到机型，则根据全称查询
                List<ProductTree> se2 = this.productTreeDomainService.lambdaQuery()
                    .eq(ProductTree::getFullName, s)
                    .eq(ProductTree::getLastLevel, 1).list();
                if (CollectionUtils.isNotEmpty(se2)) {
                    modMap.put(s, se2.get(0).getId());
                }
            }
        }
        // 确保系列不为空、oem不重复；如果机型是空的 ，表示该oem适用于所有系列下的机型
        for (ProductBomExcel b : bom) {
            if (StringUtils.isNotBlank(b.getModel())) {
                add(res, b, part, modMap, now, d,d2,d3);
            } else {
                multiAdd(res, b, part, serMap, now, d,d2,d3);
            }
        }
        parse(res);
    }

    private void parse(List<ProductPartBom> res) throws IOException {
        BufferedWriter bw = new BufferedWriter(new FileWriter(PROD_SQL + "bom.sql"));
        res.stream().forEach(p -> {
            try {
                bw.write(insertPrefix(ProductPartBom.class) +
                    String.join(",",
                        Optional.ofNullable(p.getId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getProductId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getPartId()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getCh()).map(m -> m = "\"" + m + "\"").orElse("NULL"),
                        Optional.ofNullable(p.getEn()).map(m -> m = "\"" + m + "\"").orElse("NULL"),
                        Optional.ofNullable(p.getUnit().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getNum()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getStandard()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getIsPm()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getPmCycle()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getCorrectedLifespan()).map(String::valueOf).orElse("NULL"),
                        Optional.ofNullable(p.getRepFrequency().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getSpareLevel().getValue()).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getCreatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getUpdatedAt()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL"),
                        Optional.ofNullable(p.getDeleted()).map(String::valueOf).map(m -> m = "'" + m + "'").orElse("NULL")
                            + ");"
                    ));
                bw.newLine();
                bw.flush();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        });
        bw.close();
    }

    private void multiAdd(List<ProductPartBom> res, ProductBomExcel b,
                          Map<String, ProductPart> part,
                          Map<String, List<Long>> serMap,
                          LocalDateTime now,
                          Map<String, String> d,Map<String, String> d2,Map<String, String> d3) {
        // 根据oem查询唯一零件
        ProductPart p = part.get(b.getOem());
        List<Long> trees = serMap.get(b.getSerial());
        // 找到了零件才添加BOM信息
        if (Objects.nonNull(p)) {
            if (CollectionUtils.isNotEmpty(trees)) {
                for (Long tree : trees) {
                    ProductPartBom m = new ProductPartBom();
                    m.setId(IdWorker.getId());
                    m.setPartId(p.getId());
                    m.setCh(p.getCh());
                    m.setEn(p.getEn());
                    m.setProductId(tree);
                    m.setUnit(new DictItemEntry().setValue(d.get(b.getUnit())));
                    if (StringUtils.isNotBlank(b.getStandard())) {
                        m.setStandard(Objects.equals(b.getStandard(), "是") ? true : false);
                    } else {
                        m.setStandard(false);
                    }
                    if (StringUtils.isNotBlank(b.getIsPm())) {
                        m.setIsPm(Objects.equals(b.getIsPm(), "是") ? true : false);
                    } else {
                        m.setIsPm(false);
                    }
                    m.setPmCycle(Optional.ofNullable(b.getPmCycle()).orElse(null));
                    m.setCorrectedLifespan(Optional.ofNullable(b.getCorrectedLifespan()).orElse(null));
                    m.setRepFrequency(new DictItemEntry().setValue(d.get(b.getRepFrequency())));
                    m.setSpareLevel(new DictItemEntry().setValue(d.get(b.getSpareLevel())));
                    m.setCreatedAt(now);
                    m.setUpdatedAt(now);
                    m.setDeleted(0);
                    res.add(m);
                }
            }
        }

    }

    private void add(List<ProductPartBom> res, ProductBomExcel b,
                     Map<String, ProductPart> part,
                     Map<String, Long> modMap, LocalDateTime now,
                     Map<String, String> d,Map<String, String> d2,Map<String, String> d3) {
        // 根据oem查询唯一零件
        ProductPart p = part.get(b.getOem());
        // 找到了零件才添加BOM信息
        if (Objects.nonNull(p)) {
            Long l = modMap.get(b.getModel());
            if (Objects.nonNull(l)) {
                ProductPartBom m = new ProductPartBom();
                m.setId(IdWorker.getId());
                m.setPartId(p.getId());
                m.setCh(p.getCh());
                m.setEn(p.getEn());
                m.setProductId(l);
                m.setUnit(new DictItemEntry().setValue(d.get(b.getUnit())));
                if (StringUtils.isNotBlank(b.getStandard())) {
                    m.setStandard(Objects.equals(b.getStandard(), "是") ? true : false);
                } else {
                    m.setStandard(false);
                }
                if (StringUtils.isNotBlank(b.getIsPm())) {
                    m.setIsPm(Objects.equals(b.getIsPm(), "是") ? true : false);
                } else {
                    m.setIsPm(false);
                }
                m.setPmCycle(Optional.ofNullable(b.getPmCycle()).orElse(null));
                m.setCorrectedLifespan(Optional.ofNullable(b.getCorrectedLifespan()).orElse(null));
                m.setRepFrequency(new DictItemEntry().setValue(d.get(b.getRepFrequency())));
                m.setSpareLevel(new DictItemEntry().setValue(d.get(b.getSpareLevel())));
                m.setCreatedAt(now);
                m.setUpdatedAt(now);
                m.setDeleted(0);
                res.add(m);
            }
        }
    }


    /**
     * 根据字典码、字典项描述找到值
     *
     * @param dictCode
     * @return: {@link String}
     * @Author: xhg
     * @Date: 2023/12/7 11:34
     */
    private Map<String, String> getDictItemValue(String dictCode) {
        Dict dict = this.dictDomainService.lambdaQuery().eq(Dict::getCode, dictCode).one();
        List<DictItem> dicItem = this.dictItemDomainService.lambdaQuery()
            .eq(DictItem::getDictId, dict.getId()).list();
        return dicItem.stream().collect(Collectors.toMap(DictItem::getLabel, DictItem::getValue, (k, v) -> k));
    }
}
