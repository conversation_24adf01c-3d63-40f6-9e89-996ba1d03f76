package com.hightop.benyin.search;

import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.google.common.collect.Lists;
import com.hightop.benyin.BaseTest;
import com.hightop.benyin.search.domain.service.CommodityDocSearchDomainService;
import com.hightop.benyin.search.infrastructure.document.CommodityDoc;
import com.hightop.fario.base.Trio;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: es测试
 * @Author: xhg
 * @Date: 2023/10/25 10:02
 */
public class ElasticsearchTest extends BaseTest {
    @Autowired
    CommodityDocSearchDomainService cds;

    @Test
    public void test1() {
        System.out.println(cds.createIndices(Lists.newArrayList("idx_21")
            , cds.createCommodityIndexRequest("idx_21")));
    }

    @Test
    public void test2() {
        System.out.println(cds.deleteIndices(Lists.newArrayList("idx_2")));
    }

    @Test
    public void test4() {
        List<Query> mustQueryList = new ArrayList<>();
        // 模糊查询
        mustQueryList.add(Query.of(queryBuilder -> queryBuilder
            .matchPhrase(w -> w.field(CommodityDoc.NAME_FIELD).query("打印机"))));
        List<Hit<Map>> map = cds.mustQuery("alias_commodity", mustQueryList);
        for (Hit<Map> m : map) {
            System.out.println(m.source());
        }
    }

    @Test
    public void test5() {
        cds.aliasMapping(Lists.newArrayList(
            Trio.<Boolean, String, List<String>>builder().first(false).second("alias_commodityx")
                .third(Lists.newArrayList("idx_3")).build(),
            Trio.<Boolean, String, List<String>>builder().first(false).second("alias_commodity4")
                .third(Lists.newArrayList("idx_3", "idx_2", "2023_commodity"))
                .build(),
            Trio.<Boolean, String, List<String>>builder().first(false).second("alias_commodity2")
                .third(Lists.newArrayList("idx_3", "idx_2", "2023_commodity"))
                .build()
        ));
    }
}
