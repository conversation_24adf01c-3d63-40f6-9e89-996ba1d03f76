---
description: 
globs: 
alwaysApply: true
---
# 项目结构规范

## 项目概述
这是一个基于Spring Boot的后端项目，提供REST API服务。项目入口是[Application.java](mdc:src/main/java/com/hightop/benyin/Application.java)。

## 目录结构
- `src/main/java/com/hightop/benyin/` - 主要业务代码
  - `components/` - 通用组件
  - `configurer/` - 配置类
  - `**/` - 按业务模块划分的目录结构

## 模块划分
项目按业务领域进行模块划分：
- `activity` - 活动相关
- `cart` - 购物车相关
- `customer` - 客户相关
- `engineer` - 工程师相关
- `es` - ElasticSearch相关
- `item` - 商品相关
- `order` - 订单相关
- `payment` - 支付相关
- `product` - 产品相关
- `search` - 搜索相关

## Maven依赖
主要依赖在[pom.xml](mdc:pom.xml)中定义，包括：
- Spring Boot
- ElasticSearch
- 消息模块
- Feign客户端
- 测试工具

