---
description: 
globs: 
alwaysApply: true
---
# 开发工作流规范

## 开发流程
1. **问题分析**：明确任务目标和约束条件
2. **设计方案**：设计数据结构和算法
3. **实现步骤**：自顶向下实现功能
4. **测试验证**：编写单元测试和集成测试

## 提交规范
- 提交信息必须符合语义化格式：
  - `feat: 添加新功能`
  - `fix: 修复bug`
  - `docs: 更新文档`
  - `style: 代码风格修改`
  - `refactor: 代码重构`
  - `test: 添加测试`
  - `chore: 构建过程或辅助工具变更`

## 分支管理
- `master`: 主分支，只接受经过测试的代码
- `develop`: 开发分支，功能开发完成后合并到此分支
- `feature/*`: 功能分支，用于开发新功能
- `hotfix/*`: 热修复分支，用于紧急修复生产环境的bug

## 代码审查标准
- 代码必须符合既定规范
- 关键函数和组件必须有注释
- 复杂算法需要解释思路
- 不允许提交代码质量明显下降的代码

## 测试要求
- 所有代码变更必须有对应的单元测试
- 核心功能必须有集成测试
- 测试覆盖率应不低于80%
- 修复的bug必须有对应的回归测试

## 代码稳定性保障
- 修改已完成功能前必须先理解其完整设计意图
- 所有API更改必须向下兼容
- 使用单元测试保护核心功能逻辑
- 重构代码时必须保持功能等价性

