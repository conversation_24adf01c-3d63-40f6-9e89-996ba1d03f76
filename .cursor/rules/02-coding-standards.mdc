---
description: 
globs: 
alwaysApply: true
---
# 代码规范标准

## 命名规范
- **类命名**: PascalCase，如`UserService`
- **接口命名**: 使用I前缀，如`IUserService`
- **方法命名**: camelCase，如`getUserById`
- **常量命名**: UPPER_CASE，如`MAX_RETRY_COUNT`
- **变量命名**: camelCase，如`userId`
- **包命名**: 全小写，如`com.hightop.benyin.order`
- **类型定义**: 使用T前缀，如`TUserData`

## 代码风格
- 使用4空格缩进
- 大括号开始于行尾，如:
```java
if (condition) {
    // code
}
```
- 方法之间用一个空行分隔
- 行宽不超过120个字符
- 所有公共API必须有Javadoc注释

## 异常处理
- 优先使用自定义异常
- 异常必须包含明确的错误信息
- 不允许空catch块
- 使用try-with-resources处理资源

## 日志规范
- 使用SLF4J API
- 合理使用日志级别 (ERROR, WARN, INFO, DEBUG)
- 日志信息需包含上下文信息
- 敏感信息不允许记录到日志

## 注释规范
- 类注释包含作者、日期和描述
- 复杂业务逻辑必须有注释说明
- 使用TODO标记待完成的工作
- 使用@Deprecated标记已废弃的API

