---
description: 
globs: 
alwaysApply: true
---
# 架构设计指南

## 分层架构
- **Controller层**: 负责处理HTTP请求和响应，不包含业务逻辑
- **Service层**: 负责业务逻辑实现
- **Repository层**: 负责数据访问和持久化
- **Entity/Model层**: 负责数据模型定义

## 依赖注入
- 使用Spring的依赖注入机制
- 优先使用构造函数注入
- 避免使用字段注入(@Autowired在字段上)

## API设计
- RESTful API设计规范
- 使用HTTP状态码表示请求处理结果
- API版本控制策略
- 统一的响应格式

## 数据访问
- 使用Spring Data JPA或MyBatis
- 事务管理规范
- 查询优化策略
- 连接池配置

## 安全设计
- 认证与授权机制
- 密码加密存储
- CSRF/XSS防护
- 敏感数据处理

## 缓存策略
- 本地缓存使用
- Redis分布式缓存规范
- 缓存失效策略
- 缓存一致性保障

## 异步处理
- 消息队列使用规范
- 异步任务处理
- 定时任务管理
- 并发控制策略

