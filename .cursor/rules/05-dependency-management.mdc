---
description: 
globs: 
alwaysApply: true
---
# 依赖管理规范

## Maven配置
项目使用Maven作为依赖管理工具，主要配置在[pom.xml](mdc:pom.xml)中。

## 依赖版本控制
- 依赖版本必须显式声明
- 使用`<properties>`统一管理版本号
- 继承自父POM: `magina-mysql-redis-parent`

## 依赖源配置
优先使用国内镜像站点安装依赖：
```xml
<repositories>
    <repository>
        <id>gitlab-maven</id>
        <url>http://hightop.xin:9000/api/v4/projects/476/packages/maven</url>
    </repository>
</repositories>
```

## 依赖管理最佳实践
- 避免依赖冲突
- 定期更新依赖版本解决安全漏洞
- 使用依赖排除机制解决传递依赖问题
- 使用`<scope>`合理控制依赖范围

## 常用依赖
- Spring Boot相关依赖
- ElasticSearch客户端
- Feign客户端用于服务间调用
- Hutool工具集
- FastJSON用于JSON处理
- EasyPOI用于Excel处理

## 插件配置
```xml
<plugins>
    <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
    </plugin>
    <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
    </plugin>
</plugins>
```

