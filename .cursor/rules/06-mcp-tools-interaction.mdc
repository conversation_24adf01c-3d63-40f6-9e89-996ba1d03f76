---
description: 
globs: 
alwaysApply: true
---
# MCP 工具交互规范

## MCP mcp-feedback-enhanced 使用规则
1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP mcp-feedback-enhanced。
2. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈内容调整行为。
3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP mcp-feedback-enhanced，流程才算结束。
4. 除非收到结束指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。
5. 完成任务前，必须使用 MCP mcp-feedback-enhanced 工具向用户询问反馈。

## 工具调用示例
```javascript
mcp_mcp-feedback-enhanced_interactive_feedback({
  project_directory: ".",
  summary: "我已完成了您请求的任务",
  timeout: 600
})
```

## 反馈处理流程
1. 发送初始任务完成反馈
2. 等待用户响应
3. 根据用户反馈调整实现
4. 再次请求反馈直到用户满意

## 注意事项
- 确保每次调用后等待用户反馈
- 反馈超时默认为600秒（10分钟）
- 根据用户反馈及时调整代码实现
- 保持与用户的持续交互，直到明确结束

## MCP shrimp-task-manager 使用规则

> 💡 根据您的需求选择适当的模式：
> * 规划任务时使用 **TaskPlanner** 模式
> * 执行任务时使用 **TaskExecutor** 模式

### Cursor IDE 配置
可以通过 Cursor 设置 => 功能 => 自定义模式，配置以下两种模式：

#### TaskPlanner 模式
```
您是专业任务规划专家。您必须与用户交互，分析他们的需求，并收集项目相关信息。最后，您必须使用"plan_task"创建任务。当任务创建完成后，您必须总结任务并告知用户使用"TaskExecutor"模式执行任务。
您必须专注于任务规划。不要使用"execute_task"执行任务。
严重警告：您是任务规划专家，不能直接修改程序代码，您只能规划任务，不能直接修改程序代码，您只能规划任务。
```

#### TaskExecutor 模式
```
您是专业的任务执行专家。当用户指定要执行的任务时，使用"execute_task"执行任务。
如果未指定任务，请使用"list_tasks"查找未执行的任务并执行它们。
执行完成后，必须提供摘要以通知用户结论。
您一次只能执行一个任务，当一个任务完成后，除非用户明确告诉您，否则禁止执行下一个任务。
如果用户请求"连续模式"，所有任务将按顺序执行。
```


### 与其他工具配合使用
如果您的工具不支持自定义模式，您可以：
* 在不同阶段手动粘贴适当的提示
* 或直接使用简单命令，如 `请规划以下任务：......` 或 `请开始执行任务...`

### 可用工具概览
配置后，您可以使用以下工具：

| 类别                | 工具名称              | 描述                              |
|-------------------|---------------------|----------------------------------|
| **任务规划**        | plan_task           | 开始规划任务                       |
| **任务分析**        | analyze_task        | 深入分析任务需求                    |
|                   | process_thought     | 复杂问题的逐步推理                   |
| **方案评估**        | reflect_task        | 反思和改进解决方案概念               |
| **研究与调查**      | research_mode       | 进入系统化技术研究模式               |
| **项目管理**        | init_project_rules  | 初始化或更新项目标准和规则            |
| **任务管理**        | split_tasks         | 将任务拆分为子任务                   |
|                   | list_tasks          | 显示所有任务和状态                   |
|                   | query_task          | 搜索和列出任务                      |
|                   | get_task_detail     | 显示完整的任务详情                   |
|                   | delete_task         | 删除未完成的任务                     |
| **任务执行**        | execute_task        | 执行特定任务                        |
|                   | verify_task         | 验证任务完成情况                     |

### 模式切换原则
1. 在规划阶段使用TaskPlanner模式，专注于需求分析和任务规划
2. 规划完成后，切换到TaskExecutor模式执行任务
3. 一个任务完成后，需明确用户指令再执行下一个任务
4. 执行任务过程中保持与用户的交互反馈
5. 每个任务完成后提供详细摘要

## MCP context7 使用规则

### 功能简介
Context7 是一个MCP服务，提供实时、最新的代码库文档，解决AI助手生成过时或错误代码的问题。通过将最新文档直接注入到AI对话上下文中，确保开发中获取准确的API信息和代码示例。


### 使用方法
在编写代码时，只需在提示中添加`use context7`，即可让AI自动获取最新的库文档：

**示例用法：**
- "如何使用Next.js的app router创建API路由？use context7"
- "使用React Query实现数据缓存和预加载，use context7"
- "为Tailwind按钮组件添加动画效果，use context7"

### 可用工具
Context7 MCP提供以下工具：

1. **resolve-library-id**：将一般库名解析为Context7兼容的库ID
   - 参数：`libraryName`（必填）- 要搜索的库名称

2. **get-library-docs**：使用Context7兼容的库ID获取文档
   - 参数：
     - `context7CompatibleLibraryID`（必填）- 精确的Context7兼容库ID（如`/mongodb/docs`）
     - `topic`（可选）- 将文档重点放在特定主题上（如"routing"、"hooks"）
     - `tokens`（可选，默认10000）- 返回的最大令牌数

### 使用建议
1. 在提示中明确指出库的特定版本，如"使用Next.js 13的新特性"
2. 具体说明需要的功能，如"如何在Next.js中实现API路由"
3. 提示应简短明了，专注于单一主题
4. 使用`topic`参数可获取更精确的文档内容

### 优势特点
- 获取最新文档，避免AI生成过时代码
- 减少API幻觉，提高代码可靠性
- 支持各种主流和小众库的实时文档
- 集成简单，使用方便



