---
description: 
globs: 
alwaysApply: true
---
# 清洁代码指南

## 常量优于魔法数字
- 用命名常量替代硬编码值
- 使用描述性常量名解释值的用途
- 将常量保持在文件顶部或专门的常量文件中

## 有意义的命名
- 变量、函数和类应该揭示其目的
- 名称应该解释为什么存在以及如何使用
- 避免使用缩写，除非它们被普遍理解

## 智能注释
- 不要注释代码做了什么 - 使代码自文档化
- 使用注释解释为什么以某种方式完成某事
- 记录API、复杂算法和非显而易见的副作用

## 单一责任
- 每个函数应该只做一件事
- 函数应该小而集中
- 如果函数需要注释来解释它做什么，它应该被拆分

## DRY（不要重复自己）
- 将重复代码提取到可重用函数中
- 通过适当的抽象共享通用逻辑
- 维护单一真实来源

## 清晰结构
- 保持相关代码在一起
- 以逻辑层次组织代码
- 使用一致的文件和文件夹命名约定

## 封装
- 隐藏实现细节
- 暴露清晰的接口
- 将嵌套条件移入命名良好的函数中

## 代码质量维护
- 持续重构
- 尽早修复技术债务
- 让代码比您发现时更清洁

## 测试
- 在修复错误之前编写测试
- 保持测试可读和可维护
- 测试边缘情况和错误条件

## 版本控制
- 编写清晰的提交消息
- 进行小而集中的提交
- 使用有意义的分支名称

