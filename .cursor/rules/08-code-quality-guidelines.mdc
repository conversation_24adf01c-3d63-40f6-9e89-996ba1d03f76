---
description: 
globs: 修改,调整,优化,增加,能否
alwaysApply: false
---
# 代码修改准则

## 信息验证
- 在呈现信息前始终进行验证
- 不在没有明确证据的情况下做出假设或推测
- 保持信息的准确性和可靠性

## 文件修改原则
- 逐个文件进行修改
- 每次修改后提供机会检查潜在错误
- 保持修改的可追踪性

## 沟通规范
- 避免不必要的歉意表达
- 不在注释或文档中提供理解反馈
- 保持清晰、专业的沟通风格

## 代码保留原则
- 不建议进行空白更改
- 不编造未明确要求的更改
- 不删除无关的代码或功能
- 注意保留现有结构

## 修改提交规范
- 为同一文件提供单一块编辑
- 避免多步骤指令或对同一文件的分散解释
- 确保修改的集中性和完整性

## 实施验证
- 不要求验证在提供的上下文中可见的实现
- 避免在不需要实际修改时建议更新或更改文件
- 保持代码审查的高效性

## 文档引用
- 总是提供真实文件的链接，而非临时文件
- 确保引用的一致性和可跟踪性

## 实现讨论
- 除非特别要求，否则不展示或讨论当前实现
- 保持讨论的针对性和相关性

