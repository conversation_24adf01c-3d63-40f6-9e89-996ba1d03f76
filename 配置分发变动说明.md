# 配置分发关系表改动后的完整系统流程

## 系统架构概览

```mermaid
graph TB
    A[Web管理端] --> B[后端API]
    C[Android客户端] --> B
    B --> D[b_config_distribution 分发关系表]
    B --> E[b_log_config 配置表]
    B --> F[b_device_info 设备信息表]
    
    D -.-> E
    D -.-> F
```

## 1. 配置分配流程（Web端 → 后端）

### 1.1 Web端操作流程

```javascript
// 1. 管理员选择配置和目标
const assignConfig = async () => {
  const request = {
    configSource: "123",           // 配置ID
    sourceType: "CONFIG_ID",       // 配置来源类型
    targets: [
      {
        targetType: "DEVICE",      // 目标类型
        targetId: "device001",     // 设备ID
        targetName: "测试设备1"     // 设备名称
      },
      {
        targetType: "USER",
        targetId: "user001", 
        targetName: "张三"
      }
    ],
    overrideExisting: false        // 是否覆盖已有分配
  };
  
  // 2. 调用批量分配接口
  const response = await fetch('/logcontrol/config/assign-batch', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(request)
  });
  
  return response.json();
};
```

### 1.2 后端处理流程

```java
@PostMapping("/assign-batch")
public RestResponse<BatchAssignResult> batchAssignConfig(@RequestBody BatchAssignRequest request) {
    // 1. 验证配置是否存在
    Long configId = Long.parseLong(request.getConfigSource());
    LogConfigDto config = logConfigService.getConfigById(configId);
    if (config == null) {
        return RestResponse.error("配置不存在");
    }

    // 2. 批量创建分发关系（不再创建专属配置）
    BatchAssignResult result = new BatchAssignResult();
    for (BatchAssignRequest.AssignTarget target : request.getTargets()) {
        try {
            // 创建分发关系记录
            ConfigDistribution distribution = new ConfigDistribution();
            distribution.setConfigId(configId);
            distribution.setTargetType(target.getTargetType());
            distribution.setTargetId(target.getTargetId());
            distribution.setTargetName(target.getTargetName());
            distribution.setPriority(100);
            distribution.setAssignTime(LocalDateTime.now());
            
            configDistributionRepository.save(distribution);
            result.incrementSuccess();
            
        } catch (Exception e) {
            result.incrementFailed();
        }
    }
    
    return RestResponse.ok(result);
}
```

### 1.3 数据库变化

```sql
-- 在 b_config_distribution 表中插入分发关系记录
INSERT INTO b_config_distribution (
    config_id, target_type, target_id, target_name, 
    priority, assign_time, is_active
) VALUES 
(123, 'DEVICE', 'device001', '测试设备1', 100, NOW(), 1),
(123, 'USER', 'user001', '张三', 100, NOW(), 1);

-- 注意：不再在 b_log_config 表中创建 device_xxx, user_xxx 配置
```

## 2. 配置获取流程（Android端 → 后端）

### 2.1 Android端请求（完全不变）

```kotlin
// Android端代码完全不需要修改
class LogConfigManager {
    suspend fun fetchConfig(): LogConfigResponse? {
        val userId = UserManager.getCurrentUserId()
        val deviceId = DeviceUtils.getDeviceId(context)
        val appVersion = BuildConfig.VERSION_NAME
        
        // 现有接口调用方式完全不变
        val response = logConfigApi.getConfig(userId, deviceId, appVersion)
        
        if (response.isSuccessful && response.body() != null) {
            val config = response.body()!!
            
            // 现有的配置处理逻辑完全不变
            if (hasConfigChanged(config)) {
                applyNewConfig(config)
            }
            
            return config
        }
        
        return null
    }
}
```

### 2.2 后端处理流程（核心变化）

```java
@GetMapping("/get")
public RestResponse<LogConfigDto> getLogConfig(
        @RequestHeader(value = "X-Device-Id", required = false) String deviceId,
        @RequestHeader(value = "X-User-Id", required = false) String userId,
        @RequestHeader(value = "X-App-Version", required = false) String appVersion) {

    try {
        // 1. 优先查询分发关系表中的用户配置
        LogConfigDto config = null;
        if (StringUtils.hasText(userId)) {
            config = getConfigFromDistribution("USER", userId);
        }
        
        // 2. 如果没有用户配置，查询设备配置
        if (config == null && StringUtils.hasText(deviceId)) {
            config = getConfigFromDistribution("DEVICE", deviceId);
        }
        
        // 3. 如果都没有，返回默认配置
        if (config == null) {
            config = logConfigService.getActiveConfig();
        }
        
        // 4. 异步更新设备配置信息（现有逻辑）
        if (StringUtils.hasText(deviceId)) {
            updateDeviceConfigAsync(deviceId, config);
        }
        
        return RestResponse.ok(config);
        
    } catch (Exception e) {
        log.error("获取日志配置失败", e);
        return RestResponse.ok(logConfigService.getActiveConfig());
    }
}

/**
 * 从分发关系表获取配置（新增方法）
 */
private LogConfigDto getConfigFromDistribution(String targetType, String targetId) {
    // 查询分发关系，按优先级排序
    List<ConfigDistribution> distributions = configDistributionRepository
        .findActiveByTarget(targetType, targetId);
    
    if (!distributions.isEmpty()) {
        // 获取最高优先级的配置
        ConfigDistribution topDistribution = distributions.get(0);
        return logConfigService.getConfigById(topDistribution.getConfigId());
    }
    
    return null;
}
```

### 2.3 数据库查询逻辑

```sql
-- 后端查询分发关系的SQL
SELECT cd.*, lc.* 
FROM b_config_distribution cd
LEFT JOIN b_log_config lc ON cd.config_id = lc.id
WHERE cd.target_type = 'DEVICE' 
  AND cd.target_id = 'device001'
  AND cd.is_active = 1 
  AND cd.deleted = 0
  AND lc.deleted = 0
ORDER BY cd.priority ASC, cd.assign_time DESC
LIMIT 1;
```

## 3. 配置分发状态跟踪流程

### 3.1 状态计算逻辑（后端自动）

```java
/**
 * 获取配置分发状态（Web端查询时调用）
 */
public List<ConfigAssignmentDto> getConfigAssignments(String targetType, String keyword) {
    // 关联查询分发关系、配置信息、设备信息
    String sql = """
        SELECT 
            cd.id as distributionId,
            cd.config_id as configId,
            cd.target_type as targetType,
            cd.target_id as targetId,
            cd.target_name as targetName,
            cd.assign_time as assignTime,
            cd.priority,
            lc.config_version as assignedVersion,
            di.current_config_version as currentVersion,
            CASE 
                WHEN di.current_config_version = lc.config_version THEN 'APPLIED'
                WHEN di.current_config_version IS NULL THEN 'PENDING'
                ELSE 'ASSIGNED'
            END as distributionStatus
        FROM b_config_distribution cd
        LEFT JOIN b_log_config lc ON cd.config_id = lc.id
        LEFT JOIN b_device_info di ON cd.target_id = di.device_id AND cd.target_type = 'DEVICE'
        WHERE cd.deleted = 0
        ORDER BY cd.assign_time DESC
    """;
    
    return jdbcTemplate.query(sql, new ConfigAssignmentRowMapper());
}
```

### 3.2 设备信息更新（Android端现有逻辑）

```kotlin
// Android端现有的设备信息上报逻辑（无需修改）
class DeviceInfoManager {
    suspend fun uploadDeviceInfo() {
        val configManager = LogConfigManager.getInstance(context)
        
        val deviceInfo = DeviceInfoRequest(
            deviceId = DeviceUtils.getDeviceId(context),
            // ... 其他现有字段
            
            // 关键：现有的配置版本字段（无需修改）
            currentConfigVersion = configManager.getCurrentVersion(),
            currentConfigDetails = buildConfigDetailsJson(configManager.getCurrentConfig())
        )
        
        // 现有的上报接口调用（无需修改）
        val response = deviceApi.uploadDeviceInfo(deviceInfo)
    }
}
```

## 4. 完整的配置更新流程时序图

```mermaid
sequenceDiagram
    participant W as Web管理端
    participant B as 后端API
    participant DB as 数据库
    participant A as Android客户端

    Note over W,A: 1. 配置分配阶段
    W->>B: POST /assign-batch (配置分配)
    B->>DB: INSERT INTO b_config_distribution
    B->>W: 返回分配结果

    Note over W,A: 2. 配置获取阶段
    A->>B: GET /config/get (现有接口)
    B->>DB: 查询分发关系表
    B->>DB: 获取对应配置
    B->>A: 返回配置(格式不变)
    A->>A: 应用配置(现有逻辑)

    Note over W,A: 3. 状态同步阶段
    A->>B: POST /device/info (现有上报)
    B->>DB: 更新设备当前配置版本
    
    Note over W,A: 4. 状态查询阶段
    W->>B: GET /assignments (查询分发状态)
    B->>DB: 关联查询计算状态
    B->>W: 返回分发状态统计
```

## 5. 关键数据流转

### 5.1 配置分配数据流

```
Web端选择配置 → 后端创建分发关系 → 数据库存储关系记录
```

### 5.2 配置获取数据流

```
Android请求配置 → 后端查询分发关系 → 返回对应配置 → Android应用配置
```

### 5.3 状态跟踪数据流

```
Android上报设备信息 → 后端更新配置版本 → Web端查询时计算状态
```

## 6. 各端改动总结

### 6.1 后端改动（核心）
- ✅ 新增 `b_config_distribution` 分发关系表
- ✅ 修改配置获取逻辑：从分发关系表查询配置
- ✅ 修改配置分配逻辑：创建分发关系而非专属配置
- ✅ 新增分发状态计算逻辑：通过版本比较实时计算
- ✅ 保持所有接口格式完全兼容

### 6.2 Web端改动（适配）
- ✅ 适配分发查询接口的新数据结构
- ✅ 增加分发状态的可视化展示
- ✅ 优化批量分配的用户体验
- ✅ 所有现有接口调用方式保持不变

### 6.3 Android端改动（零修改）
- ✅ 完全无需修改任何代码
- ✅ 所有现有接口调用保持不变
- ✅ 所有现有数据结构保持不变
- ✅ 所有现有业务逻辑保持不变
- ✅ 自动享受新的配置分发功能

## 7. 核心优势

1. **真正的一对多分发**：一个配置可以分发给多个用户/设备
2. **精确的状态跟踪**：通过版本比较实现准确的分发状态监控
3. **最小化改动**：Android端零修改，Web端适配性修改
4. **完全向后兼容**：现有功能完全不受影响
5. **数据一致性**：避免了配置冗余和状态不一致问题

## 8. 状态定义说明

### 8.1 分发状态枚举

- **PENDING**：已分配但设备从未获取过配置（设备当前版本为空）
- **ASSIGNED**：已分配且设备需要更新（设备当前版本 ≠ 分配版本）
- **APPLIED**：已分配且设备已应用（设备当前版本 = 分配版本）

### 8.2 状态计算逻辑

```sql
CASE
    WHEN di.current_config_version = lc.config_version THEN 'APPLIED'
    WHEN di.current_config_version IS NULL THEN 'PENDING'
    ELSE 'ASSIGNED'
END as distributionStatus
```

## 9. 数据库表结构

### 9.1 新增分发关系表

```sql
CREATE TABLE IF NOT EXISTS `b_config_distribution` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID，关联b_log_config.id',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型：USER-用户，DEVICE-设备',
  `target_id` varchar(100) NOT NULL COMMENT '目标ID（用户ID或设备ID）',
  `target_name` varchar(100) DEFAULT NULL COMMENT '目标名称（用于显示）',
  `assign_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_by` varchar(64) DEFAULT 'system' COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_target` (`config_id`, `target_type`, `target_id`, `deleted`),
  KEY `idx_target_type_id` (`target_type`, `target_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_config_distribution_config` FOREIGN KEY (`config_id`) REFERENCES `b_log_config` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置分发关系表';
```

### 9.2 现有表保持不变

- `b_log_config` 表：配置信息表，结构不变
- `b_device_info` 表：设备信息表，结构不变，利用现有的 `current_config_version` 字段

## 10. 实施风险评估

### 10.1 风险等级：低

- **Android端**：零风险（无任何修改）
- **Web端**：低风险（适配性修改，现有功能不受影响）
- **后端**：中风险（核心逻辑修改，但保持接口兼容）

### 10.2 回滚方案

1. **数据库回滚**：删除 `b_config_distribution` 表
2. **代码回滚**：恢复原有的配置获取和分配逻辑
3. **数据迁移**：如需要，可将分发关系重新转换为专属配置

### 10.3 测试重点

1. **兼容性测试**：确保Android端完全无感知
2. **功能测试**：验证配置分发的准确性
3. **性能测试**：关注新增查询的性能影响
4. **数据一致性测试**：验证状态计算的准确性

## 11. 总结

这个方案通过巧妙的设计实现了功能增强与兼容性的完美平衡：

- **后端**：承担了主要的改动，实现了真正的配置分发管理
- **Web端**：进行适配性修改，提供更丰富的管理功能
- **Android端**：完全无需修改，自动享受新功能

特别是Android端的零修改设计，大大降低了实施风险和成本，是一个理想的升级方案。
