# 配置分发关系表 - 后端执行方案

## 概述

本方案通过引入配置分发关系表(`b_config_distribution`)，解决当前基于命名规则的配置分发局限性，实现真正的一对多配置分发管理。

## 执行步骤

### 第一阶段：数据库结构调整

#### 1.1 创建配置分发关系表

**文件：** `src/main/resources/db/migration/V5.0.8.5__create_config_distribution_table.sql`

```sql
-- 配置分发关系表（简化版本）
CREATE TABLE IF NOT EXISTS `b_config_distribution` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID，关联b_log_config.id',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型：USER-用户，DEVICE-设备',
  `target_id` varchar(100) NOT NULL COMMENT '目标ID（用户ID或设备ID）',
  `target_name` varchar(100) DEFAULT NULL COMMENT '目标名称（用于显示）',
  `priority` int DEFAULT 100 COMMENT '优先级（数字越小优先级越高）',
  `assign_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_by` varchar(64) DEFAULT 'system' COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_target` (`config_id`, `target_type`, `target_id`, `deleted`),
  KEY `idx_target_type_id` (`target_type`, `target_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_priority` (`priority`),
  CONSTRAINT `fk_config_distribution_config` FOREIGN KEY (`config_id`) REFERENCES `b_log_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置分发关系表';

-- 注意：不需要 distribution_status, last_check_time, apply_time 字段
-- 分发状态通过比较 b_device_info.current_config_version 与 b_log_config.config_version 实时计算
```

#### 1.2 数据迁移脚本

**文件：** `src/main/resources/db/migration/V5.0.8.6__migrate_existing_config_assignments.sql`

```sql
-- 迁移现有的用户和设备专属配置到分发关系表
INSERT INTO `b_config_distribution` (
  `config_id`, `target_type`, `target_id`, `target_name`,
  `assign_time`, `is_active`
)
SELECT
  lc.id as config_id,
  CASE
    WHEN lc.config_name LIKE 'user_%' THEN 'USER'
    WHEN lc.config_name LIKE 'device_%' THEN 'DEVICE'
  END as target_type,
  CASE
    WHEN lc.config_name LIKE 'user_%' THEN SUBSTRING(lc.config_name, 6)
    WHEN lc.config_name LIKE 'device_%' THEN SUBSTRING(lc.config_name, 8)
  END as target_id,
  CASE
    WHEN lc.config_name LIKE 'user_%' THEN CONCAT('用户-', SUBSTRING(lc.config_name, 6))
    WHEN lc.config_name LIKE 'device_%' THEN CONCAT('设备-', SUBSTRING(lc.config_name, 8))
  END as target_name,
  lc.create_at as assign_time,
  lc.is_active
FROM `b_log_config` lc
WHERE (lc.config_name LIKE 'user_%' OR lc.config_name LIKE 'device_%')
  AND lc.deleted = 0;

-- 注意：不再设置 distribution_status，状态将通过版本号比较实时计算
```

### 第二阶段：实体类和DTO创建

#### 2.1 创建ConfigDistribution实体类

**文件：** `src/main/java/com/hightop/benyin/logcontrol/domain/entity/ConfigDistribution.java`

```java
package com.hightop.benyin.logcontrol.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("b_config_distribution")
public class ConfigDistribution {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("config_id")
    private Long configId;

    @TableField("target_type")
    private String targetType;

    @TableField("target_id")
    private String targetId;

    @TableField("target_name")
    private String targetName;

    @TableField("priority")
    private Integer priority;

    @TableField("assign_time")
    private LocalDateTime assignTime;

    @TableField("is_active")
    private Boolean isActive;

    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    @TableField("created_by")
    private String createdBy;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    // 以下字段用于查询时关联获取，不对应数据库字段
    @TableField(exist = false)
    private String assignedVersion;  // 分配的配置版本

    @TableField(exist = false)
    private String currentVersion;   // 设备当前版本

    @TableField(exist = false)
    private String distributionStatus; // 计算得出的分发状态
}
```

#### 2.2 创建相关DTO

**文件：** `src/main/java/com/hightop/benyin/logcontrol/dto/ConfigUpdateInfo.java`

```java
package com.hightop.benyin.logcontrol.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@ApiModel("配置更新信息")
public class ConfigUpdateInfo {

    @ApiModelProperty("是否有更新")
    private Boolean hasUpdate = false;

    @ApiModelProperty("最新版本")
    private String latestVersion;

    @ApiModelProperty("当前版本")
    private String currentVersion;

    @ApiModelProperty("配置来源")
    private String configSource;

    @ApiModelProperty("分配时间")
    private LocalDateTime assignTime;
}
```

### 第三阶段：Repository层实现

#### 3.1 创建ConfigDistributionMapper

**文件：** `src/main/java/com/hightop/benyin/logcontrol/infrastructure/mapper/ConfigDistributionMapper.java`

```java
package com.hightop.benyin.logcontrol.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hightop.benyin.logcontrol.domain.entity.ConfigDistribution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ConfigDistributionMapper extends BaseMapper<ConfigDistribution> {
    
    /**
     * 根据目标类型和ID查找激活的分发关系（带状态计算）
     */
    @Select("SELECT cd.*, lc.config_version as assignedVersion, di.current_config_version as currentVersion, " +
            "CASE " +
            "  WHEN di.current_config_version = lc.config_version THEN 'APPLIED' " +
            "  WHEN di.current_config_version IS NULL THEN 'PENDING' " +
            "  ELSE 'ASSIGNED' " +
            "END as distributionStatus " +
            "FROM b_config_distribution cd " +
            "LEFT JOIN b_log_config lc ON cd.config_id = lc.id " +
            "LEFT JOIN b_device_info di ON cd.target_id = di.device_id AND cd.target_type = 'DEVICE' " +
            "WHERE cd.target_type = #{targetType} AND cd.target_id = #{targetId} " +
            "AND cd.is_active = 1 AND cd.deleted = 0 " +
            "ORDER BY cd.priority ASC, cd.assign_time DESC")
    List<ConfigDistribution> findActiveByTarget(
        @Param("targetType") String targetType,
        @Param("targetId") String targetId
    );

    /**
     * 查找所有分发关系（带状态计算）
     */
    @Select("SELECT cd.*, lc.config_version as assignedVersion, di.current_config_version as currentVersion, " +
            "CASE " +
            "  WHEN di.current_config_version = lc.config_version THEN 'APPLIED' " +
            "  WHEN di.current_config_version IS NULL THEN 'PENDING' " +
            "  ELSE 'ASSIGNED' " +
            "END as distributionStatus " +
            "FROM b_config_distribution cd " +
            "LEFT JOIN b_log_config lc ON cd.config_id = lc.id " +
            "LEFT JOIN b_device_info di ON cd.target_id = di.device_id AND cd.target_type = 'DEVICE' " +
            "WHERE cd.deleted = 0 " +
            "${targetTypeCondition} ${keywordCondition} " +
            "ORDER BY cd.assign_time DESC")
    List<ConfigDistribution> findDistributionsWithStatus(
        @Param("targetType") String targetType,
        @Param("keyword") String keyword
    );

    /**
     * 检查配置分发关系是否存在
     */
    boolean existsByConfigAndTarget(
        @Param("configId") Long configId,
        @Param("targetType") String targetType,
        @Param("targetId") String targetId
    );
}
```

#### 3.2 创建Repository实现

**文件：** `src/main/java/com/hightop/benyin/logcontrol/domain/repository/ConfigDistributionRepository.java`

```java
package com.hightop.benyin.logcontrol.domain.repository;

import com.hightop.benyin.logcontrol.domain.entity.ConfigDistribution;
import java.time.LocalDateTime;
import java.util.List;

public interface ConfigDistributionRepository {
    
    boolean save(ConfigDistribution distribution);
    
    boolean update(ConfigDistribution distribution);
    
    List<ConfigDistribution> findActiveByTarget(String targetType, String targetId);
    
    boolean existsByConfigAndTarget(Long configId, String targetType, String targetId);
    
    int updateLastCheckTime(Long id, LocalDateTime checkTime);
    
    List<ConfigDistribution> findByTargetTypeAndDeleted(String targetType, Boolean deleted);
    
    List<ConfigDistribution> findByDeleted(Boolean deleted);
}
```

### 第四阶段：服务层重构

#### 4.1 创建ConfigDistributionService

**文件：** `src/main/java/com/hightop/benyin/logcontrol/application/service/ConfigDistributionService.java`

```java
package com.hightop.benyin.logcontrol.application.service;

import com.hightop.benyin.logcontrol.domain.entity.ConfigDistribution;
import com.hightop.benyin.logcontrol.domain.repository.ConfigDistributionRepository;
import com.hightop.benyin.logcontrol.dto.BatchAssignRequest;
import com.hightop.benyin.logcontrol.dto.BatchAssignResult;
import com.hightop.benyin.logcontrol.dto.ConfigAssignmentDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ConfigDistributionService {

    @Autowired
    private ConfigDistributionRepository configDistributionRepository;

    /**
     * 根据目标类型和ID获取激活的分发关系
     */
    public List<ConfigDistribution> getActiveDistributionsByTarget(String targetType, String targetId) {
        return configDistributionRepository.findActiveByTarget(targetType, targetId);
    }

    /**
     * 保存分发关系
     */
    @Transactional
    public boolean saveDistribution(ConfigDistribution distribution) {
        try {
            return configDistributionRepository.save(distribution);
        } catch (Exception e) {
            log.error("保存配置分发关系失败", e);
            return false;
        }
    }

    /**
     * 批量创建分发关系
     */
    @Transactional
    public BatchAssignResult batchCreateDistributions(Long configId, List<BatchAssignRequest.AssignTarget> targets, Boolean overrideExisting) {
        BatchAssignResult result = new BatchAssignResult();
        result.setTotal(targets.size());
        result.setSuccess(0);
        result.setFailed(0);
        result.setDetails(new ArrayList<>());

        for (BatchAssignRequest.AssignTarget target : targets) {
            BatchAssignResult.AssignResultDetail detail = new BatchAssignResult.AssignResultDetail();
            detail.setTargetType(target.getTargetType());
            detail.setTargetId(target.getTargetId());
            detail.setTargetName(target.getTargetName());

            try {
                // 检查是否已存在
                boolean exists = configDistributionRepository.existsByConfigAndTarget(
                    configId, target.getTargetType(), target.getTargetId());

                if (exists && !overrideExisting) {
                    detail.setSuccess(false);
                    detail.setMessage("配置已存在，跳过");
                    result.setFailed(result.getFailed() + 1);
                    continue;
                }

                // 创建分发关系（简化版本）
                ConfigDistribution distribution = new ConfigDistribution();
                distribution.setConfigId(configId);
                distribution.setTargetType(target.getTargetType());
                distribution.setTargetId(target.getTargetId());
                distribution.setTargetName(target.getTargetName());
                distribution.setPriority(100);
                distribution.setIsActive(true);
                distribution.setAssignTime(LocalDateTime.now());
                // 不设置 distributionStatus，状态通过版本比较实时计算

                configDistributionRepository.save(distribution);

                detail.setSuccess(true);
                detail.setMessage("分配成功");
                result.setSuccess(result.getSuccess() + 1);

            } catch (Exception e) {
                detail.setSuccess(false);
                detail.setMessage("分配失败: " + e.getMessage());
                result.setFailed(result.getFailed() + 1);
                log.error("创建配置分发关系失败", e);
            }

            result.getDetails().add(detail);
        }

        return result;
    }

    /**
     * 检查配置更新（基于版本比较）
     */
    public boolean hasConfigUpdate(String targetType, String targetId, String currentVersion) {
        try {
            List<ConfigDistribution> distributions = getActiveDistributionsByTarget(targetType, targetId);
            if (distributions.isEmpty()) {
                return false;
            }

            // 获取最高优先级的配置
            ConfigDistribution topDistribution = distributions.get(0);
            String assignedVersion = topDistribution.getAssignedVersion();

            // 比较版本号判断是否有更新
            return !Objects.equals(currentVersion, assignedVersion);
        } catch (Exception e) {
            log.error("检查配置更新失败", e);
            return false;
        }
    }

    /**
     * 获取配置分发统计
     */
    public List<ConfigAssignmentDto> getConfigAssignments(String targetType, String keyword) {
        List<ConfigDistribution> distributions;
        
        if (StringUtils.hasText(targetType)) {
            distributions = configDistributionRepository.findByTargetTypeAndDeleted(targetType, false);
        } else {
            distributions = configDistributionRepository.findByDeleted(false);
        }

        return distributions.stream()
            .filter(d -> !StringUtils.hasText(keyword) || d.getTargetId().contains(keyword))
            .map(this::convertToAssignmentDto)
            .collect(Collectors.toList());
    }

    private ConfigAssignmentDto convertToAssignmentDto(ConfigDistribution distribution) {
        ConfigAssignmentDto dto = new ConfigAssignmentDto();
        dto.setTargetType(distribution.getTargetType());
        dto.setTargetId(distribution.getTargetId());
        dto.setTargetName(distribution.getTargetName());
        dto.setConfigId(distribution.getConfigId());
        dto.setDistributionStatus(distribution.getDistributionStatus());
        dto.setAssignTime(distribution.getAssignTime());
        dto.setLastCheckTime(distribution.getLastCheckTime());
        dto.setApplyTime(distribution.getApplyTime());
        dto.setPriority(distribution.getPriority());
        return dto;
    }
}
```

### 第五阶段：控制器层重构

#### 5.1 修改LogConfigController

**文件：** `src/main/java/com/hightop/benyin/logcontrol/api/controller/LogConfigController.java`

需要修改的方法：

1. `getConfigWithPriority` - 改为优先查询分发关系表
2. `assignConfigToUser` - 改为创建分发关系
3. `assignConfigToDevice` - 改为创建分发关系
4. `batchAssignConfig` - 改为批量创建分发关系
5. 新增 `checkConfigUpdates` 接口

### 执行顺序

1. **第一阶段**：执行数据库脚本，创建新表和迁移数据
2. **第二阶段**：创建实体类和DTO
3. **第三阶段**：实现Repository层
4. **第四阶段**：实现服务层
5. **第五阶段**：重构控制器层
6. **测试验证**：确保新旧功能正常工作

### 注意事项

1. 数据迁移前请备份数据库
2. 分阶段部署，确保每个阶段功能正常
3. 保持向后兼容性，旧的配置获取逻辑仍需支持
4. 充分测试批量分配功能
5. 监控新表的查询性能

### 回滚方案

如果出现问题，可以：
1. 回滚代码到旧版本
2. 删除新创建的分发关系表
3. 恢复原有的配置分配逻辑

## 预期效果

1. 支持一个配置分发给多个目标
2. 完整的配置分发状态追踪
3. 更高效的配置查询性能
4. 更灵活的配置管理能力
