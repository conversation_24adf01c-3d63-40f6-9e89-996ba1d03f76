# 限流消息提示优化说明

## 修改概述

针对用户反馈的429错误消息不够明确的问题，对所有限流相关的错误消息进行了优化，增加了具体的等待时间信息。

## 主要改进

### 1. 动态时间显示
- **原来**：`"访问过于频繁，请稍后再试"`
- **现在**：`"访问过于频繁，请2分30秒后再试"`

### 2. 智能时间格式化
- 小于60秒：显示"X秒"
- 1-60分钟：显示"X分钟"或"X分Y秒"
- 超过1小时：显示"X小时"或"X小时Y分钟"

## 修改内容

### 1. 新增RateLimitResult类
创建了`RateLimitResult`类来封装限流结果和剩余时间信息：
- 包含当前计数、限制数量、剩余时间等详细信息
- 提供智能的时间格式化方法
- 支持动态消息生成

### 2. 升级RateLimitService
修改了`RateLimitService`的实现：
- Lua脚本返回计数和TTL信息
- 新增`checkRateLimit`方法返回详细结果
- 保持向后兼容的`isAllowed`方法

### 3. 优化RateLimitAspect
更新了限流切面：
- 使用新的`checkRateLimit`方法
- 生成包含剩余时间的动态消息
- 增强日志记录信息

### 4. 改进DDoS防护消息
优化了DDoS防护拦截器：
- IP限流消息包含具体限制数量和时间
- 临时封禁消息显示剩余封禁时间
- 区分不同类型的限流（分钟级、小时级）

### 5. 更新接口级消息
修改了所有使用@RateLimit注解的接口消息：

#### WebsiteConfigController
- `/public`: "配置访问过于频繁，请1分钟后再试"

#### WebsitePublicController
- `/homepage`: "首页访问过于频繁，请1分钟后再试"
- `/content/{type}`: "内容访问过于频繁，请1分钟后再试"
- `/images/{category}`: "图片访问过于频繁，请1分钟后再试"

#### WebsiteInquiryController
- `/submit`: "咨询提交过于频繁，请1小时后再试"

## 消息示例

### 接口级限流
```
原来：配置访问过于频繁，请稍后再试
现在：配置访问过于频繁，请45秒后再试
```

### IP级限流
```
原来：访问过于频繁，请稍后再试
现在：IP访问过于频繁，已达到每分钟300次限制，请1分钟后再试
```

### 临时封禁
```
原来：您的IP已被封禁，请联系管理员
现在：您的IP因访问过于频繁被临时封禁，请8分30秒后再试
```

### 黑名单
```
原来：您的IP已被封禁，请联系管理员
现在：您的IP已被加入黑名单，请联系管理员
```

## 技术实现

### 1. Lua脚本优化
```lua
-- 原来只返回计数
return newCount

-- 现在返回计数和TTL
return {newCount, ttl}
```

### 2. 时间格式化算法
```java
private String formatTime(long seconds) {
    if (seconds < 60) {
        return seconds + "秒";
    } else if (seconds < 3600) {
        long minutes = seconds / 60;
        long remainingSeconds = seconds % 60;
        return remainingSeconds == 0 ? 
            minutes + "分钟" : 
            minutes + "分" + remainingSeconds + "秒";
    } else {
        // 小时级处理
    }
}
```

### 3. 动态消息生成
```java
public String getFormattedMessage(String baseMessage) {
    String timeInfo = formatRemainingTime(remainingTimeSeconds);
    return baseMessage.replace("请稍后再试", "请" + timeInfo + "后再试");
}
```

## 用户体验改进

### 1. 明确的等待时间
用户现在可以清楚知道需要等待多长时间，而不是模糊的"稍后再试"。

### 2. 区分不同限流类型
- 接口级限流：通常1分钟
- IP级限流：分钟级或小时级
- 临时封禁：几分钟到几小时
- 黑名单：需要联系管理员

### 3. 智能时间显示
- 自动选择最合适的时间单位
- 避免显示过于精确的时间（如123秒显示为2分3秒）

## 兼容性说明

### 1. 向后兼容
- 保留了原有的`isAllowed`方法
- 现有代码无需修改即可工作
- 新功能是增量式的改进

### 2. 渐进式升级
- 可以逐步将现有代码迁移到新的API
- 支持混合使用新旧方法

## 监控和调试

### 1. 增强的日志记录
```
原来：请求被限流: key=config_public:ip:***********
现在：接口访问被限流: method=getPublicConfig, ip=***********, current=201, limit=200/1minutes, remainingTime=45s
```

### 2. 详细的错误信息
- 包含当前计数和限制数量
- 显示剩余时间
- 记录限流类型和原因

## 部署注意事项

### 1. Redis兼容性
- 需要Redis支持Lua脚本
- TTL命令需要正常工作

### 2. 时间同步
- 确保服务器时间准确
- Redis和应用服务器时间一致

### 3. 配置验证
- 重启后验证新消息是否正常显示
- 测试不同时间窗口的消息格式

## 测试建议

### 1. 功能测试
```bash
# 触发接口限流
for i in {1..201}; do curl "http://localhost:8080/website-config/public"; done

# 检查消息格式
curl "http://localhost:8080/website-config/public"
# 应该返回：{"code":429,"message":"配置访问过于频繁，请XX秒后再试"}
```

### 2. 时间准确性测试
- 验证显示的剩余时间与实际等待时间一致
- 测试不同时间窗口的格式化效果

### 3. 边界条件测试
- 测试TTL为0或负数的情况
- 测试Redis连接异常的降级处理
