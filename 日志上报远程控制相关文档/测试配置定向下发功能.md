# 测试配置定向下发功能

## 🧪 测试步骤

### 1. 启动应用
```bash
# 启动Spring Boot应用
mvn spring-boot:run
```

### 2. 测试配置模板接口
```bash
# 获取配置模板列表
curl -X GET "http://localhost:8080/logcontrol/config/templates" \
  -H "Content-Type: application/json"

# 预期响应：
{
  "code": 200,
  "message": "ok",
  "data": [
    {
      "templateName": "default",
      "displayName": "默认配置",
      "logLevel": "INFO",
      "enableLocationLog": true,
      "locationLogInterval": 3000,
      "logUploadInterval": 3600,
      "maxLogFiles": 5,
      "description": "适用于生产环境"
    },
    {
      "templateName": "debug",
      "displayName": "调试配置",
      "logLevel": "DEBUG",
      "enableLocationLog": true,
      "locationLogInterval": 1000,
      "logUploadInterval": 1800,
      "maxLogFiles": 10,
      "description": "适用于开发调试"
    }
  ]
}
```

### 3. 测试为用户分配配置
```bash
# 为用户101分配配置（假设配置ID为1）
curl -X POST "http://localhost:8080/logcontrol/config/assign-to-user?userId=101&configId=1" \
  -H "Content-Type: application/json"

# 预期响应：
{
  "code": 200,
  "message": "ok",
  "data": null
}
```

### 4. 测试为设备分配配置
```bash
# 为设备device123分配配置
curl -X POST "http://localhost:8080/logcontrol/config/assign-to-device?deviceId=device123&configId=1" \
  -H "Content-Type: application/json"

# 预期响应：
{
  "code": 200,
  "message": "ok",
  "data": null
}
```

### 5. 测试定向获取配置
```bash
# 用户101获取配置（应该返回专属配置）
curl -X GET "http://localhost:8080/logcontrol/config/get" \
  -H "X-User-Id: 101" \
  -H "X-Device-Id: device456" \
  -H "Content-Type: application/json"

# 设备device123获取配置（应该返回设备专属配置）
curl -X GET "http://localhost:8080/logcontrol/config/get" \
  -H "X-Device-Id: device123" \
  -H "Content-Type: application/json"

# 普通用户获取配置（应该返回默认配置）
curl -X GET "http://localhost:8080/logcontrol/config/get" \
  -H "X-User-Id: 999" \
  -H "X-Device-Id: device999" \
  -H "Content-Type: application/json"
```

### 6. 测试批量分配配置
```bash
# 批量分配配置
curl -X POST "http://localhost:8080/logcontrol/config/assign-batch" \
  -H "Content-Type: application/json" \
  -d '{
    "sourceType": "TEMPLATE",
    "configSource": "debug",
    "targets": [
      {
        "targetType": "USER",
        "targetId": "102",
        "targetName": "测试用户102"
      },
      {
        "targetType": "DEVICE",
        "targetId": "device456",
        "targetName": "测试设备456"
      }
    ],
    "overrideExisting": false
  }'

# 预期响应：
{
  "code": 200,
  "message": "ok",
  "data": {
    "total": 2,
    "success": 2,
    "failed": 0,
    "details": [
      {
        "targetType": "USER",
        "targetId": "102",
        "targetName": "测试用户102",
        "success": true,
        "message": "分配成功"
      },
      {
        "targetType": "DEVICE",
        "targetId": "device456",
        "targetName": "测试设备456",
        "success": true,
        "message": "分配成功"
      }
    ]
  }
}
```

### 7. 测试获取配置分配情况
```bash
# 获取所有配置分配情况
curl -X GET "http://localhost:8080/logcontrol/config/assignments" \
  -H "Content-Type: application/json"

# 按类型过滤
curl -X GET "http://localhost:8080/logcontrol/config/assignments?targetType=USER" \
  -H "Content-Type: application/json"

# 按关键字搜索
curl -X GET "http://localhost:8080/logcontrol/config/assignments?keyword=101" \
  -H "Content-Type: application/json"
```

### 8. 测试移除配置分配
```bash
# 移除用户101的配置分配
curl -X DELETE "http://localhost:8080/logcontrol/config/assignment/USER/101" \
  -H "Content-Type: application/json"

# 预期响应：
{
  "code": 200,
  "message": "ok",
  "data": null
}
```

## 🔍 验证数据库变化

### 查看创建的配置记录
```sql
-- 查看所有配置
SELECT id, config_name, config_version, log_level, is_active, create_at 
FROM b_log_config 
WHERE deleted = 0 
ORDER BY create_at DESC;

-- 应该看到类似以下记录：
-- user_101, user_102, device_device123, device_device456 等
```

### 验证配置优先级
```sql
-- 查看用户专属配置
SELECT * FROM b_log_config WHERE config_name = 'user_101' AND deleted = 0;

-- 查看设备专属配置  
SELECT * FROM b_log_config WHERE config_name = 'device_device123' AND deleted = 0;
```

## 📊 测试结果验证

### 成功标准
1. ✅ 配置模板接口返回预设模板
2. ✅ 用户/设备分配接口成功创建专属配置
3. ✅ 获取配置接口按优先级返回正确配置
4. ✅ 批量分配接口成功处理多个目标
5. ✅ 配置分配情况接口正确显示分配记录
6. ✅ 移除分配接口成功删除配置

### 错误处理验证
1. ✅ 不存在的配置ID返回错误信息
2. ✅ 重复分配时的处理逻辑
3. ✅ 无效参数时的错误响应

## 🚨 注意事项

1. **数据库连接**：确保数据库连接正常
2. **权限验证**：如果有权限控制，需要添加相应的token
3. **日志查看**：观察应用日志确认功能执行情况
4. **数据清理**：测试完成后清理测试数据

## 📝 测试报告模板

```
测试时间：2025-01-21
测试环境：本地开发环境
测试结果：

1. 配置模板接口：✅ 通过
2. 用户配置分配：✅ 通过  
3. 设备配置分配：✅ 通过
4. 定向配置获取：✅ 通过
5. 批量配置分配：✅ 通过
6. 配置分配查询：✅ 通过
7. 配置分配移除：✅ 通过

问题记录：
- 无

建议：
- 功能正常，可以进行下一步开发
```
