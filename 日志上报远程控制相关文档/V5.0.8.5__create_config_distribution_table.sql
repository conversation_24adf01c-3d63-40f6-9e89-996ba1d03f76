-- 配置分发关系表（简化版本）
CREATE TABLE IF NOT EXISTS `b_config_distribution` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID，关联b_log_config.id',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型：USER-用户，DEVICE-设备',
  `target_id` varchar(100) NOT NULL COMMENT '目标ID（用户ID或设备ID）',
  `target_name` varchar(100) DEFAULT NULL COMMENT '目标名称（用于显示）',
  `assign_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_by` varchar(64) DEFAULT 'system' COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_target` (`config_id`, `target_type`, `target_id`, `deleted`),
  KEY `idx_target_type_id` (`target_type`, `target_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_config_distribution_config` FOREIGN KEY (`config_id`) REFERENCES `b_log_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置分发关系表';

-- 注意：不需要 distribution_status, last_check_time, apply_time 字段
-- 分发状态通过比较 b_device_info.current_config_version 与 b_log_config.config_version 实时计算
