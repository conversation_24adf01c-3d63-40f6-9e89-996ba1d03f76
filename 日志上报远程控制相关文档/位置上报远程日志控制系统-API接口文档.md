# 位置上报远程日志控制系统 - API接口文档

## 1. 接口概述

本文档描述了位置上报远程日志控制系统的所有API接口，包括基础功能接口和新增的用户管理接口。

**基础URL**：`http://localhost:8080/logcontrol`  
**版本**：V1.0  
**更新时间**：2025-01-21

## 2. 认证说明

### 2.1 用户标识
- 通过请求头 `X-User-Id` 传递用户ID
- 通过请求头 `X-Device-Id` 传递设备ID（可选）
- 通过请求头 `X-App-Version` 传递应用版本（可选）

### 2.2 限流保护
- 大部分接口都有限流保护
- 限流基于IP地址和接口类型
- 超出限流返回429状态码

## 3. 日志配置接口

### 3.1 获取日志配置
```http
GET /config/get
```

**请求头**：
- `X-User-Id`：用户ID（可选）
- `X-Device-Id`：设备ID（可选）
- `X-App-Version`：应用版本（可选）

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "configName": "default",
    "configVersion": "1.0.0",
    "logLevel": "INFO",
    "locationLogInterval": 300000,
    "logUploadInterval": 3600000,
    "enabledLogTypes": "BUSINESS,CRASH,PERFORMANCE"
  }
}
```

### 3.2 其他配置接口
- `GET /config/by-name/{configName}` - 根据配置名称获取
- `GET /config/by-version/{configVersion}` - 根据版本获取
- `GET /config/list` - 获取配置列表
- `POST /config/update` - 更新配置
- `POST /config/activate/{id}` - 激活配置
- `DELETE /config/{id}` - 删除配置

## 4. 设备管理接口

### 4.1 上传设备信息
```http
POST /device/upload
```

**请求头**：
- `X-User-Id`：用户ID（可选）

**请求体**：
```json
{
  "deviceId": "device123",
  "userId": "user001",
  "userName": "测试用户",
  "brand": "Xiaomi",
  "model": "Mi 11",
  "osVersion": "Android 12",
  "appVersion": "1.0.0",
  "totalMemory": 8589934592,
  "isRooted": false
}
```

### 4.2 其他设备接口
- `GET /device/get/{deviceId}` - 获取设备信息
- `GET /device/get-by-id/{id}` - 根据ID获取设备信息
- `GET /device/list` - 获取设备列表
- `GET /device/count` - 获取设备总数
- `GET /device/stats/brand` - 品牌统计
- `GET /device/stats/model` - 型号统计
- `GET /device/stats/os-version` - 系统版本统计
- `GET /device/stats/root` - Root设备统计
- `DELETE /device/{id}` - 删除设备信息

## 5. 日志管理接口

### 5.1 批量上传日志
```http
POST /log/batch-upload
```

**请求体**：
```json
{
  "deviceId": "device123",
  "userId": "user001",
  "appVersion": "1.0.0",
  "logs": [
    {
      "logType": "BUSINESS",
      "level": "INFO",
      "timestamp": "2025-01-21T10:30:00",
      "tag": "MainActivity",
      "message": "用户登录成功",
      "extraData": "{\"userId\":\"user001\"}",
      "userId": "user001"
    }
  ]
}
```

### 5.2 其他日志接口
- `GET /log/list` - 查询日志列表
- `GET /log/list-by-type` - 根据类型查询日志
- `GET /log/all` - 获取所有日志
- `GET /log/count` - 获取日志总数
- `GET /log/stats/type` - 日志类型统计
- `GET /log/stats/level` - 日志级别统计
- `GET /log/unuploaded-count` - 获取未上传日志数量
- `POST /log/mark-uploaded` - 标记日志为已上传
- `DELETE /log/{id}` - 删除日志

## 6. 崩溃信息接口

### 6.1 上传崩溃信息
```http
POST /crash/upload
```

**请求体**：
```json
{
  "deviceId": "device123",
  "userId": "user001",
  "crashTime": 1642752600000,
  "exceptionType": "java.lang.NullPointerException",
  "exceptionMessage": "Attempt to invoke virtual method",
  "stackTrace": "at com.example.MainActivity.onCreate...",
  "appVersion": "1.0.0"
}
```

### 6.2 其他崩溃接口
- `POST /crash/batch-upload` - 批量上传崩溃信息
- `GET /crash/get/{id}` - 根据ID获取崩溃信息
- `GET /crash/list` - 查询崩溃信息列表
- `GET /crash/list-by-exception` - 根据异常类型查询
- `GET /crash/all` - 获取所有崩溃信息
- `GET /crash/recent` - 获取最近崩溃信息
- `GET /crash/count` - 获取崩溃总数
- `GET /crash/stats/exception-type` - 异常类型统计
- `GET /crash/stats/device` - 设备崩溃统计
- `GET /crash/stats/app-version` - 应用版本崩溃统计
- `GET /crash/unuploaded-count` - 获取未上传崩溃数量
- `POST /crash/mark-uploaded` - 标记崩溃信息为已上传
- `DELETE /crash/{id}` - 删除崩溃信息

## 7. 🆕 用户管理接口（新增）

### 7.1 注册用户
```http
POST /user/register
```

**请求体**：
```json
{
  "userId": "user001",
  "userName": "测试用户",
  "phone": "13800138001",
  "email": "<EMAIL>"
}
```

### 7.2 绑定用户设备
```http
POST /user/bind-device
```

**请求体**：
```json
{
  "userId": "user001",
  "deviceId": "device123",
  "userName": "测试用户",
  "deviceName": "我的手机",
  "relationType": "OWNER",
  "isPrimary": true
}
```

### 7.3 解绑用户设备
```http
DELETE /user/unbind-device?userId=user001&deviceId=device123
```

### 7.4 获取用户设备列表
```http
GET /user/devices/{userId}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "deviceId": "device123",
      "deviceName": "我的手机",
      "brand": "Xiaomi",
      "model": "Mi 11",
      "relationType": "OWNER",
      "isPrimary": true,
      "bindTime": "2025-01-21T10:00:00",
      "lastActiveTime": "2025-01-21T15:30:00",
      "status": "ACTIVE"
    }
  ]
}
```

### 7.5 获取用户日志统计
```http
GET /user/logs/{userId}?days=7
```

### 7.6 获取用户崩溃统计
```http
GET /user/crashes/{userId}?days=7
```

### 7.7 获取用户信息
```http
GET /user/info/{userId}
```

## 8. 统计分析接口

### 8.1 设备统计
```http
GET /analysis/device-stats
```

### 8.2 崩溃统计
```http
GET /analysis/crash-stats
```

### 8.3 日志统计
```http
GET /analysis/log-stats
```

### 8.4 综合统计
```http
GET /analysis/comprehensive-stats
```

## 9. 🆕 诊断工具接口（新增）

### 9.1 健康检查
```http
GET /diagnostic/health
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "status": "UP",
    "timestamp": 1642752600000,
    "module": "logcontrol",
    "beans": {
      "logConfigService": true,
      "logConfigRepository": true,
      "logControlConfig": true,
      "logControlRateLimitAspect": true
    }
  }
}
```

### 9.2 Bean状态检查
```http
GET /diagnostic/beans
```

### 9.3 连通性测试
```http
GET /test/ping
```

### 9.4 模块状态
```http
GET /test/status
```

## 10. 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式 |
| 404 | 接口不存在 | 检查接口路径 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 服务器内部错误 | 联系管理员 |

## 11. 使用示例

### 11.1 完整的用户设备日志流程
```bash
# 1. 注册用户
curl -X POST "http://localhost:8080/logcontrol/user/register" \
  -H "Content-Type: application/json" \
  -d '{"userId":"user001","userName":"测试用户"}'

# 2. 上传设备信息（包含用户ID）
curl -X POST "http://localhost:8080/logcontrol/device/upload" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: user001" \
  -d '{"deviceId":"device123","userId":"user001","brand":"Xiaomi"}'

# 3. 绑定用户设备
curl -X POST "http://localhost:8080/logcontrol/user/bind-device" \
  -H "Content-Type: application/json" \
  -d '{"userId":"user001","deviceId":"device123","relationType":"OWNER"}'

# 4. 上传日志（包含用户ID）
curl -X POST "http://localhost:8080/logcontrol/log/batch-upload" \
  -H "Content-Type: application/json" \
  -d '{"deviceId":"device123","userId":"user001","logs":[...]}'

# 5. 查看用户统计
curl -X GET "http://localhost:8080/logcontrol/user/logs/user001?days=7"
```

## 12. 注意事项

1. **用户ID传递**：建议通过请求头和请求体双重传递用户ID
2. **向后兼容**：所有用户ID相关参数都是可选的，保证向后兼容
3. **数据隔离**：用户只能访问自己的数据
4. **限流保护**：注意接口限流，避免频繁调用
5. **错误处理**：所有接口都返回统一的响应格式
