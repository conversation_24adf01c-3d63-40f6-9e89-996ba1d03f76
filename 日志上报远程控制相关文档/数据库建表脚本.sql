-- 位置上报远程日志控制系统 - 数据库建表脚本
-- 支持设备信息收集和崩溃日志管理的完整数据库结构

-- 1. 设备信息表
CREATE TABLE device_info (
    device_id VARCHAR(100) PRIMARY KEY COMMENT '设备唯一标识',
    brand VARCHAR(50) NOT NULL COMMENT '手机品牌',
    model VARCHAR(100) NOT NULL COMMENT '手机型号',
    os_type VARCHAR(20) NOT NULL COMMENT '操作系统类型(Android/HarmonyOS)',
    os_version VARCHAR(50) NOT NULL COMMENT '系统版本',
    app_version VARCHAR(20) NOT NULL COMMENT '应用版本',
    sdk_version INT NOT NULL COMMENT 'Android SDK版本',
    manufacturer VARCHAR(50) COMMENT '制造商',
    screen_resolution VARCHAR(20) COMMENT '屏幕分辨率',
    screen_density FLOAT COMMENT '屏幕密度',
    total_memory BIGINT COMMENT '总内存大小(字节)',
    available_storage BIGINT COMMENT '可用存储空间(字节)',
    cpu_abi VARCHAR(50) COMMENT 'CPU架构',
    is_rooted BOOLEAN DEFAULT FALSE COMMENT '是否Root',
    is_emulator BOOLEAN DEFAULT FALSE COMMENT '是否模拟器',
    network_type VARCHAR(20) COMMENT '网络类型',
    language VARCHAR(10) COMMENT '系统语言',
    time_zone VARCHAR(50) COMMENT '时区',
    first_collect_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '首次收集时间',
    last_update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    collect_count INT DEFAULT 1 COMMENT '收集次数',
    
    INDEX idx_brand_model (brand, model),
    INDEX idx_os_version (os_version),
    INDEX idx_performance (total_memory, sdk_version),
    INDEX idx_problem_device (is_rooted, is_emulator),
    INDEX idx_update_time (last_update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表';

-- 2. 崩溃信息表
CREATE TABLE crash_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '崩溃记录ID',
    device_id VARCHAR(100) NOT NULL COMMENT '设备ID',
    crash_time BIGINT NOT NULL COMMENT '崩溃时间戳',
    thread_name VARCHAR(100) COMMENT '崩溃线程名',
    exception_type VARCHAR(200) NOT NULL COMMENT '异常类型',
    exception_message TEXT COMMENT '异常消息',
    stack_trace LONGTEXT COMMENT '堆栈信息',
    app_state ENUM('FOREGROUND', 'BACKGROUND') DEFAULT 'FOREGROUND' COMMENT '应用状态',
    memory_usage BIGINT COMMENT '崩溃时内存使用量',
    available_memory BIGINT COMMENT '崩溃时可用内存',
    battery_level INT COMMENT '电池电量百分比',
    is_charging BOOLEAN COMMENT '是否在充电',
    network_status VARCHAR(20) COMMENT '网络状态',
    last_activity VARCHAR(100) COMMENT '最后访问的Activity',
    user_actions JSON COMMENT '用户操作序列',
    custom_data JSON COMMENT '自定义数据',
    is_uploaded BOOLEAN DEFAULT FALSE COMMENT '是否已上传',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (device_id) REFERENCES device_info(device_id) ON DELETE CASCADE,
    INDEX idx_device_crash_time (device_id, crash_time),
    INDEX idx_exception_type (exception_type),
    INDEX idx_crash_time (crash_time),
    INDEX idx_upload_status (is_uploaded),
    INDEX idx_app_state (app_state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='崩溃信息表';

-- 3. 日志配置表
CREATE TABLE log_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_name VARCHAR(50) NOT NULL COMMENT '配置名称',
    log_level VARCHAR(10) NOT NULL DEFAULT 'INFO' COMMENT '日志级别',
    enable_location_log BOOLEAN DEFAULT TRUE COMMENT '是否启用位置日志',
    location_log_interval BIGINT DEFAULT 300000 COMMENT '位置日志间隔(毫秒)',
    enable_performance_log BOOLEAN DEFAULT TRUE COMMENT '是否启用性能日志',
    enable_crash_log BOOLEAN DEFAULT TRUE COMMENT '是否启用崩溃日志',
    enable_business_log BOOLEAN DEFAULT TRUE COMMENT '是否启用业务日志',
    log_upload_interval BIGINT DEFAULT 3600000 COMMENT '日志上传间隔(毫秒)',
    max_log_file_size BIGINT DEFAULT 10485760 COMMENT '最大日志文件大小(字节)',
    max_log_files INT DEFAULT 5 COMMENT '最大日志文件数量',
    enable_remote_control BOOLEAN DEFAULT TRUE COMMENT '是否启用远程控制',
    config_version VARCHAR(20) NOT NULL COMMENT '配置版本',
    effective_time DATETIME COMMENT '生效时间',
    expiry_time DATETIME COMMENT '过期时间',
    target_devices JSON COMMENT '目标设备(品牌、型号筛选)',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_config_name_version (config_name, config_version),
    INDEX idx_active_config (is_active, effective_time, expiry_time),
    INDEX idx_config_version (config_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日志配置表';

-- 4. 日志条目表
CREATE TABLE log_entries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    log_type VARCHAR(20) NOT NULL COMMENT '日志类型',
    level VARCHAR(10) NOT NULL COMMENT '日志级别',
    timestamp DATETIME NOT NULL COMMENT '日志时间',
    tag VARCHAR(100) NOT NULL COMMENT '日志标签',
    message TEXT NOT NULL COMMENT '日志消息',
    extra_data JSON COMMENT '额外数据',
    device_id VARCHAR(100) COMMENT '设备ID',
    brand VARCHAR(50) COMMENT '设备品牌',
    model VARCHAR(100) COMMENT '设备型号',
    os_type VARCHAR(20) COMMENT '操作系统类型',
    os_version VARCHAR(50) COMMENT '系统版本',
    app_version VARCHAR(20) COMMENT '应用版本',
    sdk_version INT COMMENT 'SDK版本',
    crash_id BIGINT COMMENT '关联的崩溃ID',
    session_id VARCHAR(100) COMMENT '会话ID',
    is_uploaded BOOLEAN DEFAULT FALSE COMMENT '是否已上传',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (device_id) REFERENCES device_info(device_id) ON DELETE SET NULL,
    FOREIGN KEY (crash_id) REFERENCES crash_info(id) ON DELETE SET NULL,
    INDEX idx_log_type_level (log_type, level),
    INDEX idx_timestamp (timestamp),
    INDEX idx_device_info (brand, model, os_version),
    INDEX idx_session (session_id),
    INDEX idx_upload_status (is_uploaded),
    INDEX idx_tag (tag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日志条目表';

-- 5. 位置轨迹表(扩展现有表)
CREATE TABLE location_track (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '轨迹ID',
    engineer_id BIGINT NOT NULL COMMENT '工程师ID',
    device_id VARCHAR(100) COMMENT '设备ID',
    latitude DECIMAL(10,7) NOT NULL COMMENT '纬度',
    longitude DECIMAL(10,7) NOT NULL COMMENT '经度',
    accuracy FLOAT COMMENT '精度(米)',
    speed FLOAT COMMENT '速度(m/s)',
    bearing FLOAT COMMENT '方向角',
    altitude DOUBLE COMMENT '海拔高度',
    timestamp DATETIME NOT NULL COMMENT '位置时间',
    address VARCHAR(200) COMMENT '地址信息',
    is_uploaded BOOLEAN DEFAULT FALSE COMMENT '是否已上传',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (device_id) REFERENCES device_info(device_id) ON DELETE SET NULL,
    INDEX idx_engineer_time (engineer_id, timestamp),
    INDEX idx_device_time (device_id, timestamp),
    INDEX idx_location (latitude, longitude),
    INDEX idx_upload_status (is_uploaded)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='位置轨迹表';

-- 6. 兼容性分析结果表
CREATE TABLE compatibility_analysis (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分析ID',
    analysis_date DATE NOT NULL COMMENT '分析日期',
    brand VARCHAR(50) COMMENT '品牌筛选',
    model VARCHAR(100) COMMENT '型号筛选',
    os_version VARCHAR(50) COMMENT '系统版本筛选',
    total_devices INT DEFAULT 0 COMMENT '设备总数',
    total_crashes INT DEFAULT 0 COMMENT '崩溃总数',
    crash_rate DECIMAL(5,4) DEFAULT 0 COMMENT '崩溃率',
    top_exceptions JSON COMMENT '主要异常类型',
    problem_devices JSON COMMENT '问题设备列表',
    recommendations JSON COMMENT '优化建议',
    analysis_result JSON COMMENT '完整分析结果',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_analysis_date_filter (analysis_date, brand, model, os_version),
    INDEX idx_analysis_date (analysis_date),
    INDEX idx_crash_rate (crash_rate)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='兼容性分析结果表';

-- 7. 系统配置表
CREATE TABLE system_config (
    config_key VARCHAR(100) PRIMARY KEY COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型',
    description VARCHAR(200) COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO log_config (config_name, log_level, config_version) VALUES 
('default', 'INFO', '1.0.0');

INSERT INTO system_config (config_key, config_value, description) VALUES 
('log.retention.days', '30', '日志保留天数'),
('crash.retention.days', '90', '崩溃信息保留天数'),
('device.info.retention.days', '365', '设备信息保留天数'),
('upload.batch.size', '50', '批量上传大小'),
('analysis.auto.enabled', 'true', '是否启用自动分析');

-- 创建视图：设备崩溃统计
CREATE VIEW v_device_crash_stats AS
SELECT 
    di.device_id,
    di.brand,
    di.model,
    di.os_type,
    di.os_version,
    COUNT(ci.id) as crash_count,
    MAX(ci.crash_time) as last_crash_time,
    di.total_memory,
    di.sdk_version,
    di.is_rooted,
    di.is_emulator
FROM device_info di
LEFT JOIN crash_info ci ON di.device_id = ci.device_id
GROUP BY di.device_id;

-- 创建视图：日志统计
CREATE VIEW v_log_stats AS
SELECT 
    DATE(create_time) as log_date,
    log_type,
    level,
    brand,
    model,
    COUNT(*) as log_count
FROM log_entries
WHERE create_time >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(create_time), log_type, level, brand, model;

-- 创建存储过程：清理过期数据
DELIMITER //
CREATE PROCEDURE CleanupExpiredData()
BEGIN
    DECLARE log_retention_days INT DEFAULT 30;
    DECLARE crash_retention_days INT DEFAULT 90;
    DECLARE device_retention_days INT DEFAULT 365;
    
    -- 获取配置的保留天数
    SELECT CAST(config_value AS SIGNED) INTO log_retention_days 
    FROM system_config WHERE config_key = 'log.retention.days';
    
    SELECT CAST(config_value AS SIGNED) INTO crash_retention_days 
    FROM system_config WHERE config_key = 'crash.retention.days';
    
    SELECT CAST(config_value AS SIGNED) INTO device_retention_days 
    FROM system_config WHERE config_key = 'device.info.retention.days';
    
    -- 清理过期日志
    DELETE FROM log_entries 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL log_retention_days DAY);
    
    -- 清理过期崩溃信息
    DELETE FROM crash_info 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL crash_retention_days DAY);
    
    -- 清理过期设备信息
    DELETE FROM device_info 
    WHERE last_update_time < DATE_SUB(NOW(), INTERVAL device_retention_days DAY);
    
    -- 清理过期位置轨迹
    DELETE FROM location_track 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
END //
DELIMITER ;

-- 创建定时任务（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS cleanup_expired_data
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO CALL CleanupExpiredData();
