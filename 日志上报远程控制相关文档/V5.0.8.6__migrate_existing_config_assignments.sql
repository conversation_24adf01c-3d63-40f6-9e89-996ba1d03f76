-- 迁移现有的用户和设备专属配置到分发关系表
INSERT INTO `b_config_distribution` (
  `config_id`, `target_type`, `target_id`, `target_name`, 
  `assign_time`, `is_active`
)
SELECT 
  lc.id as config_id,
  CASE 
    WHEN lc.config_name LIKE 'user_%' THEN 'USER'
    WHEN lc.config_name LIKE 'device_%' THEN 'DEVICE'
  END as target_type,
  CASE 
    WHEN lc.config_name LIKE 'user_%' THEN SUBSTRING(lc.config_name, 6)
    WHEN lc.config_name LIKE 'device_%' THEN SUBSTRING(lc.config_name, 8)
  END as target_id,
  CASE 
    WHEN lc.config_name LIKE 'user_%' THEN CONCAT('用户-', SUBSTRING(lc.config_name, 6))
    WHEN lc.config_name LIKE 'device_%' THEN CONCAT('设备-', SUBSTRING(lc.config_name, 8))
  END as target_name,
  lc.create_at as assign_time,
  lc.is_active
FROM `b_log_config` lc
WHERE (lc.config_name LIKE 'user_%' OR lc.config_name LIKE 'device_%')
  AND lc.deleted = 0;

-- 注意：不再设置 distribution_status，状态将通过版本号比较实时计算
