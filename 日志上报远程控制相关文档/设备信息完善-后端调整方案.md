# 📱 设备信息完善 - 后端调整方案

## 🎯 概述

基于Android端完善的设备信息上传数据，对后端系统进行相应调整，以支持更丰富的设备信息收集和分析功能。

## 📋 当前状态分析

### 现有数据库表结构
```sql
-- b_device_info 表（当前字段）
CREATE TABLE `b_device_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `device_id` varchar(64) NOT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `user_code` varchar(64) DEFAULT NULL,
  `user_name` varchar(64) DEFAULT NULL,
  `brand` varchar(32) DEFAULT NULL,
  `model` varchar(64) DEFAULT NULL,
  `os_version` varchar(32) DEFAULT NULL,
  `app_version` varchar(32) DEFAULT NULL,
  `total_memory` bigint(20) DEFAULT NULL,
  `is_rooted` tinyint(1) DEFAULT '0',
  `first_collect_time` datetime DEFAULT NULL,
  `last_update_time` datetime DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT '0',
  `create_by` bigint(20) DEFAULT NULL,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`)
);
```

### Android端新增字段需求
```json
{
  "osType": "Android",
  "sdkVersion": 35,
  "manufacturer": "Google",
  "screenResolution": "1080x2340",
  "screenDensity": 3.0,
  "availableStorage": 53687091200,
  "cpuAbi": "x86_64",
  "isEmulator": true,
  "networkType": "WiFi",
  "language": "zh-CN",
  "timeZone": "Asia/Shanghai",
  "collectCount": 5,
  "permissionsInfo": {...}
}
```

## 🔧 数据库表结构调整

### 1. 新增字段SQL脚本
```sql
-- 为 b_device_info 表添加扩展字段
ALTER TABLE `b_device_info` 
ADD COLUMN `os_type` varchar(20) DEFAULT 'Android' COMMENT '操作系统类型' AFTER `os_version`,
ADD COLUMN `sdk_version` int(11) DEFAULT NULL COMMENT 'SDK版本' AFTER `os_type`,
ADD COLUMN `manufacturer` varchar(100) DEFAULT NULL COMMENT '制造商' AFTER `model`,
ADD COLUMN `screen_resolution` varchar(20) DEFAULT NULL COMMENT '屏幕分辨率' AFTER `manufacturer`,
ADD COLUMN `screen_density` float DEFAULT NULL COMMENT '屏幕密度' AFTER `screen_resolution`,
ADD COLUMN `available_storage` bigint(20) DEFAULT NULL COMMENT '可用存储空间(字节)' AFTER `total_memory`,
ADD COLUMN `cpu_abi` varchar(50) DEFAULT NULL COMMENT 'CPU架构' AFTER `available_storage`,
ADD COLUMN `is_emulator` tinyint(1) DEFAULT '0' COMMENT '是否模拟器' AFTER `is_rooted`,
ADD COLUMN `network_type` varchar(20) DEFAULT NULL COMMENT '网络类型' AFTER `is_emulator`,
ADD COLUMN `language` varchar(10) DEFAULT NULL COMMENT '系统语言' AFTER `network_type`,
ADD COLUMN `time_zone` varchar(50) DEFAULT NULL COMMENT '时区' AFTER `language`,
ADD COLUMN `collect_count` int(11) DEFAULT '1' COMMENT '收集次数' AFTER `last_update_time`,
ADD COLUMN `permissions_info` json DEFAULT NULL COMMENT '权限信息(JSON格式)' AFTER `collect_count`;

-- 添加索引
ALTER TABLE `b_device_info` 
ADD INDEX `idx_manufacturer` (`manufacturer`),
ADD INDEX `idx_os_type_sdk` (`os_type`, `sdk_version`),
ADD INDEX `idx_is_emulator` (`is_emulator`),
ADD INDEX `idx_network_type` (`network_type`);
```

### 2. 完整的表结构
```sql
CREATE TABLE `b_device_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(64) NOT NULL COMMENT '设备唯一标识',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `user_code` varchar(64) DEFAULT NULL COMMENT '用户编码',
  `user_name` varchar(64) DEFAULT NULL COMMENT '用户名称',
  `brand` varchar(50) DEFAULT NULL COMMENT '设备品牌',
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `manufacturer` varchar(100) DEFAULT NULL COMMENT '制造商',
  `screen_resolution` varchar(20) DEFAULT NULL COMMENT '屏幕分辨率',
  `screen_density` float DEFAULT NULL COMMENT '屏幕密度',
  `os_type` varchar(20) DEFAULT 'Android' COMMENT '操作系统类型',
  `os_version` varchar(50) DEFAULT NULL COMMENT '操作系统版本',
  `sdk_version` int(11) DEFAULT NULL COMMENT 'SDK版本',
  `app_version` varchar(20) DEFAULT NULL COMMENT '应用版本',
  `total_memory` bigint(20) DEFAULT NULL COMMENT '总内存(字节)',
  `available_storage` bigint(20) DEFAULT NULL COMMENT '可用存储空间(字节)',
  `cpu_abi` varchar(50) DEFAULT NULL COMMENT 'CPU架构',
  `is_rooted` tinyint(1) DEFAULT '0' COMMENT '是否Root',
  `is_emulator` tinyint(1) DEFAULT '0' COMMENT '是否模拟器',
  `network_type` varchar(20) DEFAULT NULL COMMENT '网络类型',
  `language` varchar(10) DEFAULT NULL COMMENT '系统语言',
  `time_zone` varchar(50) DEFAULT NULL COMMENT '时区',
  `first_collect_time` datetime DEFAULT NULL COMMENT '首次收集时间',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `collect_count` int(11) DEFAULT '1' COMMENT '收集次数',
  `permissions_info` json DEFAULT NULL COMMENT '权限信息(JSON格式)',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`),
  KEY `idx_brand_model` (`brand`, `model`),
  KEY `idx_manufacturer` (`manufacturer`),
  KEY `idx_os_type_sdk` (`os_type`, `sdk_version`),
  KEY `idx_is_emulator` (`is_emulator`),
  KEY `idx_network_type` (`network_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_at` (`create_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表';
```

## 💻 后端代码调整

### 1. DeviceInfo实体类更新
```java
package com.hightop.benyin.logcontrol.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备信息实体（完善版）
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("b_device_info")
public class DeviceInfo {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("device_id")
    private String deviceId;

    @TableField("user_id")
    private Long userId;

    @TableField("user_code")
    private String userCode;

    @TableField("user_name")
    private String userName;

    @TableField("brand")
    private String brand;

    @TableField("model")
    private String model;

    @TableField("manufacturer")
    private String manufacturer;

    @TableField("screen_resolution")
    private String screenResolution;

    @TableField("screen_density")
    private Float screenDensity;

    @TableField("os_type")
    private String osType;

    @TableField("os_version")
    private String osVersion;

    @TableField("sdk_version")
    private Integer sdkVersion;

    @TableField("app_version")
    private String appVersion;

    @TableField("total_memory")
    private Long totalMemory;

    @TableField("available_storage")
    private Long availableStorage;

    @TableField("cpu_abi")
    private String cpuAbi;

    @TableField("is_rooted")
    private Boolean isRooted;

    @TableField("is_emulator")
    private Boolean isEmulator;

    @TableField("network_type")
    private String networkType;

    @TableField("language")
    private String language;

    @TableField("time_zone")
    private String timeZone;

    @TableField("first_collect_time")
    private LocalDateTime firstCollectTime;

    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    @TableField("collect_count")
    private Integer collectCount;

    @TableField("permissions_info")
    private String permissionsInfo;

    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    @TableField("create_by")
    private Long createBy;

    @TableField(value = "create_at", fill = FieldFill.INSERT)
    private LocalDateTime createAt;

    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateAt;
}
```

### 2. DeviceInfoDto更新
```java
package com.hightop.benyin.logcontrol.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 设备信息DTO（完善版）
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@ApiModel("设备信息")
public class DeviceInfoDto {

    @ApiModelProperty("设备信息ID")
    private Long id;

    @ApiModelProperty(value = "设备唯一标识", required = true)
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("用户编码")
    private String userCode;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty(value = "设备品牌", required = true)
    @NotBlank(message = "设备品牌不能为空")
    private String brand;

    @ApiModelProperty(value = "设备型号", required = true)
    @NotBlank(message = "设备型号不能为空")
    private String model;

    @ApiModelProperty("制造商")
    private String manufacturer;

    @ApiModelProperty("屏幕分辨率")
    private String screenResolution;

    @ApiModelProperty("屏幕密度")
    private Float screenDensity;

    @ApiModelProperty("操作系统类型")
    private String osType;

    @ApiModelProperty(value = "操作系统版本", required = true)
    @NotBlank(message = "系统版本不能为空")
    private String osVersion;

    @ApiModelProperty("SDK版本")
    private Integer sdkVersion;

    @ApiModelProperty(value = "应用版本", required = true)
    @NotBlank(message = "应用版本不能为空")
    private String appVersion;

    @ApiModelProperty("总内存(字节)")
    private Long totalMemory;

    @ApiModelProperty("可用存储空间(字节)")
    private Long availableStorage;

    @ApiModelProperty("CPU架构")
    private String cpuAbi;

    @ApiModelProperty("是否Root")
    private Boolean isRooted;

    @ApiModelProperty("是否模拟器")
    private Boolean isEmulator;

    @ApiModelProperty("网络类型")
    private String networkType;

    @ApiModelProperty("系统语言")
    private String language;

    @ApiModelProperty("时区")
    private String timeZone;

    @ApiModelProperty("首次收集时间")
    private LocalDateTime firstCollectTime;

    @ApiModelProperty("最后更新时间")
    private LocalDateTime lastUpdateTime;

    @ApiModelProperty("收集次数")
    private Integer collectCount;

    @ApiModelProperty("权限信息(JSON格式)")
    private String permissionsInfo;
}
```

### 3. 新增统计接口

#### 3.1 DeviceInfoController新增接口
```java
@GetMapping("/stats/manufacturer")
@ApiOperation("获取制造商统计信息")
public RestResponse<List<Map<String, Object>>> getManufacturerStatistics() {
    try {
        List<Map<String, Object>> stats = deviceInfoService.getManufacturerStatistics();
        return RestResponse.ok(stats);
    } catch (Exception e) {
        log.error("获取制造商统计信息失败", e);
        return RestResponse.ok(null);
    }
}

@GetMapping("/stats/sdk-version")
@ApiOperation("获取SDK版本统计信息")
public RestResponse<List<Map<String, Object>>> getSdkVersionStatistics() {
    try {
        List<Map<String, Object>> stats = deviceInfoService.getSdkVersionStatistics();
        return RestResponse.ok(stats);
    } catch (Exception e) {
        log.error("获取SDK版本统计信息失败", e);
        return RestResponse.ok(null);
    }
}

@GetMapping("/stats/screen-resolution")
@ApiOperation("获取屏幕分辨率统计信息")
public RestResponse<List<Map<String, Object>>> getScreenResolutionStatistics() {
    try {
        List<Map<String, Object>> stats = deviceInfoService.getScreenResolutionStatistics();
        return RestResponse.ok(stats);
    } catch (Exception e) {
        log.error("获取屏幕分辨率统计信息失败", e);
        return RestResponse.ok(null);
    }
}

@GetMapping("/stats/cpu-abi")
@ApiOperation("获取CPU架构统计信息")
public RestResponse<List<Map<String, Object>>> getCpuAbiStatistics() {
    try {
        List<Map<String, Object>> stats = deviceInfoService.getCpuAbiStatistics();
        return RestResponse.ok(stats);
    } catch (Exception e) {
        log.error("获取CPU架构统计信息失败", e);
        return RestResponse.ok(null);
    }
}

@GetMapping("/stats/emulator")
@ApiOperation("获取模拟器设备统计")
public RestResponse<List<Map<String, Object>>> getEmulatorStatistics() {
    try {
        List<Map<String, Object>> stats = deviceInfoService.getEmulatorStatistics();
        return RestResponse.ok(stats);
    } catch (Exception e) {
        log.error("获取模拟器设备统计失败", e);
        return RestResponse.ok(null);
    }
}

@GetMapping("/stats/network-type")
@ApiOperation("获取网络类型统计信息")
public RestResponse<List<Map<String, Object>>> getNetworkTypeStatistics() {
    try {
        List<Map<String, Object>> stats = deviceInfoService.getNetworkTypeStatistics();
        return RestResponse.ok(stats);
    } catch (Exception e) {
        log.error("获取网络类型统计信息失败", e);
        return RestResponse.ok(null);
    }
}

@GetMapping("/stats/language")
@ApiOperation("获取系统语言统计信息")
public RestResponse<List<Map<String, Object>>> getLanguageStatistics() {
    try {
        List<Map<String, Object>> stats = deviceInfoService.getLanguageStatistics();
        return RestResponse.ok(stats);
    } catch (Exception e) {
        log.error("获取系统语言统计信息失败", e);
        return RestResponse.ok(null);
    }
}

@GetMapping("/advanced-search")
@ApiOperation("高级搜索设备信息")
public RestResponse<List<DeviceInfoDto>> advancedSearch(
        @RequestParam(required = false) String brand,
        @RequestParam(required = false) String manufacturer,
        @RequestParam(required = false) String osType,
        @RequestParam(required = false) Integer minSdkVersion,
        @RequestParam(required = false) Integer maxSdkVersion,
        @RequestParam(required = false) Boolean isEmulator,
        @RequestParam(required = false) String networkType,
        @RequestParam(required = false) String language) {
    try {
        List<DeviceInfoDto> devices = deviceInfoService.advancedSearch(
            brand, manufacturer, osType, minSdkVersion, maxSdkVersion,
            isEmulator, networkType, language);
        return RestResponse.ok(devices);
    } catch (Exception e) {
        log.error("高级搜索设备信息失败", e);
        return RestResponse.ok(null);
    }
}
```

#### 3.2 DeviceInfoService新增方法
```java
/**
 * 获取制造商统计信息
 */
List<Map<String, Object>> getManufacturerStatistics();

/**
 * 获取SDK版本统计信息
 */
List<Map<String, Object>> getSdkVersionStatistics();

/**
 * 获取屏幕分辨率统计信息
 */
List<Map<String, Object>> getScreenResolutionStatistics();

/**
 * 获取CPU架构统计信息
 */
List<Map<String, Object>> getCpuAbiStatistics();

/**
 * 获取模拟器设备统计
 */
List<Map<String, Object>> getEmulatorStatistics();

/**
 * 获取网络类型统计信息
 */
List<Map<String, Object>> getNetworkTypeStatistics();

/**
 * 获取系统语言统计信息
 */
List<Map<String, Object>> getLanguageStatistics();

/**
 * 高级搜索设备信息
 */
List<DeviceInfoDto> advancedSearch(String brand, String manufacturer, String osType,
                                  Integer minSdkVersion, Integer maxSdkVersion,
                                  Boolean isEmulator, String networkType, String language);
```

## 🎨 前端页面调整

### 1. 设备管理页面增强
```vue
<template>
  <div class="device-management">
    <!-- 高级搜索区域 -->
    <el-card class="search-card">
      <div slot="header">
        <span>设备搜索</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="resetSearch">重置</el-button>
      </div>
      <el-form :model="searchForm" inline>
        <el-form-item label="品牌">
          <el-input v-model="searchForm.brand" placeholder="请输入品牌" />
        </el-form-item>
        <el-form-item label="制造商">
          <el-input v-model="searchForm.manufacturer" placeholder="请输入制造商" />
        </el-form-item>
        <el-form-item label="系统类型">
          <el-select v-model="searchForm.osType" placeholder="选择系统类型" clearable>
            <el-option label="Android" value="Android" />
            <el-option label="iOS" value="iOS" />
          </el-select>
        </el-form-item>
        <el-form-item label="SDK版本">
          <el-input-number v-model="searchForm.minSdkVersion" placeholder="最小版本" :min="1" />
          <span style="margin: 0 8px;">-</span>
          <el-input-number v-model="searchForm.maxSdkVersion" placeholder="最大版本" :min="1" />
        </el-form-item>
        <el-form-item label="设备类型">
          <el-select v-model="searchForm.isEmulator" placeholder="选择设备类型" clearable>
            <el-option label="真机" :value="false" />
            <el-option label="模拟器" :value="true" />
          </el-select>
        </el-form-item>
        <el-form-item label="网络类型">
          <el-select v-model="searchForm.networkType" placeholder="选择网络类型" clearable>
            <el-option label="WiFi" value="WiFi" />
            <el-option label="4G" value="4G" />
            <el-option label="5G" value="5G" />
            <el-option label="3G" value="3G" />
            <el-option label="2G" value="2G" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 设备列表 -->
    <el-card class="device-list-card">
      <div slot="header">
        <span>设备列表 (总计: {{ total }})</span>
      </div>

      <el-table :data="devices" v-loading="loading" stripe>
        <el-table-column prop="deviceId" label="设备ID" width="150" />
        <el-table-column prop="brand" label="品牌" width="100" />
        <el-table-column prop="model" label="型号" width="150" />
        <el-table-column prop="manufacturer" label="制造商" width="120" />
        <el-table-column prop="osVersion" label="系统版本" width="120" />
        <el-table-column prop="sdkVersion" label="SDK版本" width="100" />
        <el-table-column prop="screenResolution" label="屏幕分辨率" width="120" />
        <el-table-column prop="cpuAbi" label="CPU架构" width="100" />
        <el-table-column prop="isEmulator" label="设备类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isEmulator ? 'warning' : 'success'">
              {{ scope.row.isEmulator ? '模拟器' : '真机' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="networkType" label="网络类型" width="100" />
        <el-table-column prop="language" label="语言" width="80" />
        <el-table-column prop="userName" label="用户" width="100" />
        <el-table-column prop="collectCount" label="收集次数" width="100" />
        <el-table-column prop="lastUpdateTime" label="最后更新" width="180" />
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button size="small" @click="handleViewDetail(scope.row)">详情</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设备详情对话框 -->
    <device-detail-dialog
      :visible.sync="detailDialog"
      :device="selectedDevice"
    />
  </div>
</template>

<script>
import { deviceApi } from '@/api/deviceApi'
import DeviceDetailDialog from '@/components/DeviceManagement/DeviceDetailDialog.vue'

export default {
  name: 'DeviceManagement',
  components: {
    DeviceDetailDialog
  },
  data() {
    return {
      loading: false,
      devices: [],
      total: 0,
      searchForm: {
        brand: '',
        manufacturer: '',
        osType: '',
        minSdkVersion: null,
        maxSdkVersion: null,
        isEmulator: null,
        networkType: '',
        language: ''
      },
      detailDialog: false,
      selectedDevice: null
    }
  },
  mounted() {
    this.loadDevices()
  },
  methods: {
    async loadDevices() {
      this.loading = true
      try {
        const response = await deviceApi.getDeviceList()
        this.devices = response.data || []
        this.total = this.devices.length
      } catch (error) {
        this.$message.error('加载设备列表失败')
      } finally {
        this.loading = false
      }
    },

    async handleSearch() {
      this.loading = true
      try {
        const response = await deviceApi.advancedSearch(this.searchForm)
        this.devices = response.data || []
        this.total = this.devices.length
      } catch (error) {
        this.$message.error('搜索设备失败')
      } finally {
        this.loading = false
      }
    },

    resetSearch() {
      this.searchForm = {
        brand: '',
        manufacturer: '',
        osType: '',
        minSdkVersion: null,
        maxSdkVersion: null,
        isEmulator: null,
        networkType: '',
        language: ''
      }
      this.loadDevices()
    },

    handleViewDetail(device) {
      this.selectedDevice = device
      this.detailDialog = true
    },

    async handleDelete(device) {
      try {
        await this.$confirm('确定要删除这个设备信息吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await deviceApi.deleteDevice(device.id)
        this.$message.success('删除成功')
        this.loadDevices()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    handleExport() {
      this.$message.info('导出功能开发中')
    }
  }
}
</script>
```

### 2. 设备统计图表组件
```vue
<template>
  <div class="device-stats-charts">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card>
          <div slot="header">制造商分布</div>
          <manufacturer-chart :data="manufacturerData" />
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div slot="header">SDK版本分布</div>
          <sdk-version-chart :data="sdkVersionData" />
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div slot="header">CPU架构分布</div>
          <cpu-abi-chart :data="cpuAbiData" />
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <div slot="header">屏幕分辨率分布</div>
          <screen-resolution-chart :data="screenResolutionData" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <div slot="header">网络类型分布</div>
          <network-type-chart :data="networkTypeData" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { deviceApi } from '@/api/deviceApi'

export default {
  name: 'DeviceStatsCharts',
  data() {
    return {
      manufacturerData: [],
      sdkVersionData: [],
      cpuAbiData: [],
      screenResolutionData: [],
      networkTypeData: []
    }
  },
  mounted() {
    this.loadStatsData()
  },
  methods: {
    async loadStatsData() {
      try {
        const [
          manufacturerResponse,
          sdkVersionResponse,
          cpuAbiResponse,
          screenResolutionResponse,
          networkTypeResponse
        ] = await Promise.all([
          deviceApi.getManufacturerStatistics(),
          deviceApi.getSdkVersionStatistics(),
          deviceApi.getCpuAbiStatistics(),
          deviceApi.getScreenResolutionStatistics(),
          deviceApi.getNetworkTypeStatistics()
        ])

        this.manufacturerData = manufacturerResponse.data
        this.sdkVersionData = sdkVersionResponse.data
        this.cpuAbiData = cpuAbiResponse.data
        this.screenResolutionData = screenResolutionResponse.data
        this.networkTypeData = networkTypeResponse.data
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    }
  }
}
</script>
```

## 📊 API接口更新

### 1. 设备管理API扩展
```javascript
// api/deviceApi.js
import request from '@/utils/request'

export const deviceApi = {
  // 现有接口...

  // 新增统计接口
  getManufacturerStatistics() {
    return request.get('/logcontrol/device/stats/manufacturer')
  },

  getSdkVersionStatistics() {
    return request.get('/logcontrol/device/stats/sdk-version')
  },

  getScreenResolutionStatistics() {
    return request.get('/logcontrol/device/stats/screen-resolution')
  },

  getCpuAbiStatistics() {
    return request.get('/logcontrol/device/stats/cpu-abi')
  },

  getEmulatorStatistics() {
    return request.get('/logcontrol/device/stats/emulator')
  },

  getNetworkTypeStatistics() {
    return request.get('/logcontrol/device/stats/network-type')
  },

  getLanguageStatistics() {
    return request.get('/logcontrol/device/stats/language')
  },

  // 高级搜索
  advancedSearch(params) {
    return request.get('/logcontrol/device/advanced-search', { params })
  }
}
```

## 📝 实施步骤

### 第一阶段：数据库调整
1. 执行数据库表结构调整SQL脚本
2. 验证表结构和索引创建成功
3. 测试数据插入和查询功能

### 第二阶段：后端代码更新
1. 更新DeviceInfo实体类和DeviceInfoDto
2. 扩展DeviceInfoController接口
3. 实现新增的统计和搜索功能
4. 单元测试验证

### 第三阶段：前端界面调整
1. 更新设备管理页面，支持新字段显示
2. 实现高级搜索功能
3. 添加新的统计图表组件
4. 测试前后端联调

### 第四阶段：测试验证
1. Android端上传完整设备信息测试
2. 后台管理界面功能测试
3. 统计分析功能验证
4. 性能测试和优化

## 🎯 预期效果

1. **数据完整性提升**：收集更全面的设备信息，便于问题定位和分析
2. **统计分析增强**：支持更多维度的设备统计分析
3. **管理功能完善**：提供更强大的设备搜索和管理功能
4. **用户体验优化**：更直观的设备信息展示和操作界面

这个调整方案将显著提升设备信息管理的功能和用户体验。
```
