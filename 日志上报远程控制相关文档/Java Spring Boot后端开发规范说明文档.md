# 位置上报远程日志控制系统 - Java Spring Boot后端开发规范

## 1. 项目架构设计

### 1.1 技术栈选型

```yaml
核心技术栈:
  - Spring Boot: 2.7.x
  - Spring Data JPA: 数据访问层
  - Spring Security: 安全认证
  - Spring Cache: 缓存支持
  - MySQL: 8.0+ (主数据库)
  - Redis: 6.0+ (缓存/会话)
  - Maven: 3.8+ (构建工具)
  - Java: 11+ (JDK版本)

辅助组件:
  - Swagger/OpenAPI: API文档
  - Logback: 日志框架
  - Jackson: JSON处理
  - HikariCP: 连接池
  - Quartz: 定时任务
```

### 1.2 项目目录结构

```
log-control-backend/
├── src/main/java/com/logcontrol/
│   ├── LogControlApplication.java          # 启动类
│   ├── config/                             # 配置类
│   │   ├── DatabaseConfig.java
│   │   ├── SecurityConfig.java
│   │   ├── CacheConfig.java
│   │   ├── SwaggerConfig.java
│   │   └── ScheduleConfig.java
│   ├── controller/                         # 控制器层
│   │   ├── LogConfigController.java
│   │   ├── DeviceController.java
│   │   ├── CrashController.java
│   │   ├── LogController.java
│   │   ├── LocationController.java
│   │   └── AnalysisController.java
│   ├── service/                           # 服务层
│   │   ├── LogConfigService.java
│   │   ├── DeviceInfoService.java
│   │   ├── CrashInfoService.java
│   │   ├── LogEntryService.java
│   │   ├── CompatibilityAnalysisService.java
│   │   ├── LocationTrackService.java
│   │   └── StatisticsService.java
│   ├── repository/                        # 数据访问层
│   │   ├── LogConfigRepository.java
│   │   ├── DeviceInfoRepository.java
│   │   ├── CrashInfoRepository.java
│   │   ├── LogEntryRepository.java
│   │   ├── LocationTrackRepository.java
│   │   └── CompatibilityAnalysisRepository.java
│   ├── entity/                           # 实体类
│   │   ├── LogConfig.java
│   │   ├── DeviceInfo.java
│   │   ├── CrashInfo.java
│   │   ├── LogEntry.java
│   │   ├── LocationTrack.java
│   │   └── CompatibilityAnalysis.java
│   ├── dto/                              # 数据传输对象
│   │   ├── request/
│   │   │   ├── LogUploadRequest.java
│   │   │   ├── DeviceInfoUploadRequest.java
│   │   │   ├── CrashInfoUploadRequest.java
│   │   │   └── ConfigUpdateRequest.java
│   │   └── response/
│   │       ├── LogConfigResponse.java
│   │       ├── CompatibilityAnalysisResponse.java
│   │       ├── CrashStatisticsResponse.java
│   │       └── ApiResponse.java
│   ├── util/                             # 工具类
│   │   ├── ResponseUtil.java
│   │   ├── DateUtil.java
│   │   ├── JsonUtil.java
│   │   └── AnalysisUtil.java
│   ├── exception/                        # 异常处理
│   │   ├── GlobalExceptionHandler.java
│   │   ├── BusinessException.java
│   │   └── ErrorCode.java
│   └── schedule/                         # 定时任务
│       ├── DataCleanupTask.java
│       ├── AnalysisTask.java
│       └── StatisticsTask.java
├── src/main/resources/
│   ├── application.yml                   # 主配置文件
│   ├── application-dev.yml              # 开发环境配置
│   ├── application-prod.yml             # 生产环境配置
│   ├── sql/
│   │   ├── schema.sql                   # 建表脚本
│   │   └── data.sql                     # 初始数据
│   └── logback-spring.xml               # 日志配置
├── src/test/java/                       # 测试代码
└── pom.xml                              # Maven配置
```

### 1.3 各层级职责定义

#### Controller层职责
- 接收HTTP请求，参数验证
- 调用Service层业务逻辑
- 返回统一格式响应
- 异常处理和错误码转换

#### Service层职责
- 核心业务逻辑实现
- 事务管理
- 数据转换和处理
- 调用Repository层数据操作

#### Repository层职责
- 数据库CRUD操作
- 复杂查询实现
- 数据统计和聚合

#### Entity层职责
- 数据库表映射
- 实体关系定义
- 数据验证注解

## 2. API接口规范

### 2.1 统一响应格式

```java
// 统一响应格式
{
    "code": 200,           // 状态码
    "msg": "success",      // 消息
    "data": {},           // 数据
    "timestamp": 1640995200000  // 时间戳
}

// 错误响应格式
{
    "code": 400,
    "msg": "参数错误",
    "data": null,
    "timestamp": 1640995200000
}
```

### 2.2 状态码定义

```java
public enum ErrorCode {
    SUCCESS(200, "操作成功"),
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    INTERNAL_ERROR(500, "服务器内部错误"),
    
    // 业务错误码
    INVALID_TOKEN(1001, "Token无效"),
    CONFIG_NOT_FOUND(1002, "配置不存在"),
    DEVICE_NOT_FOUND(1003, "设备不存在"),
    UPLOAD_FAILED(1004, "上传失败");
}
```

### 2.3 核心API接口定义

#### 2.3.1 日志配置管理接口

```http
# 获取日志配置
GET /api/log-config/get
Headers:
  X-Auth-Token: {token}
  X-App-Version: {appVersion}
  X-Device-Id: {deviceId}

Response:
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "configName": "default",
    "logLevel": "INFO",
    "enableLocationLog": true,
    "locationLogInterval": 300000,
    "enablePerformanceLog": true,
    "enableCrashLog": true,
    "enableBusinessLog": true,
    "logUploadInterval": 3600000,
    "maxLogFileSize": 10485760,
    "maxLogFiles": 5,
    "configVersion": "1.0.0",
    "effectiveTime": "2024-01-01T00:00:00",
    "expiryTime": "2024-12-31T23:59:59",
    "isActive": true
  }
}
```

```http
# 检查配置更新
POST /api/log-config/check-update
Headers:
  X-Auth-Token: {token}
Content-Type: application/json

Request Body:
{
  "currentVersion": "1.0.0",
  "deviceId": "device123",
  "appVersion": "2.1.0"
}

Response:
{
  "code": 200,
  "msg": "success",
  "data": {
    "hasUpdate": true,
    "latestVersion": "1.1.0",
    "updateDescription": "优化日志收集策略",
    "forceUpdate": false
  }
}
```

#### 2.3.2 日志数据上传接口

```http
# 上传日志数据
POST /api/log/upload
Headers:
  X-Auth-Token: {token}
  Content-Type: application/json

Request Body:
{
  "deviceId": "device123",
  "appVersion": "2.1.0",
  "uploadTime": "2024-01-01T12:00:00",
  "logs": [
    {
      "logType": "LOCATION",
      "level": "INFO",
      "timestamp": "2024-01-01T12:00:00",
      "tag": "LocationService",
      "message": "位置更新成功",
      "extraData": "{\"latitude\":39.9042,\"longitude\":116.4074}",
      "deviceId": "device123",
      "brand": "Huawei",
      "model": "Mate 40 Pro",
      "osType": "HarmonyOS",
      "osVersion": "3.0",
      "appVersion": "2.1.0",
      "sdkVersion": 30,
      "sessionId": "session123"
    }
  ]
}

Response:
{
  "code": 200,
  "msg": "success",
  "data": {
    "successCount": 1,
    "failureCount": 0,
    "uploadId": "upload123"
  }
}
```

#### 2.3.3 设备信息管理接口

```http
# 上传设备信息
POST /api/device/upload-info
Headers:
  X-Auth-Token: {token}
  Content-Type: application/json

Request Body:
{
  "deviceInfo": {
    "deviceId": "device123",
    "brand": "Huawei",
    "model": "Mate 40 Pro",
    "osType": "HarmonyOS",
    "osVersion": "3.0",
    "appVersion": "2.1.0",
    "sdkVersion": 30,
    "manufacturer": "HUAWEI",
    "screenResolution": "1080x2340",
    "screenDensity": 3.0,
    "totalMemory": 8589934592,
    "availableStorage": 107374182400,
    "cpuAbi": "arm64-v8a",
    "isRooted": false,
    "isEmulator": false,
    "networkType": "WiFi",
    "language": "zh-CN",
    "timeZone": "Asia/Shanghai"
  }
}

Response:
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

```http
# 获取设备兼容性分析
GET /api/device/compatibility-analysis
Headers:
  X-Auth-Token: {token}
Query Parameters:
  brand: Huawei (可选)
  model: Mate 40 Pro (可选)
  osVersion: HarmonyOS 3.0 (可选)

Response:
{
  "code": 200,
  "msg": "success",
  "data": {
    "deviceStats": [
      {
        "brand": "Huawei",
        "model": "Mate 40 Pro",
        "osVersion": "HarmonyOS 3.0",
        "userCount": 1250,
        "crashRate": 0.02,
        "performanceScore": 95.5
      }
    ],
    "crashStats": [
      {
        "exceptionType": "OutOfMemoryError",
        "count": 45,
        "affectedDevices": 23,
        "trend": "INCREASING"
      }
    ],
    "problemDevices": [
      {
        "deviceId": "device123",
        "brand": "Xiaomi",
        "model": "Redmi Note 8",
        "osVersion": "Android 10",
        "crashCount": 12,
        "lastCrashTime": "2024-01-01T12:00:00",
        "mainIssues": ["低内存", "系统版本过低"]
      }
    ],
    "recommendations": [
      "发现3种高风险设备型号，建议加强测试",
      "内存相关崩溃占比较高，建议优化内存使用"
    ]
  }
}
```

#### 2.3.4 崩溃信息管理接口

```http
# 上传崩溃信息
POST /api/crash/upload
Headers:
  X-Auth-Token: {token}
  Content-Type: application/json

Request Body:
{
  "crashes": [
    {
      "deviceId": "device123",
      "crashTime": 1640995200000,
      "threadName": "main",
      "exceptionType": "java.lang.NullPointerException",
      "exceptionMessage": "Attempt to invoke virtual method",
      "stackTrace": "java.lang.NullPointerException: Attempt to invoke...",
      "appState": "FOREGROUND",
      "memoryUsage": 536870912,
      "availableMemory": 2147483648,
      "batteryLevel": 85,
      "isCharging": false,
      "networkStatus": "WiFi",
      "lastActivity": "MainActivity",
      "userActions": "[{\"action\":\"点击按钮\",\"screen\":\"主页\",\"timestamp\":1640995190000}]",
      "customData": "{\"processId\":12345,\"threadCount\":8}"
    }
  ]
}

Response:
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

```http
# 获取崩溃统计
GET /api/crash/statistics
Headers:
  X-Auth-Token: {token}
Query Parameters:
  startTime: 1640995200000 (可选)
  endTime: 1641081600000 (可选)
  deviceId: device123 (可选)

Response:
{
  "code": 200,
  "msg": "success",
  "data": {
    "totalCrashes": 156,
    "crashTrend": [
      {
        "date": "2024-01-01",
        "crashCount": 23,
        "affectedUsers": 18
      }
    ],
    "topExceptions": [
      {
        "exceptionType": "OutOfMemoryError",
        "count": 45,
        "affectedDevices": 23,
        "trend": "INCREASING"
      }
    ],
    "deviceCrashRanking": [
      {
        "brand": "Samsung",
        "model": "Galaxy A50",
        "osVersion": "Android 10",
        "userCount": 500,
        "crashRate": 0.15,
        "performanceScore": 75.0
      }
    ]
  }
}
```

#### 2.3.5 位置轨迹管理接口

```http
# 上传位置轨迹
POST /api/location/upload-track
Headers:
  X-Auth-Token: {token}
  Content-Type: application/json

Request Body:
{
  "engineerId": 12345,
  "trackData": [
    {
      "latitude": 39.9042,
      "longitude": 116.4074,
      "accuracy": 10.5,
      "timestamp": "2024-01-01T12:00:00",
      "speed": 0.0,
      "bearing": 0.0
    }
  ]
}

Response:
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

### 2.4 认证授权规范

#### Token验证机制
```java
// 请求头格式
X-Auth-Token: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// Token包含信息
{
  "userId": 12345,
  "username": "engineer001",
  "role": "ENGINEER",
  "exp": 1640995200,
  "iat": 1640908800
}
```

#### 权限级别定义
```java
public enum Role {
    ENGINEER("工程师", Arrays.asList("log:upload", "device:upload")),
    ADMIN("管理员", Arrays.asList("*")),
    VIEWER("查看者", Arrays.asList("log:view", "device:view"));
}
```

## 3. 数据模型设计

### 3.1 JPA实体类定义

#### 3.1.1 设备信息实体
```java
@Entity
@Table(name = "device_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfo {

    @Id
    @Column(name = "device_id", length = 100)
    private String deviceId;

    @Column(name = "brand", length = 50, nullable = false)
    private String brand;

    @Column(name = "model", length = 100, nullable = false)
    private String model;

    @Column(name = "os_type", length = 20, nullable = false)
    private String osType;

    @Column(name = "os_version", length = 50, nullable = false)
    private String osVersion;

    @Column(name = "app_version", length = 20, nullable = false)
    private String appVersion;

    @Column(name = "sdk_version", nullable = false)
    private Integer sdkVersion;

    @Column(name = "manufacturer", length = 50)
    private String manufacturer;

    @Column(name = "screen_resolution", length = 20)
    private String screenResolution;

    @Column(name = "screen_density")
    private Float screenDensity;

    @Column(name = "total_memory")
    private Long totalMemory;

    @Column(name = "available_storage")
    private Long availableStorage;

    @Column(name = "cpu_abi", length = 50)
    private String cpuAbi;

    @Column(name = "is_rooted")
    private Boolean isRooted = false;

    @Column(name = "is_emulator")
    private Boolean isEmulator = false;

    @Column(name = "network_type", length = 20)
    private String networkType;

    @Column(name = "language", length = 10)
    private String language;

    @Column(name = "time_zone", length = 50)
    private String timeZone;

    @Column(name = "first_collect_time")
    @CreationTimestamp
    private LocalDateTime firstCollectTime;

    @Column(name = "last_update_time")
    @UpdateTimestamp
    private LocalDateTime lastUpdateTime;

    @Column(name = "collect_count")
    private Integer collectCount = 1;

    // 关联关系
    @OneToMany(mappedBy = "deviceInfo", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CrashInfo> crashInfos;

    @OneToMany(mappedBy = "deviceInfo", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LogEntry> logEntries;
}
```

#### 3.1.2 崩溃信息实体
```java
@Entity
@Table(name = "crash_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrashInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "device_id", length = 100, nullable = false)
    private String deviceId;

    @Column(name = "crash_time", nullable = false)
    private Long crashTime;

    @Column(name = "thread_name", length = 100)
    private String threadName;

    @Column(name = "exception_type", length = 200, nullable = false)
    private String exceptionType;

    @Column(name = "exception_message", columnDefinition = "TEXT")
    private String exceptionMessage;

    @Column(name = "stack_trace", columnDefinition = "LONGTEXT")
    private String stackTrace;

    @Enumerated(EnumType.STRING)
    @Column(name = "app_state")
    private AppState appState = AppState.FOREGROUND;

    @Column(name = "memory_usage")
    private Long memoryUsage;

    @Column(name = "available_memory")
    private Long availableMemory;

    @Column(name = "battery_level")
    private Integer batteryLevel;

    @Column(name = "is_charging")
    private Boolean isCharging;

    @Column(name = "network_status", length = 20)
    private String networkStatus;

    @Column(name = "last_activity", length = 100)
    private String lastActivity;

    @Column(name = "user_actions", columnDefinition = "JSON")
    private String userActions;

    @Column(name = "custom_data", columnDefinition = "JSON")
    private String customData;

    @Column(name = "is_uploaded")
    private Boolean isUploaded = false;

    @Column(name = "create_time")
    @CreationTimestamp
    private LocalDateTime createTime;

    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "device_id", referencedColumnName = "device_id", insertable = false, updatable = false)
    private DeviceInfo deviceInfo;

    public enum AppState {
        FOREGROUND, BACKGROUND
    }
}
```

#### 3.1.3 日志配置实体
```java
@Entity
@Table(name = "log_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "config_name", length = 50, nullable = false)
    private String configName;

    @Column(name = "log_level", length = 10, nullable = false)
    private String logLevel = "INFO";

    @Column(name = "enable_location_log")
    private Boolean enableLocationLog = true;

    @Column(name = "location_log_interval")
    private Long locationLogInterval = 300000L;

    @Column(name = "enable_performance_log")
    private Boolean enablePerformanceLog = true;

    @Column(name = "enable_crash_log")
    private Boolean enableCrashLog = true;

    @Column(name = "enable_business_log")
    private Boolean enableBusinessLog = true;

    @Column(name = "log_upload_interval")
    private Long logUploadInterval = 3600000L;

    @Column(name = "max_log_file_size")
    private Long maxLogFileSize = 10485760L;

    @Column(name = "max_log_files")
    private Integer maxLogFiles = 5;

    @Column(name = "enable_remote_control")
    private Boolean enableRemoteControl = true;

    @Column(name = "config_version", length = 20, nullable = false)
    private String configVersion;

    @Column(name = "effective_time")
    private LocalDateTime effectiveTime;

    @Column(name = "expiry_time")
    private LocalDateTime expiryTime;

    @Column(name = "target_devices", columnDefinition = "JSON")
    private String targetDevices;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "create_time")
    @CreationTimestamp
    private LocalDateTime createTime;

    @Column(name = "update_time")
    @UpdateTimestamp
    private LocalDateTime updateTime;
}
```

#### 3.1.4 日志条目实体
```java
@Entity
@Table(name = "log_entries")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogEntry {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "log_type", length = 20, nullable = false)
    private String logType;

    @Column(name = "level", length = 10, nullable = false)
    private String level;

    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;

    @Column(name = "tag", length = 100, nullable = false)
    private String tag;

    @Column(name = "message", columnDefinition = "TEXT", nullable = false)
    private String message;

    @Column(name = "extra_data", columnDefinition = "JSON")
    private String extraData;

    @Column(name = "device_id", length = 100)
    private String deviceId;

    @Column(name = "brand", length = 50)
    private String brand;

    @Column(name = "model", length = 100)
    private String model;

    @Column(name = "os_type", length = 20)
    private String osType;

    @Column(name = "os_version", length = 50)
    private String osVersion;

    @Column(name = "app_version", length = 20)
    private String appVersion;

    @Column(name = "sdk_version")
    private Integer sdkVersion;

    @Column(name = "crash_id")
    private Long crashId;

    @Column(name = "session_id", length = 100)
    private String sessionId;

    @Column(name = "is_uploaded")
    private Boolean isUploaded = false;

    @Column(name = "create_time")
    @CreationTimestamp
    private LocalDateTime createTime;

    // 关联关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "device_id", referencedColumnName = "device_id", insertable = false, updatable = false)
    private DeviceInfo deviceInfo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "crash_id", referencedColumnName = "id", insertable = false, updatable = false)
    private CrashInfo crashInfo;
}
```

### 3.2 DTO类定义

#### 3.2.1 请求DTO
```java
// 日志上传请求
@Data
public class LogUploadRequest {
    private String deviceId;
    private String appVersion;
    private String uploadTime;
    private List<LogEntryDto> logs;

    @Data
    public static class LogEntryDto {
        private String logType;
        private String level;
        private String timestamp;
        private String tag;
        private String message;
        private String extraData;
        private String deviceId;
        private String brand;
        private String model;
        private String osType;
        private String osVersion;
        private String appVersion;
        private Integer sdkVersion;
        private Long crashId;
        private String sessionId;
    }
}

// 设备信息上传请求
@Data
public class DeviceInfoUploadRequest {
    private DeviceInfoDto deviceInfo;

    @Data
    public static class DeviceInfoDto {
        private String deviceId;
        private String brand;
        private String model;
        private String osType;
        private String osVersion;
        private String appVersion;
        private Integer sdkVersion;
        private String manufacturer;
        private String screenResolution;
        private Float screenDensity;
        private Long totalMemory;
        private Long availableStorage;
        private String cpuAbi;
        private Boolean isRooted;
        private Boolean isEmulator;
        private String networkType;
        private String language;
        private String timeZone;
    }
}

// 崩溃信息上传请求
@Data
public class CrashInfoUploadRequest {
    private List<CrashInfoDto> crashes;

    @Data
    public static class CrashInfoDto {
        private String deviceId;
        private Long crashTime;
        private String threadName;
        private String exceptionType;
        private String exceptionMessage;
        private String stackTrace;
        private String appState;
        private Long memoryUsage;
        private Long availableMemory;
        private Integer batteryLevel;
        private Boolean isCharging;
        private String networkStatus;
        private String lastActivity;
        private String userActions;
        private String customData;
    }
}
```

#### 3.2.2 响应DTO
```java
// 统一响应格式
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponse<T> {
    private Integer code;
    private String msg;
    private T data;
    private Long timestamp;

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "success", data, System.currentTimeMillis());
    }

    public static <T> ApiResponse<T> error(Integer code, String msg) {
        return new ApiResponse<>(code, msg, null, System.currentTimeMillis());
    }
}

// 日志配置响应
@Data
public class LogConfigResponse {
    private Long id;
    private String configName;
    private String logLevel;
    private Boolean enableLocationLog;
    private Long locationLogInterval;
    private Boolean enablePerformanceLog;
    private Boolean enableCrashLog;
    private Boolean enableBusinessLog;
    private Long logUploadInterval;
    private Long maxLogFileSize;
    private Integer maxLogFiles;
    private String configVersion;
    private String effectiveTime;
    private String expiryTime;
    private Boolean isActive;
}

// 兼容性分析响应
@Data
public class CompatibilityAnalysisResponse {
    private List<DeviceStatDto> deviceStats;
    private List<CrashStatDto> crashStats;
    private List<ProblemDeviceDto> problemDevices;
    private List<String> recommendations;

    @Data
    public static class DeviceStatDto {
        private String brand;
        private String model;
        private String osVersion;
        private Integer userCount;
        private Double crashRate;
        private Double performanceScore;
    }

    @Data
    public static class CrashStatDto {
        private String exceptionType;
        private Integer count;
        private Integer affectedDevices;
        private String trend;
    }

    @Data
    public static class ProblemDeviceDto {
        private String deviceId;
        private String brand;
        private String model;
        private String osVersion;
        private Integer crashCount;
        private String lastCrashTime;
        private List<String> mainIssues;
    }
}
```

## 4. 核心功能实现指南

### 4.1 兼容性分析算法实现

#### 4.1.1 设备兼容性评分算法
```java
@Service
public class CompatibilityAnalysisService {

    /**
     * 计算设备兼容性评分
     */
    public DeviceCompatibilityScore calculateCompatibilityScore(DeviceInfo device, List<CrashInfo> crashes) {
        double baseScore = 100.0;

        // 1. 崩溃率影响 (权重: 40%)
        double crashRate = calculateCrashRate(device.getDeviceId(), crashes);
        double crashPenalty = Math.min(crashRate * 1000, 40); // 最大扣40分

        // 2. 硬件性能影响 (权重: 30%)
        double hardwarePenalty = calculateHardwarePenalty(device);

        // 3. 系统版本影响 (权重: 20%)
        double systemPenalty = calculateSystemPenalty(device);

        // 4. 特殊状态影响 (权重: 10%)
        double specialPenalty = calculateSpecialPenalty(device);

        double finalScore = baseScore - crashPenalty - hardwarePenalty - systemPenalty - specialPenalty;
        return new DeviceCompatibilityScore(
            Math.max(0, Math.min(100, finalScore)),
            crashRate,
            getRiskLevel(finalScore)
        );
    }

    private double calculateCrashRate(String deviceId, List<CrashInfo> crashes) {
        long totalSessions = getTotalSessionCount(deviceId);
        long crashCount = crashes.size();
        return totalSessions > 0 ? (double) crashCount / totalSessions : 0;
    }

    private double calculateHardwarePenalty(DeviceInfo device) {
        double penalty = 0;

        // 内存评估
        long memoryGB = device.getTotalMemory() / (1024 * 1024 * 1024);
        if (memoryGB < 2) penalty += 15;
        else if (memoryGB < 4) penalty += 8;
        else if (memoryGB < 6) penalty += 3;

        // CPU架构评估
        if ("armeabi".equals(device.getCpuAbi())) penalty += 5;

        return penalty;
    }

    private double calculateSystemPenalty(DeviceInfo device) {
        double penalty = 0;

        // SDK版本评估
        if (device.getSdkVersion() < 21) penalty += 15; // Android 5.0以下
        else if (device.getSdkVersion() < 23) penalty += 10; // Android 6.0以下
        else if (device.getSdkVersion() < 26) penalty += 5; // Android 8.0以下

        return penalty;
    }

    private double calculateSpecialPenalty(DeviceInfo device) {
        double penalty = 0;

        if (Boolean.TRUE.equals(device.getIsRooted())) penalty += 5;
        if (Boolean.TRUE.equals(device.getIsEmulator())) penalty += 8;

        return penalty;
    }

    private String getRiskLevel(double score) {
        if (score >= 80) return "LOW";
        else if (score >= 60) return "MEDIUM";
        else if (score >= 40) return "HIGH";
        else return "CRITICAL";
    }
}
```

#### 4.1.2 崩溃模式分析算法
```java
@Service
public class CrashPatternAnalysisService {

    /**
     * 分析崩溃模式
     */
    public CrashPatternAnalysisResult analyzeCrashPatterns(List<CrashInfo> crashes, int days) {
        // 1. 异常类型分布分析
        Map<String, List<CrashInfo>> exceptionGroups = crashes.stream()
            .collect(Collectors.groupBy(CrashInfo::getExceptionType));

        List<ExceptionPattern> exceptionPatterns = exceptionGroups.entrySet().stream()
            .map(entry -> analyzeExceptionPattern(entry.getKey(), entry.getValue()))
            .sorted((a, b) -> Integer.compare(b.getCount(), a.getCount()))
            .collect(Collectors.toList());

        // 2. 时间趋势分析
        TrendAnalysis trendAnalysis = analyzeTrend(crashes, days);

        // 3. 设备相关性分析
        DeviceCorrelationAnalysis deviceAnalysis = analyzeDeviceCorrelation(crashes);

        // 4. 生成分析报告
        return CrashPatternAnalysisResult.builder()
            .totalCrashes(crashes.size())
            .exceptionPatterns(exceptionPatterns)
            .trendAnalysis(trendAnalysis)
            .deviceAnalysis(deviceAnalysis)
            .analysisTime(LocalDateTime.now())
            .build();
    }

    private ExceptionPattern analyzeExceptionPattern(String exceptionType, List<CrashInfo> crashes) {
        // 计算影响的设备数量
        Set<String> affectedDevices = crashes.stream()
            .map(CrashInfo::getDeviceId)
            .collect(Collectors.toSet());

        // 分析趋势
        String trend = calculateTrend(crashes);

        // 识别关联因素
        List<String> correlationFactors = identifyCorrelationFactors(crashes);

        return ExceptionPattern.builder()
            .exceptionType(exceptionType)
            .count(crashes.size())
            .affectedDevices(affectedDevices.size())
            .trend(trend)
            .correlationFactors(correlationFactors)
            .severity(calculateSeverity(exceptionType, crashes.size(), affectedDevices.size()))
            .build();
    }

    private String calculateTrend(List<CrashInfo> crashes) {
        if (crashes.size() < 2) return "STABLE";

        // 按时间排序
        List<CrashInfo> sortedCrashes = crashes.stream()
            .sorted(Comparator.comparing(CrashInfo::getCrashTime))
            .collect(Collectors.toList());

        // 计算最近一半时间的崩溃数量与前一半的比较
        int halfSize = sortedCrashes.size() / 2;
        int recentCount = sortedCrashes.subList(halfSize, sortedCrashes.size()).size();
        int previousCount = sortedCrashes.subList(0, halfSize).size();

        if (recentCount > previousCount * 1.5) return "INCREASING";
        else if (recentCount < previousCount * 0.5) return "DECREASING";
        else return "STABLE";
    }

    private List<String> identifyCorrelationFactors(List<CrashInfo> crashes) {
        List<String> factors = new ArrayList<>();

        // 分析内存相关性
        long memoryRelated = crashes.stream()
            .filter(crash -> crash.getExceptionType().toLowerCase().contains("memory") ||
                           crash.getExceptionType().toLowerCase().contains("outofmemory"))
            .count();

        if (memoryRelated > crashes.size() * 0.7) {
            factors.add("内存不足");
        }

        // 分析网络相关性
        long networkRelated = crashes.stream()
            .filter(crash -> crash.getExceptionType().toLowerCase().contains("network") ||
                           crash.getExceptionType().toLowerCase().contains("socket") ||
                           crash.getExceptionType().toLowerCase().contains("connect"))
            .count();

        if (networkRelated > crashes.size() * 0.7) {
            factors.add("网络异常");
        }

        // 分析应用状态相关性
        long backgroundCrashes = crashes.stream()
            .filter(crash -> CrashInfo.AppState.BACKGROUND.equals(crash.getAppState()))
            .count();

        if (backgroundCrashes > crashes.size() * 0.8) {
            factors.add("后台运行");
        }

        return factors;
    }
}
```

### 4.2 数据统计和聚合服务

#### 4.2.1 设备统计服务
```java
@Service
public class DeviceStatisticsService {

    @Autowired
    private DeviceInfoRepository deviceInfoRepository;

    @Autowired
    private CrashInfoRepository crashInfoRepository;

    /**
     * 生成设备统计报告
     */
    public DeviceStatisticsReport generateDeviceStatistics(StatisticsQuery query) {
        // 1. 基础设备分布统计
        List<DeviceDistribution> brandDistribution = getBrandDistribution(query);
        List<DeviceDistribution> modelDistribution = getModelDistribution(query);
        List<DeviceDistribution> osDistribution = getOsDistribution(query);

        // 2. 性能等级分布
        Map<String, Integer> performanceLevelDistribution = getPerformanceLevelDistribution(query);

        // 3. 问题设备识别
        List<ProblemDevice> problemDevices = identifyProblemDevices(query);

        // 4. 趋势分析
        List<DeviceTrend> trends = analyzeDeviceTrends(query);

        return DeviceStatisticsReport.builder()
            .brandDistribution(brandDistribution)
            .modelDistribution(modelDistribution)
            .osDistribution(osDistribution)
            .performanceLevelDistribution(performanceLevelDistribution)
            .problemDevices(problemDevices)
            .trends(trends)
            .totalDevices(deviceInfoRepository.countByQuery(query))
            .reportTime(LocalDateTime.now())
            .build();
    }

    private List<DeviceDistribution> getBrandDistribution(StatisticsQuery query) {
        return deviceInfoRepository.findBrandDistribution(query.getStartTime(), query.getEndTime())
            .stream()
            .map(result -> DeviceDistribution.builder()
                .name((String) result[0])
                .count(((Number) result[1]).intValue())
                .percentage(calculatePercentage(((Number) result[1]).intValue(), query))
                .build())
            .collect(Collectors.toList());
    }

    private List<ProblemDevice> identifyProblemDevices(StatisticsQuery query) {
        // 查询崩溃率高的设备
        List<Object[]> highCrashRateDevices = crashInfoRepository.findHighCrashRateDevices(
            query.getStartTime(), query.getEndTime(), 0.05); // 崩溃率超过5%

        return highCrashRateDevices.stream()
            .map(result -> ProblemDevice.builder()
                .deviceId((String) result[0])
                .brand((String) result[1])
                .model((String) result[2])
                .crashCount(((Number) result[3]).intValue())
                .crashRate(((Number) result[4]).doubleValue())
                .riskLevel(calculateRiskLevel(((Number) result[4]).doubleValue()))
                .build())
            .collect(Collectors.toList());
    }
}
```

### 4.3 日志配置动态管理

#### 4.3.1 配置管理服务
```java
@Service
public class LogConfigService {

    @Autowired
    private LogConfigRepository logConfigRepository;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CONFIG_CACHE_KEY = "log_config:";
    private static final int CACHE_EXPIRE_HOURS = 24;

    /**
     * 获取设备适用的日志配置
     */
    public LogConfig getApplicableConfig(String deviceId, String appVersion, String brand, String model) {
        // 1. 尝试从缓存获取
        String cacheKey = CONFIG_CACHE_KEY + deviceId;
        LogConfig cachedConfig = (LogConfig) redisTemplate.opsForValue().get(cacheKey);

        if (cachedConfig != null && isConfigValid(cachedConfig)) {
            return cachedConfig;
        }

        // 2. 从数据库查询最适合的配置
        LogConfig config = findBestMatchConfig(deviceId, appVersion, brand, model);

        // 3. 缓存配置
        if (config != null) {
            redisTemplate.opsForValue().set(cacheKey, config, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
        }

        return config != null ? config : getDefaultConfig();
    }

    private LogConfig findBestMatchConfig(String deviceId, String appVersion, String brand, String model) {
        LocalDateTime now = LocalDateTime.now();

        // 1. 查找设备特定配置
        LogConfig deviceSpecificConfig = logConfigRepository.findDeviceSpecificConfig(
            deviceId, now);
        if (deviceSpecificConfig != null) {
            return deviceSpecificConfig;
        }

        // 2. 查找品牌型号特定配置
        LogConfig modelSpecificConfig = logConfigRepository.findModelSpecificConfig(
            brand, model, now);
        if (modelSpecificConfig != null) {
            return modelSpecificConfig;
        }

        // 3. 查找品牌特定配置
        LogConfig brandSpecificConfig = logConfigRepository.findBrandSpecificConfig(
            brand, now);
        if (brandSpecificConfig != null) {
            return brandSpecificConfig;
        }

        // 4. 返回默认配置
        return logConfigRepository.findDefaultConfig(now);
    }

    /**
     * 动态更新配置
     */
    @Transactional
    public void updateConfig(LogConfig newConfig) {
        // 1. 保存新配置
        logConfigRepository.save(newConfig);

        // 2. 清除相关缓存
        clearConfigCache(newConfig);

        // 3. 发送配置更新通知
        sendConfigUpdateNotification(newConfig);

        // 4. 记录配置变更日志
        logConfigChange(newConfig);
    }

    private void clearConfigCache(LogConfig config) {
        // 根据配置的目标设备清除缓存
        if (config.getTargetDevices() != null) {
            // 解析目标设备JSON，清除特定设备缓存
            // 实现略...
        } else {
            // 清除所有配置缓存
            Set<String> keys = redisTemplate.keys(CONFIG_CACHE_KEY + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
        }
    }
}
```

### 4.4 定时任务配置

#### 4.4.1 数据清理任务
```java
@Component
public class DataCleanupTask {

    @Autowired
    private LogEntryRepository logEntryRepository;

    @Autowired
    private CrashInfoRepository crashInfoRepository;

    @Autowired
    private DeviceInfoRepository deviceInfoRepository;

    /**
     * 每天凌晨2点执行数据清理
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredData() {
        log.info("开始执行数据清理任务");

        try {
            // 1. 清理过期日志数据
            cleanupExpiredLogs();

            // 2. 清理过期崩溃数据
            cleanupExpiredCrashes();

            // 3. 清理过期设备数据
            cleanupExpiredDevices();

            // 4. 清理过期分析结果
            cleanupExpiredAnalysis();

            log.info("数据清理任务执行完成");

        } catch (Exception e) {
            log.error("数据清理任务执行失败", e);
        }
    }

    private void cleanupExpiredLogs() {
        int retentionDays = getRetentionDays("log.retention.days", 30);
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);

        int deletedCount = logEntryRepository.deleteByCreateTimeBefore(cutoffTime);
        log.info("清理过期日志数据: {} 条", deletedCount);
    }

    private void cleanupExpiredCrashes() {
        int retentionDays = getRetentionDays("crash.retention.days", 90);
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);

        int deletedCount = crashInfoRepository.deleteByCreateTimeBefore(cutoffTime);
        log.info("清理过期崩溃数据: {} 条", deletedCount);
    }

    private void cleanupExpiredDevices() {
        int retentionDays = getRetentionDays("device.retention.days", 365);
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);

        int deletedCount = deviceInfoRepository.deleteByLastUpdateTimeBefore(cutoffTime);
        log.info("清理过期设备数据: {} 条", deletedCount);
    }
}
```

#### 4.4.2 自动分析任务
```java
@Component
public class AutoAnalysisTask {

    @Autowired
    private CompatibilityAnalysisService compatibilityAnalysisService;

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 每天凌晨3点执行兼容性分析
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void performDailyAnalysis() {
        log.info("开始执行每日兼容性分析");

        try {
            // 1. 执行设备兼容性分析
            performDeviceCompatibilityAnalysis();

            // 2. 执行崩溃模式分析
            performCrashPatternAnalysis();

            // 3. 生成统计报告
            generateDailyStatistics();

            // 4. 检查异常趋势
            checkAbnormalTrends();

            log.info("每日兼容性分析执行完成");

        } catch (Exception e) {
            log.error("每日兼容性分析执行失败", e);
        }
    }

    private void performDeviceCompatibilityAnalysis() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        CompatibilityAnalysisResult result = compatibilityAnalysisService
            .analyzeCompatibility(yesterday, yesterday);

        // 保存分析结果
        compatibilityAnalysisService.saveAnalysisResult(result);

        // 检查是否有新的问题设备
        if (!result.getProblemDevices().isEmpty()) {
            sendProblemDeviceAlert(result.getProblemDevices());
        }
    }

    private void checkAbnormalTrends() {
        // 检查崩溃率异常增长
        List<CrashTrend> trends = statisticsService.getCrashTrends(7); // 最近7天

        for (CrashTrend trend : trends) {
            if (trend.getGrowthRate() > 0.5) { // 增长率超过50%
                sendCrashRateAlert(trend);
            }
        }
    }
}
```

## 5. 安全和性能要求

### 5.1 API认证授权机制

#### 5.1.1 JWT Token验证
```java
@Component
public class JwtTokenProvider {

    private static final String SECRET_KEY = "your-secret-key";
    private static final long TOKEN_VALIDITY = 24 * 60 * 60 * 1000; // 24小时

    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("role", userDetails.getAuthorities().iterator().next().getAuthority());
        return createToken(claims, userDetails.getUsername());
    }

    private String createToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
            .setClaims(claims)
            .setSubject(subject)
            .setIssuedAt(new Date(System.currentTimeMillis()))
            .setExpiration(new Date(System.currentTimeMillis() + TOKEN_VALIDITY))
            .signWith(SignatureAlgorithm.HS512, SECRET_KEY)
            .compact();
    }

    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = getUsernameFromToken(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }

    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token).getBody();
    }

    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }
}
```

#### 5.1.2 Security配置
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    private JwtRequestFilter jwtRequestFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/log-config/get").hasAnyRole("ENGINEER", "ADMIN")
                .requestMatchers("/api/log/upload").hasAnyRole("ENGINEER", "ADMIN")
                .requestMatchers("/api/device/upload-info").hasAnyRole("ENGINEER", "ADMIN")
                .requestMatchers("/api/crash/upload").hasAnyRole("ENGINEER", "ADMIN")
                .requestMatchers("/api/device/compatibility-analysis").hasAnyRole("ADMIN", "VIEWER")
                .requestMatchers("/api/crash/statistics").hasAnyRole("ADMIN", "VIEWER")
                .anyRequest().authenticated()
            )
            .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint)
            .and()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        http.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

### 5.2 数据验证和安全过滤

#### 5.2.1 请求参数验证
```java
@RestController
@RequestMapping("/api/log")
@Validated
public class LogController {

    @PostMapping("/upload")
    public ApiResponse<LogUploadResult> uploadLogs(
            @Valid @RequestBody LogUploadRequest request,
            @RequestHeader("X-Auth-Token") String token) {

        // 参数验证
        validateUploadRequest(request);

        // 数据清洗和过滤
        List<LogEntry> cleanedLogs = sanitizeLogData(request.getLogs());

        // 执行上传逻辑
        LogUploadResult result = logService.uploadLogs(cleanedLogs);

        return ApiResponse.success(result);
    }

    private void validateUploadRequest(LogUploadRequest request) {
        if (request.getLogs() == null || request.getLogs().isEmpty()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "日志数据不能为空");
        }

        if (request.getLogs().size() > 100) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "单次上传日志数量不能超过100条");
        }

        // 验证设备ID格式
        if (!isValidDeviceId(request.getDeviceId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "设备ID格式不正确");
        }
    }

    private List<LogEntry> sanitizeLogData(List<LogUploadRequest.LogEntryDto> logs) {
        return logs.stream()
            .map(this::sanitizeLogEntry)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private LogEntry sanitizeLogEntry(LogUploadRequest.LogEntryDto dto) {
        // 过滤敏感信息
        String sanitizedMessage = filterSensitiveInfo(dto.getMessage());
        String sanitizedExtraData = filterSensitiveInfo(dto.getExtraData());

        // 验证数据长度
        if (sanitizedMessage.length() > 1000) {
            sanitizedMessage = sanitizedMessage.substring(0, 1000) + "...";
        }

        return LogEntry.builder()
            .logType(dto.getLogType())
            .level(dto.getLevel())
            .timestamp(parseTimestamp(dto.getTimestamp()))
            .tag(dto.getTag())
            .message(sanitizedMessage)
            .extraData(sanitizedExtraData)
            .deviceId(dto.getDeviceId())
            .brand(dto.getBrand())
            .model(dto.getModel())
            .osType(dto.getOsType())
            .osVersion(dto.getOsVersion())
            .appVersion(dto.getAppVersion())
            .sdkVersion(dto.getSdkVersion())
            .sessionId(dto.getSessionId())
            .build();
    }

    private String filterSensitiveInfo(String content) {
        if (content == null) return null;

        // 过滤密码
        content = content.replaceAll("(?i)password[\"']?\\s*[:=]\\s*[\"']?[^\\s,}\"']+", "password:***");

        // 过滤Token
        content = content.replaceAll("(?i)token[\"']?\\s*[:=]\\s*[\"']?[^\\s,}\"']+", "token:***");

        // 过滤手机号
        content = content.replaceAll("1[3-9]\\d{9}", "***********");

        // 过滤身份证号
        content = content.replaceAll("\\d{17}[\\dXx]", "******************");

        return content;
    }
}
```

### 5.3 数据库查询优化

#### 5.3.1 Repository查询优化
```java
@Repository
public interface CrashInfoRepository extends JpaRepository<CrashInfo, Long> {

    /**
     * 使用索引优化的崩溃统计查询
     */
    @Query(value = """
        SELECT ci.exception_type, COUNT(*) as count,
               COUNT(DISTINCT ci.device_id) as affected_devices
        FROM crash_info ci
        WHERE ci.crash_time BETWEEN :startTime AND :endTime
        GROUP BY ci.exception_type
        ORDER BY count DESC
        LIMIT :limit
        """, nativeQuery = true)
    List<Object[]> findTopExceptionTypes(
        @Param("startTime") long startTime,
        @Param("endTime") long endTime,
        @Param("limit") int limit
    );

    /**
     * 分页查询设备崩溃统计
     */
    @Query(value = """
        SELECT ci.device_id, di.brand, di.model, di.os_version,
               COUNT(*) as crash_count,
               MAX(ci.crash_time) as last_crash_time
        FROM crash_info ci
        LEFT JOIN device_info di ON ci.device_id = di.device_id
        WHERE ci.crash_time >= :startTime
        GROUP BY ci.device_id, di.brand, di.model, di.os_version
        HAVING crash_count >= :minCrashCount
        ORDER BY crash_count DESC
        """,
        countQuery = """
        SELECT COUNT(DISTINCT ci.device_id)
        FROM crash_info ci
        WHERE ci.crash_time >= :startTime
        GROUP BY ci.device_id
        HAVING COUNT(*) >= :minCrashCount
        """,
        nativeQuery = true)
    Page<Object[]> findDeviceCrashStatistics(
        @Param("startTime") long startTime,
        @Param("minCrashCount") int minCrashCount,
        Pageable pageable
    );

    /**
     * 批量删除过期数据
     */
    @Modifying
    @Query("DELETE FROM CrashInfo c WHERE c.createTime < :cutoffTime")
    int deleteByCreateTimeBefore(@Param("cutoffTime") LocalDateTime cutoffTime);
}
```

#### 5.3.2 数据库连接池配置
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: SpringBootJPAHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000
```

### 5.4 缓存策略

#### 5.4.1 Redis缓存配置
```java
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LazyLoadingAspectJWeaver.class, ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(mapper);

        template.setValueSerializer(serializer);
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);
        template.afterPropertiesSet();

        return template;
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1)) // 默认1小时过期
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));

        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .build();
    }
}
```

#### 5.4.2 缓存使用示例
```java
@Service
public class DeviceInfoService {

    @Cacheable(value = "deviceInfo", key = "#deviceId", unless = "#result == null")
    public DeviceInfo getDeviceInfo(String deviceId) {
        return deviceInfoRepository.findById(deviceId).orElse(null);
    }

    @CacheEvict(value = "deviceInfo", key = "#deviceInfo.deviceId")
    public DeviceInfo updateDeviceInfo(DeviceInfo deviceInfo) {
        return deviceInfoRepository.save(deviceInfo);
    }

    @Cacheable(value = "deviceStats", key = "'brand:' + #brand + ':model:' + #model")
    public DeviceStatistics getDeviceStatistics(String brand, String model) {
        return calculateDeviceStatistics(brand, model);
    }
}
```

### 5.5 异步处理方案

#### 5.5.1 异步配置
```java
@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean(name = "analysisExecutor")
    public Executor analysisExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("analysis-");
        executor.initialize();
        return executor;
    }
}
```

#### 5.5.2 异步服务实现
```java
@Service
public class AsyncAnalysisService {

    @Async("analysisExecutor")
    public CompletableFuture<CompatibilityAnalysisResult> performAsyncAnalysis(
            List<DeviceInfo> devices, List<CrashInfo> crashes) {

        try {
            // 执行耗时的兼容性分析
            CompatibilityAnalysisResult result = performDetailedAnalysis(devices, crashes);

            // 保存分析结果
            saveAnalysisResult(result);

            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            log.error("异步分析执行失败", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    @Async("taskExecutor")
    public void processLogUploadAsync(List<LogEntry> logs) {
        try {
            // 异步处理日志上传
            batchInsertLogs(logs);

            // 触发实时分析
            triggerRealTimeAnalysis(logs);

        } catch (Exception e) {
            log.error("异步日志处理失败", e);
        }
    }
}
```

## 6. 部署和运维规范

### 6.1 生产环境配置

#### 6.1.1 应用配置文件
```yaml
# application-prod.yml
server:
  port: 8080
  servlet:
    context-path: /log-control
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain

spring:
  profiles:
    active: prod

  datasource:
    url: *************************************************************************************************************
    username: ${DB_USERNAME:log_user}
    password: ${DB_PASSWORD:your_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      auto-commit: true
      idle-timeout: 30000
      pool-name: LogControlHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      leak-detection-threshold: 60000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        use_sql_comments: false
        jdbc:
          batch_size: 50
          order_inserts: true
          order_updates: true

  redis:
    host: ${REDIS_HOST:redis-server}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:your_redis_password}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 5

  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时

# 日志配置
logging:
  level:
    com.logcontrol: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/log-control/application.log
    max-size: 100MB
    max-history: 30

# 应用配置
app:
  jwt:
    secret: ${JWT_SECRET:your-jwt-secret-key}
    expiration: 86400000 # 24小时

  upload:
    max-batch-size: 100
    max-file-size: 10MB

  analysis:
    enable-auto-analysis: true
    analysis-interval: 3600000 # 1小时

  cleanup:
    log-retention-days: 30
    crash-retention-days: 90
    device-retention-days: 365

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
```

#### 6.1.2 Docker配置
```dockerfile
# Dockerfile
FROM openjdk:11-jre-slim

LABEL maintainer="log-control-team"

# 创建应用目录
RUN mkdir -p /app/logs

# 复制应用文件
COPY target/log-control-backend-*.jar /app/app.jar

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/log-control/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar"]

# 暴露端口
EXPOSE 8080
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  log-control-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_USERNAME=log_user
      - DB_PASSWORD=your_password
      - REDIS_HOST=redis
      - REDIS_PASSWORD=your_redis_password
      - JWT_SECRET=your-jwt-secret-key
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/var/log/log-control
    restart: unless-stopped
    networks:
      - log-control-network

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=log_control_db
      - MYSQL_USER=log_user
      - MYSQL_PASSWORD=your_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - log-control-network

  redis:
    image: redis:6.2-alpine
    command: redis-server --requirepass your_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - log-control-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - log-control-app
    restart: unless-stopped
    networks:
      - log-control-network

volumes:
  mysql_data:
  redis_data:

networks:
  log-control-network:
    driver: bridge
```

### 6.2 监控指标和日志记录

#### 6.2.1 自定义监控指标
```java
@Component
public class CustomMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter logUploadCounter;
    private final Counter crashUploadCounter;
    private final Timer analysisTimer;
    private final Gauge activeDevicesGauge;

    public CustomMetrics(MeterRegistry meterRegistry, DeviceInfoRepository deviceInfoRepository) {
        this.meterRegistry = meterRegistry;

        // 日志上传计数器
        this.logUploadCounter = Counter.builder("log.upload.count")
            .description("日志上传总数")
            .tag("type", "log")
            .register(meterRegistry);

        // 崩溃上传计数器
        this.crashUploadCounter = Counter.builder("crash.upload.count")
            .description("崩溃上传总数")
            .tag("type", "crash")
            .register(meterRegistry);

        // 分析耗时计时器
        this.analysisTimer = Timer.builder("analysis.duration")
            .description("兼容性分析耗时")
            .register(meterRegistry);

        // 活跃设备数量
        this.activeDevicesGauge = Gauge.builder("devices.active.count")
            .description("活跃设备数量")
            .register(meterRegistry, this, CustomMetrics::getActiveDeviceCount);
    }

    public void incrementLogUpload(int count) {
        logUploadCounter.increment(count);
    }

    public void incrementCrashUpload(int count) {
        crashUploadCounter.increment(count);
    }

    public Timer.Sample startAnalysisTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordAnalysisTime(Timer.Sample sample) {
        sample.stop(analysisTimer);
    }

    private double getActiveDeviceCount() {
        // 获取最近24小时活跃的设备数量
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        return deviceInfoRepository.countByLastUpdateTimeAfter(yesterday);
    }
}
```

#### 6.2.2 业务日志记录
```java
@Component
public class BusinessLogger {

    private static final Logger businessLog = LoggerFactory.getLogger("BUSINESS");
    private static final Logger securityLog = LoggerFactory.getLogger("SECURITY");
    private static final Logger performanceLog = LoggerFactory.getLogger("PERFORMANCE");

    public void logUploadEvent(String deviceId, String logType, int count, boolean success) {
        businessLog.info("UPLOAD_EVENT device={} type={} count={} success={}",
            deviceId, logType, count, success);
    }

    public void logAnalysisEvent(String analysisType, long duration, int deviceCount, int issueCount) {
        businessLog.info("ANALYSIS_EVENT type={} duration={}ms devices={} issues={}",
            analysisType, duration, deviceCount, issueCount);
    }

    public void logSecurityEvent(String event, String userId, String ip, boolean success) {
        securityLog.warn("SECURITY_EVENT event={} user={} ip={} success={}",
            event, userId, ip, success);
    }

    public void logPerformanceEvent(String operation, long duration, String details) {
        if (duration > 5000) { // 超过5秒记录性能日志
            performanceLog.warn("SLOW_OPERATION operation={} duration={}ms details={}",
                operation, duration, details);
        }
    }

    public void logConfigChange(String configName, String oldValue, String newValue, String operator) {
        businessLog.info("CONFIG_CHANGE config={} old={} new={} operator={}",
            configName, oldValue, newValue, operator);
    }
}
```

### 6.3 数据备份和恢复策略

#### 6.3.1 数据库备份脚本
```bash
#!/bin/bash
# backup.sh

# 配置参数
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="log_control_db"
DB_USER="backup_user"
DB_PASSWORD="backup_password"
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/log_control_backup_$DATE.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --hex-blob \
  --opt \
  $DB_NAME > $BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

# 记录备份日志
echo "$(date): 数据库备份完成 - $BACKUP_FILE.gz" >> /var/log/backup.log

# 验证备份文件
if [ -f "$BACKUP_FILE.gz" ]; then
    echo "备份成功: $BACKUP_FILE.gz"
    exit 0
else
    echo "备份失败"
    exit 1
fi
```

#### 6.3.2 数据恢复脚本
```bash
#!/bin/bash
# restore.sh

if [ $# -ne 1 ]; then
    echo "使用方法: $0 <backup_file.sql.gz>"
    exit 1
fi

BACKUP_FILE=$1
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="log_control_db"
DB_USER="restore_user"
DB_PASSWORD="restore_password"

# 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
    echo "备份文件不存在: $BACKUP_FILE"
    exit 1
fi

# 解压备份文件
echo "正在解压备份文件..."
gunzip -c $BACKUP_FILE > /tmp/restore.sql

# 确认恢复操作
echo "警告: 此操作将覆盖现有数据库 $DB_NAME"
read -p "确认继续? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "恢复操作已取消"
    rm -f /tmp/restore.sql
    exit 1
fi

# 执行恢复
echo "正在恢复数据库..."
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME < /tmp/restore.sql

if [ $? -eq 0 ]; then
    echo "数据库恢复成功"
    echo "$(date): 数据库恢复成功 - $BACKUP_FILE" >> /var/log/restore.log
else
    echo "数据库恢复失败"
    echo "$(date): 数据库恢复失败 - $BACKUP_FILE" >> /var/log/restore.log
    exit 1
fi

# 清理临时文件
rm -f /tmp/restore.sql
```

### 6.4 运维监控脚本

#### 6.4.1 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

APP_URL="http://localhost:8080/log-control/actuator/health"
LOG_FILE="/var/log/health_check.log"
EMAIL_ALERT="<EMAIL>"

# 检查应用健康状态
check_app_health() {
    response=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL)

    if [ "$response" = "200" ]; then
        echo "$(date): 应用健康检查通过" >> $LOG_FILE
        return 0
    else
        echo "$(date): 应用健康检查失败 - HTTP $response" >> $LOG_FILE
        return 1
    fi
}

# 检查数据库连接
check_database() {
    mysql -h$DB_HOST -u$DB_USER -p$DB_PASSWORD -e "SELECT 1" $DB_NAME > /dev/null 2>&1

    if [ $? -eq 0 ]; then
        echo "$(date): 数据库连接正常" >> $LOG_FILE
        return 0
    else
        echo "$(date): 数据库连接失败" >> $LOG_FILE
        return 1
    fi
}

# 检查Redis连接
check_redis() {
    redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD ping > /dev/null 2>&1

    if [ $? -eq 0 ]; then
        echo "$(date): Redis连接正常" >> $LOG_FILE
        return 0
    else
        echo "$(date): Redis连接失败" >> $LOG_FILE
        return 1
    fi
}

# 发送告警邮件
send_alert() {
    local message=$1
    echo "$message" | mail -s "日志控制系统告警" $EMAIL_ALERT
}

# 主检查流程
main() {
    local failed_checks=""

    if ! check_app_health; then
        failed_checks="$failed_checks 应用健康检查"
    fi

    if ! check_database; then
        failed_checks="$failed_checks 数据库连接"
    fi

    if ! check_redis; then
        failed_checks="$failed_checks Redis连接"
    fi

    if [ -n "$failed_checks" ]; then
        send_alert "日志控制系统检查失败:$failed_checks"
        exit 1
    else
        echo "$(date): 所有健康检查通过" >> $LOG_FILE
        exit 0
    fi
}

main
```

#### 6.4.2 性能监控脚本
```bash
#!/bin/bash
# performance_monitor.sh

METRICS_URL="http://localhost:8080/log-control/actuator/metrics"
LOG_FILE="/var/log/performance.log"
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=85

# 获取JVM内存使用率
get_memory_usage() {
    local used=$(curl -s "$METRICS_URL/jvm.memory.used" | jq '.measurements[0].value')
    local max=$(curl -s "$METRICS_URL/jvm.memory.max" | jq '.measurements[0].value')

    if [ "$max" != "null" ] && [ "$max" != "0" ]; then
        echo "scale=2; $used * 100 / $max" | bc
    else
        echo "0"
    fi
}

# 获取CPU使用率
get_cpu_usage() {
    local cpu=$(curl -s "$METRICS_URL/process.cpu.usage" | jq '.measurements[0].value')
    echo "scale=2; $cpu * 100" | bc
}

# 获取活跃线程数
get_thread_count() {
    curl -s "$METRICS_URL/jvm.threads.live" | jq '.measurements[0].value'
}

# 监控主函数
monitor() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local memory_usage=$(get_memory_usage)
    local cpu_usage=$(get_cpu_usage)
    local thread_count=$(get_thread_count)

    # 记录性能指标
    echo "$timestamp,Memory:${memory_usage}%,CPU:${cpu_usage}%,Threads:$thread_count" >> $LOG_FILE

    # 检查告警阈值
    if (( $(echo "$memory_usage > $ALERT_THRESHOLD_MEMORY" | bc -l) )); then
        echo "内存使用率告警: ${memory_usage}%" | mail -s "性能告警" <EMAIL>
    fi

    if (( $(echo "$cpu_usage > $ALERT_THRESHOLD_CPU" | bc -l) )); then
        echo "CPU使用率告警: ${cpu_usage}%" | mail -s "性能告警" <EMAIL>
    fi
}

monitor
```

## 7. 开发和部署检查清单

### 7.1 开发阶段检查清单
- [ ] 所有API接口已实现并通过测试
- [ ] 数据库表结构与Android端数据模型匹配
- [ ] JWT认证机制正常工作
- [ ] 数据验证和安全过滤已实现
- [ ] 缓存策略已配置并测试
- [ ] 异步处理功能正常
- [ ] 定时任务配置正确
- [ ] 单元测试覆盖率达到80%以上
- [ ] 集成测试通过
- [ ] 性能测试满足要求

### 7.2 部署阶段检查清单
- [ ] 生产环境配置文件已准备
- [ ] 数据库已创建并初始化
- [ ] Redis缓存服务已配置
- [ ] SSL证书已配置
- [ ] 防火墙规则已设置
- [ ] 监控系统已部署
- [ ] 日志收集已配置
- [ ] 备份策略已实施
- [ ] 健康检查脚本已部署
- [ ] 告警机制已配置

### 7.3 上线后检查清单
- [ ] 应用启动正常
- [ ] 数据库连接正常
- [ ] Redis连接正常
- [ ] API接口响应正常
- [ ] 监控指标正常
- [ ] 日志输出正常
- [ ] 与Android端联调成功
- [ ] 数据上传功能正常
- [ ] 兼容性分析功能正常
- [ ] 定时任务执行正常

通过以上详细的开发规范说明文档，后端开发团队可以完整地实现与Android端对应的Java Spring Boot后端系统，确保系统的安全性、性能和可维护性。
```
```
```
