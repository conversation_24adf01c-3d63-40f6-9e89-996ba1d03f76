# 日志上报功能 - userName字段支持说明

## 📋 **功能概述**

为了方便后续的查询和统计展示，在日志上报功能中增加了用户姓名（userName）字段支持。

## 🔧 **实现详情**

### 1. **数据库表结构**

所有相关表都已包含`user_name`字段：

```sql
-- b_log_entry表
ALTER TABLE b_log_entry ADD COLUMN user_name varchar(64) DEFAULT NULL COMMENT '用户姓名（冗余字段，便于查询统计）';

-- b_device_info表  
ALTER TABLE b_device_info ADD COLUMN user_name varchar(64) DEFAULT NULL COMMENT '用户姓名（冗余字段，便于查询统计）';

-- b_crash_info表
ALTER TABLE b_crash_info ADD COLUMN user_name varchar(64) DEFAULT NULL COMMENT '用户姓名（冗余字段，便于查询统计）';
```

### 2. **前端数据格式**

#### Android Kotlin示例
```kotlin
data class LogUploadRequest(
    val deviceId: String,
    val userId: String,
    val userCode: String,
    @SerializedName("userName")
    val userName: String = "",  // 用户姓名字段
    val appVersion: String,
    val logs: List<LogEntry>
)

data class LogEntry(
    val userId: String,
    val userCode: String,
    @SerializedName("userName")
    val userName: String = "",  // 每条日志包含用户姓名
    val logType: String,
    val level: String,
    val timestamp: String,
    val tag: String,
    val message: String,
    val extraData: String?
)
```

### 3. **API接口格式**

#### 日志上传接口
```json
POST /logcontrol/log/batch-upload
{
  "deviceId": "android_device_001",
  "userId": "101",
  "userCode": "sa",
  "userName": "超级管理员",        // 用户姓名字段
  "appVersion": "1.0.0",
  "logs": [
    {
      "userId": "101",
      "userCode": "sa", 
      "userName": "超级管理员",    // 每条日志包含用户姓名
      "logType": "BUSINESS",
      "level": "INFO",
      "timestamp": "2025-01-21T10:30:00",
      "tag": "MainActivity",
      "message": "用户登录成功"
    }
  ]
}
```

#### 设备信息上传接口
```json
POST /logcontrol/device/upload
{
  "deviceId": "android_device_001",
  "userId": "101",
  "userCode": "sa",
  "userName": "超级管理员",        // 用户姓名字段
  "brand": "Huawei",
  "model": "Mate 40 Pro"
}
```

#### 崩溃信息上传接口
```json
POST /logcontrol/crash/upload
{
  "deviceId": "android_device_001",
  "userId": "101",
  "userCode": "sa",
  "userName": "超级管理员",        // 用户姓名字段
  "crashTime": 1642752600000,
  "exceptionType": "java.lang.NullPointerException",
  "exceptionMessage": "Attempt to invoke virtual method on null object"
}
```

## 📊 **查询和统计优势**

### 1. **直接查询用户姓名**
```sql
-- 无需关联查询，直接获取用户姓名
SELECT user_name, COUNT(*) as log_count 
FROM b_log_entry 
WHERE log_type = 'BUSINESS' 
GROUP BY user_name 
ORDER BY log_count DESC;
```

### 2. **统计报表展示**
```sql
-- 用户日志统计（包含姓名）
SELECT 
    user_name,
    COUNT(CASE WHEN log_type = 'BUSINESS' THEN 1 END) as business_logs,
    COUNT(CASE WHEN log_type = 'ERROR' THEN 1 END) as error_logs,
    COUNT(CASE WHEN log_type = 'PERFORMANCE' THEN 1 END) as performance_logs,
    COUNT(*) as total_logs
FROM b_log_entry 
WHERE user_name IS NOT NULL
GROUP BY user_name
ORDER BY total_logs DESC;
```

### 3. **设备使用统计**
```sql
-- 按用户姓名统计设备使用情况
SELECT 
    d.user_name,
    d.brand,
    d.model,
    COUNT(l.id) as log_count,
    MAX(l.timestamp) as last_active_time
FROM b_device_info d
LEFT JOIN b_log_entry l ON d.device_id = l.device_id
WHERE d.user_name IS NOT NULL
GROUP BY d.user_name, d.device_id
ORDER BY d.user_name, log_count DESC;
```

## 🔄 **数据一致性处理**

### 1. **自动填充机制**
当前端没有提供userName时，后端会自动从用户表获取：

```java
// DeviceInfoController中的处理逻辑
if (userCode == null || userCode.trim().isEmpty()) {
    UserIdValidationService.UserBasicInfo userInfo = userIdValidationService.getUserBasicInfo(userId);
    if (userInfo != null) {
        deviceInfo.setUserCode(userInfo.getCode());
        deviceInfo.setUserName(userInfo.getName());  // 自动填充用户姓名
    }
}
```

### 2. **数据同步更新**
```sql
-- 批量更新现有数据的用户姓名
UPDATE b_log_entry 
SET user_name = (SELECT name FROM st_user_basic WHERE id = b_log_entry.user_id) 
WHERE user_id IS NOT NULL AND user_name IS NULL;

UPDATE b_device_info 
SET user_name = (SELECT name FROM st_user_basic WHERE id = b_device_info.user_id) 
WHERE user_id IS NOT NULL AND user_name IS NULL;

UPDATE b_crash_info 
SET user_name = (SELECT name FROM st_user_basic WHERE id = b_crash_info.user_id) 
WHERE user_id IS NOT NULL AND user_name IS NULL;
```

## ✅ **使用建议**

### 1. **前端开发建议**
- 在所有日志上传请求中包含`userName`字段
- 使用`@SerializedName("userName")`注解确保字段名一致
- 建议在用户登录后缓存用户姓名，避免重复获取

### 2. **后端处理建议**
- 优先使用前端传递的`userName`
- 当前端未提供时，自动从用户表获取
- 定期检查和更新数据一致性

### 3. **查询优化建议**
- 利用`user_name`字段避免频繁的关联查询
- 在统计报表中直接使用`user_name`提高性能
- 建立适当的索引支持按用户姓名查询

## 🎯 **应用场景**

### 1. **管理后台展示**
- 日志列表直接显示用户姓名
- 统计图表按用户姓名分组
- 问题排查时快速识别用户

### 2. **数据分析**
- 用户行为分析报告
- 用户活跃度统计
- 问题用户识别

### 3. **运营支持**
- 用户使用情况报告
- 设备分布统计
- 用户反馈关联

通过添加`userName`字段，日志上报功能在保持数据完整性的同时，大大提升了查询和统计展示的便利性。
