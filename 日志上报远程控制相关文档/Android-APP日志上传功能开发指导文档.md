# Android APP 日志上传功能开发指导文档

## 📋 文档概述

本文档基于实际后端代码和数据库结构，为Android开发人员提供完整的日志上传功能开发指导，包括API接口、数据结构、实现示例和最佳实践。

## 📚 目录

1. [API接口详情](#-api接口详情)
   - [日志上传接口](#1-日志上传接口)
   - [其他相关接口](#2-其他相关接口)
   - [设备信息上传接口](#3-设备信息上传接口)
   - [崩溃信息上传接口](#4-崩溃信息上传接口)
   - [日志配置获取接口](#5-日志配置获取接口)

2. [数据库表结构](#-数据库表结构)
   - [b_log_entry 表字段说明](#blog_entry-表字段说明)

3. [Android端实现示例](#-android端实现示例)
   - [数据模型定义](#1-数据模型定义)
   - [API接口定义](#2-api接口定义)
   - [网络请求实现](#3-网络请求实现)
   - [日志收集器实现](#4-日志收集器实现)

4. [字段映射关系](#-字段映射关系)

5. [错误处理](#-错误处理)

6. [最佳实践建议](#-最佳实践建议)

7. [日志类型和级别规范](#-日志类型和级别规范)

8. [实际使用示例](#-实际使用示例)

9. [完整实现架构](#-完整实现架构)

10. [安全和隐私考虑](#-安全和隐私考虑)

11. [监控和统计](#-监控和统计)

12. [测试指导](#-测试指导)

13. [完整使用示例](#-完整使用示例)

## 🚀 快速开始

### 1. 添加依赖
```gradle
// build.gradle (Module: app)
dependencies {
    // 网络请求
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.3'

    // 协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'

    // 数据库
    implementation 'androidx.room:room-runtime:2.4.3'
    implementation 'androidx.room:room-ktx:2.4.3'
    kapt 'androidx.room:room-compiler:2.4.3'

    // 后台任务
    implementation 'androidx.work:work-runtime-ktx:2.8.1'

    // JSON序列化
    implementation 'com.google.code.gson:gson:2.10.1'
}
```

### 2. 权限配置
```xml
<!-- AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

### 3. 基础配置
```kotlin
// 在Application中初始化
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        // 初始化日志系统
        LogCollector.getInstance(this)

        // 启动后台上传任务
        LogUploadWorker.scheduleWork(this)
    }
}
```

### 4. 基本使用
```kotlin
// 收集日志
LogCollector.getInstance(context).collectLog(
    logType = "BUSINESS",
    level = "INFO",
    tag = "MainActivity",
    message = "用户操作",
    extraData = mapOf("action" to "click")
)

// 手动上传日志
GlobalScope.launch {
    LogCollector.getInstance(context).uploadPendingLogs()
}
```

## 🔗 API接口详情

### 1. 日志上传接口

**接口地址**：`POST /logcontrol/log/upload`

**功能说明**：批量上传日志数据到服务器

**限流规则**：每分钟最多20次调用

**请求头**：
```http
Content-Type: application/json
```

**请求体结构**：
```json
{
  "deviceId": "string",      // 必填：设备唯一标识
  "userId": "string",        // 可选：用户ID（String格式避免精度丢失）
  "userCode": "string",      // 可选：用户编码
  "userName": "string",      // 可选：用户姓名
  "appVersion": "string",    // 必填：应用版本号
  "logs": [                  // 必填：日志条目数组
    {
      "logType": "string",     // 必填：日志类型
      "level": "string",       // 必填：日志级别
      "timestamp": "2025-01-22T10:30:00", // 必填：日志时间（ISO格式）
      "tag": "string",         // 可选：日志标签
      "message": "string",     // 必填：日志消息
      "extraData": "string",   // 可选：额外数据（JSON字符串）
      "userId": "string",      // 可选：用户ID
      "userCode": "string",    // 可选：用户编码
      "userName": "string"     // 可选：用户姓名
    }
  ]
}
```

**响应格式**：
```json
{
  "code": 200,
  "message": "ok",
  "data": null
}
```

### 2. 其他相关接口

#### 2.1 获取日志列表
**接口地址**：`GET /logcontrol/log/list`

**请求参数**：
- `deviceId` (可选): 设备ID
- `logType` (可选): 日志类型

#### 2.2 获取未上传日志数量
**接口地址**：`GET /logcontrol/log/unuploaded-count`

#### 2.3 标记日志为已上传
**接口地址**：`POST /logcontrol/log/mark-uploaded`

**请求体**：
```json
[1, 2, 3, 4, 5]  // 日志ID数组
```

### 3. 设备信息上传接口

**接口地址**：`POST /logcontrol/device/upload`

**功能说明**：上传设备信息，建立设备与用户的关联关系

**请求体结构**：
```json
{
  "deviceId": "string",        // 必填：设备唯一标识
  "userId": "string",          // 可选：用户ID
  "userCode": "string",        // 可选：用户编码
  "userName": "string",        // 可选：用户姓名
  "brand": "string",           // 必填：设备品牌
  "model": "string",           // 必填：设备型号
  "manufacturer": "string",    // 可选：制造商
  "osVersion": "string",       // 必填：系统版本
  "osType": "string",          // 可选：操作系统类型
  "appVersion": "string",      // 必填：应用版本
  "totalMemory": 8589934592,   // 可选：总内存(字节)
  "availableStorage": 1073741824, // 可选：可用存储(字节)
  "isRooted": false,           // 可选：是否Root
  "isEmulator": false,         // 可选：是否模拟器
  "permissionsInfo": "string", // 可选：权限信息JSON
  "currentConfigVersion": "string", // 可选：当前日志配置版本
  "currentConfigDetails": "string"  // 可选：当前日志配置详情JSON
}
```

### 4. 崩溃信息上传接口

**接口地址**：`POST /logcontrol/crash/upload`

**功能说明**：上传应用崩溃信息

**请求体结构**：
```json
{
  "crashes": [                 // 崩溃信息数组，支持批量上传
    {
      "deviceId": "string",      // 必填：设备ID
      "userId": "string",        // 可选：用户ID
      "userCode": "string",      // 可选：用户编码
      "userName": "string",      // 可选：用户姓名
      "crashTime": "2025-01-22T10:30:00", // 必填：崩溃时间
      "exceptionType": "string", // 必填：异常类型
      "exceptionMessage": "string", // 可选：异常消息
      "stackTrace": "string",    // 可选：堆栈跟踪
      "threadName": "string",    // 可选：线程名称
      "appState": "string",      // 可选：应用状态
      "memoryUsage": 1073741824, // 可选：内存使用量
      "availableMemory": 2147483648, // 可选：可用内存
      "batteryLevel": 85,        // 可选：电池电量
      "isCharging": false,       // 可选：是否充电
      "networkStatus": "WIFI",   // 可选：网络状态
      "lastActivity": "string",  // 可选：最后活动
      "userActions": "string",   // 可选：用户操作记录
      "customData": "string",    // 可选：自定义数据
      "appVersion": "string"     // 可选：应用版本
    }
  ]
}
```

### 5. 日志配置获取接口

**接口地址**：`GET /logcontrol/config/get`

**功能说明**：获取设备当前应使用的日志配置

**请求头**：
```http
X-Device-Id: device123    // 可选：设备ID
X-User-Id: user001       // 可选：用户ID
X-App-Version: 1.0.0     // 可选：应用版本
```

**响应格式**：
```json
{
  "code": 200,
  "message": "ok",
  "data": {
    "id": 1,
    "configName": "default",
    "logLevel": "INFO",
    "enableLocationLog": true,
    "locationLogInterval": 3000,
    "logUploadInterval": 3600,
    "maxLogFiles": 5,
    "configVersion": "1.0.0",
    "isActive": true
  }
}
```

## 📊 数据库表结构

### b_log_entry 表字段说明

| 字段名 | 数据类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| id | bigint(20) | 自动生成 | 主键ID |
| device_id | varchar(100) | 必填 | 设备唯一标识 |
| user_id | bigint(20) | 可选 | 用户ID |
| user_code | varchar(64) | 可选 | 用户编码 |
| user_name | varchar(64) | 可选 | 用户姓名 |
| log_type | varchar(20) | 必填 | 日志类型 |
| level | varchar(10) | 必填 | 日志级别 |
| timestamp | datetime | 必填 | 日志时间 |
| tag | varchar(100) | 可选 | 日志标签 |
| message | text | 可选 | 日志消息 |
| extra_data | json | 可选 | 额外数据 |
| app_version | varchar(20) | 可选 | 应用版本 |
| is_uploaded | tinyint(1) | 默认0 | 是否已上传 |
| deleted | tinyint(1) | 默认0 | 逻辑删除标记 |
| create_by | bigint(20) | 可选 | 创建人 |
| create_at | timestamp | 自动生成 | 创建时间 |

### b_device_info 表字段说明（设备信息表）

| 字段名 | 数据类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| id | bigint(20) | 自动生成 | 主键ID |
| device_id | varchar(100) | 必填 | 设备唯一标识 |
| user_id | bigint(20) | 可选 | 用户ID |
| user_code | varchar(64) | 可选 | 用户编码 |
| user_name | varchar(64) | 可选 | 用户姓名 |
| brand | varchar(50) | 必填 | 设备品牌 |
| model | varchar(100) | 必填 | 设备型号 |
| manufacturer | varchar(100) | 可选 | 制造商 |
| os_version | varchar(50) | 必填 | 系统版本 |
| os_type | varchar(20) | 可选 | 操作系统类型 |
| app_version | varchar(20) | 必填 | 应用版本 |
| total_memory | bigint(20) | 可选 | 总内存大小(字节) |
| available_storage | bigint(20) | 可选 | 可用存储空间(字节) |
| is_rooted | tinyint(1) | 可选 | 是否Root |
| is_emulator | tinyint(1) | 可选 | 是否模拟器 |
| permissions_info | json | 可选 | 权限信息JSON |
| **current_config_version** | **varchar(20)** | **可选** | **当前日志配置版本** |
| **current_config_details** | **json** | **可选** | **当前日志配置详情JSON** |
| deleted | tinyint(1) | 默认0 | 逻辑删除标记 |
| create_at | timestamp | 自动生成 | 创建时间 |
| update_at | timestamp | 自动更新 | 更新时间 |

#### 配置详情JSON格式示例
```json
{
  "configId": 1,
  "configName": "default",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 3000,
  "logUploadInterval": 3600,
  "maxLogFiles": 5
}
```

## 📱 Android端实现示例

### 1. 数据模型定义

#### LogUploadRequest.kt
```kotlin
data class LogUploadRequest(
    @SerializedName("deviceId")
    val deviceId: String,
    
    @SerializedName("userId")
    val userId: String? = null,
    
    @SerializedName("userCode")
    val userCode: String? = null,
    
    @SerializedName("userName")
    val userName: String? = null,
    
    @SerializedName("appVersion")
    val appVersion: String,
    
    @SerializedName("logs")
    val logs: List<LogEntry>
)

data class LogEntry(
    @SerializedName("logType")
    val logType: String,
    
    @SerializedName("level")
    val level: String,
    
    @SerializedName("timestamp")
    val timestamp: String,
    
    @SerializedName("tag")
    val tag: String? = null,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("extraData")
    val extraData: String? = null,
    
    @SerializedName("userId")
    val userId: String? = null,
    
    @SerializedName("userCode")
    val userCode: String? = null,
    
    @SerializedName("userName")
    val userName: String? = null
)
```

#### ApiResponse.kt
```kotlin
data class ApiResponse<T>(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: T?
)
```

### 2. API接口定义

#### LogUploadApi.kt
```kotlin
interface LogUploadApi {

    @POST("logcontrol/log/upload")
    suspend fun uploadLogs(@Body request: LogUploadRequest): Response<ApiResponse<Void>>

    @GET("logcontrol/log/list")
    suspend fun getLogList(
        @Query("deviceId") deviceId: String? = null,
        @Query("logType") logType: String? = null
    ): Response<ApiResponse<List<LogEntry>>>

    @GET("logcontrol/log/unuploaded-count")
    suspend fun getUnuploadedCount(): Response<ApiResponse<Long>>

    @POST("logcontrol/log/mark-uploaded")
    suspend fun markAsUploaded(@Body ids: List<Long>): Response<ApiResponse<Int>>

    // 设备信息上传
    @POST("logcontrol/device/upload")
    suspend fun uploadDeviceInfo(@Body request: DeviceInfoRequest): Response<ApiResponse<Void>>

    // 崩溃信息上传
    @POST("logcontrol/crash/upload")
    suspend fun uploadCrashInfo(@Body request: CrashUploadRequest): Response<ApiResponse<Void>>

    // 获取日志配置
    @GET("logcontrol/config/get")
    suspend fun getLogConfig(
        @Header("X-Device-Id") deviceId: String? = null,
        @Header("X-User-Id") userId: String? = null,
        @Header("X-App-Version") appVersion: String? = null
    ): Response<ApiResponse<LogConfig>>
}

// 设备信息请求
data class DeviceInfoRequest(
    @SerializedName("deviceId")
    val deviceId: String,

    @SerializedName("userId")
    val userId: String? = null,

    @SerializedName("userCode")
    val userCode: String? = null,

    @SerializedName("userName")
    val userName: String? = null,

    @SerializedName("brand")
    val brand: String,

    @SerializedName("model")
    val model: String,

    @SerializedName("manufacturer")
    val manufacturer: String? = null,

    @SerializedName("osVersion")
    val osVersion: String,

    @SerializedName("osType")
    val osType: String? = "Android",

    @SerializedName("appVersion")
    val appVersion: String,

    @SerializedName("totalMemory")
    val totalMemory: Long? = null,

    @SerializedName("availableStorage")
    val availableStorage: Long? = null,

    @SerializedName("isRooted")
    val isRooted: Boolean? = null,

    @SerializedName("isEmulator")
    val isEmulator: Boolean? = null,

    @SerializedName("permissionsInfo")
    val permissionsInfo: String? = null,

    @SerializedName("currentConfigVersion")
    val currentConfigVersion: String? = null,

    @SerializedName("currentConfigDetails")
    val currentConfigDetails: String? = null
)

// 崩溃信息上传请求
data class CrashUploadRequest(
    @SerializedName("crashes")
    val crashes: List<CrashInfo>
)

data class CrashInfo(
    @SerializedName("deviceId")
    val deviceId: String,

    @SerializedName("userId")
    val userId: String? = null,

    @SerializedName("userCode")
    val userCode: String? = null,

    @SerializedName("userName")
    val userName: String? = null,

    @SerializedName("crashTime")
    val crashTime: String,

    @SerializedName("exceptionType")
    val exceptionType: String,

    @SerializedName("exceptionMessage")
    val exceptionMessage: String? = null,

    @SerializedName("stackTrace")
    val stackTrace: String? = null,

    @SerializedName("threadName")
    val threadName: String? = null,

    @SerializedName("appState")
    val appState: String? = null,

    @SerializedName("memoryUsage")
    val memoryUsage: Long? = null,

    @SerializedName("availableMemory")
    val availableMemory: Long? = null,

    @SerializedName("batteryLevel")
    val batteryLevel: Int? = null,

    @SerializedName("isCharging")
    val isCharging: Boolean? = null,

    @SerializedName("networkStatus")
    val networkStatus: String? = null,

    @SerializedName("lastActivity")
    val lastActivity: String? = null,

    @SerializedName("userActions")
    val userActions: String? = null,

    @SerializedName("customData")
    val customData: String? = null,

    @SerializedName("appVersion")
    val appVersion: String? = null
)

// 日志配置响应
data class LogConfig(
    @SerializedName("id")
    val id: Long,

    @SerializedName("configName")
    val configName: String,

    @SerializedName("logLevel")
    val logLevel: String,

    @SerializedName("enableLocationLog")
    val enableLocationLog: Boolean,

    @SerializedName("locationLogInterval")
    val locationLogInterval: Int,

    @SerializedName("logUploadInterval")
    val logUploadInterval: Int,

    @SerializedName("maxLogFiles")
    val maxLogFiles: Int,

    @SerializedName("configVersion")
    val configVersion: String,

    @SerializedName("isActive")
    val isActive: Boolean
)
```

### 3. 网络请求实现

#### LogUploadService.kt
```kotlin
class LogUploadService(private val api: LogUploadApi) {
    
    companion object {
        private const val TAG = "LogUploadService"
    }
    
    /**
     * 上传日志
     */
    suspend fun uploadLogs(
        deviceId: String,
        appVersion: String,
        logs: List<LogEntry>,
        userId: String? = null,
        userCode: String? = null,
        userName: String? = null
    ): Result<Boolean> {
        return try {
            val request = LogUploadRequest(
                deviceId = deviceId,
                userId = userId,
                userCode = userCode,
                userName = userName,
                appVersion = appVersion,
                logs = logs
            )
            
            val response = api.uploadLogs(request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.code == 200) {
                    Log.i(TAG, "日志上传成功，数量: ${logs.size}")
                    Result.success(true)
                } else {
                    Log.e(TAG, "日志上传失败: ${body?.message}")
                    Result.failure(Exception("上传失败: ${body?.message}"))
                }
            } else {
                Log.e(TAG, "网络请求失败: ${response.code()}")
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "上传日志异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量上传日志（分批处理）
     */
    suspend fun batchUploadLogs(
        deviceId: String,
        appVersion: String,
        logs: List<LogEntry>,
        batchSize: Int = 50,
        userId: String? = null,
        userCode: String? = null,
        userName: String? = null
    ): Result<Int> {
        return try {
            var successCount = 0
            val batches = logs.chunked(batchSize)
            
            for (batch in batches) {
                val result = uploadLogs(deviceId, appVersion, batch, userId, userCode, userName)
                if (result.isSuccess) {
                    successCount += batch.size
                } else {
                    Log.w(TAG, "批次上传失败: ${result.exceptionOrNull()?.message}")
                }
                
                // 批次间延迟，避免频繁请求
                delay(100)
            }
            
            Log.i(TAG, "批量上传完成，成功: $successCount/${logs.size}")
            Result.success(successCount)
        } catch (e: Exception) {
            Log.e(TAG, "批量上传异常", e)
            Result.failure(e)
        }
    }

    /**
     * 上传设备信息
     */
    suspend fun uploadDeviceInfo(deviceInfo: DeviceInfoRequest): Result<Boolean> {
        return try {
            val response = api.uploadDeviceInfo(deviceInfo)

            if (response.isSuccessful) {
                val body = response.body()
                if (body?.code == 200) {
                    Log.i(TAG, "设备信息上传成功")
                    Result.success(true)
                } else {
                    Log.e(TAG, "设备信息上传失败: ${body?.message}")
                    Result.failure(Exception("上传失败: ${body?.message}"))
                }
            } else {
                Log.e(TAG, "设备信息上传网络请求失败: ${response.code()}")
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "上传设备信息异常", e)
            Result.failure(e)
        }
    }

    /**
     * 上传崩溃信息
     */
    suspend fun uploadCrashInfo(crashes: List<CrashInfo>): Result<Boolean> {
        return try {
            val request = CrashUploadRequest(crashes)
            val response = api.uploadCrashInfo(request)

            if (response.isSuccessful) {
                val body = response.body()
                if (body?.code == 200) {
                    Log.i(TAG, "崩溃信息上传成功，数量: ${crashes.size}")
                    Result.success(true)
                } else {
                    Log.e(TAG, "崩溃信息上传失败: ${body?.message}")
                    Result.failure(Exception("上传失败: ${body?.message}"))
                }
            } else {
                Log.e(TAG, "崩溃信息上传网络请求失败: ${response.code()}")
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "上传崩溃信息异常", e)
            Result.failure(e)
        }
    }

    /**
     * 获取日志配置
     */
    suspend fun getLogConfig(
        deviceId: String,
        userId: String? = null,
        appVersion: String? = null
    ): Result<LogConfig> {
        return try {
            val response = api.getLogConfig(deviceId, userId, appVersion)

            if (response.isSuccessful) {
                val body = response.body()
                if (body?.code == 200 && body.data != null) {
                    Log.i(TAG, "获取日志配置成功")
                    Result.success(body.data)
                } else {
                    Log.e(TAG, "获取日志配置失败: ${body?.message}")
                    Result.failure(Exception("获取配置失败: ${body?.message}"))
                }
            } else {
                Log.e(TAG, "获取日志配置网络请求失败: ${response.code()}")
                Result.failure(Exception("网络请求失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取日志配置异常", e)
            Result.failure(e)
        }
    }
}
```

### 4. 日志收集器实现

#### LogCollector.kt
```kotlin
class LogCollector private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: LogCollector? = null
        
        fun getInstance(context: Context): LogCollector {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LogCollector(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val logDatabase = LogDatabase.getInstance(context)
    private val uploadService = LogUploadService(RetrofitClient.logUploadApi)
    
    /**
     * 收集日志
     */
    fun collectLog(
        logType: String,
        level: String,
        tag: String,
        message: String,
        extraData: Map<String, Any>? = null
    ) {
        try {
            val logEntry = LogEntry(
                logType = logType,
                level = level,
                timestamp = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault()).format(Date()),
                tag = tag,
                message = message,
                extraData = extraData?.let { Gson().toJson(it) },
                userId = UserManager.getCurrentUserId(),
                userCode = UserManager.getCurrentUserCode(),
                userName = UserManager.getCurrentUserName()
            )
            
            // 存储到本地数据库
            GlobalScope.launch(Dispatchers.IO) {
                logDatabase.logDao().insertLog(logEntry.toEntity())
            }
            
        } catch (e: Exception) {
            Log.e("LogCollector", "收集日志失败", e)
        }
    }
    
    /**
     * 上传未上传的日志
     */
    suspend fun uploadPendingLogs(): Boolean {
        return try {
            val pendingLogs = logDatabase.logDao().getPendingLogs()
            if (pendingLogs.isEmpty()) {
                return true
            }
            
            val result = uploadService.batchUploadLogs(
                deviceId = DeviceUtils.getDeviceId(context),
                appVersion = BuildConfig.VERSION_NAME,
                logs = pendingLogs.map { it.toLogEntry() },
                userId = UserManager.getCurrentUserId(),
                userCode = UserManager.getCurrentUserCode(),
                userName = UserManager.getCurrentUserName()
            )
            
            if (result.isSuccess) {
                // 标记为已上传
                val ids = pendingLogs.map { it.id }
                logDatabase.logDao().markAsUploaded(ids)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e("LogCollector", "上传日志失败", e)
            false
        }
    }
}
```

## 🔧 字段映射关系

### 前端请求字段 → 数据库字段映射

| 前端字段 | 数据库字段 | 数据类型转换 | 说明 |
|----------|------------|--------------|------|
| deviceId | device_id | String → varchar(100) | 设备唯一标识 |
| userId | user_id | String → bigint(20) | 用户ID转换为Long |
| userCode | user_code | String → varchar(64) | 用户编码 |
| userName | user_name | String → varchar(64) | 用户姓名 |
| appVersion | app_version | String → varchar(20) | 应用版本 |
| logs[].logType | log_type | String → varchar(20) | 日志类型 |
| logs[].level | level | String → varchar(10) | 日志级别 |
| logs[].timestamp | timestamp | String → datetime | ISO时间格式转换 |
| logs[].tag | tag | String → varchar(100) | 日志标签 |
| logs[].message | message | String → text | 日志消息 |
| logs[].extraData | extra_data | String → json | JSON字符串转换 |

### 设备信息字段映射

| 前端字段 | 数据库字段 | 数据类型转换 | 说明 |
|----------|------------|--------------|------|
| deviceId | device_id | String → varchar(100) | 设备唯一标识 |
| userId | user_id | String → bigint(20) | 用户ID转换为Long |
| userCode | user_code | String → varchar(64) | 用户编码 |
| userName | user_name | String → varchar(64) | 用户姓名 |
| brand | brand | String → varchar(50) | 设备品牌 |
| model | model | String → varchar(100) | 设备型号 |
| manufacturer | manufacturer | String → varchar(100) | 制造商 |
| osVersion | os_version | String → varchar(50) | 系统版本 |
| osType | os_type | String → varchar(20) | 操作系统类型 |
| appVersion | app_version | String → varchar(20) | 应用版本 |
| totalMemory | total_memory | Long → bigint(20) | 总内存大小 |
| availableStorage | available_storage | Long → bigint(20) | 可用存储空间 |
| isRooted | is_rooted | Boolean → tinyint(1) | 是否Root |
| isEmulator | is_emulator | Boolean → tinyint(1) | 是否模拟器 |
| permissionsInfo | permissions_info | String → json | 权限信息JSON |
| **currentConfigVersion** | **current_config_version** | **String → varchar(20)** | **当前配置版本** |
| **currentConfigDetails** | **current_config_details** | **String → json** | **配置详情JSON** |

## ⚠️ 错误处理

### 常见错误码

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | 正常处理 |
| 400 | 请求参数错误 | 检查请求参数格式 |
| 429 | 请求频率超限 | 实施退避重试策略 |
| 500 | 服务器内部错误 | 记录错误日志，稍后重试 |

### 错误处理示例

```kotlin
suspend fun handleUploadError(error: Throwable): Boolean {
    return when (error) {
        is HttpException -> {
            when (error.code()) {
                429 -> {
                    // 频率限制，延迟重试
                    delay(60000) // 等待1分钟
                    true // 可以重试
                }
                400 -> {
                    // 参数错误，不重试
                    Log.e("Upload", "请求参数错误: ${error.message()}")
                    false
                }
                else -> {
                    // 其他HTTP错误，可以重试
                    Log.e("Upload", "HTTP错误: ${error.code()}")
                    true
                }
            }
        }
        is IOException -> {
            // 网络错误，可以重试
            Log.e("Upload", "网络错误", error)
            true
        }
        else -> {
            // 其他错误，不重试
            Log.e("Upload", "未知错误", error)
            false
        }
    }
}
```

## 💡 最佳实践建议

### 1. 批量上传策略
- **批次大小**：建议每批50-100条日志
- **上传频率**：避免过于频繁，建议间隔至少1分钟
- **网络状态**：仅在WiFi或良好的移动网络下上传

### 2. 重试机制
```kotlin
class RetryUploadManager {
    private val maxRetries = 3
    private val baseDelay = 1000L // 1秒
    
    suspend fun uploadWithRetry(uploadAction: suspend () -> Result<Boolean>): Boolean {
        repeat(maxRetries) { attempt ->
            try {
                val result = uploadAction()
                if (result.isSuccess) {
                    return true
                }
                
                // 指数退避
                val delay = baseDelay * (1 shl attempt)
                delay(delay)
            } catch (e: Exception) {
                if (attempt == maxRetries - 1) {
                    Log.e("RetryUpload", "重试失败，放弃上传", e)
                    return false
                }
                
                val delay = baseDelay * (1 shl attempt)
                delay(delay)
            }
        }
        return false
    }
}
```

### 3. 数据验证
```kotlin
fun validateLogEntry(entry: LogEntry): Boolean {
    return entry.logType.isNotBlank() &&
           entry.level.isNotBlank() &&
           entry.message.isNotBlank() &&
           entry.timestamp.isNotBlank() &&
           entry.logType.length <= 20 &&
           entry.level.length <= 10 &&
           (entry.tag?.length ?: 0) <= 100
}
```

### 4. 本地存储管理
- **数据库清理**：定期清理已上传的旧日志
- **存储限制**：限制本地日志数量，避免占用过多存储空间
- **数据压缩**：对大量日志数据进行压缩存储

### 5. 性能优化
- **异步处理**：所有日志操作都在后台线程执行
- **内存管理**：及时释放大对象，避免内存泄漏
- **网络优化**：使用连接池，复用网络连接

## 📞 技术支持

如有开发过程中的技术问题，请参考：
- 后端API文档：查看完整的接口规范
- 数据库设计文档：了解数据存储结构
- 错误日志：记录详细的错误信息便于排查

## 🗂️ 日志类型和级别规范

### 日志类型 (logType)
基于实际数据库数据，支持以下日志类型：

| 类型 | 说明 | 使用场景 |
|------|------|----------|
| LOCATION | 位置日志 | GPS定位、位置更新 |
| BUSINESS | 业务日志 | 用户操作、业务流程 |
| CRASH | 崩溃日志 | 应用崩溃、异常 |
| TEST | 测试日志 | 测试环境、调试信息 |
| SYSTEM | 系统日志 | 系统事件、配置变更 |

### 日志级别 (level)
| 级别 | 说明 | 使用场景 |
|------|------|----------|
| DEBUG | 调试信息 | 开发调试 |
| INFO | 一般信息 | 正常流程记录 |
| WARN | 警告信息 | 潜在问题 |
| ERROR | 错误信息 | 异常和错误 |

## 📝 实际使用示例

### 1. 位置日志上传示例
```kotlin
// 收集位置日志
fun logLocationUpdate(latitude: Double, longitude: Double, accuracy: Float) {
    val extraData = mapOf(
        "latitude" to latitude,
        "longitude" to longitude,
        "accuracy" to accuracy,
        "provider" to "GPS"
    )

    LogCollector.getInstance(context).collectLog(
        logType = "LOCATION",
        level = "INFO",
        tag = "LocationService",
        message = "位置更新成功",
        extraData = extraData
    )
}
```

### 2. 业务操作日志示例
```kotlin
// 用户登录日志
fun logUserLogin(userId: String, loginMethod: String) {
    val extraData = mapOf(
        "loginMethod" to loginMethod,
        "deviceInfo" to Build.MODEL,
        "osVersion" to Build.VERSION.RELEASE
    )

    LogCollector.getInstance(context).collectLog(
        logType = "BUSINESS",
        level = "INFO",
        tag = "LoginActivity",
        message = "用户登录成功",
        extraData = extraData
    )
}
```

### 3. 错误日志示例
```kotlin
// 网络请求失败日志
fun logNetworkError(url: String, errorCode: Int, errorMessage: String) {
    val extraData = mapOf(
        "url" to url,
        "errorCode" to errorCode,
        "networkType" to getNetworkType(),
        "timestamp" to System.currentTimeMillis()
    )

    LogCollector.getInstance(context).collectLog(
        logType = "BUSINESS",
        level = "ERROR",
        tag = "NetworkManager",
        message = "网络请求失败: $errorMessage",
        extraData = extraData
    )
}
```

## 🏗️ 完整实现架构

### 1. 本地数据库设计

#### LogEntity.kt
```kotlin
@Entity(tableName = "logs")
data class LogEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,

    val logType: String,
    val level: String,
    val timestamp: String,
    val tag: String?,
    val message: String,
    val extraData: String?,
    val userId: String?,
    val userCode: String?,
    val userName: String?,
    val isUploaded: Boolean = false,
    val createTime: Long = System.currentTimeMillis()
)
```

#### LogDao.kt
```kotlin
@Dao
interface LogDao {

    @Insert
    suspend fun insertLog(log: LogEntity): Long

    @Insert
    suspend fun insertLogs(logs: List<LogEntity>): List<Long>

    @Query("SELECT * FROM logs WHERE isUploaded = 0 ORDER BY createTime ASC LIMIT :limit")
    suspend fun getPendingLogs(limit: Int = 1000): List<LogEntity>

    @Query("UPDATE logs SET isUploaded = 1 WHERE id IN (:ids)")
    suspend fun markAsUploaded(ids: List<Long>): Int

    @Query("DELETE FROM logs WHERE isUploaded = 1 AND createTime < :beforeTime")
    suspend fun deleteUploadedLogs(beforeTime: Long): Int

    @Query("SELECT COUNT(*) FROM logs WHERE isUploaded = 0")
    suspend fun getPendingCount(): Int
}
```

### 2. 网络配置

#### RetrofitClient.kt
```kotlin
object RetrofitClient {

    private const val BASE_URL = "https://your-api-domain.com/"

    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .addInterceptor(LoggingInterceptor())
        .addInterceptor(RetryInterceptor())
        .build()

    private val retrofit = Retrofit.Builder()
        .baseUrl(BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()

    val logUploadApi: LogUploadApi = retrofit.create(LogUploadApi::class.java)
}
```

### 3. 后台上传服务

#### LogUploadWorker.kt
```kotlin
class LogUploadWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        const val WORK_NAME = "log_upload_work"

        fun scheduleWork(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(true)
                .build()

            val workRequest = PeriodicWorkRequestBuilder<LogUploadWorker>(
                15, TimeUnit.MINUTES
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    WORK_NAME,
                    ExistingPeriodicWorkPolicy.KEEP,
                    workRequest
                )
        }
    }

    override suspend fun doWork(): Result {
        return try {
            val logCollector = LogCollector.getInstance(applicationContext)
            val success = logCollector.uploadPendingLogs()

            if (success) {
                Log.i("LogUploadWorker", "日志上传成功")
                Result.success()
            } else {
                Log.w("LogUploadWorker", "日志上传失败，将重试")
                Result.retry()
            }
        } catch (e: Exception) {
            Log.e("LogUploadWorker", "日志上传异常", e)
            Result.failure()
        }
    }
}
```

## 🔒 安全和隐私考虑

### 1. 数据脱敏
```kotlin
fun sanitizeLogData(message: String, extraData: String?): Pair<String, String?> {
    // 移除敏感信息
    val sanitizedMessage = message
        .replace(Regex("password=\\w+"), "password=***")
        .replace(Regex("token=\\w+"), "token=***")
        .replace(Regex("\\d{11}"), "***") // 手机号脱敏

    val sanitizedExtraData = extraData?.let { data ->
        try {
            val json = JSONObject(data)
            // 移除敏感字段
            json.remove("password")
            json.remove("token")
            json.remove("privateKey")
            json.toString()
        } catch (e: Exception) {
            data
        }
    }

    return Pair(sanitizedMessage, sanitizedExtraData)
}
```

### 2. 数据加密
```kotlin
class LogEncryption {

    fun encryptSensitiveData(data: String): String {
        // 实现数据加密逻辑
        return Base64.encodeToString(data.toByteArray(), Base64.DEFAULT)
    }

    fun decryptSensitiveData(encryptedData: String): String {
        // 实现数据解密逻辑
        return String(Base64.decode(encryptedData, Base64.DEFAULT))
    }
}
```

## 📈 监控和统计

### 1. 上传统计
```kotlin
class LogUploadStatistics {

    private val prefs = context.getSharedPreferences("log_stats", Context.MODE_PRIVATE)

    fun recordUploadSuccess(count: Int) {
        val totalUploaded = prefs.getLong("total_uploaded", 0L) + count
        val lastUploadTime = System.currentTimeMillis()

        prefs.edit()
            .putLong("total_uploaded", totalUploaded)
            .putLong("last_upload_time", lastUploadTime)
            .apply()
    }

    fun recordUploadFailure(reason: String) {
        val failureCount = prefs.getInt("failure_count", 0) + 1
        val lastFailureTime = System.currentTimeMillis()

        prefs.edit()
            .putInt("failure_count", failureCount)
            .putLong("last_failure_time", lastFailureTime)
            .putString("last_failure_reason", reason)
            .apply()
    }

    fun getUploadStatistics(): Map<String, Any> {
        return mapOf(
            "totalUploaded" to prefs.getLong("total_uploaded", 0L),
            "failureCount" to prefs.getInt("failure_count", 0),
            "lastUploadTime" to prefs.getLong("last_upload_time", 0L),
            "lastFailureTime" to prefs.getLong("last_failure_time", 0L),
            "lastFailureReason" to prefs.getString("last_failure_reason", "")
        )
    }
}
```

## 🧪 测试指导

### 1. 单元测试示例
```kotlin
@Test
fun testLogUploadRequest() {
    val logEntry = LogEntry(
        logType = "TEST",
        level = "INFO",
        timestamp = "2025-01-22T10:30:00",
        tag = "UnitTest",
        message = "测试日志",
        extraData = "{\"test\": true}"
    )

    val request = LogUploadRequest(
        deviceId = "test_device_001",
        appVersion = "1.0.0",
        logs = listOf(logEntry)
    )

    // 验证请求数据
    assertEquals("test_device_001", request.deviceId)
    assertEquals("1.0.0", request.appVersion)
    assertEquals(1, request.logs.size)
    assertEquals("TEST", request.logs[0].logType)
}
```

### 2. 集成测试
```kotlin
@Test
fun testLogUploadIntegration() = runTest {
    val mockApi = mockk<LogUploadApi>()
    val service = LogUploadService(mockApi)

    // Mock API响应
    coEvery { mockApi.uploadLogs(any()) } returns Response.success(
        ApiResponse(200, "ok", null)
    )

    // 执行上传
    val result = service.uploadLogs(
        deviceId = "test_device",
        appVersion = "1.0.0",
        logs = listOf(createTestLogEntry())
    )

    // 验证结果
    assertTrue(result.isSuccess)
    coVerify { mockApi.uploadLogs(any()) }
}
```

## 🚀 完整使用示例

### 1. 应用启动时的初始化流程

```kotlin
class MyApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        // 初始化日志系统
        initLogSystem()

        // 上传设备信息
        uploadDeviceInfo()

        // 获取日志配置
        fetchLogConfig()

        // 启动后台上传任务
        LogUploadWorker.scheduleWork(this)
    }

    private fun initLogSystem() {
        // 初始化日志收集器
        LogCollector.getInstance(this)

        // 设置全局异常处理器
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            // 收集崩溃信息
            collectCrashInfo(thread, exception)

            // 调用系统默认处理器
            val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
            defaultHandler?.uncaughtException(thread, exception)
        }
    }

    private fun uploadDeviceInfo() {
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val deviceInfo = DeviceInfoRequest(
                    deviceId = DeviceUtils.getDeviceId(this@MyApplication),
                    userId = UserManager.getCurrentUserId(),
                    userCode = UserManager.getCurrentUserCode(),
                    userName = UserManager.getCurrentUserName(),
                    brand = Build.BRAND,
                    model = Build.MODEL,
                    manufacturer = Build.MANUFACTURER,
                    osVersion = Build.VERSION.RELEASE,
                    osType = "Android",
                    appVersion = BuildConfig.VERSION_NAME,
                    totalMemory = DeviceUtils.getTotalMemory(),
                    availableStorage = DeviceUtils.getAvailableStorage(),
                    isRooted = DeviceUtils.isRooted(),
                    isEmulator = DeviceUtils.isEmulator(),
                    permissionsInfo = PermissionUtils.getPermissionsJson(),
                    currentConfigVersion = LogConfigManager.getCurrentConfigVersion(),
                    currentConfigDetails = LogConfigManager.getCurrentConfigDetailsJson()
                )

                val uploadService = LogUploadService(RetrofitClient.logUploadApi)
                val result = uploadService.uploadDeviceInfo(deviceInfo)

                if (result.isSuccess) {
                    Log.i("MyApplication", "设备信息上传成功")
                } else {
                    Log.e("MyApplication", "设备信息上传失败: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e("MyApplication", "上传设备信息异常", e)
            }
        }
    }

    private fun fetchLogConfig() {
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val uploadService = LogUploadService(RetrofitClient.logUploadApi)
                val result = uploadService.getLogConfig(
                    deviceId = DeviceUtils.getDeviceId(this@MyApplication),
                    userId = UserManager.getCurrentUserId(),
                    appVersion = BuildConfig.VERSION_NAME
                )

                if (result.isSuccess) {
                    val config = result.getOrNull()
                    config?.let {
                        // 应用日志配置
                        LogConfigManager.getInstance(this@MyApplication).applyConfig(it)
                        Log.i("MyApplication", "日志配置获取成功: ${it.configVersion}")
                    }
                } else {
                    Log.e("MyApplication", "获取日志配置失败: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e("MyApplication", "获取日志配置异常", e)
            }
        }
    }

    private fun collectCrashInfo(thread: Thread, exception: Throwable) {
        try {
            val crashInfo = CrashInfo(
                deviceId = DeviceUtils.getDeviceId(this),
                userId = UserManager.getCurrentUserId(),
                userCode = UserManager.getCurrentUserCode(),
                userName = UserManager.getCurrentUserName(),
                crashTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault()).format(Date()),
                exceptionType = exception.javaClass.name,
                exceptionMessage = exception.message,
                stackTrace = Log.getStackTraceString(exception),
                threadName = thread.name,
                appState = getAppState(),
                memoryUsage = DeviceUtils.getMemoryUsage(),
                availableMemory = DeviceUtils.getAvailableMemory(),
                batteryLevel = DeviceUtils.getBatteryLevel(),
                isCharging = DeviceUtils.isCharging(),
                networkStatus = NetworkUtils.getNetworkType(),
                lastActivity = ActivityManager.getLastActivity(),
                appVersion = BuildConfig.VERSION_NAME
            )

            // 立即上传崩溃信息
            GlobalScope.launch(Dispatchers.IO) {
                val uploadService = LogUploadService(RetrofitClient.logUploadApi)
                uploadService.uploadCrashInfo(listOf(crashInfo))
            }

        } catch (e: Exception) {
            Log.e("MyApplication", "收集崩溃信息失败", e)
        }
    }
}
```

### 2. 业务代码中的日志使用

```kotlin
class MainActivity : AppCompatActivity() {

    private val logCollector by lazy { LogCollector.getInstance(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 记录页面访问日志
        logCollector.collectLog(
            logType = "BUSINESS",
            level = "INFO",
            tag = "MainActivity",
            message = "用户进入主页面",
            extraData = mapOf(
                "userId" to UserManager.getCurrentUserId(),
                "timestamp" to System.currentTimeMillis(),
                "source" to "direct"
            )
        )

        setupClickListeners()
    }

    private fun setupClickListeners() {
        findViewById<Button>(R.id.btnLogin).setOnClickListener {
            performLogin()
        }

        findViewById<Button>(R.id.btnUploadLogs).setOnClickListener {
            uploadPendingLogs()
        }
    }

    private fun performLogin() {
        // 记录登录尝试
        logCollector.collectLog(
            logType = "BUSINESS",
            level = "INFO",
            tag = "LoginManager",
            message = "用户尝试登录",
            extraData = mapOf(
                "loginMethod" to "password",
                "deviceInfo" to Build.MODEL
            )
        )

        // 执行登录逻辑
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val result = AuthService.login("username", "password")

                withContext(Dispatchers.Main) {
                    if (result.isSuccess) {
                        // 登录成功日志
                        logCollector.collectLog(
                            logType = "BUSINESS",
                            level = "INFO",
                            tag = "LoginManager",
                            message = "用户登录成功",
                            extraData = mapOf(
                                "userId" to result.userId,
                                "loginTime" to System.currentTimeMillis()
                            )
                        )

                        Toast.makeText(this@MainActivity, "登录成功", Toast.LENGTH_SHORT).show()
                    } else {
                        // 登录失败日志
                        logCollector.collectLog(
                            logType = "BUSINESS",
                            level = "ERROR",
                            tag = "LoginManager",
                            message = "用户登录失败",
                            extraData = mapOf(
                                "errorCode" to result.errorCode,
                                "errorMessage" to result.errorMessage
                            )
                        )

                        Toast.makeText(this@MainActivity, "登录失败: ${result.errorMessage}", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                // 登录异常日志
                logCollector.collectLog(
                    logType = "BUSINESS",
                    level = "ERROR",
                    tag = "LoginManager",
                    message = "登录过程发生异常",
                    extraData = mapOf(
                        "exception" to e.javaClass.simpleName,
                        "message" to (e.message ?: "unknown")
                    )
                )

                withContext(Dispatchers.Main) {
                    Toast.makeText(this@MainActivity, "登录异常", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun uploadPendingLogs() {
        GlobalScope.launch(Dispatchers.IO) {
            val success = logCollector.uploadPendingLogs()

            withContext(Dispatchers.Main) {
                if (success) {
                    Toast.makeText(this@MainActivity, "日志上传成功", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this@MainActivity, "日志上传失败", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
}
```

### 3. 工具类实现示例

```kotlin
object DeviceUtils {

    fun getDeviceId(context: Context): String {
        val sharedPrefs = context.getSharedPreferences("device_info", Context.MODE_PRIVATE)
        var deviceId = sharedPrefs.getString("device_id", null)

        if (deviceId == null) {
            deviceId = UUID.randomUUID().toString()
            sharedPrefs.edit().putString("device_id", deviceId).apply()
        }

        return deviceId
    }

    fun getTotalMemory(): Long {
        val memInfo = ActivityManager.MemoryInfo()
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        activityManager.getMemoryInfo(memInfo)
        return memInfo.totalMem
    }

    fun getAvailableStorage(): Long {
        val stat = StatFs(Environment.getDataDirectory().path)
        return stat.availableBytes
    }

    fun isRooted(): Boolean {
        return try {
            val process = Runtime.getRuntime().exec("su")
            process.destroy()
            true
        } catch (e: Exception) {
            false
        }
    }

    fun isEmulator(): Boolean {
        return Build.FINGERPRINT.startsWith("generic") ||
               Build.FINGERPRINT.startsWith("unknown") ||
               Build.MODEL.contains("google_sdk") ||
               Build.MODEL.contains("Emulator") ||
               Build.MANUFACTURER.contains("Genymotion")
    }
}

object UserManager {
    private var currentUserId: String? = null
    private var currentUserCode: String? = null
    private var currentUserName: String? = null

    fun getCurrentUserId(): String? = currentUserId
    fun getCurrentUserCode(): String? = currentUserCode
    fun getCurrentUserName(): String? = currentUserName

    fun setCurrentUser(userId: String, userCode: String, userName: String) {
        currentUserId = userId
        currentUserCode = userCode
        currentUserName = userName
    }
}

object LogConfigManager {
    private var currentConfig: LogConfig? = null

    fun getInstance(context: Context): LogConfigManager = this

    fun applyConfig(config: LogConfig) {
        currentConfig = config
        // 应用配置到日志系统
        LogCollector.getInstance(context).updateConfig(config)
    }

    fun getCurrentConfigVersion(): String? {
        return currentConfig?.configVersion
    }

    fun getCurrentConfigDetailsJson(): String? {
        return currentConfig?.let { config ->
            Gson().toJson(mapOf(
                "configId" to config.id,
                "configName" to config.configName,
                "logLevel" to config.logLevel,
                "enableLocationLog" to config.enableLocationLog,
                "locationLogInterval" to config.locationLogInterval,
                "logUploadInterval" to config.logUploadInterval,
                "maxLogFiles" to config.maxLogFiles
            ))
        }
    }

    fun getCurrentConfig(): LogConfig? = currentConfig
}

object PermissionUtils {
    fun getPermissionsJson(): String {
        // 获取应用权限状态
        val permissions = mapOf(
            "CAMERA" to hasPermission(Manifest.permission.CAMERA),
            "LOCATION" to hasPermission(Manifest.permission.ACCESS_FINE_LOCATION),
            "STORAGE" to hasPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE),
            "PHONE" to hasPermission(Manifest.permission.READ_PHONE_STATE),
            "MICROPHONE" to hasPermission(Manifest.permission.RECORD_AUDIO)
        )
        return Gson().toJson(permissions)
    }

    private fun hasPermission(permission: String): Boolean {
        return ContextCompat.checkSelfPermission(
            context, permission
        ) == PackageManager.PERMISSION_GRANTED
    }
}
```

## 📋 设备配置信息字段详解

### 1. 配置版本字段 (currentConfigVersion)

**用途**：记录设备当前使用的日志配置版本号

**数据类型**：String (varchar(20))

**示例值**：
- `"1.0.0"` - 默认配置版本
- `"1.1.0"` - 调试配置版本
- `"2.0.1"` - 自定义配置版本

**更新时机**：
- 应用启动时获取最新配置
- 配置发生变更时
- 定期同步配置时

### 2. 配置详情字段 (currentConfigDetails)

**用途**：存储完整的日志配置信息，便于后台分析和问题排查

**数据类型**：JSON String

**完整示例**：
```json
{
  "configId": 1,
  "configName": "default",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 3000,
  "logUploadInterval": 3600,
  "maxLogFiles": 5,
  "configSource": "DEFAULT",
  "appliedAt": "2025-01-22T10:30:00Z",
  "syncTime": "2025-01-22T10:30:00Z"
}
```

**字段说明**：
- `configId`: 配置在数据库中的ID
- `configName`: 配置名称
- `logLevel`: 日志级别 (DEBUG/INFO/WARN/ERROR)
- `enableLocationLog`: 是否启用位置日志
- `locationLogInterval`: 位置日志间隔(毫秒)
- `logUploadInterval`: 日志上传间隔(毫秒)
- `maxLogFiles`: 最大日志文件数量
- `configSource`: 配置来源 (DEFAULT/USER_SPECIFIC/DEVICE_SPECIFIC)
- `appliedAt`: 配置应用时间
- `syncTime`: 配置同步时间

### 3. 实际数据库示例

基于实际数据库数据：
```sql
SELECT device_id, current_config_version, current_config_details
FROM b_device_info
WHERE current_config_version IS NOT NULL;
```

**结果示例**：
```json
{
  "device_id": "cf7f6ce27817ef1a",
  "current_config_version": "1.0.0",
  "current_config_details": {
    "configId": 1,
    "logLevel": "INFO",
    "configName": "default",
    "maxLogFiles": 5,
    "enableLocationLog": true,
    "logUploadInterval": 3600,
    "locationLogInterval": 3000
  }
}
```

### 4. Android端配置管理

```kotlin
// 配置更新示例
class ConfigManager {

    fun updateDeviceConfigInfo(config: LogConfig) {
        val deviceInfo = DeviceInfoRequest(
            deviceId = DeviceUtils.getDeviceId(context),
            // ... 其他字段
            currentConfigVersion = config.configVersion,
            currentConfigDetails = buildConfigDetailsJson(config)
        )

        // 上传更新后的设备信息
        uploadDeviceInfo(deviceInfo)
    }

    private fun buildConfigDetailsJson(config: LogConfig): String {
        val details = mapOf(
            "configId" to config.id,
            "configName" to config.configName,
            "logLevel" to config.logLevel,
            "enableLocationLog" to config.enableLocationLog,
            "locationLogInterval" to config.locationLogInterval,
            "logUploadInterval" to config.logUploadInterval,
            "maxLogFiles" to config.maxLogFiles,
            "configSource" to determineConfigSource(config.configName),
            "appliedAt" to SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(Date()),
            "syncTime" to SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").format(Date())
        )
        return Gson().toJson(details)
    }
}
```

### 5. 配置字段的重要性

**后台分析价值**：
- **问题排查**：知道设备使用的具体配置，便于定位问题
- **配置效果评估**：分析不同配置对日志行为的影响
- **版本分布统计**：了解各配置版本的使用情况
- **配置推送验证**：确认配置是否成功应用到设备

**数据一致性保证**：
- 设备信息与实际使用的配置保持同步
- 便于后台管理界面显示设备当前状态
- 支持配置变更历史追踪

本文档基于实际后端代码和数据库结构编写，提供了完整的Android日志上传功能实现指导，确保所有接口和字段信息的准确性。
