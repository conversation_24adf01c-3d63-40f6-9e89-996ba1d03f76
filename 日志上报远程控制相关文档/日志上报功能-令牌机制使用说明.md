# 日志上报功能 - 令牌机制使用说明

## 📋 **令牌机制调整说明**

为了与现有项目结构保持统一，日志上报功能的API接口已调整为使用项目现有的`x-auth-token`令牌机制，用户信息作为请求体参数而不是请求头。

## 🔧 **API接口调整详情**

### **调整前（旧方式）**
```bash
# 使用自定义请求头传递用户信息
curl -X POST "http://localhost:8080/logcontrol/device/upload" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 101" \
  -H "X-User-Code: sa" \
  -d '{"deviceId": "device123", "brand": "Huawei"}'
```

### **调整后（新方式）**
```bash
# 使用现有的x-auth-token令牌机制，用户信息作为请求体参数
curl -X POST "http://localhost:8080/logcontrol/device/upload" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: your_auth_token_here" \
  -d '{
    "deviceId": "device123",
    "userId": "101",
    "userCode": "sa", 
    "userName": "超级管理员",
    "brand": "Huawei"
  }'
```

## 🚀 **所有API接口示例**

### 1. **设备信息上传**
```bash
curl -X POST "http://localhost:8080/logcontrol/device/upload" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: your_auth_token_here" \
  -d '{
    "deviceId": "android_device_001",
    "userId": "101",
    "userCode": "sa",
    "userName": "超级管理员",
    "brand": "Huawei",
    "model": "Mate 40 Pro",
    "osVersion": "Android 12",
    "appVersion": "1.0.0",
    "totalMemory": 8589934592,
    "isRooted": false
  }'
```

### 2. **批量日志上传**
```bash
curl -X POST "http://localhost:8080/logcontrol/log/batch-upload" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: your_auth_token_here" \
  -d '{
    "deviceId": "android_device_001",
    "userId": "101",
    "userCode": "sa",
    "userName": "超级管理员",
    "appVersion": "1.0.0",
    "logs": [
      {
        "userId": "101",
        "userCode": "sa",
        "userName": "超级管理员",
        "logType": "BUSINESS",
        "level": "INFO",
        "timestamp": "2025-01-21T10:30:00",
        "tag": "MainActivity",
        "message": "用户登录成功",
        "extraData": "{\"action\":\"login\"}"
      }
    ]
  }'
```

### 3. **崩溃信息上传**
```bash
curl -X POST "http://localhost:8080/logcontrol/crash/upload" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: your_auth_token_here" \
  -d '{
    "deviceId": "android_device_001",
    "userId": "101",
    "userCode": "sa",
    "userName": "超级管理员",
    "crashTime": 1642752600000,
    "exceptionType": "java.lang.NullPointerException",
    "exceptionMessage": "Attempt to invoke virtual method on null object",
    "stackTrace": "at com.example.MainActivity.onCreate(MainActivity.java:45)\\n...",
    "appVersion": "1.0.0"
  }'
```

## 🔍 **后端处理逻辑**

### **用户信息验证和自动填充**
```java
// 1. 验证用户ID（如果提供）
if (request.getUserId() != null && !request.getUserId().trim().isEmpty()) {
    Long userId = userIdValidationService.validateUserIdCompletely(request.getUserId());
    if (userId == null) {
        log.warn("无效的用户ID: {}", request.getUserId());
        return RestResponse.ok(null);
    }
    
    // 2. 自动填充缺失的用户信息
    if ((request.getUserCode() == null || request.getUserCode().trim().isEmpty()) ||
        (request.getUserName() == null || request.getUserName().trim().isEmpty())) {
        UserBasicInfo userInfo = userIdValidationService.getUserBasicInfo(userId);
        if (userInfo != null) {
            if (request.getUserCode() == null || request.getUserCode().trim().isEmpty()) {
                request.setUserCode(userInfo.getCode());
            }
            if (request.getUserName() == null || request.getUserName().trim().isEmpty()) {
                request.setUserName(userInfo.getName());
            }
        }
    }
}
```

## 📱 **前端集成调整**

### **Android Kotlin示例**
```kotlin
// HTTP请求配置
class LogUploadService {
    private val authToken = "your_auth_token_here"
    
    fun uploadLogs(logRequest: LogUploadRequest) {
        val request = Request.Builder()
            .url("http://localhost:8080/logcontrol/log/batch-upload")
            .addHeader("Content-Type", "application/json")
            .addHeader("x-auth-token", authToken)  // 使用现有的令牌机制
            .post(RequestBody.create(MediaType.parse("application/json"), gson.toJson(logRequest)))
            .build()
            
        // 执行请求...
    }
}

// 数据结构（用户信息作为请求体参数）
data class LogUploadRequest(
    val deviceId: String,
    val userId: String,        // 作为请求体参数
    val userCode: String,      // 作为请求体参数
    @SerializedName("userName")
    val userName: String = "", // 作为请求体参数
    val appVersion: String,
    val logs: List<LogEntry>
)
```

## ✅ **调整优势**

### 1. **与现有项目统一**
- ✅ 使用项目现有的`x-auth-token`令牌机制
- ✅ 保持API设计的一致性
- ✅ 符合项目的安全规范

### 2. **用户信息处理优化**
- ✅ 用户信息作为请求体参数，便于验证
- ✅ 支持自动填充缺失的用户信息
- ✅ 统一的用户信息处理逻辑

### 3. **开发体验改善**
- ✅ 前端无需处理复杂的请求头
- ✅ 后端统一的参数验证逻辑
- ✅ 更好的错误处理和日志记录

### 4. **安全性增强**
- ✅ 利用现有的令牌验证机制
- ✅ 用户信息验证和存在性检查
- ✅ 防止无效用户数据上传

## 📊 **数据流程**

```
前端Android
  ↓ 携带x-auth-token令牌
API网关/拦截器
  ↓ 令牌验证通过
控制器层
  ↓ 接收请求体中的用户信息
用户验证服务
  ↓ 验证用户ID有效性
自动填充服务
  ↓ 补充缺失的用户信息
业务服务层
  ↓ 处理业务逻辑
数据库层
  ↓ 存储完整的用户关联数据
```

## ⚠️ **注意事项**

### 1. **令牌获取**
- 确保前端已正确获取和存储`x-auth-token`
- 令牌过期时需要重新获取
- 建议实现令牌自动刷新机制

### 2. **用户信息完整性**
- 建议前端提供完整的用户信息（userId, userCode, userName）
- 后端会自动填充缺失信息，但可能增加数据库查询
- 用户信息不一致时会记录警告日志

### 3. **错误处理**
- 无效的用户ID会导致请求被拒绝
- 建议前端实现重试机制
- 关注后端日志中的用户验证警告

### 4. **性能考虑**
- 用户信息自动填充会增加数据库查询
- 建议前端缓存用户信息，减少后端查询
- 批量操作时建议提供完整的用户信息

通过这次调整，日志上报功能完全融入了现有项目的架构体系，保持了良好的一致性和可维护性。
