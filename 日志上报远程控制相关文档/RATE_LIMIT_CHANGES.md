# 限流配置修改说明

## 修改概述

针对 `website-config/public` 接口429错误持续时间过长的问题，进行了以下优化：

## 1. 主要问题分析

原问题：
- 生产环境IP临时封禁时长为60分钟
- IP限流阈值过低（每分钟100次）
- 接口级限流次数偏少（每分钟120次）

## 2. 修改内容

### 2.1 DDoS防护配置优化 (application-ddos.yml)

**默认配置调整：**
- 全局限流：每秒2000次 → 每分钟20000次（翻倍）
- IP限流：每秒20次 → 每分钟600次 → 每小时7200次（翻倍）
- 临时封禁阈值：800次/分钟（提高）
- 临时封禁时长：3分钟（大幅缩短）

**测试环境配置：**
- IP限流：每分钟2000次，每小时20000次
- 临时封禁时长：1分钟
- 配置接口限流：500次/分钟

**生产环境配置：**
- IP限流：每分钟300次，每小时3600次（适度放宽）
- 临时封禁阈值：500次/分钟
- 临时封禁时长：10分钟（从60分钟大幅缩短）
- 配置接口限流：150次/分钟

### 2.2 接口级限流调整

**WebsiteConfigController.getPublicConfig()：**
- 限流次数：120次/分钟 → 200次/分钟

### 2.3 默认配置类优化 (DDoSProtectionConfig.java)

**全局限流：**
- 每秒：1000 → 2000次
- 每分钟：10000 → 20000次

**IP限流：**
- 每秒：10 → 20次
- 每分钟：300 → 600次
- 每小时：3600 → 7200次
- 每天：50000 → 100000次
- 封禁阈值：500 → 800次/分钟
- 封禁时长：30 → 3分钟

**接口限流：**
- 首页：60 → 200次/分钟
- 内容：120 → 200次/分钟
- 图片：200 → 300次/分钟
- 配置：30 → 200次/分钟
- 咨询：5 → 100次/小时

### 2.4 新增管理接口

**DDoSProtectionController 新增方法：**
1. `POST /website-ddos/ban/remove` - 解除IP临时封禁
2. `POST /website-ddos/rate-limit/clear` - 清除IP限流记录

**DDoSProtectionService 新增方法：**
- `removeBan(String ip)` - 解除IP临时封禁

## 3. 解决方案总结

### 3.1 限流时间优化
- **接口级限流**：1分钟后自动解除（不变）
- **IP临时封禁**：
  - 测试环境：1分钟
  - 默认环境：3分钟
  - 生产环境：10分钟（原60分钟）

### 3.2 限流阈值优化
- 大幅提高各项限流阈值
- 降低触发封禁的门槛
- 缩短封禁时长

### 3.3 管理功能增强
- 提供手动解除封禁接口
- 提供清除限流记录接口
- 便于运维管理和问题处理

## 4. 使用建议

### 4.1 正常情况
修改后，正常用户访问不会再遇到长时间的429错误。

### 4.2 紧急处理
如果仍然遇到429错误，可以：
1. 等待自动解除（最长10分钟）
2. 使用管理接口手动解除：
   ```bash
   # 解除IP封禁
   curl -X POST "http://your-domain/website-ddos/ban/remove?ip=YOUR_IP"

   # 清除IP限流记录
   curl -X POST "http://your-domain/website-ddos/rate-limit/clear?ip=YOUR_IP&key=config_public"

   # 根据key清除限流记录
   curl -X POST "http://your-domain/website-ddos/rate-limit/clear-by-key?key=config_public:ip:YOUR_IP"
   ```

### 4.3 监控建议
- 监控IP访问统计：`GET /website-ddos/stats/ip/{ip}`
- 定期检查限流日志
- 根据实际访问情况调整参数

## 5. 注意事项

1. **重启应用**：配置修改后需要重启应用生效
2. **Redis清理**：可能需要清理现有的限流记录
3. **环境区分**：确认当前运行环境，应用对应的配置
4. **安全平衡**：在用户体验和安全防护之间找到平衡点

## 6. 回滚方案

如果修改后出现问题，可以：
1. 恢复原配置文件
2. 重启应用
3. 或临时关闭DDoS防护：
   ```yaml
   website:
     ddos-protection:
       enabled: false
   ```
