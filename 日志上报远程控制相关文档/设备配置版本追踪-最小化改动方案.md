# 设备配置版本追踪 - 最小化改动方案

## 📋 方案概述

基于现有设备信息表进行最小化改动，仅添加必要字段实现设备配置版本追踪，后续查询分析功能在后台管理界面中实现。

## 🗄️ 数据库改动

### 扩展设备信息表 (b_device_info)

**仅添加2个字段**：
```sql
ALTER TABLE `b_device_info` 
ADD COLUMN `current_config_version` VARCHAR(20) COMMENT '当前配置版本',
ADD COLUMN `current_config_details` JSON COMMENT '当前配置详细信息';
```

**配置详情JSON格式**：
```json
{
  "configId": 1,
  "configName": "default",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 3000,
  "logUploadInterval": 3600,
  "maxLogFiles": 5
}
```

## 🔄 后端调整

### 1. 扩展DeviceInfoDto

```java
@Data
@ApiModel("设备信息")
public class DeviceInfoDto {
    // 现有字段保持不变...
    
    @ApiModelProperty("当前配置版本")
    private String currentConfigVersion;
    
    @ApiModelProperty("当前配置详细信息")
    private String currentConfigDetails;
}
```

### 2. 修改配置获取接口

**LogConfigController.java 最小调整**：
```java
@GetMapping("/get")
@ApiOperation("获取激活的日志配置")
public RestResponse<LogConfigDto> getLogConfig(
        @RequestHeader(value = "X-Device-Id", required = false) String deviceId,
        @RequestHeader(value = "X-User-Id", required = false) String userId) {
    
    try {
        LogConfigDto config = getConfigWithPriority(userId, deviceId);
        
        // 异步更新设备配置信息（不影响主流程）
        if (StringUtils.hasText(deviceId)) {
            updateDeviceConfigAsync(deviceId, config);
        }
        
        return RestResponse.ok(config);
    } catch (Exception e) {
        log.error("获取日志配置失败", e);
        return RestResponse.ok(logConfigService.getActiveConfig());
    }
}

/**
 * 异步更新设备配置信息
 */
@Async
private void updateDeviceConfigAsync(String deviceId, LogConfigDto config) {
    try {
        DeviceInfoDto deviceInfo = deviceInfoService.getDeviceInfo(deviceId);
        if (deviceInfo != null && !config.getConfigVersion().equals(deviceInfo.getCurrentConfigVersion())) {
            deviceInfo.setCurrentConfigVersion(config.getConfigVersion());
            deviceInfo.setCurrentConfigDetails(buildConfigJson(config));
            deviceInfoService.updateDeviceInfo(deviceInfo);
        }
    } catch (Exception e) {
        log.warn("更新设备配置信息失败: {}", e.getMessage());
    }
}

/**
 * 构建配置JSON
 */
private String buildConfigJson(LogConfigDto config) {
    Map<String, Object> details = Map.of(
        "configId", config.getId(),
        "configName", config.getConfigName(),
        "logLevel", config.getLogLevel(),
        "enableLocationLog", config.getEnableLocationLog(),
        "locationLogInterval", config.getLocationLogInterval(),
        "logUploadInterval", config.getLogUploadInterval(),
        "maxLogFiles", config.getMaxLogFiles()
    );
    return new ObjectMapper().writeValueAsString(details);
}
```

## 📱 Android端调整

### 1. 数据模型调整

**DeviceInfoDto.kt 微调**：
```kotlin
data class DeviceInfoDto(
    // 现有字段...
    val deviceId: String,
    val brand: String,
    val model: String,
    val appVersion: String,
    // ... 其他现有字段
    
    // 新增字段
    val currentConfigVersion: String? = null,
    val currentConfigDetails: String? = null
)
```

### 2. 配置管理器调整

**LogConfigManager.kt 微调**：
```kotlin
class LogConfigManager private constructor(private val context: Context) {
    
    /**
     * 更新配置时同步设备信息
     */
    suspend fun updateConfig(newConfig: LogConfigResponse) {
        currentConfig = newConfig
        saveConfigToLocal(newConfig)
        
        // 更新设备信息中的配置版本
        updateDeviceConfigInfo(newConfig)
    }
    
    /**
     * 更新设备配置信息
     */
    private suspend fun updateDeviceConfigInfo(config: LogConfigResponse) {
        try {
            val deviceInfo = DeviceInfoManager.getInstance(context).getCurrentDeviceInfo()
            deviceInfo.currentConfigVersion = config.configVersion
            deviceInfo.currentConfigDetails = buildConfigJson(config)
            
            DeviceInfoManager.getInstance(context).uploadDeviceInfo(deviceInfo)
        } catch (e: Exception) {
            Log.w("ConfigManager", "更新设备配置信息失败", e)
        }
    }
    
    /**
     * 构建配置JSON
     */
    private fun buildConfigJson(config: LogConfigResponse): String {
        val details = mapOf(
            "configId" to config.id,
            "configName" to config.configName,
            "logLevel" to config.logLevel,
            "enableLocationLog" to config.enableLocationLog,
            "locationLogInterval" to config.locationLogInterval,
            "logUploadInterval" to config.logUploadInterval,
            "maxLogFiles" to config.maxLogFiles
        )
        return Gson().toJson(details)
    }
}
```

## 🖥️ 后台管理界面扩展

### 设备信息查询页面添加配置信息显示

**前端页面调整**：
```javascript
// 设备列表表格添加配置版本列
const columns = [
  // 现有列...
  { title: '设备ID', dataIndex: 'deviceId', key: 'deviceId' },
  { title: '品牌', dataIndex: 'brand', key: 'brand' },
  { title: '型号', dataIndex: 'model', key: 'model' },
  { title: '应用版本', dataIndex: 'appVersion', key: 'appVersion' },
  
  // 新增配置版本列
  { 
    title: '配置版本', 
    dataIndex: 'currentConfigVersion', 
    key: 'currentConfigVersion',
    render: (version) => version || '未知'
  },
  {
    title: '配置详情',
    dataIndex: 'currentConfigDetails',
    key: 'currentConfigDetails',
    render: (details) => {
      if (!details) return '-';
      try {
        const config = JSON.parse(details);
        return (
          <Tooltip title={<pre>{JSON.stringify(config, null, 2)}</pre>}>
            <Button size="small" type="link">查看详情</Button>
          </Tooltip>
        );
      } catch {
        return '格式错误';
      }
    }
  }
];
```

## 🚀 实施步骤

### 第一步：数据库调整 (30分钟)
```sql
ALTER TABLE `b_device_info` 
ADD COLUMN `current_config_version` VARCHAR(20) COMMENT '当前配置版本',
ADD COLUMN `current_config_details` JSON COMMENT '当前配置详细信息';
```

### 第二步：后端调整 (半天)
1. DeviceInfoDto添加2个字段
2. 配置获取接口添加异步更新逻辑
3. 测试接口功能

### 第三步：Android端调整 (半天)
1. 数据模型添加字段
2. 配置管理器添加更新逻辑
3. 测试配置同步

### 第四步：后台界面调整 (半天)
1. 设备列表添加配置版本显示
2. 配置详情查看功能
3. 测试界面功能

## ✅ 实现效果

1. **设备配置版本追踪**：每个设备记录当前使用的配置版本
2. **配置详情存储**：完整的配置信息以JSON格式存储
3. **后台查询分析**：在设备管理界面可查看和分析配置分布
4. **最小化影响**：不影响现有业务逻辑和性能

## ⚠️ 注意事项

1. **异步更新**：配置信息更新采用异步方式，不影响主流程性能
2. **容错处理**：配置更新失败不影响正常业务
3. **JSON格式**：确保配置详情JSON格式正确
4. **向后兼容**：新字段可为空，兼容旧版本

这个最小化方案通过最少的改动实现了核心功能，后续的查询分析功能可以在后台管理界面中逐步完善。
