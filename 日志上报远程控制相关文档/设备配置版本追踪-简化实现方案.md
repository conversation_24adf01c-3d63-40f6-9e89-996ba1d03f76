# 设备配置版本追踪 - 简化实现方案

## 📋 方案概述

基于现有的设备信息表 (b_device_info) 进行微调，通过扩展现有字段实现设备配置版本追踪功能，避免复杂的表结构变更。

## 🗄️ 数据库微调

### 1. 扩展设备信息表 (b_device_info)

**只需添加3个字段**：
```sql
-- 为设备信息表添加配置相关字段
ALTER TABLE `b_device_info` 
ADD COLUMN `current_config_version` VARCHAR(20) COMMENT '当前配置版本',
ADD COLUMN `current_config_details` JSON COMMENT '当前配置详细信息',
ADD COLUMN `last_config_sync_time` DATETIME COMMENT '最后配置同步时间',
ADD INDEX `idx_config_version` (`current_config_version`);
```

**配置详情JSON格式**：
```json
{
  "configId": 1,
  "configName": "default",
  "logLevel": "INFO",
  "enableLocationLog": true,
  "locationLogInterval": 3000,
  "logUploadInterval": 3600,
  "maxLogFiles": 5,
  "configSource": "DEFAULT"
}
```

## 🔄 后端接口微调

### 1. 扩展设备信息上传接口

**DeviceInfoDto 添加字段**：
```java
@Data
@ApiModel("设备信息")
public class DeviceInfoDto {
    // 现有字段...
    
    @ApiModelProperty("当前配置版本")
    private String currentConfigVersion;
    
    @ApiModelProperty("当前配置详细信息(JSON格式)")
    private String currentConfigDetails;
    
    @ApiModelProperty("最后配置同步时间")
    private LocalDateTime lastConfigSyncTime;
}
```

### 2. 修改配置获取接口逻辑

**LogConfigController.java 调整**：
```java
@GetMapping("/get")
@ApiOperation("获取激活的日志配置")
public RestResponse<LogConfigDto> getLogConfig(
        @RequestHeader(value = "X-Device-Id", required = false) String deviceId,
        @RequestHeader(value = "X-User-Id", required = false) String userId) {
    
    try {
        LogConfigDto config = getConfigWithPriority(userId, deviceId);
        
        // 更新设备配置信息
        if (StringUtils.hasText(deviceId)) {
            updateDeviceConfigInfo(deviceId, config);
        }
        
        return RestResponse.ok(config);
    } catch (Exception e) {
        log.error("获取日志配置失败", e);
        return RestResponse.ok(logConfigService.getActiveConfig());
    }
}

/**
 * 更新设备配置信息
 */
private void updateDeviceConfigInfo(String deviceId, LogConfigDto config) {
    try {
        DeviceInfoDto deviceInfo = deviceInfoService.getDeviceInfo(deviceId);
        if (deviceInfo != null) {
            // 检查配置是否有变化
            if (!config.getConfigVersion().equals(deviceInfo.getCurrentConfigVersion())) {
                // 更新设备配置信息
                deviceInfo.setCurrentConfigVersion(config.getConfigVersion());
                deviceInfo.setCurrentConfigDetails(buildConfigDetails(config));
                deviceInfo.setLastConfigSyncTime(LocalDateTime.now());
                
                deviceInfoService.updateDeviceInfo(deviceInfo);
                
                log.info("设备 {} 配置已更新: {} -> {}", 
                    deviceId, deviceInfo.getCurrentConfigVersion(), config.getConfigVersion());
            }
        }
    } catch (Exception e) {
        log.warn("更新设备配置信息失败: {}", e.getMessage());
    }
}

/**
 * 构建配置详情JSON
 */
private String buildConfigDetails(LogConfigDto config) {
    Map<String, Object> details = new HashMap<>();
    details.put("configId", config.getId());
    details.put("configName", config.getConfigName());
    details.put("logLevel", config.getLogLevel());
    details.put("enableLocationLog", config.getEnableLocationLog());
    details.put("locationLogInterval", config.getLocationLogInterval());
    details.put("logUploadInterval", config.getLogUploadInterval());
    details.put("maxLogFiles", config.getMaxLogFiles());
    details.put("configSource", determineConfigSource(config.getConfigName()));
    
    return new ObjectMapper().writeValueAsString(details);
}
```

### 3. 新增设备配置查询接口

```java
@GetMapping("/device-config/{deviceId}")
@ApiOperation("获取设备当前配置信息")
public RestResponse<DeviceConfigInfo> getDeviceConfigInfo(
        @PathVariable String deviceId) {
    try {
        DeviceInfoDto deviceInfo = deviceInfoService.getDeviceInfo(deviceId);
        if (deviceInfo == null) {
            return RestResponse.ok(null);
        }
        
        DeviceConfigInfo configInfo = new DeviceConfigInfo();
        configInfo.setDeviceId(deviceId);
        configInfo.setCurrentConfigVersion(deviceInfo.getCurrentConfigVersion());
        configInfo.setCurrentConfigDetails(deviceInfo.getCurrentConfigDetails());
        configInfo.setLastConfigSyncTime(deviceInfo.getLastConfigSyncTime());
        
        return RestResponse.ok(configInfo);
    } catch (Exception e) {
        log.error("获取设备配置信息失败", e);
        return RestResponse.ok(null);
    }
}
```

## 📱 Android端微调

### 1. 数据模型微调

**DeviceInfoDto Kotlin调整**：
```kotlin
data class DeviceInfoDto(
    // 现有字段...
    val deviceId: String,
    val brand: String,
    val model: String,
    val appVersion: String,
    
    // 新增配置字段
    val currentConfigVersion: String? = null,
    val currentConfigDetails: String? = null,
    val lastConfigSyncTime: String? = null
)
```

### 2. 设备信息管理器调整

**DeviceInfoManager.kt 微调**：
```kotlin
class DeviceInfoManager private constructor(private val context: Context) {
    
    /**
     * 上传设备信息时包含配置信息
     */
    suspend fun uploadDeviceInfo() {
        try {
            val configInfo = LogConfigManager.getInstance(context).getCurrentConfigInfo()
            
            val deviceInfo = DeviceInfoDto(
                deviceId = DeviceUtils.getDeviceId(context),
                brand = Build.BRAND,
                model = Build.MODEL,
                appVersion = BuildConfig.VERSION_NAME,
                // ... 其他现有字段
                
                // 添加配置信息
                currentConfigVersion = configInfo.version,
                currentConfigDetails = configInfo.details,
                lastConfigSyncTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
                    .format(Date(configInfo.syncTime))
            )
            
            val response = deviceApi.uploadDeviceInfo(deviceInfo)
            if (response.isSuccessful) {
                Log.i("DeviceInfo", "设备信息上传成功，配置版本: ${configInfo.version}")
            }
        } catch (e: Exception) {
            Log.e("DeviceInfo", "上传设备信息失败", e)
        }
    }
}
```

### 3. 配置管理器微调

**LogConfigManager.kt 微调**：
```kotlin
class LogConfigManager private constructor(private val context: Context) {
    
    private var currentConfig: LogConfigResponse? = null
    private var configSyncTime: Long = 0
    
    /**
     * 获取当前配置信息
     */
    fun getCurrentConfigInfo(): ConfigInfo {
        return ConfigInfo(
            version = currentConfig?.configVersion ?: "unknown",
            details = currentConfig?.let { buildConfigDetailsJson(it) },
            syncTime = configSyncTime
        )
    }
    
    /**
     * 更新配置时同步设备信息
     */
    suspend fun updateConfig(newConfig: LogConfigResponse) {
        currentConfig = newConfig
        configSyncTime = System.currentTimeMillis()
        
        // 保存到本地
        saveConfigToLocal(newConfig)
        
        // 同步更新设备信息
        DeviceInfoManager.getInstance(context).uploadDeviceInfo()
        
        Log.i("ConfigManager", "配置已更新: ${newConfig.configVersion}")
    }
    
    /**
     * 构建配置详情JSON
     */
    private fun buildConfigDetailsJson(config: LogConfigResponse): String {
        val details = mapOf(
            "configId" to config.id,
            "configName" to config.configName,
            "logLevel" to config.logLevel,
            "enableLocationLog" to config.enableLocationLog,
            "locationLogInterval" to config.locationLogInterval,
            "logUploadInterval" to config.logUploadInterval,
            "maxLogFiles" to config.maxLogFiles,
            "configSource" to determineConfigSource(config.configName)
        )
        return Gson().toJson(details)
    }
    
    private fun determineConfigSource(configName: String): String {
        return when {
            configName.startsWith("user_") -> "USER_SPECIFIC"
            configName.startsWith("device_") -> "DEVICE_SPECIFIC"
            else -> "DEFAULT"
        }
    }
}

data class ConfigInfo(
    val version: String,
    val details: String?,
    val syncTime: Long
)
```

## 🔍 查询和分析

### 1. 设备配置版本分布查询

```sql
-- 查看设备配置版本分布
SELECT 
    current_config_version,
    COUNT(*) as device_count,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM b_device_info WHERE deleted = 0) as percentage
FROM b_device_info 
WHERE deleted = 0 
  AND current_config_version IS NOT NULL
GROUP BY current_config_version
ORDER BY device_count DESC;
```

### 2. 配置同步状态查询

```sql
-- 查看配置同步状态
SELECT 
    device_id,
    brand,
    model,
    app_version,
    current_config_version,
    last_config_sync_time,
    TIMESTAMPDIFF(HOUR, last_config_sync_time, NOW()) as hours_since_sync
FROM b_device_info 
WHERE deleted = 0 
  AND current_config_version IS NOT NULL
ORDER BY last_config_sync_time DESC
LIMIT 20;
```

### 3. 配置详情分析

```sql
-- 分析配置详情
SELECT 
    current_config_version,
    JSON_EXTRACT(current_config_details, '$.logLevel') as log_level,
    JSON_EXTRACT(current_config_details, '$.configSource') as config_source,
    COUNT(*) as device_count
FROM b_device_info 
WHERE deleted = 0 
  AND current_config_details IS NOT NULL
GROUP BY current_config_version, log_level, config_source
ORDER BY device_count DESC;
```

## 🚀 实施步骤

### 第一步：数据库调整 (1天)
```sql
-- 执行字段添加
ALTER TABLE `b_device_info` 
ADD COLUMN `current_config_version` VARCHAR(20) COMMENT '当前配置版本',
ADD COLUMN `current_config_details` JSON COMMENT '当前配置详细信息',
ADD COLUMN `last_config_sync_time` DATETIME COMMENT '最后配置同步时间',
ADD INDEX `idx_config_version` (`current_config_version`);
```

### 第二步：后端调整 (2-3天)
1. 修改DeviceInfoDto添加新字段
2. 调整配置获取接口逻辑
3. 添加设备配置查询接口

### 第三步：Android端调整 (2-3天)
1. 更新数据模型
2. 修改设备信息上传逻辑
3. 调整配置管理器

### 第四步：测试验证 (1天)
1. 验证配置信息正确记录
2. 测试配置变更追踪
3. 检查查询接口功能

## ✅ 优势

1. **最小化改动**：只需添加3个字段，不破坏现有结构
2. **实现简单**：利用现有的设备信息上传机制
3. **向后兼容**：新字段可选，不影响旧版本
4. **查询方便**：直接从设备信息表查询配置状态
5. **维护简单**：减少表关联，降低复杂度

## ⚠️ 注意事项

1. **数据一致性**：确保配置变更时及时更新设备信息
2. **JSON字段性能**：监控JSON字段查询性能
3. **同步频率**：避免过于频繁的设备信息更新
4. **数据大小**：控制配置详情JSON的大小

这个简化方案通过最小的改动实现了配置版本追踪功能，适合快速实施和维护。
