-- 修复上传状态：将现有数据标记为已上传
-- 执行时间：2025-07-24
-- 说明：由于之前的逻辑错误，所有已经在服务器上的数据都被标记为未上传
--       现在需要将这些数据正确标记为已上传状态

-- 1. 备份当前状态（可选，用于回滚）
-- CREATE TABLE b_log_entry_backup_20250724 AS SELECT * FROM b_log_entry WHERE is_uploaded = 0;
-- CREATE TABLE b_crash_info_backup_20250724 AS SELECT * FROM b_crash_info WHERE is_uploaded = 0;

-- 2. 更新日志条目的上传状态
UPDATE b_log_entry 
SET is_uploaded = 1, 
    update_at = CURRENT_TIMESTAMP 
WHERE deleted = 0 
  AND is_uploaded = 0;

-- 3. 更新崩溃信息的上传状态  
UPDATE b_crash_info 
SET is_uploaded = 1,
    update_at = CURRENT_TIMESTAMP
WHERE deleted = 0 
  AND is_uploaded = 0;

-- 4. 验证更新结果
-- 查看更新后的统计信息
SELECT 
    '日志条目' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN is_uploaded = 1 THEN 1 END) as uploaded_count,
    COUNT(CASE WHEN is_uploaded = 0 THEN 1 END) as not_uploaded_count,
    ROUND(COUNT(CASE WHEN is_uploaded = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as upload_rate
FROM b_log_entry 
WHERE deleted = 0

UNION ALL

SELECT 
    '崩溃信息' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN is_uploaded = 1 THEN 1 END) as uploaded_count,
    COUNT(CASE WHEN is_uploaded = 0 THEN 1 END) as not_uploaded_count,
    ROUND(COUNT(CASE WHEN is_uploaded = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as upload_rate
FROM b_crash_info 
WHERE deleted = 0;

-- 5. 记录修复操作日志
INSERT INTO operation_log (operation_type, operation_desc, operation_time, operator) 
VALUES ('DATA_FIX', '修复日志和崩溃信息的上传状态标记', NOW(), 'SYSTEM');
