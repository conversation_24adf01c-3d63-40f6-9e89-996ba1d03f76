-- 为位置上报远程日志控制系统相关表添加 update_at 字段
-- 执行时间：2025-07-23
-- 说明：为了支持按更新时间排序，需要为相关表添加 update_at 字段

-- 1. 为 b_log_entry 表添加 update_at 字段
ALTER TABLE b_log_entry 
ADD COLUMN update_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 2. 为 b_crash_info 表添加 update_at 字段
ALTER TABLE b_crash_info 
ADD COLUMN update_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 3. 初始化现有数据的 update_at 字段
-- 将现有记录的 update_at 设置为 create_at 的值
UPDATE b_log_entry SET update_at = create_at WHERE update_at IS NULL;
UPDATE b_crash_info SET update_at = create_at WHERE update_at IS NULL;

-- 4. 创建索引以优化按更新时间排序的查询性能
-- b_log_entry 表索引
CREATE INDEX idx_log_entry_update_time ON b_log_entry(update_at DESC, timestamp DESC);
CREATE INDEX idx_log_entry_device_update ON b_log_entry(device_id, update_at DESC, timestamp DESC);
CREATE INDEX idx_log_entry_type_update ON b_log_entry(log_type, update_at DESC, timestamp DESC);
CREATE INDEX idx_log_entry_level_update ON b_log_entry(level, update_at DESC, timestamp DESC);

-- b_crash_info 表索引
CREATE INDEX idx_crash_info_update_time ON b_crash_info(update_at DESC, crash_time DESC);
CREATE INDEX idx_crash_info_device_update ON b_crash_info(device_id, update_at DESC, crash_time DESC);
CREATE INDEX idx_crash_info_exception_update ON b_crash_info(exception_type, update_at DESC, crash_time DESC);

-- b_device_info 表索引（该表已有 update_at 字段，只需添加索引）
CREATE INDEX idx_device_info_update_time ON b_device_info(update_at DESC, last_update_time DESC);

-- 5. 验证索引创建情况
-- 可以通过以下查询验证索引是否创建成功：
-- SHOW INDEX FROM b_log_entry WHERE Key_name LIKE 'idx_log_entry_%';
-- SHOW INDEX FROM b_crash_info WHERE Key_name LIKE 'idx_crash_info_%';
-- SHOW INDEX FROM b_device_info WHERE Key_name LIKE 'idx_device_info_%';
