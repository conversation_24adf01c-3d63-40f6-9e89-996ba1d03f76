-- 位置上报远程日志控制系统 - 添加用户支持
-- 版本: V5.0.8.2
-- 作者: system
-- 日期: 2025-01-21
-- 说明: 为logcontrol系统添加用户ID支持，基于现有st_user_basic表实现用户-设备-日志的完整关联

-- 1. 为设备信息表添加用户关联字段
-- 使用st_user_basic表的id作为外键，code作为业务标识
ALTER TABLE `b_device_info`
ADD COLUMN `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（关联st_user_basic.id）' AFTER `device_id`,
ADD COLUMN `user_code` varchar(64) DEFAULT NULL COMMENT '用户编码（关联st_user_basic.code）' AFTER `user_id`,
ADD COLUMN `user_name` varchar(64) DEFAULT NULL COMMENT '用户名称（冗余字段，便于查询）' AFTER `user_code`,
ADD INDEX `idx_user_id` (`user_id`),
ADD INDEX `idx_user_code` (`user_code`),
ADD INDEX `idx_user_device` (`user_id`, `device_id`),
ADD CONSTRAINT `fk_device_user` FOREIGN KEY (`user_id`) REFERENCES `st_user_basic` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 2. 为日志条目表添加用户关联字段
ALTER TABLE `b_log_entry`
ADD COLUMN `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（关联st_user_basic.id）' AFTER `device_id`,
ADD COLUMN `user_code` varchar(64) DEFAULT NULL COMMENT '用户编码（关联st_user_basic.code）' AFTER `user_id`,
ADD INDEX `idx_user_id` (`user_id`),
ADD INDEX `idx_user_code` (`user_code`),
ADD INDEX `idx_user_device_log` (`user_id`, `device_id`, `log_type`),
ADD CONSTRAINT `fk_log_user` FOREIGN KEY (`user_id`) REFERENCES `st_user_basic` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 3. 为崩溃信息表添加用户关联字段
ALTER TABLE `b_crash_info`
ADD COLUMN `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（关联st_user_basic.id）' AFTER `device_id`,
ADD COLUMN `user_code` varchar(64) DEFAULT NULL COMMENT '用户编码（关联st_user_basic.code）' AFTER `user_id`,
ADD INDEX `idx_user_id` (`user_id`),
ADD INDEX `idx_user_code` (`user_code`),
ADD INDEX `idx_user_device_crash` (`user_id`, `device_id`, `crash_time`),
ADD CONSTRAINT `fk_crash_user` FOREIGN KEY (`user_id`) REFERENCES `st_user_basic` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 4. 创建用户设备关联表（支持一个用户多个设备）
-- 使用st_user_basic表的id作为用户标识
CREATE TABLE IF NOT EXISTS `b_user_device_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID（关联st_user_basic.id）',
  `user_code` varchar(64) NOT NULL COMMENT '用户编码（关联st_user_basic.code）',
  `user_name` varchar(64) DEFAULT NULL COMMENT '用户名称（冗余字段）',
  `device_id` varchar(100) NOT NULL COMMENT '设备ID',
  `device_name` varchar(128) DEFAULT NULL COMMENT '设备名称（用户自定义）',
  `relation_type` varchar(16) DEFAULT 'OWNER' COMMENT '关联类型：OWNER-拥有者，SHARED-共享',
  `is_primary` tinyint(1) DEFAULT '0' COMMENT '是否为主设备',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_active_time` datetime DEFAULT NULL COMMENT '最后活跃时间',
  `status` varchar(16) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-活跃，INACTIVE-非活跃，UNBIND-已解绑',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_device` (`user_id`, `device_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_code` (`user_code`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_relation_type` (`relation_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_at` (`create_at`),
  CONSTRAINT `fk_relation_user` FOREIGN KEY (`user_id`) REFERENCES `st_user_basic` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设备关联表';


-- 5. 为日志配置表添加用户相关字段（支持用户级别的配置）
ALTER TABLE `b_log_config`
ADD COLUMN `target_users` text COMMENT '目标用户列表(JSON格式，为空表示全部用户)' AFTER `target_device_types`;


-- 6. 创建视图，便于查询用户设备日志关联信息
-- 基于现有的st_user_basic表
CREATE OR REPLACE VIEW `v_user_device_log_summary` AS
SELECT
    u.id as user_id,
    u.code as user_code,
    u.name as user_name,
    d.device_id,
    d.brand,
    d.model,
    d.app_version,
    COUNT(DISTINCT l.id) as log_count,
    COUNT(DISTINCT c.id) as crash_count,
    MAX(l.timestamp) as last_log_time,
    MAX(c.crash_time) as last_crash_time,
    r.bind_time,
    r.last_active_time,
    r.status as relation_status,
    r.is_primary
FROM `st_user_basic` u
LEFT JOIN `b_user_device_relation` r ON u.id = r.user_id
LEFT JOIN `b_device_info` d ON r.device_id = d.device_id
LEFT JOIN `b_log_entry` l ON d.device_id = l.device_id AND u.id = l.user_id
LEFT JOIN `b_crash_info` c ON d.device_id = c.device_id AND u.id = c.user_id
WHERE u.is_available = 1 AND (r.deleted = 0 OR r.deleted IS NULL)
GROUP BY u.id, d.device_id;

-- 7. 添加注释说明
ALTER TABLE `b_device_info` COMMENT = '设备信息表（已支持用户关联）';
ALTER TABLE `b_log_entry` COMMENT = '日志条目表（已支持用户关联）';
ALTER TABLE `b_crash_info` COMMENT = '崩溃信息表（已支持用户关联）';

-- 8. 插入测试数据（可选）
-- 基于现有的st_user_basic表，这里只是示例，实际使用时应该使用现有用户
-- INSERT INTO `b_user_device_relation` (`user_id`, `user_code`, `user_name`, `device_id`, `device_name`) VALUES
-- (101, 'sa', '超级管理员', 'test_device_001', '测试设备1')
-- ON DUPLICATE KEY UPDATE `update_at` = CURRENT_TIMESTAMP;
