-- 设备信息表字段完善
-- 版本: V5.0.8.4
-- 作者: system
-- 日期: 2025-01-22
-- 说明: 基于Android端完善的设备信息上传数据，扩展设备信息表字段

-- 为 b_device_info 表添加扩展字段
ALTER TABLE `b_device_info` 
ADD COLUMN `os_type` varchar(20) DEFAULT 'Android' COMMENT '操作系统类型' AFTER `os_version`,
ADD COLUMN `sdk_version` int(11) DEFAULT NULL COMMENT 'SDK版本' AFTER `os_type`,
ADD COLUMN `manufacturer` varchar(100) DEFAULT NULL COMMENT '制造商' AFTER `model`,
ADD COLUMN `screen_resolution` varchar(20) DEFAULT NULL COMMENT '屏幕分辨率' AFTER `manufacturer`,
ADD COLUMN `screen_density` float DEFAULT NULL COMMENT '屏幕密度' AFTER `screen_resolution`,
ADD COLUMN `available_storage` bigint(20) DEFAULT NULL COMMENT '可用存储空间(字节)' AFTER `total_memory`,
ADD COLUMN `cpu_abi` varchar(50) DEFAULT NULL COMMENT 'CPU架构' AFTER `available_storage`,
ADD COLUMN `is_emulator` tinyint(1) DEFAULT '0' COMMENT '是否模拟器' AFTER `is_rooted`,
ADD COLUMN `network_type` varchar(20) DEFAULT NULL COMMENT '网络类型' AFTER `is_emulator`,
ADD COLUMN `language` varchar(10) DEFAULT NULL COMMENT '系统语言' AFTER `network_type`,
ADD COLUMN `time_zone` varchar(50) DEFAULT NULL COMMENT '时区' AFTER `language`,
ADD COLUMN `collect_count` int(11) DEFAULT '1' COMMENT '收集次数' AFTER `last_update_time`,
ADD COLUMN `permissions_info` json DEFAULT NULL COMMENT '权限信息(JSON格式)' AFTER `collect_count`;

-- 调整现有字段长度
ALTER TABLE `b_device_info` 
MODIFY COLUMN `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识',
MODIFY COLUMN `brand` varchar(50) DEFAULT NULL COMMENT '设备品牌',
MODIFY COLUMN `model` varchar(100) DEFAULT NULL COMMENT '设备型号',
MODIFY COLUMN `os_version` varchar(50) DEFAULT NULL COMMENT '操作系统版本',
MODIFY COLUMN `app_version` varchar(20) DEFAULT NULL COMMENT '应用版本';

-- 添加索引以提升查询性能
ALTER TABLE `b_device_info` 
ADD INDEX `idx_manufacturer` (`manufacturer`),
ADD INDEX `idx_os_type_sdk` (`os_type`, `sdk_version`),
ADD INDEX `idx_is_emulator` (`is_emulator`),
ADD INDEX `idx_network_type` (`network_type`),
ADD INDEX `idx_language` (`language`),
ADD INDEX `idx_cpu_abi` (`cpu_abi`),
ADD INDEX `idx_collect_count` (`collect_count`);

-- 更新表注释
ALTER TABLE `b_device_info` COMMENT='设备信息表（完善版）';

-- 插入测试数据（可选）
INSERT INTO `b_device_info` (
    `device_id`, `user_id`, `user_code`, `user_name`, 
    `brand`, `model`, `manufacturer`, `screen_resolution`, `screen_density`,
    `os_type`, `os_version`, `sdk_version`, `app_version`, 
    `total_memory`, `available_storage`, `cpu_abi`, 
    `is_rooted`, `is_emulator`, `network_type`, `language`, `time_zone`,
    `first_collect_time`, `last_update_time`, `collect_count`
) VALUES (
    'test_device_001', 1, 'B0000001', '测试用户1',
    'google', 'sdk_gphone64_x86_64', 'Google', '1080x2340', 3.0,
    'Android', '15 (API 35)', 35, '1.0-debug',
    2067398656, 53687091200, 'x86_64',
    0, 1, 'WiFi', 'zh-CN', 'Asia/Shanghai',
    NOW(), NOW(), 1
) ON DUPLICATE KEY UPDATE 
    `last_update_time` = NOW(),
    `collect_count` = `collect_count` + 1;
