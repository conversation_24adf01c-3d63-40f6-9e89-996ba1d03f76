-- 位置上报远程日志控制系统 - 简化版数据库表结构
-- 版本: V5.0.8.1
-- 作者: system
-- 日期: 2025-01-21
-- 说明: 为小规模用户群体设计的轻量级日志控制系统

-- 1. 设备信息表
CREATE TABLE IF NOT EXISTS `b_device_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(64) NOT NULL COMMENT '设备唯一标识',
  `brand` varchar(32) DEFAULT NULL COMMENT '设备品牌',
  `model` varchar(64) DEFAULT NULL COMMENT '设备型号',
  `os_version` varchar(32) DEFAULT NULL COMMENT '操作系统版本',
  `app_version` varchar(32) DEFAULT NULL COMMENT '应用版本',
  `is_rooted` tinyint(1) DEFAULT '0' COMMENT '是否Root (0:否 1:是)',
  `screen_width` int(11) DEFAULT NULL COMMENT '屏幕宽度',
  `screen_height` int(11) DEFAULT NULL COMMENT '屏幕高度',
  `memory_total` bigint(20) DEFAULT NULL COMMENT '总内存(字节)',
  `storage_total` bigint(20) DEFAULT NULL COMMENT '总存储(字节)',
  `cpu_info` varchar(128) DEFAULT NULL COMMENT 'CPU信息',
  `network_type` varchar(16) DEFAULT NULL COMMENT '网络类型',
  `carrier` varchar(32) DEFAULT NULL COMMENT '运营商',
  `language` varchar(16) DEFAULT NULL COMMENT '系统语言',
  `timezone` varchar(32) DEFAULT NULL COMMENT '时区',
  `first_install_time` datetime DEFAULT NULL COMMENT '首次安装时间',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `extra_info` text COMMENT '扩展信息(JSON格式)',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`),
  KEY `idx_brand_model` (`brand`, `model`),
  KEY `idx_app_version` (`app_version`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表';

-- 2. 日志配置表
CREATE TABLE IF NOT EXISTS `b_log_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(64) NOT NULL COMMENT '配置名称',
  `config_version` varchar(16) NOT NULL COMMENT '配置版本',
  `log_level` varchar(16) DEFAULT 'INFO' COMMENT '日志级别',
  `location_log_interval` int(11) DEFAULT '300000' COMMENT '位置日志间隔(毫秒)',
  `log_upload_interval` int(11) DEFAULT '3600000' COMMENT '日志上传间隔(毫秒)',
  `max_log_files` int(11) DEFAULT '5' COMMENT '最大日志文件数量',
  `log_file_max_size` int(11) DEFAULT '10485760' COMMENT '单个日志文件最大大小(字节)',
  `enabled_log_types` varchar(256) DEFAULT 'BUSINESS,CRASH,PERFORMANCE' COMMENT '启用的日志类型',
  `crash_report_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用崩溃报告',
  `performance_monitor_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用性能监控',
  `debug_mode_enabled` tinyint(1) DEFAULT '0' COMMENT '是否启用调试模式',
  `is_active` tinyint(1) DEFAULT '0' COMMENT '是否激活',
  `target_app_versions` varchar(256) DEFAULT NULL COMMENT '目标应用版本(逗号分隔)',
  `target_device_types` varchar(256) DEFAULT NULL COMMENT '目标设备类型(逗号分隔)',
  `effective_start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `effective_end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `config_data` text COMMENT '配置数据(JSON格式)',
  `description` varchar(256) DEFAULT NULL COMMENT '配置描述',
  `created_by` varchar(64) DEFAULT 'system' COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_name_version` (`config_name`, `config_version`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_effective_time` (`effective_start_time`, `effective_end_time`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日志配置表';

-- 3. 日志条目表
CREATE TABLE IF NOT EXISTS `b_log_entry` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(64) NOT NULL COMMENT '设备ID',
  `app_version` varchar(32) DEFAULT NULL COMMENT '应用版本',
  `log_type` varchar(32) NOT NULL COMMENT '日志类型',
  `level` varchar(16) NOT NULL COMMENT '日志级别',
  `timestamp` datetime NOT NULL COMMENT '日志时间戳',
  `tag` varchar(64) DEFAULT NULL COMMENT '日志标签',
  `message` text NOT NULL COMMENT '日志消息',
  `extra_data` text COMMENT '扩展数据(JSON格式)',
  `is_uploaded` tinyint(1) DEFAULT '0' COMMENT '是否已上传',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  KEY `idx_device_log_type` (`device_id`, `log_type`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_level` (`level`),
  KEY `idx_is_uploaded` (`is_uploaded`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日志条目表';

-- 4. 崩溃信息表
CREATE TABLE IF NOT EXISTS `b_crash_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` varchar(64) NOT NULL COMMENT '设备ID',
  `app_version` varchar(32) DEFAULT NULL COMMENT '应用版本',
  `crash_time` datetime NOT NULL COMMENT '崩溃时间',
  `exception_type` varchar(128) NOT NULL COMMENT '异常类型',
  `exception_message` text COMMENT '异常消息',
  `stack_trace` longtext COMMENT '堆栈跟踪',
  `thread_name` varchar(64) DEFAULT NULL COMMENT '线程名称',
  `is_main_thread` tinyint(1) DEFAULT '0' COMMENT '是否主线程',
  `memory_usage` bigint(20) DEFAULT NULL COMMENT '内存使用量(字节)',
  `available_memory` bigint(20) DEFAULT NULL COMMENT '可用内存(字节)',
  `battery_level` int(11) DEFAULT NULL COMMENT '电池电量百分比',
  `network_status` varchar(32) DEFAULT NULL COMMENT '网络状态',
  `user_actions` text COMMENT '用户操作序列(JSON格式)',
  `custom_data` text COMMENT '自定义数据(JSON格式)',
  `is_uploaded` tinyint(1) DEFAULT '0' COMMENT '是否已上传',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  KEY `idx_device_crash_time` (`device_id`, `crash_time`),
  KEY `idx_exception_type` (`exception_type`),
  KEY `idx_app_version` (`app_version`),
  KEY `idx_is_uploaded` (`is_uploaded`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='崩溃信息表';

-- 插入默认日志配置
INSERT INTO `b_log_config` (
  `config_name`, `config_version`, `log_level`, `location_log_interval`, 
  `log_upload_interval`, `max_log_files`, `log_file_max_size`, 
  `enabled_log_types`, `crash_report_enabled`, `performance_monitor_enabled`, 
  `debug_mode_enabled`, `is_active`, `description`, `created_by`
) VALUES (
  'default', '1.0.0', 'INFO', 300000, 
  3600000, 5, 10485760, 
  'BUSINESS,CRASH,PERFORMANCE', 1, 1, 
  0, 1, '默认日志配置', 'system'
) ON DUPLICATE KEY UPDATE 
  `updated_time` = CURRENT_TIMESTAMP;
