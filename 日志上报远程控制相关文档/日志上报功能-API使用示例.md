# 日志上报功能 - API使用示例

## 📋 **核心功能说明**

日志上报功能是现有系统的扩展功能，主要目的是：
1. **用户标识**：每条日志明确标识上传用户
2. **设备关联**：支持一个用户多个设备的场景
3. **数据追溯**：完整的用户-设备-日志关联链

## 🚀 **API接口使用示例**

### 1. **设备信息上传**

```bash
curl -X POST "http://localhost:8080/logcontrol/device/upload" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: your_auth_token_here" \
  -d '{
    "deviceId": "android_device_001",
    "userId": "101",
    "userCode": "sa",
    "userName": "超级管理员",
    "brand": "Huawei",
    "model": "Mate 40 Pro",
    "osVersion": "Android 12",
    "appVersion": "1.0.0",
    "totalMemory": 8589934592,
    "isRooted": false
  }'
```

**关键点**：
- 使用项目现有的`x-auth-token`令牌机制进行身份验证
- `userId`: String类型，避免大整数精度问题
- `userCode`: 用户编码，辅助验证
- `deviceId`: 设备唯一标识
- 用户信息作为请求体参数，不使用请求头

### 2. **批量日志上传**

```bash
curl -X POST "http://localhost:8080/logcontrol/log/batch-upload" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: your_auth_token_here" \
  -d '{
    "deviceId": "android_device_001",
    "userId": "101",
    "userCode": "sa",
    "userName": "超级管理员",
    "appVersion": "1.0.0",
    "logs": [
      {
        "userId": "101",
        "userCode": "sa",
        "userName": "超级管理员",
        "logType": "BUSINESS",
        "level": "INFO",
        "timestamp": "2025-01-21T10:30:00",
        "tag": "MainActivity",
        "message": "用户登录成功",
        "extraData": "{\"action\":\"login\",\"duration\":1200}"
      },
      {
        "userId": "101",
        "userCode": "sa",
        "userName": "超级管理员",
        "logType": "PERFORMANCE",
        "level": "INFO",
        "timestamp": "2025-01-21T10:30:05",
        "tag": "NetworkManager",
        "message": "API请求完成",
        "extraData": "{\"url\":\"/api/user/info\",\"duration\":350}"
      }
    ]
  }'
```

**关键点**：
- 使用项目现有的`x-auth-token`令牌机制进行身份验证
- 每条日志都包含`userId`、`userCode`和`userName`
- 支持批量上传提高效率
- `extraData`可以包含结构化的扩展信息
- 用户信息作为请求体参数，便于验证和自动填充

### 3. **崩溃信息上传**

```bash
curl -X POST "http://localhost:8080/logcontrol/crash/upload" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: your_auth_token_here" \
  -d '{
    "deviceId": "android_device_001",
    "userId": "101",
    "userCode": "sa",
    "userName": "超级管理员",
    "crashTime": 1642752600000,
    "exceptionType": "java.lang.NullPointerException",
    "exceptionMessage": "Attempt to invoke virtual method on null object",
    "stackTrace": "at com.example.MainActivity.onCreate(MainActivity.java:45)\\n...",
    "appVersion": "1.0.0"
  }'
```

## 📊 **数据查询示例**

### 1. **按用户查询日志**
```sql
-- 查询指定用户的所有日志
SELECT * FROM b_log_entry 
WHERE user_id = 101 
ORDER BY timestamp DESC 
LIMIT 100;
```

### 2. **按设备查询日志**
```sql
-- 查询指定设备的所有日志
SELECT * FROM b_log_entry 
WHERE device_id = 'android_device_001' 
ORDER BY timestamp DESC 
LIMIT 100;
```

### 3. **用户设备统计**
```sql
-- 查询用户的设备和日志统计
SELECT 
    d.device_id,
    d.brand,
    d.model,
    COUNT(l.id) as total_logs,
    MAX(l.timestamp) as last_log_time
FROM b_device_info d
LEFT JOIN b_log_entry l ON d.device_id = l.device_id
WHERE d.user_id = 101
GROUP BY d.device_id;
```

### 4. **完整追溯查询**
```sql
-- 用户-设备-日志完整关联查询
SELECT 
    u.id as user_id,
    u.code as user_code,
    u.name as user_name,
    d.device_id,
    d.brand,
    d.model,
    l.log_type,
    l.level,
    l.message,
    l.timestamp
FROM st_user_basic u
INNER JOIN b_device_info d ON u.id = d.user_id
INNER JOIN b_log_entry l ON d.device_id = l.device_id AND u.id = l.user_id
WHERE u.id = 101
ORDER BY l.timestamp DESC;
```

## 🔍 **前端集成示例**

### Android Kotlin示例
```kotlin
// 日志上传数据结构
data class LogUploadRequest(
    val deviceId: String,
    val userId: String,        // String类型避免精度问题
    val userCode: String,
    val appVersion: String,
    val logs: List<LogEntry>
)

data class LogEntry(
    val userId: String,        // 每条日志包含用户ID
    val userCode: String,      // 用户编码
    @SerializedName("userName")
    val userName: String = "", // 用户姓名（便于查询统计展示）
    val logType: String,       // BUSINESS, ERROR, PERFORMANCE等
    val level: String,         // INFO, WARN, ERROR等
    val timestamp: String,     // ISO格式时间戳
    val tag: String,           // 日志标签
    val message: String,       // 日志消息
    val extraData: String?     // 扩展数据(JSON格式)
)

// 使用示例
val logRequest = LogUploadRequest(
    deviceId = "android_device_001",
    userId = "101",
    userCode = "sa",
    userName = "超级管理员",
    appVersion = "1.0.0",
    logs = listOf(
        LogEntry(
            userId = "101",
            userCode = "sa",
            userName = "超级管理员",
            logType = "BUSINESS",
            level = "INFO",
            timestamp = "2025-01-21T10:30:00",
            tag = "MainActivity",
            message = "用户操作",
            extraData = "{\"action\":\"click\",\"button\":\"login\"}"
        )
    )
)
```

## ⚠️ **注意事项**

### 1. **数据格式要求**
- `userId`: 必须为String类型，避免大整数精度问题
- `deviceId`: 设备唯一标识，建议使用Android ID或UUID
- `timestamp`: 建议使用ISO 8601格式

### 2. **性能建议**
- 建议批量上传日志，减少网络请求
- 单次上传建议不超过100条日志
- 非关键日志可以异步上传

### 3. **错误处理**
- 接口返回200状态码表示请求被接收
- 具体的成功/失败通过服务端日志记录
- 建议客户端实现重试机制

### 4. **数据安全**
- 敏感信息不要放在日志消息中
- `extraData`中的个人信息需要脱敏处理
- 建议使用HTTPS传输

## 📈 **监控和分析**

### 1. **日志统计**
- 按用户统计日志数量和类型分布
- 按设备统计日志上传频率
- 按时间统计日志趋势

### 2. **问题排查**
- 通过用户ID快速定位用户问题
- 通过设备ID分析设备特定问题
- 通过日志类型分析业务问题

### 3. **性能分析**
- 分析PERFORMANCE类型日志
- 统计API响应时间分布
- 监控应用启动时间等关键指标

这个日志上报功能为现有系统提供了完整的用户行为追踪和问题排查能力，同时保持了简洁的设计和良好的扩展性。
