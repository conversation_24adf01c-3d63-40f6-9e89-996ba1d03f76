# 配置获取优先级示例

## 修改后的行为说明

### 数据准备

#### b_log_config 表数据
```sql
-- 配置1：默认配置，激活状态
INSERT INTO b_log_config (id, config_name, config_version, log_level, is_active) 
VALUES (1, 'default_config', '1.0.0', 'INFO', 1);

-- 配置2：生产配置，停用状态
INSERT INTO b_log_config (id, config_name, config_version, log_level, is_active) 
VALUES (2, 'production_config', '2.0.0', 'DEBUG', 0);

-- 配置3：测试配置，激活状态
INSERT INTO b_log_config (id, config_name, config_version, log_level, is_active) 
VALUES (3, 'test_config', '1.5.0', 'WARN', 1);
```

#### b_config_distribution 表数据
```sql
-- 用户user001分发了配置2（配置2是停用状态）
INSERT INTO b_config_distribution (config_id, target_type, target_id, target_name, is_active) 
VALUES (2, 'USER', 'user001', '张三', 1);
```

### 修改后的配置获取逻辑

```java
private LogConfigDto getConfigWithPriority(String userId, String deviceId) {
    // 1. 优先查询用户配置（即使配置被停用也返回）
    if (StringUtils.hasText(userId)) {
        LogConfigDto config = getConfigFromDistribution("USER", userId);
        if (config != null) {
            log.info("为用户 {} 返回分发配置，配置ID: {}", userId, config.getId());
            return config; // 返回配置2，即使它是停用状态
        }
    }
    
    // 2. 查询设备配置
    if (StringUtils.hasText(deviceId)) {
        LogConfigDto config = getConfigFromDistribution("DEVICE", deviceId);
        if (config != null) {
            return config;
        }
    }
    
    // 3. 返回默认配置
    return logConfigService.getActiveConfig(); // 返回配置1
}

private LogConfigDto getConfigFromDistribution(String targetType, String targetId) {
    var distributions = configDistributionService.getActiveDistributionsByTarget(targetType, targetId);
    
    if (!distributions.isEmpty()) {
        var topDistribution = distributions.get(0);
        // 关键修改：使用 getConfigByIdIgnoreStatus，不检查配置激活状态
        return logConfigService.getConfigByIdIgnoreStatus(topDistribution.getConfigId());
    }
    
    return null;
}
```

### 新增的Repository方法

```java
@Override
public LogConfig findByIdIgnoreStatus(Long id) {
    // 直接根据ID查询，不检查激活状态
    LambdaQueryWrapper<LogConfig> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(LogConfig::getId, id);
    wrapper.eq(LogConfig::getDeleted, false); // 只检查是否被删除，不检查激活状态
    return logConfigMapper.selectOne(wrapper);
}
```

## 结果对比

### 修改前的行为
当用户 `user001` 请求配置时：
1. 查询分发关系表，找到配置2
2. 调用 `getConfigById(2)`，由于配置2是停用状态，可能返回null
3. 最终返回默认配置（配置1）

**结果：返回配置1（default_config）**

### 修改后的行为
当用户 `user001` 请求配置时：
1. 查询分发关系表，找到配置2
2. 调用 `getConfigByIdIgnoreStatus(2)`，忽略配置2的停用状态
3. 直接返回配置2

**结果：返回配置2（production_config），即使它是停用状态**

## 业务逻辑说明

### 设计原则
**分发关系优先**：只要分发关系表中存在有效的分发关系，就优先返回该配置，不管配置本身是否被停用。

### 两个 is_active 字段的新含义

1. **b_log_config.is_active**：
   - 控制配置在管理界面的显示和编辑
   - 控制配置是否可以被新分配
   - **不影响**已有分发关系的配置获取

2. **b_config_distribution.is_active**：
   - 控制分发关系是否生效
   - 这是配置获取的**真正开关**

### 应用场景

#### 场景1：配置紧急修复
```sql
-- 配置2有问题，但不想影响已分配的用户
UPDATE b_log_config SET is_active = 0 WHERE id = 2;
```
**结果**：
- 已分配配置2的用户仍然使用配置2
- 新用户无法被分配配置2
- 管理员可以修复配置2后重新激活

#### 场景2：临时停用分发关系
```sql
-- 临时停用某个用户的配置分发
UPDATE b_config_distribution SET is_active = 0 
WHERE target_type = 'USER' AND target_id = 'user001';
```
**结果**：
- 用户user001会降级到默认配置
- 配置2本身不受影响
- 其他用户的配置2分发不受影响

## 优势

1. **更精细的控制**：可以分别控制配置的可用性和分发关系的有效性
2. **更好的稳定性**：已分配的配置不会因为配置停用而突然失效
3. **更灵活的管理**：可以在不影响现有用户的情况下停用问题配置

## 注意事项

1. **管理界面提示**：需要在Web管理界面明确显示配置的激活状态
2. **监控告警**：建议监控停用配置的使用情况
3. **文档说明**：需要向运维人员说明新的行为逻辑
