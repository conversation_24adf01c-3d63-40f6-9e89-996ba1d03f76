-- =====================================================
-- 完整的库存批次差异查询SQL
-- 功能：查询所有有差异的物品及其对应的批次差异详情
-- 作者：系统分析
-- 日期：2025-08-01
-- =====================================================

-- 主查询：获取所有有差异的商品及其批次详情（MySQL兼容版本）
SELECT
    -- 商品基本信息
    @row_number := @row_number + 1 AS 序号,
    t1.code AS 商品编码,
    t1.name AS 商品名称,
    t2.number_oem AS OEM编号,
    t2.part_brand AS 品牌,
    t2.manufacturer_channel AS 制造商渠道,

    -- 数量对比信息
    t1.sum_warehouse_number AS 库存表数量,
    COALESCE(fs.flow_calculated_qty, 0) AS 流水计算数量,
    (t1.sum_warehouse_number - COALESCE(fs.flow_calculated_qty, 0)) AS 数量差异,

    -- 问题分类
    CASE
        WHEN fs.flow_calculated_qty IS NULL THEN '无流水记录'
        WHEN t1.sum_warehouse_number > COALESCE(fs.flow_calculated_qty, 0) THEN '库存表偏大'
        WHEN t1.sum_warehouse_number < COALESCE(fs.flow_calculated_qty, 0) THEN '库存表偏小'
        ELSE '数据一致'
    END AS 问题类型,

    -- 优先级分类
    CASE
        WHEN ABS(t1.sum_warehouse_number - COALESCE(fs.flow_calculated_qty, 0)) >= 10 THEN '高优先级'
        WHEN ABS(t1.sum_warehouse_number - COALESCE(fs.flow_calculated_qty, 0)) >= 5 THEN '中优先级'
        WHEN ABS(t1.sum_warehouse_number - COALESCE(fs.flow_calculated_qty, 0)) > 0 THEN '低优先级'
        ELSE '正常'
    END AS 优先级,

    -- 批次统计信息
    COALESCE(bs.total_batches, 0) AS 批次总数,
    COALESCE(bs.active_batch_count, 0) AS 有库存批次数,
    COALESCE(bs.problem_batch_count, 0) AS 问题批次数,
    COALESCE(bs.total_duplicates, 0) AS 重复记录总数,

    -- 流水记录统计
    COALESCE(fs.total_flow_records, 0) AS 原始流水记录数,
    COALESCE(fs.dedup_flow_records, 0) AS 去重后记录数,
    COALESCE(fs.duplicate_records, 0) AS 流水重复记录数,

    -- 批次详情
    COALESCE(bs.batch_summary_info, '无批次信息') AS 批次详情,

    -- 时间信息
    DATE(t1.created_at) AS 库存创建日期,
    DATE(t1.updated_at) AS 库存更新日期,
    DATEDIFF(CURDATE(), t1.updated_at) AS 更新间隔天数

FROM b_storage_inventory t1
CROSS JOIN (SELECT @row_number := 0) r
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0
JOIN b_storage_warehouse t3 ON t1.warehouse_id = t3.id AND t3.deleted = 0
LEFT JOIN (
    SELECT
        code, warehouse_id,
        SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as flow_calculated_qty,
        COUNT(DISTINCT batch_code) as batch_count,
        COUNT(*) as total_flow_records,
        COUNT(DISTINCT CONCAT(flow_id, '-', batch_code, '-', in_out_type, '-', number, '-', type, '-', DATE(created_at))) as dedup_flow_records,
        COUNT(*) - COUNT(DISTINCT CONCAT(flow_id, '-', batch_code, '-', in_out_type, '-', number, '-', type, '-', DATE(created_at))) as duplicate_records
    FROM b_storage_warehouse_flow
    WHERE deleted = 0 AND warehouse_id = 1731282648590000130
    GROUP BY code, warehouse_id
) fs ON t1.code = fs.code AND t1.warehouse_id = fs.warehouse_id
LEFT JOIN (
    SELECT
        code, warehouse_id,
        COUNT(*) as total_batches,
        SUM(batch_stock) as total_batch_stock,
        SUM(CASE WHEN batch_duplicate_count > 0 THEN 1 ELSE 0 END) as problem_batch_count,
        SUM(CASE WHEN batch_stock > 0 THEN 1 ELSE 0 END) as active_batch_count,
        SUM(batch_duplicate_count) as total_duplicates,
        GROUP_CONCAT(
            CASE WHEN batch_stock != 0 OR batch_duplicate_count > 0
            THEN CONCAT(batch_code, ':', batch_in_qty, '入-', batch_out_qty, '出=', batch_stock,
                       CASE WHEN batch_duplicate_count > 0 THEN CONCAT('(重复', batch_duplicate_count, ')') ELSE '' END)
            END
            ORDER BY batch_duplicate_count DESC, batch_stock DESC SEPARATOR '; '
        ) as batch_summary_info
    FROM (
        SELECT
            code, warehouse_id, batch_code,
            SUM(CASE WHEN in_out_type = 1 THEN number ELSE 0 END) as batch_in_qty,
            SUM(CASE WHEN in_out_type = 2 THEN number ELSE 0 END) as batch_out_qty,
            SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as batch_stock,
            COUNT(*) - COUNT(DISTINCT CONCAT(flow_id, '-', in_out_type, '-', number, '-', type, '-', DATE(created_at))) as batch_duplicate_count
        FROM b_storage_warehouse_flow
        WHERE deleted = 0 AND warehouse_id = 1731282648590000130
        GROUP BY code, warehouse_id, batch_code
    ) batch_calc
    GROUP BY code, warehouse_id
) bs ON t1.code = bs.code AND t1.warehouse_id = bs.warehouse_id

WHERE t1.deleted = 0
  AND t1.warehouse_id = 1731282648590000130
  AND (t1.sum_warehouse_number != COALESCE(fs.flow_calculated_qty, 0))

ORDER BY
    -- 按优先级和差异大小排序
    CASE
        WHEN ABS(t1.sum_warehouse_number - COALESCE(fs.flow_calculated_qty, 0)) >= 10 THEN 1
        WHEN ABS(t1.sum_warehouse_number - COALESCE(fs.flow_calculated_qty, 0)) >= 5 THEN 2
        ELSE 3
    END,
    ABS(t1.sum_warehouse_number - COALESCE(fs.flow_calculated_qty, 0)) DESC,
    t1.code;

-- =====================================================
-- 补充查询：获取指定商品的详细批次信息
-- 使用方法：将 'WPID231130000016' 替换为要查询的商品编码
-- =====================================================

-- 查询指定商品的所有批次详细信息
SELECT
    '=== 商品基本信息 ===' AS 信息类型,
    t1.code AS 商品编码,
    t1.name AS 商品名称,
    t2.number_oem AS OEM编号,
    t2.part_brand AS 品牌,
    CAST(t1.sum_warehouse_number AS CHAR) AS 库存表数量,
    CAST(COALESCE(flow_total.total_flow_qty, 0) AS CHAR) AS 流水计算数量,
    CAST((t1.sum_warehouse_number - COALESCE(flow_total.total_flow_qty, 0)) AS CHAR) AS 数量差异,
    '' AS 批次号,
    '' AS 入库数量,
    '' AS 出库数量,
    '' AS 批次库存,
    '' AS 重复记录,
    '' AS 单价元,
    '' AS 首次时间

UNION ALL

SELECT
    '=== 批次库存明细 ===' AS 信息类型,
    batch_info.code AS 商品编码,
    '' AS 商品名称,
    '' AS OEM编号,
    '' AS 品牌,
    '' AS 库存表数量,
    '' AS 流水计算数量,
    '' AS 数量差异,
    batch_info.batch_code AS 批次号,
    CAST(batch_info.in_qty AS CHAR) AS 入库数量,
    CAST(batch_info.out_qty AS CHAR) AS 出库数量,
    CAST(batch_info.batch_stock AS CHAR) AS 批次库存,
    CASE
        WHEN batch_info.duplicate_count > 0 THEN CONCAT('⚠️', batch_info.duplicate_count, '条重复')
        ELSE '✅无重复'
    END AS 重复记录,
    CAST(ROUND(batch_info.avg_price/100, 2) AS CHAR) AS 单价元,
    CAST(batch_info.first_time AS CHAR) AS 首次时间

FROM (
    SELECT
        f.code,
        f.batch_code,
        SUM(CASE WHEN f.in_out_type = 1 THEN f.number ELSE 0 END) as in_qty,
        SUM(CASE WHEN f.in_out_type = 2 THEN f.number ELSE 0 END) as out_qty,
        SUM(CASE WHEN f.in_out_type = 1 THEN f.number ELSE -f.number END) as batch_stock,
        COUNT(*) - COUNT(DISTINCT CONCAT(f.flow_id, '-', f.in_out_type, '-', f.number, '-', f.type, '-', DATE(f.created_at))) as duplicate_count,
        AVG(CASE WHEN f.in_out_type = 1 THEN
            COALESCE((SELECT AVG(price) FROM b_storage_in_warehouse_goods ig
                     WHERE ig.code = f.code AND ig.batch_code = f.batch_code AND ig.warehouse_id = f.warehouse_id), 0)
            ELSE NULL END) as avg_price,
        MIN(f.created_at) as first_time
    FROM b_storage_warehouse_flow f
    WHERE f.deleted = 0
      AND f.warehouse_id = 1731282648590000130
      AND f.code = 'WPID231130000016'  -- 替换为要查询的商品编码
    GROUP BY f.code, f.batch_code
) batch_info

UNION ALL

SELECT
    '=== 重复记录详情 ===' AS 信息类型,
    dup_records.code AS 商品编码,
    '' AS 商品名称,
    '' AS OEM编号,
    '' AS 品牌,
    '' AS 库存表数量,
    '' AS 流水计算数量,
    '' AS 数量差异,
    dup_records.batch_code AS 批次号,
    dup_records.flow_id AS 入库数量,
    CAST(dup_records.number AS CHAR) AS 出库数量,
    CASE WHEN dup_records.in_out_type = 1 THEN '入库' ELSE '出库' END AS 批次库存,
    CAST(dup_records.duplicate_count AS CHAR) AS 重复记录,
    dup_records.type AS 单价元,
    CAST(dup_records.created_at AS CHAR) AS 首次时间

FROM (
    SELECT
        f.code,
        f.batch_code,
        f.flow_id,
        f.number,
        f.in_out_type,
        f.type,
        f.created_at,
        COUNT(*) OVER (PARTITION BY f.code, f.batch_code, f.flow_id, f.in_out_type, f.type, DATE(f.created_at)) as duplicate_count
    FROM b_storage_warehouse_flow f
    WHERE f.deleted = 0
      AND f.warehouse_id = 1731282648590000130
      AND f.code = 'WPID231130000016'  -- 替换为要查询的商品编码
      AND (f.code, f.batch_code, f.flow_id, f.in_out_type, f.type, DATE(f.created_at)) IN (
        SELECT code, batch_code, flow_id, in_out_type, type, DATE(created_at)
        FROM b_storage_warehouse_flow
        WHERE deleted = 0 AND warehouse_id = 1731282648590000130
        GROUP BY code, batch_code, flow_id, in_out_type, type, DATE(created_at)
        HAVING COUNT(*) > 1
      )
) dup_records
JOIN b_storage_inventory t1 ON dup_records.code = t1.code AND t1.warehouse_id = 1731282648590000130
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0
LEFT JOIN (
    SELECT
        code,
        SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as total_flow_qty
    FROM b_storage_warehouse_flow
    WHERE deleted = 0 AND warehouse_id = 1731282648590000130
    GROUP BY code
) flow_total ON t1.code = flow_total.code

ORDER BY
    CASE 信息类型
        WHEN '=== 商品基本信息 ===' THEN 1
        WHEN '=== 批次库存明细 ===' THEN 2
        WHEN '=== 重复记录详情 ===' THEN 3
    END,
    批次号, 首次时间;

-- =====================================================
-- 快速查询：获取所有重复记录的详细信息
-- =====================================================

-- 查询所有有重复记录的流水详情
SELECT
    f.code AS 商品编码,
    a.name AS 商品名称,
    f.batch_code AS 批次号,
    f.flow_id AS 流水单号,
    f.number AS 数量,
    CASE WHEN f.in_out_type = 1 THEN '入库' ELSE '出库' END AS 进出库类型,
    CASE
        WHEN f.type = 'purchase' THEN '采购入库'
        WHEN f.type = 'purchase_return' THEN '采购退库'
        WHEN f.type = 'apply_return' THEN '申请退库'
        WHEN f.type = 'engineer_apply' THEN '工程师申请'
        WHEN f.type = 'shopping_mall' THEN '商城销售'
        WHEN f.type = 'init' THEN '初始化'
        ELSE f.type
    END AS 业务类型,
    f.created_at AS 创建时间,
    f.operator_id AS 操作员ID,
    f.id AS 记录ID,
    COUNT(*) OVER (PARTITION BY f.code, f.batch_code, f.flow_id, f.in_out_type, f.type, DATE(f.created_at)) as 重复次数,
    CASE
        WHEN COUNT(*) OVER (PARTITION BY f.code, f.batch_code, f.flow_id, f.in_out_type, f.type, DATE(f.created_at)) > 1
        THEN '⚠️ 重复记录'
        ELSE '✅ 正常记录'
    END AS 记录状态
FROM b_storage_warehouse_flow f
JOIN b_storage_article a ON f.code = a.code AND a.deleted = 0
WHERE f.deleted = 0
  AND f.warehouse_id = 1731282648590000130
  AND (f.code, f.batch_code, f.flow_id, f.in_out_type, f.type, DATE(f.created_at)) IN (
    SELECT code, batch_code, flow_id, in_out_type, type, DATE(created_at)
    FROM b_storage_warehouse_flow
    WHERE deleted = 0 AND warehouse_id = 1731282648590000130
    GROUP BY code, batch_code, flow_id, in_out_type, type, DATE(created_at)
    HAVING COUNT(*) > 1
  )
ORDER BY f.code, f.batch_code, f.flow_id, f.created_at;

-- =====================================================
-- 统计查询：重复记录统计分析
-- =====================================================

-- 重复记录统计汇总
SELECT
    '重复记录统计' AS 统计类型,
    COUNT(DISTINCT code) AS 涉及商品数,
    COUNT(DISTINCT CONCAT(code, '-', batch_code)) AS 涉及批次数,
    COUNT(DISTINCT flow_id) AS 涉及流水单数,
    COUNT(*) AS 重复记录总数,
    SUM(number) AS 重复记录总数量,
    COUNT(*) - COUNT(DISTINCT CONCAT(code, batch_code, flow_id, in_out_type, type, DATE(created_at))) AS 需删除记录数
FROM b_storage_warehouse_flow
WHERE deleted = 0
  AND warehouse_id = 1731282648590000130
  AND (code, batch_code, flow_id, in_out_type, type, DATE(created_at)) IN (
    SELECT code, batch_code, flow_id, in_out_type, type, DATE(created_at)
    FROM b_storage_warehouse_flow
    WHERE deleted = 0 AND warehouse_id = 1731282648590000130
    GROUP BY code, batch_code, flow_id, in_out_type, type, DATE(created_at)
    HAVING COUNT(*) > 1
  )

UNION ALL

-- 按业务类型统计重复记录
SELECT
    CONCAT('业务类型-',
        CASE
            WHEN type = 'purchase' THEN '采购入库'
            WHEN type = 'purchase_return' THEN '采购退库'
            WHEN type = 'apply_return' THEN '申请退库'
            WHEN type = 'engineer_apply' THEN '工程师申请'
            WHEN type = 'shopping_mall' THEN '商城销售'
            ELSE type
        END
    ) AS 统计类型,
    COUNT(DISTINCT code) AS 涉及商品数,
    COUNT(DISTINCT CONCAT(code, '-', batch_code)) AS 涉及批次数,
    COUNT(DISTINCT flow_id) AS 涉及流水单数,
    COUNT(*) AS 重复记录总数,
    SUM(number) AS 重复记录总数量,
    COUNT(*) - COUNT(DISTINCT CONCAT(code, batch_code, flow_id, in_out_type, type, DATE(created_at))) AS 需删除记录数
FROM b_storage_warehouse_flow
WHERE deleted = 0
  AND warehouse_id = 1731282648590000130
  AND (code, batch_code, flow_id, in_out_type, type, DATE(created_at)) IN (
    SELECT code, batch_code, flow_id, in_out_type, type, DATE(created_at)
    FROM b_storage_warehouse_flow
    WHERE deleted = 0 AND warehouse_id = 1731282648590000130
    GROUP BY code, batch_code, flow_id, in_out_type, type, DATE(created_at)
    HAVING COUNT(*) > 1
  )
GROUP BY type
ORDER BY 重复记录总数 DESC;
