# 应用更新系统定向发布功能实施文档

## 1. 概述

本文档描述了在现有应用更新系统基础上，通过最小化改动实现定向用户更新功能的完整实施方案。该方案支持两种更新模式：指定用户更新和默认全局更新，同时保持向后兼容性。

## 2. 数据库变更

### 2.1 创建版本分发关系表

```sql
-- 创建应用版本分发关系表
CREATE TABLE `b_app_version_distribution` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `version_id` bigint(20) NOT NULL COMMENT '版本ID，关联b_app_version.id',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型：USER-用户，DEVICE-设备，GROUP-用户组',
  `target_id` varchar(100) NOT NULL COMMENT '目标ID（用户ID或设备ID或用户组ID）',
  `target_name` varchar(100) DEFAULT NULL COMMENT '目标名称（用于显示）',
  `assign_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `created_by` varchar(64) DEFAULT 'system' COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_version_target_unique` (`version_id`, `target_type`, `target_id`, `deleted`),
  KEY `idx_target_type_id` (`target_type`, `target_id`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_app_version_distribution_version` FOREIGN KEY (`version_id`) REFERENCES `b_app_version` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用版本分发关系表';
```

### 2.2 扩展现有版本表

```sql
-- 为现有版本表添加发布类型字段
ALTER TABLE `b_app_version` 
ADD COLUMN `release_type` varchar(20) DEFAULT 'GLOBAL' COMMENT '发布类型：GLOBAL-全局发布，TARGETED-定向发布' 
AFTER `is_active`;

-- 为现有数据设置默认值，保持向后兼容
UPDATE `b_app_version` SET `release_type` = 'GLOBAL' WHERE `release_type` IS NULL;
```

## 3. Java代码实现

### 3.1 实体类扩展

#### AppVersion实体类修改

```java
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@TableName("b_app_version")
@ApiModel("应用版本")
public class AppVersion {
    // ... 现有字段保持不变
    
    @TableField("is_active")
    @ApiModelProperty("是否启用")
    Boolean isActive;
    
    // 新增字段
    @TableField("release_type")
    @ApiModelProperty("发布类型：GLOBAL-全局发布，TARGETED-定向发布")
    String releaseType;
    
    @TableField("download_count")
    @ApiModelProperty("下载次数")
    Integer downloadCount;
    
    // ... 其他现有字段保持不变
}
```

#### 新增AppVersionDistribution实体类

```java
package com.hightop.benyin.appupdate.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

import java.time.LocalDateTime;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@TableName("b_app_version_distribution")
@ApiModel("应用版本分发关系")
public class AppVersionDistribution {
    
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    Long id;
    
    @TableField("version_id")
    @ApiModelProperty("版本ID")
    Long versionId;
    
    @TableField("target_type")
    @ApiModelProperty("目标类型：USER-用户，DEVICE-设备，GROUP-用户组")
    String targetType;
    
    @TableField("target_id")
    @ApiModelProperty("目标ID")
    String targetId;
    
    @TableField("target_name")
    @ApiModelProperty("目标名称")
    String targetName;
    
    @TableField("assign_time")
    @ApiModelProperty("分配时间")
    LocalDateTime assignTime;
    
    @TableField("is_active")
    @ApiModelProperty("是否激活")
    Boolean isActive;
    
    @TableField("created_by")
    @ApiModelProperty("创建人")
    String createdBy;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    LocalDateTime createdTime;
    
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    LocalDateTime updatedTime;
    
    @TableField("deleted")
    @TableLogic
    @ApiModelProperty("逻辑删除标志")
    Integer deleted;
}
```

### 3.2 Mapper接口

```java
package com.hightop.benyin.appupdate.infrastructure.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersionDistribution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AppVersionDistributionMapper extends MPJBaseMapper<AppVersionDistribution> {
    
    @Select("SELECT * FROM b_app_version_distribution " +
            "WHERE target_type = #{targetType} AND target_id = #{targetId} " +
            "AND is_active = 1 AND deleted = 0 " +
            "ORDER BY assign_time DESC")
    List<AppVersionDistribution> findActiveByTarget(@Param("targetType") String targetType, 
                                                   @Param("targetId") String targetId);
}
```

### 3.3 领域服务层

#### 版本分发领域服务

```java
package com.hightop.benyin.appupdate.domain.service;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.hightop.benyin.appupdate.infrastructure.entity.AppVersionDistribution;
import com.hightop.benyin.appupdate.infrastructure.mapper.AppVersionDistributionMapper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppVersionDistributionDomainService extends MPJBaseServiceImpl<AppVersionDistributionMapper, AppVersionDistribution> {
    
    public List<AppVersionDistribution> getActiveDistributionsByTarget(String targetType, String targetId) {
        return this.baseMapper.findActiveByTarget(targetType, targetId);
    }
    
    public List<AppVersionDistribution> getDistributionsByVersion(Long versionId) {
        return this.lambdaQuery()
                .eq(AppVersionDistribution::getVersionId, versionId)
                .eq(AppVersionDistribution::getIsActive, true)
                .list();
    }
    
    public boolean batchSave(List<AppVersionDistribution> distributions) {
        return this.saveBatch(distributions);
    }
}
```

#### 版本领域服务扩展

```java
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppVersionDomainService extends MPJBaseServiceImpl<AppVersionMapper, AppVersion> {
    
    /**
     * 获取最新的活跃全局版本（新增方法）
     */
    public AppVersion getLatestActiveGlobalVersion() {
        return this.lambdaQuery()
                .eq(AppVersion::getIsActive, true)
                .eq(AppVersion::getReleaseType, "GLOBAL")
                .orderByDesc(AppVersion::getVersionCode)
                .last("LIMIT 1")
                .one();
    }
    
    /**
     * 获取最新的活跃版本（保持向后兼容）
     */
    public AppVersion getLatestActiveVersion() {
        return this.lambdaQuery()
                .eq(AppVersion::getIsActive, true)
                .orderByDesc(AppVersion::getVersionCode)
                .last("LIMIT 1")
                .one();
    }
    
    // 其他现有方法保持不变...
}
```

### 3.4 应用服务层

#### 更新检查服务核心逻辑修改

```java
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class AppUpdateService {
    
    AppVersionDomainService appVersionDomainService;
    AppVersionDistributionDomainService distributionDomainService;
    
    public UpdateCheckResponse checkUpdate(UpdateCheckRequest request) {
        // 1. 优先检查用户专属版本（定向发布）
        AppVersion targetVersion = getTargetVersionForUser(request);
        
        // 2. 如果没有专属版本，获取全局最新版本
        if (targetVersion == null) {
            targetVersion = appVersionDomainService.getLatestActiveGlobalVersion();
        }
        
        if (targetVersion == null) {
            return new UpdateCheckResponse().setHasUpdate(false);
        }
        
        // 3. 检查是否需要更新
        boolean needUpdate = shouldUpdate(request.getCurrentVersionCode(), targetVersion);
        
        if (!needUpdate) {
            return new UpdateCheckResponse().setHasUpdate(false);
        }
        
        // 4. 构建更新响应
        UpdateCheckResponse response = new UpdateCheckResponse()
                .setHasUpdate(true)
                .setVersionName(targetVersion.getVersionName())
                .setVersionCode(targetVersion.getVersionCode())
                .setDownloadUrl(targetVersion.getCosUrl())
                .setUpdateLog(targetVersion.getUpdateLog())
                .setIsForce(determineForceUpdate(request.getCurrentVersionCode(), targetVersion))
                .setFileSize(targetVersion.getFileSize())
                .setFileMd5(targetVersion.getFileMd5());
                
        return response;
    }
    
    private AppVersion getTargetVersionForUser(UpdateCheckRequest request) {
        if (request.getUserId() == null && request.getDeviceId() == null) {
            return null;
        }
        
        AppVersion userVersion = null;
        
        // 1. 优先检查用户专属版本
        if (request.getUserId() != null) {
            userVersion = getVersionByTarget("USER", request.getUserId());
        }
        
        // 2. 如果没有用户专属版本，检查设备专属版本
        if (userVersion == null && request.getDeviceId() != null) {
            userVersion = getVersionByTarget("DEVICE", request.getDeviceId());
        }
        
        return userVersion;
    }
    
    private AppVersion getVersionByTarget(String targetType, String targetId) {
        List<AppVersionDistribution> distributions = 
            distributionDomainService.getActiveDistributionsByTarget(targetType, targetId);
            
        if (distributions.isEmpty()) {
            return null;
        }
        
        Long versionId = distributions.get(0).getVersionId();
        AppVersion version = appVersionDomainService.getById(versionId);
        
        return (version != null && version.getIsActive()) ? version : null;
    }
    
    // 其他现有方法保持不变...
}
```

#### 版本管理服务扩展

```java
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Transactional
public class AppVersionService {

    AppVersionDomainService appVersionDomainService;
    AppVersionDistributionDomainService distributionDomainService;

    public void setTargetedRelease(Long versionId, TargetedReleaseDto releaseDto) {
        // 1. 更新版本发布类型
        appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getReleaseType, "TARGETED")
                .eq(AppVersion::getId, versionId)
                .update();

        // 2. 清除现有分发关系（如果需要覆盖）
        if (releaseDto.getOverrideExisting()) {
            distributionDomainService.lambdaUpdate()
                    .set(AppVersionDistribution::getDeleted, 1)
                    .eq(AppVersionDistribution::getVersionId, versionId)
                    .update();
        }

        // 3. 创建新的分发关系
        List<AppVersionDistribution> distributions = new ArrayList<>();

        // 用户分发
        if (releaseDto.getUserIds() != null) {
            for (String userId : releaseDto.getUserIds()) {
                distributions.add(createDistribution(versionId, "USER", userId, null));
            }
        }

        // 设备分发
        if (releaseDto.getDeviceIds() != null) {
            for (String deviceId : releaseDto.getDeviceIds()) {
                distributions.add(createDistribution(versionId, "DEVICE", deviceId, null));
            }
        }

        // 批量保存
        if (!distributions.isEmpty()) {
            distributionDomainService.batchSave(distributions);
        }
    }

    public void setGlobalRelease(Long versionId) {
        // 1. 更新版本发布类型
        appVersionDomainService.lambdaUpdate()
                .set(AppVersion::getReleaseType, "GLOBAL")
                .eq(AppVersion::getId, versionId)
                .update();

        // 2. 删除所有分发关系
        distributionDomainService.lambdaUpdate()
                .set(AppVersionDistribution::getDeleted, 1)
                .eq(AppVersionDistribution::getVersionId, versionId)
                .update();
    }

    private AppVersionDistribution createDistribution(Long versionId, String targetType,
                                                    String targetId, String targetName) {
        return new AppVersionDistribution()
                .setVersionId(versionId)
                .setTargetType(targetType)
                .setTargetId(targetId)
                .setTargetName(targetName)
                .setIsActive(true)
                .setCreatedBy("system");
    }

    // 其他现有方法保持不变...
}
```

### 3.5 API接口层

#### 更新检查请求DTO扩展

```java
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
@ApiModel("更新检查请求")
public class UpdateCheckRequest {

    @ApiModelProperty("当前版本号")
    @NotNull(message = "当前版本号不能为空")
    Integer currentVersionCode;

    @ApiModelProperty("设备ID")
    String deviceId;

    // 新增字段，可选参数，保持向后兼容
    @ApiModelProperty("用户ID")
    String userId;

    @ApiModelProperty("IP地址")
    String ipAddress;

    @ApiModelProperty("用户代理")
    String userAgent;
}
```

#### 更新检查接口扩展

```java
@RestController
@RequestMapping("/api/app")
@Api(tags = "应用更新")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppUpdateController {

    AppUpdateService appUpdateService;

    @GetMapping("/update")
    @ApiOperation("检查应用更新")
    @Anonymous
    @IgnoreOperationLog
    public RestResponse<UpdateCheckResponse> checkUpdate(
            @RequestParam @ApiParam("当前版本号") Integer currentVersionCode,
            @RequestParam(required = false) @ApiParam("设备ID") String deviceId,
            @RequestParam(required = false) @ApiParam("用户ID") String userId, // 新增可选参数
            HttpServletRequest request) {

        UpdateCheckRequest checkRequest = new UpdateCheckRequest()
                .setCurrentVersionCode(currentVersionCode)
                .setDeviceId(deviceId)
                .setUserId(userId)
                .setIpAddress(getClientIpAddress(request))
                .setUserAgent(request.getHeader("User-Agent"));

        return RestResponse.ok(appUpdateService.checkUpdate(checkRequest));
    }

    // 其他现有方法保持不变...
}
```

#### 管理端定向发布接口

```java
@RestController
@RequestMapping("/api/admin/app-version")
@Api(tags = "应用版本管理")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppVersionAdminController {

    AppVersionService appVersionService;

    @PostMapping("/{versionId}/targeted-release")
    @ApiOperation("设置版本定向发布")
    public RestResponse<Void> setTargetedRelease(@PathVariable Long versionId,
                                               @Valid @RequestBody TargetedReleaseDto releaseDto) {
        appVersionService.setTargetedRelease(versionId, releaseDto);
        return RestResponse.ok();
    }

    @PutMapping("/{versionId}/global-release")
    @ApiOperation("转为全局发布")
    public RestResponse<Void> setGlobalRelease(@PathVariable Long versionId) {
        appVersionService.setGlobalRelease(versionId);
        return RestResponse.ok();
    }
}
```

#### 定向发布DTO

```java
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("定向发布")
public class TargetedReleaseDto {

    @ApiModelProperty("目标用户ID列表")
    List<String> userIds;

    @ApiModelProperty("目标设备ID列表")
    List<String> deviceIds;

    @ApiModelProperty("目标用户组ID列表")
    List<String> groupIds;

    @ApiModelProperty("是否覆盖现有分发关系")
    Boolean overrideExisting = false;
}
```

## 4. 实施步骤

### 4.1 数据库变更

1. 执行表结构变更SQL
2. 验证外键约束和索引
3. 确认现有数据兼容性

### 4.2 代码部署

1. 部署新增实体类和Mapper
2. 部署领域服务扩展
3. 部署应用服务修改
4. 部署API接口扩展

### 4.3 功能验证

1. 验证向后兼容性
2. 测试定向发布功能
3. 测试全局发布功能
4. 验证降级策略

## 5. 使用示例

### 5.1 发布定向版本

```sql
-- 1. 发布一个定向版本
INSERT INTO b_app_version (version_name, version_code, apk_file_name, cos_key, cos_url,
                          file_size, file_md5, update_log, is_force, admin_force,
                          is_active, release_type, download_count, created_by)
VALUES ('1.0.2-beta', 2, 'app-beta.apk', 'app/beta.apk', 'https://cos-url/app/beta.apk',
        28000000, 'abc123def456', '测试版本', 0, 0, 1, 'TARGETED', 0, 1);

-- 2. 为特定用户分发该版本
INSERT INTO b_app_version_distribution (version_id, target_type, target_id, target_name, is_active)
VALUES (LAST_INSERT_ID(), 'USER', '1730205532934926338', '测试用户1', 1),
       (LAST_INSERT_ID(), 'USER', '1730205532934926339', '测试用户2', 1);
```

### 5.2 API调用示例

```bash
# 原有调用方式（向后兼容）
GET /api/app/update?currentVersionCode=1&deviceId=device123

# 新的调用方式（支持用户定向）
GET /api/app/update?currentVersionCode=1&deviceId=device123&userId=1730205532934926338
```

### 5.3 管理端操作

```bash
# 设置定向发布
POST /api/admin/app-version/123/targeted-release
{
  "userIds": ["1730205532934926338", "1730205532934926339"],
  "deviceIds": ["device123", "device456"],
  "overrideExisting": true
}

# 转为全局发布
PUT /api/admin/app-version/123/global-release
```

## 6. 向后兼容性保证

### 6.1 数据库兼容性

- 新增字段设置默认值：`release_type`默认为`'GLOBAL'`
- 现有数据自动迁移：所有现有版本设置为全局发布
- 外键约束：使用`ON DELETE CASCADE`确保数据一致性

### 6.2 API兼容性

- 保持现有API签名：原有接口参数保持不变
- 新增参数为可选：`userId`参数为可选，不影响现有调用
- 降级策略：定向版本不可用时自动降级到全局版本

### 6.3 业务逻辑兼容性

- 如果没有传递用户ID，系统按原有逻辑工作
- 定向版本查找失败时，自动使用全局版本
- 保持现有的强制更新和版本比较逻辑

## 7. 监控和维护

### 7.1 关键指标监控

- 定向发布版本数量
- 用户分发关系数量
- 定向更新成功率
- 全局更新降级率

### 7.2 日志记录

- 定向版本匹配日志
- 分发关系创建日志
- 降级策略触发日志
- 管理操作审计日志

## 8. 扩展规划

### 8.1 短期扩展

- 支持用户组批量分发
- 增加灰度发布功能
- 添加发布策略管理界面

### 8.2 长期规划

- A/B测试支持
- 地域定向发布
- 自动化发布流程
- 发布效果分析
