# Android应用自动更新方案（简化版）

## 📋 项目概述

**适用场景**: 中国企业内部少数人员使用的Android应用
**推荐方案**: 极简化设计 + 现有API服务器
**技术栈**: Kotlin + Retrofit + 现有网络架构
**设计理念**: 最小化配置，最大化易用性

## 🎯 方案选择

### 推荐方案：AppUpdate库 + 企业内部服务器

**选择理由**:
- ✅ 无需Google Play依赖，适合企业内网环境
- ✅ 完全自主控制更新流程和时机
- ✅ 可复用现有API服务器和网络架构
- ✅ 用户群体小，便于统一管理和推送
- ✅ 支持强制更新、静默下载等企业级功能

## 🛠️ 极简化技术实现方案

### 1. 服务器端改造（极简化）

#### 1.1 API接口设计（只需1个接口）
```
GET /api/app/update?v={currentVersion}
- 功能：一站式更新检查和配置
- 参数：v=当前版本号（必需）
- 返回：完整更新信息（包含所有必要数据）

响应示例：
{
  "update": true,                    // 是否需要更新
  "version": "1.3.0",               // 新版本号
  "url": "/files/app-v1.3.0.apk",   // 下载链接
  "log": "修复重要问题",              // 更新说明
  "force": false,                   // 是否强制更新
  "size": "15MB"                    // 文件大小
}
```

#### 1.2 数据库设计（极简化）
只需一个简单的版本表：
```sql
CREATE TABLE app_update (
    id INT PRIMARY KEY AUTO_INCREMENT,
    version VARCHAR(20) NOT NULL,          -- 版本号
    apk_file VARCHAR(255) NOT NULL,        -- APK文件名
    update_log TEXT,                       -- 更新说明
    is_force BOOLEAN DEFAULT FALSE,        -- 是否强制更新
    admin_force BOOLEAN DEFAULT FALSE,     -- 管理员强制标志
    file_size VARCHAR(10),                 -- 文件大小
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE         -- 是否启用
);

-- 示例数据
INSERT INTO app_update (version, apk_file, update_log, is_force)
VALUES ('1.3.0', 'app-v1.3.0.apk', '修复重要问题\n优化性能', false);
```

#### 1.3 文件存储（极简化）
直接使用现有静态文件目录：
```
/static/files/
├── app-v1.0.0.apk
├── app-v1.1.0.apk
└── app-v1.2.0.apk
```

**上传方式**：直接通过FTP/SFTP上传到静态目录，无需复杂的文件管理系统

### 2. 客户端实现方案（极简化）

#### 2.1 无需第三方库
直接使用Android原生API：
- DownloadManager：系统下载管理器
- PackageInstaller：系统安装器
- 无需额外依赖，减少APK体积

#### 2.2 网络层极简扩展
只需在现有ApiClient.kt中添加一个方法：
```kotlin
// 只需要这一个方法
suspend fun checkUpdate(currentVersion: String): UpdateInfo
```

#### 2.3 一体化更新管理器
```kotlin
object SimpleUpdateManager {
    fun checkAndUpdate(context: Context) {
        // 一行代码完成所有更新逻辑
    }
}
```

#### 2.4 智能化策略
- **自动检查**：应用启动时自动检查
- **智能下载**：WiFi环境自动下载，移动网络询问
- **一键更新**：用户只需点击"更新"按钮
- **静默安装**：下载完成后自动提示安装

## 🎮 极简化管理操作

### 1. APK发布流程（3步完成）

#### 步骤1：上传APK文件
```bash
# 直接上传到服务器静态目录
scp app-v1.3.0.apk user@server:/static/files/
```

#### 步骤2：更新数据库（一条SQL）
```sql
-- 发布新版本（普通更新）
INSERT INTO app_update (version, apk_file, update_log)
VALUES ('1.3.0', 'app-v1.3.0.apk', '修复bug\n性能优化');

-- 发布强制更新
INSERT INTO app_update (version, apk_file, update_log, is_force)
VALUES ('1.3.1', 'app-v1.3.1.apk', '紧急安全修复', true);
```

#### 步骤3：完成
无需重启服务器，立即生效！

### 2. 紧急操作（1步完成）

#### 版本回退
```sql
-- 启用管理员强制更新（如回退到1.2.5）
UPDATE app_update SET admin_force = true WHERE version = '1.2.5';
```

#### 暂停更新
```sql
-- 暂停所有更新
UPDATE app_update SET is_active = false;
```

#### 恢复正常
```sql
-- 恢复正常更新检测
UPDATE app_update SET admin_force = false, is_active = true;
```

### 3. Web管理界面（完整版）

#### 主要功能页面
- **版本管理页面**：查看所有版本、发布新版本
- **文件上传页面**：直接上传APK文件
- **紧急操作页面**：版本回退、暂停更新等
- **统计监控页面**：更新率、用户分布等
- **系统设置页面**：基础配置管理

#### Web界面技术栈
- **后端**：基于您现有的后端框架
- **前端**：Vue 2 + Element UI（与现有架构一致）
- **文件上传**：Element Upload组件
- **权限控制**：基于现有的权限系统

## 🌐 Web管理界面详细设计

### 1. Vue 2 登录页面
```vue
<template>
  <div class="login-container">
    <el-card class="login-card">
      <div slot="header" class="card-header">
        <h3>🔐 应用更新管理系统</h3>
      </div>
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.native.prevent="handleLogin">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="el-icon-user">
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            @keyup.enter.native="handleLogin">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            class="login-btn"
            @click="handleLogin">
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$api.login(this.loginForm).then(response => {
            this.$message.success('登录成功')
            this.$router.push('/admin/dashboard')
          }).catch(error => {
            this.$message.error('登录失败：' + error.message)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.login-card {
  width: 400px;
}
.card-header {
  text-align: center;
  color: #409EFF;
}
.login-btn {
  width: 100%;
}
</style>
```

### 2. Vue 2 主控制台页面
```vue
<template>
  <div class="dashboard-container">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="header-content">
        <h2>📱 应用更新管理控制台</h2>
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link">
            管理员 <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-header>

    <el-main>
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card stat-info">
            <div class="stat-content">
              <div class="stat-title">当前版本</div>
              <div class="stat-value">{{ stats.currentVersion }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card stat-success">
            <div class="stat-content">
              <div class="stat-title">总用户数</div>
              <div class="stat-value">{{ stats.totalUsers }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card stat-warning">
            <div class="stat-content">
              <div class="stat-title">待更新用户</div>
              <div class="stat-value">{{ stats.pendingUsers }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card stat-danger">
            <div class="stat-content">
              <div class="stat-title">更新成功率</div>
              <div class="stat-value">{{ stats.successRate }}%</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="content-row">
        <!-- 版本管理 -->
        <el-col :span="16">
          <el-card>
            <div slot="header" class="card-header">
              <span>📦 版本管理</span>
              <el-button type="primary" size="small" @click="showNewVersionDialog">
                + 发布新版本
              </el-button>
            </div>
            <el-table :data="versionList" v-loading="tableLoading">
              <el-table-column prop="version" label="版本号" width="120"></el-table-column>
              <el-table-column prop="createdAt" label="发布时间" width="180">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.createdAt) }}
                </template>
              </el-table-column>
              <el-table-column label="更新类型" width="120">
                <template slot-scope="scope">
                  <el-tag :type="getUpdateTypeColor(scope.row)">
                    {{ getUpdateTypeText(scope.row) }}
                  </el-tag>
                  <el-tag v-if="scope.row.adminForce" type="warning" size="mini">
                    管理员强制
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="downloadCount" label="下载次数" width="100"></el-table-column>
              <el-table-column label="状态" width="80">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.isActive ? 'success' : 'info'">
                    {{ scope.row.isActive ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template slot-scope="scope">
                  <el-button-group>
                    <el-button size="mini" @click="editVersion(scope.row)">编辑</el-button>
                    <el-button
                      size="mini"
                      type="warning"
                      @click="toggleForce(scope.row)">
                      {{ scope.row.adminForce ? '取消强制' : '设为强制' }}
                    </el-button>
                    <el-button
                      size="mini"
                      type="danger"
                      @click="deleteVersion(scope.row)">
                      删除
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 快速操作 -->
        <el-col :span="8">
          <el-card>
            <div slot="header">⚡ 快速操作</div>
            <div class="quick-actions">
              <el-button
                type="warning"
                class="action-btn"
                @click="emergencyRollback">
                🔄 紧急版本回退
              </el-button>
              <el-button
                type="danger"
                class="action-btn"
                @click="pauseAllUpdates">
                ⏸️ 暂停所有更新
              </el-button>
              <el-button
                type="success"
                class="action-btn"
                @click="resumeUpdates">
                ▶️ 恢复更新推送
              </el-button>
              <el-button
                type="info"
                class="action-btn"
                @click="refreshStats">
                📊 刷新统计数据
              </el-button>
            </div>
          </el-card>

          <!-- 系统状态 -->
          <el-card class="system-status">
            <div slot="header">🔧 系统状态</div>
            <div class="status-item">
              <span class="status-label">更新服务状态</span>
              <el-tag type="success" size="mini">正常运行</el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">存储空间</span>
              <el-progress :percentage="35" :show-text="false"></el-progress>
              <span class="status-text">350MB / 1GB</span>
            </div>
            <div class="status-item">
              <span class="status-label">最后检查时间</span>
              <span class="status-text">{{ stats.lastCheckTime }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-main>

    <!-- 发布新版本对话框 -->
    <new-version-dialog
      :visible.sync="newVersionDialogVisible"
      @success="handlePublishSuccess">
    </new-version-dialog>
  </div>
</template>

<script>
import NewVersionDialog from './components/NewVersionDialog.vue'

export default {
  name: 'Dashboard',
  components: {
    NewVersionDialog
  },
  data() {
    return {
      stats: {
        currentVersion: '1.2.0',
        totalUsers: 45,
        pendingUsers: 12,
        successRate: 87,
        lastCheckTime: '2024-01-15 14:30:25'
      },
      versionList: [],
      tableLoading: false,
      newVersionDialogVisible: false
    }
  },
  mounted() {
    this.loadVersionList()
    this.loadStats()
  },
  methods: {
    // 加载版本列表
    loadVersionList() {
      this.tableLoading = true
      this.$api.getVersions().then(response => {
        this.versionList = response.data
      }).finally(() => {
        this.tableLoading = false
      })
    },

    // 加载统计数据
    loadStats() {
      this.$api.getStats().then(response => {
        this.stats = response.data
      })
    },

    // 显示发布新版本对话框
    showNewVersionDialog() {
      this.newVersionDialogVisible = true
    },

    // 发布成功回调
    handlePublishSuccess() {
      this.$message.success('版本发布成功！')
      this.loadVersionList()
      this.loadStats()
    },

    // 紧急操作
    emergencyRollback() {
      this.$confirm('确认要执行紧急版本回退吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.emergencyRollback().then(() => {
          this.$message.success('紧急回退已执行！')
          this.loadVersionList()
        })
      })
    },

    pauseAllUpdates() {
      this.$confirm('确认要暂停所有更新推送吗？', '确认', {
        type: 'warning'
      }).then(() => {
        this.$api.pauseUpdates().then(() => {
          this.$message.success('已暂停所有更新推送')
          this.loadStats()
        })
      })
    },

    resumeUpdates() {
      this.$api.resumeUpdates().then(() => {
        this.$message.success('已恢复更新推送')
        this.loadStats()
      })
    },

    refreshStats() {
      this.loadStats()
      this.loadVersionList()
      this.$message.success('数据已刷新')
    },

    // 切换强制更新
    toggleForce(row) {
      const action = row.adminForce ? '取消强制更新' : '设为强制更新'
      this.$confirm(`确认要${action}吗？`, '确认', {
        type: 'warning'
      }).then(() => {
        this.$api.toggleForce(row.id, !row.adminForce).then(() => {
          this.$message.success(`${action}成功`)
          this.loadVersionList()
        })
      })
    },

    // 工具方法
    formatDate(date) {
      return new Date(date).toLocaleString()
    },

    getUpdateTypeColor(row) {
      if (row.isForce) return 'danger'
      return 'primary'
    },

    getUpdateTypeText(row) {
      return row.isForce ? '强制' : '可选'
    },

    handleCommand(command) {
      if (command === 'logout') {
        this.$router.push('/login')
      }
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
}
.header {
  background: #409EFF;
  color: white;
  display: flex;
  align-items: center;
}
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.stats-row {
  margin-bottom: 20px;
}
.stat-card {
  text-align: center;
}
.stat-content {
  padding: 20px;
}
.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
}
.stat-info .stat-value { color: #409EFF; }
.stat-success .stat-value { color: #67C23A; }
.stat-warning .stat-value { color: #E6A23C; }
.stat-danger .stat-value { color: #F56C6C; }
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.action-btn {
  width: 100%;
}
.system-status {
  margin-top: 20px;
}
.status-item {
  margin-bottom: 15px;
}
.status-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}
.status-text {
  font-size: 12px;
  color: #333;
}
</style>
```

### 3. Vue 2 发布新版本组件
```vue
<template>
  <el-dialog
    title="📦 发布新版本"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose">
    <el-form
      ref="versionForm"
      :model="form"
      :rules="rules"
      label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="版本号" prop="version">
            <el-input
              v-model="form.version"
              placeholder="1.3.0">
            </el-input>
            <div class="form-tip">格式：主版本.次版本.修订版本</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="更新类型" prop="updateType">
            <el-select v-model="form.updateType" style="width: 100%">
              <el-option label="可选更新" value="optional"></el-option>
              <el-option label="推荐更新" value="recommended"></el-option>
              <el-option label="强制更新" value="forced"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="APK文件" prop="apkFile">
        <el-upload
          ref="upload"
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          accept=".apk"
          :limit="1">
          <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
          <div slot="tip" class="el-upload__tip">
            支持的格式：.apk，最大50MB
          </div>
        </el-upload>
      </el-form-item>

      <el-form-item label="更新说明" prop="updateLog">
        <el-input
          v-model="form.updateLog"
          type="textarea"
          :rows="4"
          placeholder="1. 修复重要问题&#10;2. 性能优化&#10;3. 新增功能">
        </el-input>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item>
            <el-checkbox v-model="form.adminForce">
              管理员强制推送
            </el-checkbox>
            <div class="form-tip">绕过版本检测，强制推送此版本</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <el-checkbox v-model="form.isActive">
              立即启用
            </el-checkbox>
            <div class="form-tip">发布后立即对用户可见</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :loading="uploading"
        @click="handleSubmit">
        发布版本
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'NewVersionDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      uploading: false,
      fileList: [],
      form: {
        version: '',
        updateType: 'optional',
        updateLog: '',
        adminForce: false,
        isActive: true,
        apkFile: null
      },
      rules: {
        version: [
          { required: true, message: '请输入版本号', trigger: 'blur' },
          { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确', trigger: 'blur' }
        ],
        updateType: [
          { required: true, message: '请选择更新类型', trigger: 'change' }
        ],
        updateLog: [
          { required: true, message: '请输入更新说明', trigger: 'blur' }
        ],
        apkFile: [
          { required: true, message: '请选择APK文件', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    handleFileChange(file, fileList) {
      if (file.size > 50 * 1024 * 1024) {
        this.$message.error('文件大小不能超过50MB')
        return false
      }
      this.form.apkFile = file.raw
      this.fileList = fileList
    },

    handleSubmit() {
      this.$refs.versionForm.validate((valid) => {
        if (valid) {
          this.uploadVersion()
        }
      })
    },

    uploadVersion() {
      this.uploading = true

      const formData = new FormData()
      formData.append('version', this.form.version)
      formData.append('updateType', this.form.updateType)
      formData.append('updateLog', this.form.updateLog)
      formData.append('adminForce', this.form.adminForce)
      formData.append('isActive', this.form.isActive)
      formData.append('apkFile', this.form.apkFile)

      this.$api.publishVersion(formData).then(response => {
        this.$message.success('版本发布成功！')
        this.handleClose()
        this.$emit('success')
      }).catch(error => {
        this.$message.error('发布失败：' + error.message)
      }).finally(() => {
        this.uploading = false
      })
    },

    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },

    resetForm() {
      this.$refs.versionForm.resetFields()
      this.fileList = []
      this.form.apkFile = null
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
```

## 📁 文件结构规划（包含Web管理）

### 4. Vue 2 API调用服务
```javascript
// api.js - API调用封装
import axios from 'axios'

const api = axios.create({
  baseURL: '/admin/api',
  timeout: 30000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加token等认证信息
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response?.status === 401) {
      // 未授权，跳转到登录页
      window.location.href = '/admin/login'
    }
    return Promise.reject(error)
  }
)

export default {
  // 登录
  login(data) {
    return api.post('/login', data)
  },

  // 获取版本列表
  getVersions() {
    return api.get('/versions')
  },

  // 发布新版本
  publishVersion(formData) {
    return api.post('/publish', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取统计数据
  getStats() {
    return api.get('/stats')
  },

  // 紧急操作
  emergencyRollback() {
    return api.post('/emergency/rollback')
  },

  pauseUpdates() {
    return api.post('/emergency/pause')
  },

  resumeUpdates() {
    return api.post('/emergency/resume')
  },

  // 切换强制更新
  toggleForce(versionId, force) {
    return api.post('/toggle-force', {
      versionId,
      force
    })
  },

  // 删除版本
  deleteVersion(versionId) {
    return api.delete(`/versions/${versionId}`)
  },

  // 编辑版本
  updateVersion(versionId, data) {
    return api.put(`/versions/${versionId}`, data)
  }
}
```

### 5. Vue 2 路由配置
```javascript
// router.js
import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '@/views/Login.vue'
import Dashboard from '@/views/Dashboard.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/admin/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/admin/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin',
    redirect: '/admin/dashboard'
  }
]

const router = new VueRouter({
  mode: 'history',
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      next('/admin/login')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
```

### 6. Vue 2 主应用入口
```javascript
// main.js
import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import App from './App.vue'
import router from './router'
import api from './api'

Vue.use(ElementUI)

// 全局注册API
Vue.prototype.$api = api

Vue.config.productionTip = false

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
```

### 5. 后端控制器实现
```java
@Controller
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private UpdateService updateService;

    // 显示管理主页
    @GetMapping("/")
    public String adminHome(HttpSession session, Model model) {
        if (!isLoggedIn(session)) {
            return "redirect:/admin/login";
        }
        return "admin/dashboard";
    }

    // 登录页面
    @GetMapping("/login")
    public String loginPage() {
        return "admin/login";
    }

    // 处理登录
    @PostMapping("/login")
    public String handleLogin(@RequestParam String username,
                             @RequestParam String password,
                             HttpSession session) {
        if (validateAdmin(username, password)) {
            session.setAttribute("admin", username);
            return "redirect:/admin/";
        }
        return "redirect:/admin/login?error=1";
    }

    // API：获取版本列表
    @GetMapping("/api/versions")
    @ResponseBody
    public List<AppVersion> getVersions() {
        return updateService.getAllVersions();
    }

    // API：发布新版本
    @PostMapping("/api/publish")
    @ResponseBody
    public ResponseEntity<?> publishVersion(
            @RequestParam String version,
            @RequestParam String updateType,
            @RequestParam String updateLog,
            @RequestParam MultipartFile apkFile,
            @RequestParam(defaultValue = "false") boolean adminForce,
            @RequestParam(defaultValue = "true") boolean isActive) {

        try {
            // 保存APK文件
            String fileName = "app-v" + version + ".apk";
            String filePath = saveApkFile(apkFile, fileName);

            // 保存到数据库
            AppVersion appVersion = new AppVersion();
            appVersion.setVersion(version);
            appVersion.setApkFile(fileName);
            appVersion.setUpdateLog(updateLog);
            appVersion.setIsForce("forced".equals(updateType));
            appVersion.setAdminForce(adminForce);
            appVersion.setIsActive(isActive);
            appVersion.setFileSize(formatFileSize(apkFile.getSize()));

            updateService.saveVersion(appVersion);

            return ResponseEntity.ok("发布成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("发布失败：" + e.getMessage());
        }
    }

    // API：紧急回退
    @PostMapping("/api/emergency/rollback")
    @ResponseBody
    public ResponseEntity<?> emergencyRollback() {
        try {
            updateService.emergencyRollback();
            return ResponseEntity.ok("回退成功");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("回退失败：" + e.getMessage());
        }
    }

    // API：暂停更新
    @PostMapping("/api/emergency/pause")
    @ResponseBody
    public ResponseEntity<?> pauseUpdates() {
        updateService.pauseAllUpdates();
        return ResponseEntity.ok("已暂停更新");
    }

    // API：恢复更新
    @PostMapping("/api/emergency/resume")
    @ResponseBody
    public ResponseEntity<?> resumeUpdates() {
        updateService.resumeUpdates();
        return ResponseEntity.ok("已恢复更新");
    }

    // API：切换强制状态
    @PostMapping("/api/toggle-force")
    @ResponseBody
    public ResponseEntity<?> toggleForce(@RequestParam Long versionId,
                                        @RequestParam boolean force) {
        updateService.toggleForceUpdate(versionId, force);
        return ResponseEntity.ok("操作成功");
    }

    // API：获取统计数据
    @GetMapping("/api/stats")
    @ResponseBody
    public Map<String, Object> getStats() {
        return updateService.getUpdateStats();
    }

    // 文件上传处理
    private String saveApkFile(MultipartFile file, String fileName) throws IOException {
        String uploadDir = "/static/files/";
        File uploadPath = new File(uploadDir);
        if (!uploadPath.exists()) {
            uploadPath.mkdirs();
        }

        File destFile = new File(uploadPath, fileName);
        file.transferTo(destFile);

        return destFile.getAbsolutePath();
    }

    // 格式化文件大小
    private String formatFileSize(long size) {
        if (size < 1024 * 1024) {
            return String.format("%.1fKB", size / 1024.0);
        } else {
            return String.format("%.1fMB", size / (1024.0 * 1024.0));
        }
    }
}
```

### 新增文件列表（包含Web管理）
```
# 客户端文件
app/src/main/java/com/example/repairorderapp/
├── network/
│   └── ApiClient.kt                      # 添加更新检查方法
└── update/
    ├── SimpleUpdateManager.kt            # 一体化更新管理器
    ├── UpdateInfo.kt                     # 更新信息数据类
    └── UpdateDialog.kt                   # 简化的更新对话框

# 服务器端文件
src/main/java/com/yourpackage/
├── controller/
│   └── AdminController.java             # Web管理控制器
├── service/
│   └── UpdateService.java               # 更新业务逻辑
└── entity/
    └── AppVersion.java                   # 版本实体类

# Web前端文件（Vue 2项目）
admin-frontend/
├── src/
│   ├── views/
│   │   ├── Login.vue                    # 登录页面
│   │   └── Dashboard.vue                # 管理主页
│   ├── components/
│   │   └── NewVersionDialog.vue         # 发布新版本组件
│   ├── api.js                          # API调用封装
│   ├── router.js                       # 路由配置
│   ├── main.js                         # 应用入口
│   └── App.vue                         # 根组件
├── package.json                        # 依赖配置
└── vue.config.js                       # Vue配置
```

### 7. Vue 2 项目配置文件
```json
// package.json
{
  "name": "app-update-admin",
  "version": "1.0.0",
  "scripts": {
    "serve": "vue-cli-service serve",
    "build": "vue-cli-service build",
    "lint": "vue-cli-service lint"
  },
  "dependencies": {
    "vue": "^2.6.14",
    "vue-router": "^3.5.1",
    "element-ui": "^2.15.9",
    "axios": "^0.27.2"
  },
  "devDependencies": {
    "@vue/cli-service": "^5.0.0",
    "vue-template-compiler": "^2.6.14"
  }
}
```

```javascript
// vue.config.js
module.exports = {
  publicPath: '/admin/',
  outputDir: 'dist',
  devServer: {
    port: 8080,
    proxy: {
      '/admin/api': {
        target: 'http://localhost:8081', // 后端服务地址
        changeOrigin: true
      }
    }
  }
}
```

**总计：客户端3个文件 + 服务器端6个文件 + Vue 2前端项目**

### 修改文件列表（极简化）
```
app/src/main/java/com/example/repairorderapp/
├── network/
│   └── ApiClient.kt                      # 添加一个更新检查方法
└── MainActivity.kt                       # 添加一行更新检查调用
```

**总计只需修改2个现有文件！**

## 🚨 高权限控制机制（最小化设计）

### 核心设计理念
在复杂的版本检测规则之上，提供一个简单的"管理员开关"，用于特殊情况下绕过所有常规检测逻辑。

### 实现方式
- **数据库**：只添加一个 `force_check` 布尔字段
- **API**：在响应中包含此字段
- **客户端**：优先检查此字段，为 `true` 时直接判定为有更新

### 使用场景
1. **版本回退**：当新版本有严重问题，需要回退到旧版本时
2. **紧急修复**：绕过版本号限制，强制推送修复版本
3. **特殊维护**：临时需要所有用户更新到指定版本

### 操作方式
```sql
-- 启用强制检查（如版本回退）
UPDATE app_versions SET force_check = TRUE WHERE version_name = '1.2.5';

-- 恢复正常检测
UPDATE app_versions SET force_check = FALSE WHERE version_name = '1.2.5';
```

### 优势
- ✅ **操作极简**：只需改一个布尔值
- ✅ **代码侵入小**：客户端只需加几行判断
- ✅ **效果立即**：设置后立即生效
- ✅ **权限可控**：只有管理员可操作

## 🎛️ 强制更新控制机制

### 1. 更新类型分级

#### OPTIONAL（可选更新）
- **场景**：功能优化、界面改进、非关键bug修复
- **用户体验**：显示更新提示，用户可选择"立即更新"或"稍后提醒"
- **行为**：用户可以无限次跳过，应用正常使用

#### RECOMMENDED（推荐更新）
- **场景**：重要功能添加、性能优化、重要bug修复
- **用户体验**：显示推荐更新对话框，强调更新重要性
- **行为**：用户可跳过3次，第4次变为强制更新

#### FORCED（强制更新）
- **场景**：安全漏洞修复、关键业务逻辑变更、服务器API变更
- **用户体验**：显示强制更新对话框，只有"立即更新"按钮
- **行为**：用户无法跳过，必须更新后才能使用应用

### 2. 版本控制策略

#### 2.1 基于版本号的控制
```json
{
  "currentVersion": "1.2.0",
  "latestVersion": "1.3.0",
  "updateType": "RECOMMENDED",
  "minSupportedVersion": "1.1.0",    // 最低支持版本
  "forceUpdateBefore": "1.0.5",      // 此版本及以下强制更新
  "gracePeriodDays": 7,               // 宽限期
  "skipCount": 2,                     // 已跳过次数
  "maxSkipCount": 3                   // 最大跳过次数
}
```

#### 2.2 基于时间的控制
```json
{
  "updateReleaseDate": "2024-01-15T10:00:00Z",
  "forceUpdateAfter": "2024-01-22T10:00:00Z",  // 7天后强制更新
  "gracePeriodExpired": false
}
```

#### 2.3 基于业务规则的控制
```json
{
  "rules": [
    {
      "fromVersion": "1.0.x",
      "toVersion": "1.3.0",
      "updateType": "FORCED",
      "reason": "安全漏洞修复"
    },
    {
      "fromVersion": "1.2.x",
      "toVersion": "1.3.0",
      "updateType": "RECOMMENDED",
      "reason": "功能优化"
    }
  ]
}
```

### 3. 管理员控制面板

#### 3.1 版本发布配置
- 设置新版本的更新类型（可选/推荐/强制）
- 配置宽限期和生效时间
- 设置特定版本的强制更新规则

#### 3.2 实时策略调整
- 紧急情况下可将已发布版本改为强制更新
- 支持按设备ID或用户组设置不同策略
- 可暂停或恢复特定版本的更新推送

#### 3.3 更新统计监控
- 各版本的更新率统计
- 用户跳过更新的行为分析
- 强制更新的执行效果监控

### 4. 客户端实现逻辑

#### 4.1 更新检查流程
```
启动应用 → 检查更新 → 获取更新策略 → 判断更新类型 → 显示对应UI → 执行更新操作
```

#### 4.2 策略判断算法
```kotlin
fun checkForUpdate(): UpdateResult {
    val response = apiClient.checkUpdate(...)

    // 🚨 优先检查强制检查标志（高权限控制）
    if (response.forceCheck) {
        return UpdateResult.HasUpdate(response) // 直接返回有更新，绕过所有常规检测
    }

    // 常规检测逻辑
    return if (response.latestVersionCode > BuildConfig.VERSION_CODE) {
        UpdateResult.HasUpdate(response)
    } else {
        UpdateResult.NoUpdate
    }
}

fun determineUpdateType(currentVersion: String, updateInfo: UpdateInfo): UpdateType {
    // 1. 检查强制检查标志
    if (updateInfo.forceCheck) {
        return UpdateType.FORCED // 强制检查时默认为强制更新
    }

    // 2. 检查是否在强制更新版本范围内
    if (isInForceUpdateRange(currentVersion, updateInfo.forceUpdateBefore)) {
        return UpdateType.FORCED
    }

    // 3. 检查宽限期是否已过
    if (isGracePeriodExpired(updateInfo.releaseDate, updateInfo.gracePeriodDays)) {
        return UpdateType.FORCED
    }

    // 4. 检查跳过次数是否超限
    if (getSkipCount(currentVersion) >= updateInfo.maxSkipCount) {
        return UpdateType.FORCED
    }

    // 5. 返回配置的更新类型
    return updateInfo.updateType
}
```

### 5. 用户体验设计

#### 5.1 可选更新对话框
- 标题：发现新版本
- 内容：更新日志 + 版本信息
- 按钮：[立即更新] [稍后提醒]

#### 5.2 推荐更新对话框
- 标题：推荐更新到新版本
- 内容：重要性说明 + 更新日志
- 按钮：[立即更新] [稍后提醒] + 跳过次数提示

#### 5.3 强制更新对话框
- 标题：必须更新才能继续使用
- 内容：强制更新原因 + 更新日志
- 按钮：[立即更新] （无跳过选项）

## 😊 用户体验优化（极简化）

### 1. 零操作更新体验

#### 智能检查时机
- **应用启动时**：后台静默检查，不影响启动速度
- **空闲时间**：用户无操作时进行检查
- **WiFi连接时**：自动检查并预下载

#### 一键式更新流程
```
发现更新 → 一键确认 → 自动下载 → 自动安装 → 完成
```

### 2. 简化的用户界面

#### 普通更新对话框
```
┌─────────────────────────┐
│  🔄 发现新版本 v1.3.0    │
│                         │
│  📝 修复重要问题         │
│     优化性能表现         │
│                         │
│  📦 大小: 15MB          │
│                         │
│  [立即更新] [稍后提醒]   │
└─────────────────────────┘
```

#### 强制更新对话框
```
┌─────────────────────────┐
│  ⚠️ 必须更新才能继续     │
│                         │
│  📝 紧急安全修复         │
│                         │
│  📦 大小: 12MB          │
│                         │
│      [立即更新]         │
└─────────────────────────┘
```

#### 下载进度（简洁版）
```
┌─────────────────────────┐
│  📥 正在下载更新...      │
│                         │
│  ████████░░ 80%         │
│                         │
│  15MB / 18MB            │
└─────────────────────────┘
```

### 3. 智能化决策

#### 自动判断更新类型
- **WiFi环境**：自动下载，完成后提示安装
- **移动网络**：询问是否下载
- **低电量**：延迟到充电时更新
- **存储不足**：提示清理空间

#### 用户友好提示
- **更新原因**：清楚说明为什么需要更新
- **更新内容**：简洁明了的功能说明
- **时间预估**：显示预计下载和安装时间
- **网络提醒**：提醒流量消耗情况

## 🔧 实施步骤（极简化）

### 阶段一：服务器端基础（1天）
1. **数据库准备**（30分钟）
   - 创建app_update表
   - 创建admin_users表
   - 插入测试数据

2. **API接口开发**（2小时）
   - 客户端更新检查接口
   - Web管理API接口
   - 文件上传处理

3. **Vue 2 Web管理界面**（4小时）
   - 创建Vue 2项目结构
   - 登录页面组件
   - 管理控制台页面组件
   - 发布新版本组件
   - API调用封装

4. **测试验证**（1.5小时）
   - 接口功能测试
   - Web界面测试
   - 文件上传测试

### 阶段二：客户端集成（1天）
1. **网络层扩展**（1小时）
   - 在ApiClient.kt中添加更新检查方法
   - 创建UpdateInfo数据类

2. **更新管理器**（3小时）
   - 创建SimpleUpdateManager类
   - 实现版本检查逻辑
   - 实现下载和安装功能

3. **用户界面**（2小时）
   - 创建UpdateDialog对话框
   - 集成到MainActivity
   - 优化用户体验

4. **测试调试**（2小时）
   - 端到端测试
   - 各种场景验证
   - 性能优化

### 阶段三：完善和部署（1天）
1. **功能完善**（3小时）
   - 错误处理优化
   - 用户体验细节调整
   - 安全性加固

2. **管理功能测试**（2小时）
   - Web管理界面全功能测试
   - 紧急操作验证
   - 权限控制测试

3. **生产部署**（3小时）
   - 生产环境配置
   - 数据库迁移
   - 服务部署和验证

**总计：3天完成（包含完整Web管理界面）！**

## 📊 配置参数（极简化）

### 智能化配置（无需手动设置）
```kotlin
object SimpleUpdateConfig {
    // 所有配置都有智能默认值，无需手动配置
    const val AUTO_CHECK_ON_START = true      // 启动时自动检查
    const val SMART_DOWNLOAD = true           // 智能下载（WiFi自动，移动网络询问）
    const val AUTO_RETRY = true               // 自动重试失败的下载
    const val SHOW_PROGRESS = true            // 显示下载进度

    // 用户几乎不需要关心这些配置，系统会智能处理
}
```

### 服务器配置
```properties
# application.properties
app.update.storage.path=/app/updates
app.update.max.file.size=100MB
app.update.allowed.extensions=.apk
```

## 🔒 安全考虑

### 1. APK签名验证
- 确保下载的APK与应用签名一致
- 防止恶意APK替换

### 2. 传输安全
- 使用HTTPS传输
- 添加MD5校验确保文件完整性

### 3. 权限控制
- 只有企业内部设备可访问更新接口
- 可选择性地添加设备白名单机制

## 💰 成本评估

### 开发成本（包含Web管理界面）
- 服务器端开发：1人天（包含Web管理界面）
- 客户端开发：1人天
- 测试验证：1人天
- **总计：3人天**

### 运维成本（包含Web管理）
- 存储成本：直接使用现有静态文件目录，无额外成本
- 带宽成本：企业内部使用，成本极低
- 维护成本：极低，通过Web界面可视化管理，无需SQL操作

## 🚀 预期效果

### 用户体验
- 应用内直接更新，无需手动下载安装
- 支持后台下载，不影响正常使用
- 自定义更新提示，符合企业风格

### 管理效率
- 统一版本管理，便于问题追踪
- 支持灰度发布，降低更新风险
- 可强制更新关键安全补丁

### 技术优势
- 复用现有技术栈，学习成本低
- 代码侵入性小，不影响现有功能
- 扩展性好，后续可添加更多功能

## 📞 后续支持

## 🎮 管理员配置示例

### 1. 常见更新场景配置

#### 场景1：日常功能更新
```sql
-- 发布新版本1.3.0，设置为可选更新
INSERT INTO app_versions (version_name, version_code, download_url, update_log, update_priority)
VALUES ('1.3.0', 13, '/api/app/download/app-v1.3.0.apk', '1. 新增报表功能\n2. 界面优化', 1);
```

#### 场景2：重要功能更新
```sql
-- 发布新版本1.4.0，设置为推荐更新，7天后强制
INSERT INTO app_versions (version_name, version_code, download_url, update_log, update_priority)
VALUES ('1.4.0', 14, '/api/app/download/app-v1.4.0.apk', '1. 重要业务流程优化\n2. 性能提升50%', 2);

INSERT INTO version_update_rules (from_version, to_version, update_type, grace_period_days)
VALUES ('1.3.x', '1.4.0', 'RECOMMENDED', 7);
```

#### 场景3：安全漏洞修复（紧急强制更新）
```sql
-- 发布安全修复版本1.3.1，立即强制更新
INSERT INTO app_versions (version_name, version_code, download_url, update_log, update_priority, force_check)
VALUES ('1.3.1', 131, '/api/app/download/app-v1.3.1.apk', '紧急安全漏洞修复，请立即更新', 3, TRUE);

INSERT INTO version_update_rules (from_version, to_version, update_type, grace_period_days, effective_date)
VALUES ('1.3.0', '1.3.1', 'FORCED', 0, NOW());
```

#### 场景4：版本回退（使用强制检查）
```sql
-- 紧急情况：回退到稳定版本1.2.5，绕过版本号检测
UPDATE app_versions SET force_check = TRUE WHERE version_name = '1.2.5';
-- 同时可以更新说明
UPDATE app_versions SET update_log = '紧急回退：修复严重问题，请立即更新到稳定版本' WHERE version_name = '1.2.5';
```

#### 场景5：废弃旧版本
```sql
-- 设置1.1.0及以下版本强制更新到1.4.0
UPDATE app_versions SET force_update_before = '1.1.0' WHERE version_name = '1.4.0';
```

### 2. 管理员操作API示例

#### 2.1 发布新版本
```http
POST /api/admin/app/release
{
  "versionName": "1.5.0",
  "versionCode": 15,
  "updateLog": "1. 新增数据导出功能\n2. 修复已知问题",
  "updateType": "RECOMMENDED",
  "gracePeriodDays": 5,
  "targetUsers": "ALL"  // 或指定用户组
}
```

#### 2.2 紧急调整更新策略
```http
PUT /api/admin/app/update-policy
{
  "fromVersion": "1.4.x",
  "toVersion": "1.5.0",
  "updateType": "FORCED",
  "reason": "发现严重bug，需要紧急更新",
  "effectiveImmediately": true
}
```

#### 2.3 设置强制检查（版本回退等特殊情况）
```http
PUT /api/app/force-check/1.2.5
{
  "forceCheck": true,
  "reason": "紧急回退到稳定版本"
}

-- 恢复正常检测
PUT /api/app/force-check/1.2.5
{
  "forceCheck": false
}
```

#### 2.4 查看更新统计
```http
GET /api/admin/app/update-stats
Response:
{
  "totalUsers": 50,
  "currentVersionDistribution": {
    "1.3.0": 5,
    "1.4.0": 30,
    "1.5.0": 15
  },
  "updateProgress": {
    "1.5.0": {
      "targetUsers": 45,
      "updatedUsers": 15,
      "updateRate": "33.3%",
      "skipCount": 8
    }
  }
}
```

### 可选扩展功能
1. **灰度发布**：按设备ID或用户组分批推送
2. **回滚机制**：支持快速回退到上一版本
3. **统计分析**：更新成功率、下载速度等数据
4. **多渠道支持**：支持不同渠道的差异化更新
5. **智能推送**：基于用户行为和设备状态的智能更新时机选择

### 技术支持
- 提供完整的代码实现
- 详细的集成文档
- 问题排查指南
- 后续功能扩展建议

## 🎯 极简化方案总结

### 与原方案对比

| 对比项目 | 原方案 | 极简化方案 | 改进效果 |
|---------|--------|------------|----------|
| **API接口数量** | 4个接口 | 1个接口 | 减少75% |
| **数据库表** | 2个复杂表 | 1个简单表 | 减少50% |
| **客户端文件** | 8个新文件 | 3个新文件 | 减少62% |
| **开发时间** | 5-8人天 | 3人天 | 减少62% |
| **配置复杂度** | 15+参数 | 4个参数 | 减少73% |
| **管理操作** | 多步骤 | 3步完成 | 极大简化 |

### 核心优势

#### 🚀 开发效率
- **3天完成**：从设计到部署只需3天（包含完整Web管理界面）
- **3个文件**：客户端只需新增3个文件
- **1个接口**：客户端API只需1个接口
- **Web管理**：完整的可视化管理界面

#### 😊 用户体验
- **零配置**：用户无需任何设置
- **一键更新**：点击即可完成更新
- **智能判断**：自动选择最佳更新时机
- **流畅体验**：无复杂操作步骤

#### 🛠️ 管理便利
- **Web界面发布**：通过Web界面上传APK并填写信息即可发布
- **一键紧急操作**：紧急回退、暂停更新等一键完成
- **可视化管理**：版本列表、统计数据、系统状态一目了然
- **零技术门槛**：管理员无需懂SQL，通过界面即可完成所有操作

#### 💰 成本控制
- **开发成本**：降低62%（包含完整Web管理界面）
- **维护成本**：接近零
- **学习成本**：极低（Web界面操作）
- **错误风险**：最小化

#### 🌐 Web管理界面特性
- **📊 实时统计**：当前版本、用户数量、更新成功率等关键指标
- **📦 版本管理**：查看所有版本、编辑版本信息、删除旧版本
- **⚡ 快速操作**：紧急回退、暂停更新、恢复推送等一键操作
- **📁 文件上传**：直接通过Web界面上传APK文件
- **🔐 权限控制**：管理员登录验证，操作日志记录
- **📱 响应式设计**：支持桌面和移动设备访问
- **🎯 智能提示**：操作确认、错误提示、成功反馈

### 适用场景确认

✅ **完美适合**：
- 企业内部应用（10-100人使用）
- 更新频率不高（月更或季更）
- 需要快速实现更新功能
- 希望最小化维护工作量

✅ **特别优势**：
- 可处理版本回退等特殊情况
- 支持强制更新和可选更新
- 完全自主控制，不依赖第三方
- 与现有架构无缝集成

---

**结论**: 此极简化方案在保持功能完整性的前提下，将复杂度降到最低，是企业内部应用更新的最佳选择。
