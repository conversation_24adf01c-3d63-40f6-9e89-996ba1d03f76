# 📋 库存数据不一致完整检查清单

## 📊 **总体统计概览**

| 统计类型 | 数量 | 总差异量 | 平均差异 | 最大差异 |
|---------|------|---------|---------|---------|
| **总体不一致商品** | 52个 | 115件 | 2.21件 | 12件 |
| **无流水记录** | 1个 | 9件 | 9.00件 | 9件 |
| **库存表偏大** | 24个 | 65件 | 2.71件 | 12件 |
| **库存表偏小** | 27个 | 41件 | 1.52件 | 4件 |

## 🚨 **优先级分类**

| 优先级 | 数量 | 总差异量 | 平均差异 | 处理建议 |
|--------|------|---------|---------|---------|
| **🔴 高优先级** (≥10件差异) | 2个 | 22件 | 11.00件 | 立即处理 |
| **🟡 中优先级** (5-9件差异) | 3个 | 20件 | 6.67件 | 本周处理 |
| **🟢 低优先级** (1-4件差异) | 47个 | 73件 | 1.55件 | 月内处理 |

## 📝 **检查清单使用说明**

### 🎯 **检查目标**
对52个库存数据不一致的商品进行逐一核实，确保库存数据准确性。

### 📝 **检查步骤**
1. **实地盘点**: 到仓库实际清点商品数量
2. **记录实际库存**: 在"实际库存"列填写盘点结果
3. **确定处理方式**: 根据实际情况选择处理方案
4. **标记检查状态**: 完成检查后勾选"□已检查"
5. **填写检查人**: 记录检查人员姓名

### 🔍 **处理方式参考**
- **调整库存表**: 库存表数量错误时
- **补充流水记录**: 缺少进出库记录时
- **清理重复记录**: 存在重复流水时
- **无需处理**: 数据一致时

## 🔴 **高优先级问题商品** (立即处理)

### 1. **理光5200原装鼓刮板2441** (WPID231130000016)
- **差异**: +12件 (库存表34件 vs 流水22件)
- **状态**: 库存表偏大
- **重复记录**: 8条
- **建议**: 清理重复流水记录，调整库存表数量
- **检查状态**: □已检查
- **实际库存**: ________
- **处理方式**: ________
- **检查人**: ________

### 2. **FC9100原装碳粉K** (WPID250506000006)  
- **差异**: +10件 (库存表23件 vs 流水13件)
- **状态**: 库存表偏大
- **重复记录**: 1条
- **建议**: 核实实际库存，调整库存表数量
- **检查状态**: □已检查
- **实际库存**: ________
- **处理方式**: ________
- **检查人**: ________

## 🟡 **中优先级问题商品** (本周处理)

### 1. **进粉口海绵** (WPID202412230101)
- **差异**: +9件 (库存表9件 vs 流水0件)
- **状态**: 🚨 **无流水记录**
- **问题**: 库存表有数据但完全无流水记录
- **建议**: 补充初始化流水记录或调查数据来源
- **检查状态**: □已检查
- **实际库存**: ________
- **处理方式**: ________
- **检查人**: ________

### 2. **零件5200-7100鼓清洁刮板** (WPID231130000021)
- **差异**: +6件 (库存表43件 vs 流水37件)
- **状态**: 库存表偏大
- **重复记录**: 6条
- **建议**: 清理重复记录后重新核算
- **检查状态**: □已检查
- **实际库存**: ________
- **处理方式**: ________
- **检查人**: ________

### 3. **C7500电源线** (WPID250414000002)
- **差异**: +5件 (库存表20件 vs 流水15件)
- **状态**: 库存表偏大
- **重复记录**: 3条
- **建议**: 清理重复记录，核实实际库存
- **检查状态**: □已检查
- **实际库存**: ________
- **处理方式**: ________
- **检查人**: ________

## 📋 **完整检查清单** (共52项)

| 序号 | 优先级 | 商品编码 | 商品名称 | OEM编号 | 品牌 | 库存表 | 流水计算 | 差异 | 问题类型 | 重复数 | 更新日期 | 检查状态 | 实际库存 | 处理方式 | 检查人 | 备注 |
|------|--------|----------|----------|---------|------|--------|----------|------|----------|--------|----------|----------|----------|----------|--------|------|
| 1 | 🔴 | WPID231130000016 | 理光5200原装鼓刮板2441 | D1362441 | 理光 | 34 | 22 | +12 | 库存偏大 | 8 | 2025-07-28 | □已检查 |  |  |  |  |
| 2 | 🔴 | WPID250506000006 | FC9100原装碳粉K | 828569 | 理光 | 23 | 13 | +10 | 库存偏大 | 1 | 2025-07-25 | □已检查 |  |  |  |  |
| 3 | 🟡 | WPID202412230101 | 进粉口海绵 | RI20241224021 | 理光 | 9 | 0 | +9 | 无流水 | 0 | 2025-01-23 | □已检查 |  |  |  |  |
| 4 | 🟡 | WPID231130000021 | 零件鼓清洁刮板 | D1362365 | 理光 | 43 | 37 | +6 | 库存偏大 | 6 | 2025-07-29 | □已检查 |  |  |  |  |
| 5 | 🟡 | WPID250414000002 | C7500电源线 |  | None | 20 | 15 | +5 | 库存偏大 | 3 | 2025-06-05 | □已检查 |  |  |  |  |
| 6 | 🟢 | WPID241221000411 | RiTNC定制碳粉M | RI20250215003 | 理光 | 6 | 2 | +4 | 库存偏大 | 2 | 2025-07-28 | □已检查 |  |  |  |  |
| 7 | 🟢 | WPID241221000250 | RiC5300原装碳粉红色 | 828611 | 理光 | 18 | 22 | -4 | 库存偏小 | 4 | 2025-07-29 | □已检查 |  |  |  |  |
| 8 | 🟢 | WPID241221000129 | 黑色色粉6% | 828281 | 理光 | 86 | 82 | +4 | 库存偏大 | 1 | 2025-07-24 | □已检查 |  |  |  |  |
| 9 | 🟢 | WPID241221000341 | 红色碳粉 | 828685 | 理光 | 27 | 30 | -3 | 库存偏小 | 3 | 2025-07-28 | □已检查 |  |  |  |  |
| 10 | 🟢 | WPID231210000094 | 分离轮 | AF032098 | 理光 | 27 | 24 | +3 | 库存偏大 | 3 | 2025-07-29 | □已检查 |  |  |  |  |
| 11 | 🟢 | WPID250217000001 | V170黑色碳粉 | CT203442 | 施乐 | 19 | 22 | -3 | 库存偏小 | 2 | 2025-07-23 | □已检查 |  |  |  |  |
| 12 | 🟢 | WPID231201000015 | XeC60/7785亚版分装粉K | CT201702 | 施乐 | 3 | 5 | -2 | 库存偏小 | 2 | 2025-07-26 | □已检查 |  |  |  |  |
| 13 | 🟢 | WPID241221000398 | RiTNC5200分装粉红M | 828440 | 理光 | 6 | 8 | -2 | 库存偏小 | 2 | 2025-07-24 | □已检查 |  |  |  |  |
| 14 | 🟢 | WPID231210000129 | 进纸轮-手送 | AF031046 | 理光 | 21 | 19 | +2 | 库存偏大 | 2 | 2025-07-16 | □已检查 |  |  |  |  |
| 15 | 🟢 | WPID231206000021 | RiTNC9200原装碳粉黑K | 828569 | 理光 | 25 | 27 | -2 | 库存偏小 | 2 | 2025-07-25 | □已检查 |  |  |  |  |
| 16 | 🟢 | WPID241221000350 | 青色碳粉 | 828686 | 理光 | 17 | 19 | -2 | 库存偏小 | 2 | 2025-07-28 | □已检查 |  |  |  |  |
| 17 | 🟢 | WPID231210000126 | Ri8100下辊轴承 | AE030086 | 理光 | 26 | 28 | -2 | 库存偏小 | 2 | 2025-07-24 | □已检查 |  |  |  |  |
| 18 | 🟢 | WPID241221000409 | RiTNC定制碳粉C | RI20250215002 | 理光 | 24 | 22 | +2 | 库存偏大 | 1 | 2025-07-28 | □已检查 |  |  |  |  |
| 19 | 🟢 | WPID241221000343 | 黄色碳粉 | 828684 | 理光 | 18 | 20 | -2 | 库存偏小 | 2 | 2025-07-28 | □已检查 |  |  |  |  |
| 20 | 🟢 | WPID250409000002 | C20590墨盒芯片K | RI20250409001 | 爱普生 | 2 | 0 | +2 | 库存偏大 | 1 | 2025-04-16 | □已检查 |  |  |  |  |
| 21 | 🟢 | WPID231206000004 | 零件原装5200手送进纸轮 | AF030049 | 理光 | 15 | 13 | +2 | 库存偏大 | 2 | 2025-07-16 | □已检查 |  |  |  |  |
| 22 | 🟢 | WPID241221000405 | RiTNC6502/6503分装粉黑K | RI20250215005 | 理光 | 8 | 10 | -2 | 库存偏小 | 1 | 2025-07-28 | □已检查 |  |  |  |  |
| 23 | 🟢 | WPID241221000128 | 黑色粉6%FZ | 828281 | 理光 | 32 | 33 | -1 | 库存偏小 | 1 | 2025-07-28 | □已检查 |  |  |  |  |
| 24 | 🟢 | WPID202412230115 | 墨水900ml | 1915877 | 爱普生 | 20 | 21 | -1 | 库存偏小 | 1 | 2025-07-22 | □已检查 |  |  |  |  |
| 25 | 🟢 | WPID241221000412 | RiTNC定制碳粉Y | RI20250215004 | 理光 | 23 | 22 | +1 | 库存偏大 | 2 | 2025-07-28 | □已检查 |  |  |  |  |
| 26 | 🟢 | WPID241221000043 | OPC鼓 | D1799510 | 理光 | 14 | 13 | +1 | 库存偏大 | 1 | 2025-07-28 | □已检查 |  |  |  |  |
| 27 | 🟢 | WPID250331000007 | C5300原装分装碳粉M | 828611 | 理光 | 16 | 17 | -1 | 库存偏小 | 1 | 2025-07-24 | □已检查 |  |  |  |  |
| 28 | 🟢 | WPID241221000171 | 清洁纸 | AE045070 | 理光 | 33 | 32 | +1 | 库存偏大 | 1 | 2025-07-28 | □已检查 |  |  |  |  |
| 29 | 🟢 | WPID241221000403 | RiTNC5200分装粉黄Y | 828439 | 理光 | 6 | 7 | -1 | 库存偏小 | 1 | 2025-07-24 | □已检查 |  |  |  |  |
| 30 | 🟢 | WPID250217000030 | TN620/619分装粉M | TN620/619M | None | 8 | 9 | -1 | 库存偏小 | 1 | 2025-07-24 | □已检查 |  |  |  |  |
| 31 | 🟢 | WPID231210000152 | 搓纸轮 | AF030094 | 理光 | 19 | 20 | -1 | 库存偏小 | 1 | 2025-07-25 | □已检查 |  |  |  |  |
| 32 | 🟢 | WPID241221000121 | 鼓清洁刮板 | D1862230 | 理光 | 24 | 25 | -1 | 库存偏小 | 1 | 2025-07-07 | □已检查 |  |  |  |  |
| 33 | 🟢 | WPID202412230016 | 爱普生C5000墨水-黑330g | 1915877 | 爱普生 | 23 | 24 | -1 | 库存偏小 | 1 | 2025-07-21 | □已检查 |  |  |  |  |
| 34 | 🟢 | WPID241221000251 | RiC5300原装碳粉黄色 | 828610 | 理光 | 22 | 23 | -1 | 库存偏小 | 1 | 2025-07-29 | □已检查 |  |  |  |  |
| 35 | 🟢 | WPID231206000023 | RiTNC9200原装碳粉红M | 828571 | 理光 | 34 | 35 | -1 | 库存偏小 | 1 | 2025-07-25 | □已检查 |  |  |  |  |
| 36 | 🟢 | WPID240606000003 | 大纸库-进纸轮 | AF030171 | 理光 | 33 | 32 | +1 | 库存偏大 | 1 | 2025-07-28 | □已检查 |  |  |  |  |
| 37 | 🟢 | WPID241221000168 | 清洁纸 | AE045062 | 理光 | 50 | 49 | +1 | 库存偏大 | 1 | 2025-07-28 | □已检查 |  |  |  |  |
| 38 | 🟢 | WPID250217000029 | TN620/619分装粉Y | TN620/619Y | None | 7 | 8 | -1 | 库存偏小 | 1 | 2025-07-24 | □已检查 |  |  |  |  |
| 39 | 🟢 | WPID241221000083 | 定影压辊 | AE020220 | 理光 | 8 | 7 | +1 | 库存偏大 | 1 | 2025-07-21 | □已检查 |  |  |  |  |
| 40 | 🟢 | WPID241221000410 | RiTNC定制碳粉K | RI20250215001 | 理光 | 25 | 26 | -1 | 库存偏小 | 1 | 2025-07-28 | □已检查 |  |  |  |  |
| 41 | 🟢 | WPID240328000011 | V80黄色墨粉 | CT202295 | 施乐 | 2 | 1 | +1 | 库存偏大 | 1 | 2025-07-08 | □已检查 |  |  |  |  |
| 42 | 🟢 | WPID241221000131 | 黑色碳粉 | D1469990 | 理光 | 20 | 21 | -1 | 库存偏小 | 1 | 2025-07-08 | □已检查 |  |  |  |  |
| 43 | 🟢 | WPID241221000075 | 定影带 | D1794197 | 理光 | 6 | 7 | -1 | 库存偏小 | 1 | 2025-07-24 | □已检查 |  |  |  |  |
| 44 | 🟢 | WPID241221000210 | 纸张转印偏压辊 | M0EB6668 | 理光 | 5 | 4 | +1 | 库存偏大 | 1 | 2025-07-18 | □已检查 |  |  |  |  |
| 45 | 🟢 | WPID231206000020 | RiTNC9200原装碳粉蓝C | 828572 | 理光 | 35 | 36 | -1 | 库存偏小 | 1 | 2025-07-25 | □已检查 |  |  |  |  |
| 46 | 🟢 | WPID231210000212 | 一转辊 | D2586166 | 理光 | 24 | 23 | +1 | 库存偏大 | 1 | 2025-07-24 | □已检查 |  |  |  |  |
| 47 | 🟢 | WPID231130000013 | 零件鼓蜡条 | D2582411 | 理光 | 53 | 52 | +1 | 库存偏大 | 1 | 2025-07-29 | □已检查 |  |  |  |  |
| 48 | 🟢 | WPID241221000459 | OPC鼓DIA60 C7500 | M0EB9510 | 理光 | 22 | 21 | +1 | 库存偏大 | 1 | 2025-07-24 | □已检查 |  |  |  |  |
| 49 | 🟢 | WPID231210000097 | 零件转印带清洁刮板 | D2586321 | 理光 | 14 | 15 | -1 | 库存偏小 | 9 | 2025-07-29 | □已检查 |  |  |  |  |
| 50 | 🟢 | WPID241221000066 | 充电辊清洁海绵辊 | D1382204 | 理光 | 6 | 7 | -1 | 库存偏小 | 1 | 2025-07-08 | □已检查 |  |  |  |  |
| 51 | 🟢 | WPID241221000209 | 一转转印辊 | M0EB6226 | 理光 | 10 | 9 | +1 | 库存偏大 | 1 | 2025-07-03 | □已检查 |  |  |  |  |
| 52 | 🟢 | WPID231210000153 | 零件5100机器纸盒分离轮 | AF032094 | 理光 | 25 | 24 | +1 | 库存偏大 | 1 | 2025-07-29 | □已检查 |  |  |  |  |

---

## 📈 **检查进度统计**

| 优先级 | 数量 | 已完成 | 进度 |
|--------|------|--------|------|
| 🔴 高优先级 | 2 | 0 | 0% |
| 🟡 中优先级 | 3 | 0 | 0% |
| 🟢 低优先级 | 47 | 0 | 0% |
| **总计** | **52** | **0** | **0%** |

## 💡 **解决方案建议**

### 🔧 **立即执行**
1. **清理重复流水记录**
   ```sql
   -- 建议先备份，再删除重复记录
   DELETE FROM b_storage_warehouse_flow
   WHERE id NOT IN (
       SELECT MIN(id) FROM b_storage_warehouse_flow
       GROUP BY code, flow_id, in_out_type, number, type, DATE(created_at), batch_code
   );
   ```

2. **修复无流水记录商品**
   - 为 WPID202412230101 补充初始化流水记录
   - 或调查该商品的数据来源

3. **调整库存表数量**
   - 高优先级商品需要人工核实实际库存
   - 根据实际情况调整库存表或补充流水记录

## 📊 **SQL检查脚本**

### 1. **库存一致性检查脚本（按批次详细分析）**
```sql
-- 检查库存表与流水计算的差异（包含批次信息）
SELECT
    t1.code AS 商品编码,
    t1.name AS 商品名称,
    t2.number_oem AS OEM编号,
    t2.part_brand AS 品牌,
    t1.sum_warehouse_number AS 库存表数量,
    COALESCE(batch_summary.total_batch_qty, 0) AS 批次汇总数量,
    COALESCE(fsd.flow_calculated_qty_dedup, 0) AS 流水计算数量,
    (t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0)) AS 数量差异,
    CASE
        WHEN fsd.flow_calculated_qty_dedup IS NULL THEN '无流水记录'
        WHEN t1.sum_warehouse_number > COALESCE(fsd.flow_calculated_qty_dedup, 0) THEN '库存表偏大'
        WHEN t1.sum_warehouse_number < COALESCE(fsd.flow_calculated_qty_dedup, 0) THEN '库存表偏小'
        ELSE '数据一致'
    END AS 问题类型,
    CASE
        WHEN ABS(t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0)) >= 10 THEN '高优先级'
        WHEN ABS(t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0)) >= 5 THEN '中优先级'
        WHEN ABS(t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0)) > 0 THEN '低优先级'
        ELSE '正常'
    END AS 优先级,
    batch_summary.batch_details AS 批次明细
FROM b_storage_inventory t1
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0
JOIN b_storage_warehouse t3 ON t1.warehouse_id = t3.id AND t3.deleted = 0
LEFT JOIN (
    SELECT
        code, warehouse_id,
        SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as flow_calculated_qty_dedup
    FROM (
        SELECT DISTINCT
            code, warehouse_id, flow_id, in_out_type, number, type,
            DATE(created_at) as flow_date, batch_code
        FROM b_storage_warehouse_flow
        WHERE deleted = 0
    ) deduplicated_flow
    GROUP BY code, warehouse_id
) fsd ON t1.code = fsd.code AND t1.warehouse_id = fsd.warehouse_id
LEFT JOIN (
    SELECT
        code, warehouse_id,
        SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as total_batch_qty,
        GROUP_CONCAT(
            CONCAT(batch_code, ':',
                   SUM(CASE WHEN in_out_type = 1 THEN number ELSE 0 END), '入-',
                   SUM(CASE WHEN in_out_type = 2 THEN number ELSE 0 END), '出=',
                   SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END))
            ORDER BY batch_code SEPARATOR '; '
        ) as batch_details
    FROM (
        SELECT DISTINCT
            code, warehouse_id, flow_id, in_out_type, number, type,
            DATE(created_at) as flow_date, batch_code
        FROM b_storage_warehouse_flow
        WHERE deleted = 0
    ) deduplicated_flow
    GROUP BY code, warehouse_id
) batch_summary ON t1.code = batch_summary.code AND t1.warehouse_id = batch_summary.warehouse_id
WHERE t1.deleted = 0
  AND (t1.sum_warehouse_number != COALESCE(fsd.flow_calculated_qty_dedup, 0))
ORDER BY ABS(t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0)) DESC;
```

### 2. **重复流水记录检查脚本**
```sql
-- 查找重复的流水记录（包含批次信息）
SELECT
    code AS 商品编码,
    flow_id AS 流水单号,
    batch_code AS 批次号,
    COUNT(*) as 重复次数,
    SUM(number) as 总数量,
    in_out_type AS 进出库类型,
    CASE WHEN in_out_type = 1 THEN '入库' ELSE '出库' END AS 进出库说明,
    type AS 业务类型,
    CASE
        WHEN type = 'purchase' THEN '采购入库'
        WHEN type = 'purchase_return' THEN '采购退库'
        WHEN type = 'apply_return' THEN '申请退库'
        WHEN type = 'engineer_apply' THEN '工程师申请'
        WHEN type = 'shopping_mall' THEN '商城销售'
        ELSE type
    END AS 业务类型说明,
    DATE(created_at) AS 创建日期,
    GROUP_CONCAT(DISTINCT TIME(created_at) ORDER BY created_at SEPARATOR ', ') AS 创建时间列表,
    GROUP_CONCAT(DISTINCT operator_id ORDER BY created_at SEPARATOR ', ') AS 操作员列表
FROM b_storage_warehouse_flow
WHERE deleted = 0
GROUP BY code, flow_id, batch_code, in_out_type, type, DATE(created_at)
HAVING COUNT(*) > 1
ORDER BY 重复次数 DESC, code, batch_code;
```

### 2.1 **按批次分组的重复记录详细检查**
```sql
-- 查看具体重复记录的详细信息（按批次分组）
SELECT
    t1.code AS 商品编码,
    t2.name AS 商品名称,
    t1.batch_code AS 批次号,
    t1.flow_id AS 流水单号,
    t1.number AS 数量,
    t1.in_out_type AS 进出库类型,
    t1.type AS 业务类型,
    t1.created_at AS 创建时间,
    t1.operator_id AS 操作员,
    t1.id AS 流水记录ID
FROM b_storage_warehouse_flow t1
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0
WHERE t1.deleted = 0
  AND (t1.code, t1.flow_id, t1.batch_code, t1.in_out_type, t1.type, DATE(t1.created_at)) IN (
    SELECT code, flow_id, batch_code, in_out_type, type, DATE(created_at)
    FROM b_storage_warehouse_flow
    WHERE deleted = 0
    GROUP BY code, flow_id, batch_code, in_out_type, type, DATE(created_at)
    HAVING COUNT(*) > 1
  )
ORDER BY t1.code, t1.batch_code, t1.flow_id, t1.created_at;
```

### 2.2 **按商品查看批次库存明细**
```sql
-- 查看指定商品的所有批次库存情况
SELECT
    t1.code AS 商品编码,
    t2.name AS 商品名称,
    t1.batch_code AS 批次号,
    SUM(CASE WHEN t1.in_out_type = 1 THEN t1.number ELSE 0 END) AS 入库总量,
    SUM(CASE WHEN t1.in_out_type = 2 THEN t1.number ELSE 0 END) AS 出库总量,
    SUM(CASE WHEN t1.in_out_type = 1 THEN t1.number ELSE -t1.number END) AS 批次库存,
    COUNT(DISTINCT t1.flow_id) AS 流水单数,
    MIN(t1.created_at) AS 首次入库时间,
    MAX(t1.created_at) AS 最后操作时间,
    GROUP_CONCAT(DISTINCT
        CONCAT(t1.type, '(', t1.number, ')')
        ORDER BY t1.created_at SEPARATOR ', '
    ) AS 操作记录
FROM b_storage_warehouse_flow t1
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0
WHERE t1.deleted = 0
  AND t1.warehouse_id = 1731282648590000130  -- 指定仓库ID
  -- AND t1.code = 'WPID231130000016'  -- 可指定具体商品编码
GROUP BY t1.code, t2.name, t1.batch_code
HAVING 批次库存 != 0  -- 只显示有库存的批次
ORDER BY t1.code, t1.batch_code;
```

### 2.3 **批次级别库存差异检查**
```sql
-- 检查每个批次的库存是否与入库商品表一致
SELECT
    t1.code AS 商品编码,
    t2.name AS 商品名称,
    t1.batch_code AS 批次号,
    COALESCE(in_goods.audit_in_warehouse_number, 0) AS 入库商品表数量,
    COALESCE(flow_in.in_qty, 0) AS 流水入库数量,
    COALESCE(flow_out.out_qty, 0) AS 流水出库数量,
    COALESCE(flow_in.in_qty, 0) - COALESCE(flow_out.out_qty, 0) AS 流水计算库存,
    COALESCE(in_goods.audit_in_warehouse_number, 0) - COALESCE(flow_out.out_qty, 0) AS 应有库存,
    (COALESCE(in_goods.audit_in_warehouse_number, 0) - COALESCE(flow_out.out_qty, 0)) -
    (COALESCE(flow_in.in_qty, 0) - COALESCE(flow_out.out_qty, 0)) AS 批次差异,
    CASE
        WHEN (COALESCE(in_goods.audit_in_warehouse_number, 0) - COALESCE(flow_out.out_qty, 0)) -
             (COALESCE(flow_in.in_qty, 0) - COALESCE(flow_out.out_qty, 0)) != 0
        THEN '❌ 批次数据不一致'
        ELSE '✅ 批次数据一致'
    END AS 批次状态,
    in_goods.price AS 入库单价,
    in_goods.created_at AS 入库时间
FROM (
    SELECT DISTINCT code, batch_code
    FROM b_storage_warehouse_flow
    WHERE deleted = 0 AND warehouse_id = 1731282648590000130
    UNION
    SELECT DISTINCT code, batch_code
    FROM b_storage_in_warehouse_goods
    WHERE warehouse_id = 1731282648590000130
) batch_list
JOIN b_storage_article t2 ON batch_list.code = t2.code AND t2.deleted = 0
LEFT JOIN (
    SELECT code, batch_code, SUM(audit_in_warehouse_number) as audit_in_warehouse_number,
           AVG(price) as price, MIN(created_at) as created_at
    FROM b_storage_in_warehouse_goods
    WHERE warehouse_id = 1731282648590000130
    GROUP BY code, batch_code
) in_goods ON batch_list.code = in_goods.code AND batch_list.batch_code = in_goods.batch_code
LEFT JOIN (
    SELECT code, batch_code, SUM(number) as in_qty
    FROM b_storage_warehouse_flow
    WHERE deleted = 0 AND warehouse_id = 1731282648590000130 AND in_out_type = 1
    GROUP BY code, batch_code
) flow_in ON batch_list.code = flow_in.code AND batch_list.batch_code = flow_in.batch_code
LEFT JOIN (
    SELECT code, batch_code, SUM(number) as out_qty
    FROM b_storage_warehouse_flow
    WHERE deleted = 0 AND warehouse_id = 1731282648590000130 AND in_out_type = 2
    GROUP BY code, batch_code
) flow_out ON batch_list.code = flow_out.code AND batch_list.batch_code = flow_out.batch_code
WHERE (COALESCE(in_goods.audit_in_warehouse_number, 0) - COALESCE(flow_out.out_qty, 0)) -
      (COALESCE(flow_in.in_qty, 0) - COALESCE(flow_out.out_qty, 0)) != 0
   OR (COALESCE(in_goods.audit_in_warehouse_number, 0) > 0 AND COALESCE(flow_in.in_qty, 0) = 0)
   OR (COALESCE(flow_in.in_qty, 0) > 0 AND COALESCE(in_goods.audit_in_warehouse_number, 0) = 0)
ORDER BY ABS((COALESCE(in_goods.audit_in_warehouse_number, 0) - COALESCE(flow_out.out_qty, 0)) -
             (COALESCE(flow_in.in_qty, 0) - COALESCE(flow_out.out_qty, 0))) DESC,
         batch_list.code, batch_list.batch_code;
```

### 3. **无流水记录商品检查脚本**
```sql
-- 查找有库存但无流水记录的商品
SELECT
    t1.code AS 商品编码,
    t1.name AS 商品名称,
    t2.number_oem AS OEM编号,
    t1.sum_warehouse_number AS 库存数量,
    t1.created_at AS 库存创建时间,
    t1.updated_at AS 库存更新时间
FROM b_storage_inventory t1
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0
LEFT JOIN b_storage_warehouse_flow t3 ON t1.code = t3.code AND t1.warehouse_id = t3.warehouse_id AND t3.deleted = 0
WHERE t1.deleted = 0
  AND t1.sum_warehouse_number > 0
  AND t3.code IS NULL
ORDER BY t1.sum_warehouse_number DESC;
```

### 4. **库存统计汇总脚本**
```sql
-- 库存不一致问题统计
SELECT
    CASE
        WHEN fsd.flow_calculated_qty_dedup IS NULL THEN '无流水记录'
        WHEN t1.sum_warehouse_number > COALESCE(fsd.flow_calculated_qty_dedup, 0) THEN '库存表偏大'
        WHEN t1.sum_warehouse_number < COALESCE(fsd.flow_calculated_qty_dedup, 0) THEN '库存表偏小'
    END as 问题类型,
    COUNT(*) as 商品数量,
    SUM(ABS(t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0))) as 总差异量,
    AVG(ABS(t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0))) as 平均差异,
    MAX(ABS(t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0))) as 最大差异
FROM b_storage_inventory t1
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0
LEFT JOIN (
    SELECT
        code, warehouse_id,
        SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as flow_calculated_qty_dedup
    FROM (
        SELECT DISTINCT
            code, warehouse_id, flow_id, in_out_type, number, type,
            DATE(created_at) as flow_date, batch_code
        FROM b_storage_warehouse_flow
        WHERE deleted = 0
    ) deduplicated_flow
    GROUP BY code, warehouse_id
) fsd ON t1.code = fsd.code AND t1.warehouse_id = fsd.warehouse_id
WHERE t1.deleted = 0
  AND (t1.sum_warehouse_number != COALESCE(fsd.flow_calculated_qty_dedup, 0))
GROUP BY 问题类型
ORDER BY 商品数量 DESC;
```

### 5. **数据修复脚本模板**
```sql
-- 1. 备份重复记录（执行删除前先备份，包含批次信息）
CREATE TABLE b_storage_warehouse_flow_backup_20250801 AS
SELECT * FROM b_storage_warehouse_flow
WHERE deleted = 0
  AND (code, flow_id, batch_code, in_out_type, type, DATE(created_at)) IN (
    SELECT code, flow_id, batch_code, in_out_type, type, DATE(created_at)
    FROM b_storage_warehouse_flow
    WHERE deleted = 0
    GROUP BY code, flow_id, batch_code, in_out_type, type, DATE(created_at)
    HAVING COUNT(*) > 1
  );

-- 2. 删除重复流水记录（保留最早的记录，包含批次信息）
DELETE t1 FROM b_storage_warehouse_flow t1
INNER JOIN b_storage_warehouse_flow t2
WHERE t1.id > t2.id
  AND t1.code = t2.code
  AND t1.flow_id = t2.flow_id
  AND t1.batch_code = t2.batch_code  -- 增加批次条件
  AND t1.in_out_type = t2.in_out_type
  AND t1.type = t2.type
  AND DATE(t1.created_at) = DATE(t2.created_at)
  AND t1.deleted = 0
  AND t2.deleted = 0;

-- 3. 为无流水记录商品补充初始化记录（示例：WPID202412230101）
INSERT INTO b_storage_warehouse_flow (
    flow_id, code, warehouse_id, in_out_type, number, type,
    batch_code, created_at, updated_at, operator_id, deleted
) VALUES (
    'INIT20250801001', 'WPID202412230101', 1731282648590000130, 1, 9, 'init',
    'INIT001', NOW(), NOW(), 1, 0
);

-- 4. 库存表数量调整（根据实际盘点结果）
-- 示例：调整理光5200原装鼓刮板2441的库存数量
UPDATE b_storage_inventory
SET sum_warehouse_number = 22, -- 根据实际盘点结果调整
    updated_at = NOW()
WHERE code = 'WPID231130000016'
  AND warehouse_id = 1731282648590000130;
```

### 6. **定期检查脚本**
```sql
-- 每日库存一致性检查（建议加入定时任务）
SELECT
    DATE(NOW()) as 检查日期,
    COUNT(*) as 不一致商品数,
    SUM(ABS(t1.sum_warehouse_number - COALESCE(fsd.flow_calculated_qty_dedup, 0))) as 总差异量,
    CASE
        WHEN COUNT(*) = 0 THEN '✅ 数据一致'
        WHEN COUNT(*) <= 5 THEN '⚠️ 轻微差异'
        WHEN COUNT(*) <= 20 THEN '🟡 中等差异'
        ELSE '🔴 严重差异'
    END as 状态评估
FROM b_storage_inventory t1
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0
LEFT JOIN (
    SELECT
        code, warehouse_id,
        SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as flow_calculated_qty_dedup
    FROM (
        SELECT DISTINCT
            code, warehouse_id, flow_id, in_out_type, number, type,
            DATE(created_at) as flow_date, batch_code
        FROM b_storage_warehouse_flow
        WHERE deleted = 0
    ) deduplicated_flow
    GROUP BY code, warehouse_id
) fsd ON t1.code = fsd.code AND t1.warehouse_id = fsd.warehouse_id
WHERE t1.deleted = 0
  AND (t1.sum_warehouse_number != COALESCE(fsd.flow_calculated_qty_dedup, 0));
```

### 7. **问题商品批次详情查询脚本**
```sql
-- 查看指定问题商品的所有批次详细信息
-- 使用方法：将 'WPID231130000016' 替换为要查询的商品编码
SELECT
    '=== 商品基本信息 ===' AS 信息类型,
    t1.code AS 商品编码,
    t1.name AS 商品名称,
    t2.number_oem AS OEM编号,
    t2.part_brand AS 品牌,
    t1.sum_warehouse_number AS 库存表数量,
    t1.created_at AS 库存创建时间,
    t1.updated_at AS 库存更新时间,
    '' AS 批次号,
    '' AS 入库数量,
    '' AS 出库数量,
    '' AS 批次库存,
    '' AS 入库单价,
    '' AS 操作时间

UNION ALL

SELECT
    '=== 批次库存明细 ===' AS 信息类型,
    batch_detail.code AS 商品编码,
    '' AS 商品名称,
    '' AS OEM编号,
    '' AS 品牌,
    '' AS 库存表数量,
    '' AS 库存创建时间,
    '' AS 库存更新时间,
    batch_detail.batch_code AS 批次号,
    CAST(batch_detail.in_qty AS CHAR) AS 入库数量,
    CAST(batch_detail.out_qty AS CHAR) AS 出库数量,
    CAST(batch_detail.batch_stock AS CHAR) AS 批次库存,
    CAST(batch_detail.avg_price AS CHAR) AS 入库单价,
    batch_detail.first_in_time AS 操作时间
FROM (
    SELECT
        flow_summary.code,
        flow_summary.batch_code,
        COALESCE(flow_summary.in_qty, 0) as in_qty,
        COALESCE(flow_summary.out_qty, 0) as out_qty,
        COALESCE(flow_summary.in_qty, 0) - COALESCE(flow_summary.out_qty, 0) as batch_stock,
        in_goods.avg_price,
        in_goods.first_in_time
    FROM (
        SELECT
            code, batch_code,
            SUM(CASE WHEN in_out_type = 1 THEN number ELSE 0 END) as in_qty,
            SUM(CASE WHEN in_out_type = 2 THEN number ELSE 0 END) as out_qty
        FROM b_storage_warehouse_flow
        WHERE deleted = 0
          AND warehouse_id = 1731282648590000130
          AND code = 'WPID231130000016'  -- 替换为要查询的商品编码
        GROUP BY code, batch_code
    ) flow_summary
    LEFT JOIN (
        SELECT code, batch_code, AVG(price) as avg_price, MIN(created_at) as first_in_time
        FROM b_storage_in_warehouse_goods
        WHERE warehouse_id = 1731282648590000130
          AND code = 'WPID231130000016'  -- 替换为要查询的商品编码
        GROUP BY code, batch_code
    ) in_goods ON flow_summary.code = in_goods.code AND flow_summary.batch_code = in_goods.batch_code
) batch_detail

UNION ALL

SELECT
    '=== 流水记录汇总 ===' AS 信息类型,
    flow_stats.code AS 商品编码,
    '' AS 商品名称,
    '' AS OEM编号,
    '' AS 品牌,
    CAST(flow_stats.total_records AS CHAR) AS 库存表数量,
    CAST(flow_stats.dedup_records AS CHAR) AS 库存创建时间,
    CAST(flow_stats.duplicate_records AS CHAR) AS 库存更新时间,
    '总记录/去重/重复' AS 批次号,
    flow_stats.first_flow_time AS 入库数量,
    flow_stats.last_flow_time AS 出库数量,
    '' AS 批次库存,
    '' AS 入库单价,
    '' AS 操作时间
FROM (
    SELECT
        code,
        COUNT(*) as total_records,
        COUNT(DISTINCT CONCAT(flow_id, '-', batch_code, '-', in_out_type, '-', number, '-', type, '-', DATE(created_at))) as dedup_records,
        COUNT(*) - COUNT(DISTINCT CONCAT(flow_id, '-', batch_code, '-', in_out_type, '-', number, '-', type, '-', DATE(created_at))) as duplicate_records,
        MIN(created_at) as first_flow_time,
        MAX(created_at) as last_flow_time
    FROM b_storage_warehouse_flow
    WHERE deleted = 0
      AND warehouse_id = 1731282648590000130
      AND code = 'WPID231130000016'  -- 替换为要查询的商品编码
    GROUP BY code
) flow_stats
JOIN b_storage_inventory t1 ON flow_stats.code = t1.code AND t1.warehouse_id = 1731282648590000130
JOIN b_storage_article t2 ON t1.code = t2.code AND t2.deleted = 0

ORDER BY 信息类型, 批次号;
```

### 8. **批次级别问题分析示例**
以 **理光5200原装鼓刮板2441 (WPID231130000016)** 为例：

#### 📊 **批次库存分析结果**
```
商品编码: WPID231130000016
商品名称: 理光5200原装鼓（润滑）刮板2441
库存表数量: 25件
实际批次库存: 25件 (仅250710005批次有库存)

批次明细:
┌─────────────┬────────┬────────┬────────┬──────────┐
│   批次号    │ 入库量 │ 出库量 │ 库存量 │  状态    │
├─────────────┼────────┼────────┼────────┼──────────┤
│ 20250125    │   46   │   46   │   0    │ 已清空   │
│ 250212008   │   52   │   52   │   0    │ 已清空   │
│ 250225011   │   16   │   16   │   0    │ 已清空   │
│ 250302013   │   79   │   79   │   0    │ 已清空   │
│ 250306003   │   53   │   53   │   0    │ 已清空   │
│ 250406020   │  100   │  100   │   0    │ 已清空   │
│ 250414023   │   67   │   67   │   0    │ 已清空   │
│ 250417028   │   30   │   30   │   0    │ 已清空   │
│ 250509007   │   47   │   47   │   0    │ 已清空   │
│ 250521010   │   55   │   55   │   0    │ 已清空   │
│ 250522013   │  114   │  114   │   0    │ 已清空   │
│ 250615007   │  125   │  125   │   0    │ 已清空   │
│ 250710005   │   80   │   55   │  25    │ 有库存   │
└─────────────┴────────┴────────┴────────┴──────────┘

重复记录问题:
- 发现15个流水单存在重复记录
- 主要集中在申请退库(apply_return)和工程师申请(engineer_apply)业务
- 每个重复记录都是同一天内的相同操作被记录2次
```

#### 🔍 **问题分析结论**
1. **数据一致性**: ✅ 库存表数量(25)与批次计算数量(25)一致
2. **重复记录**: ❌ 存在15个重复流水记录，但不影响最终库存计算
3. **批次管理**: ✅ 批次进出库记录完整，只有最新批次250710005有剩余库存
4. **处理建议**: 清理重复记录，但库存数量无需调整

#### 📝 **SQL验证脚本**
```sql
-- 验证该商品的库存一致性
SELECT
    '库存表数量' as 类型, sum_warehouse_number as 数量
FROM b_storage_inventory
WHERE code = 'WPID231130000016' AND warehouse_id = 1731282648590000130

UNION ALL

SELECT
    '批次计算数量' as 类型,
    SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as 数量
FROM b_storage_warehouse_flow
WHERE code = 'WPID231130000016'
  AND warehouse_id = 1731282648590000130
  AND deleted = 0

UNION ALL

SELECT
    '去重后数量' as 类型,
    SUM(CASE WHEN in_out_type = 1 THEN number ELSE -number END) as 数量
FROM (
    SELECT DISTINCT code, flow_id, batch_code, in_out_type, number, type, DATE(created_at)
    FROM b_storage_warehouse_flow
    WHERE code = 'WPID231130000016'
      AND warehouse_id = 1731282648590000130
      AND deleted = 0
) dedup_flow;
```

### 🛡️ **预防措施**
1. **添加数据库约束**
   ```sql
   ALTER TABLE b_storage_warehouse_flow
   ADD UNIQUE KEY uk_flow_unique (code, flow_id, in_out_type, batch_code, DATE(created_at));
   ```

2. **建立监控机制**
   - 每日运行库存一致性检查
   - 设置差异阈值告警
   - 建立数据修复标准流程

3. **优化业务流程**
   - 确保每次库存变动都生成对应流水记录
   - 建立库存盘点与流水核对机制
   - 定期进行数据一致性审计

## 📝 **检查注意事项**

1. **优先处理**: 按优先级顺序检查，先处理🔴高优先级商品
2. **仔细核对**: 确认商品编码、名称、OEM编号无误
3. **准确计数**: 实地盘点时要仔细清点，避免遗漏
4. **及时记录**: 检查完成后立即填写相关信息
5. **异常上报**: 发现重大差异时及时上报主管

---

**检查负责人**: ________________
**检查开始日期**: ________________
**预计完成日期**: ________________
**报告生成时间**: 2025-08-01
**数据范围**: 至简智印本部仓 (ID: 1731282648590000130)
**建议复查周期**: 每周一次，直至问题全部解决
